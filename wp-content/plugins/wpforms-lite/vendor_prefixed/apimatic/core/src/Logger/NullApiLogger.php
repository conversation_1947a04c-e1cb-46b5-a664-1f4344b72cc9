<?php

namespace WPForms\Vendor\Core\Logger;

use WPF<PERSON>\Vendor\CoreInterfaces\Core\Logger\ApiLoggerInterface;
use WPForms\Vendor\CoreInterfaces\Core\Request\RequestInterface;
use WPForms\Vendor\CoreInterfaces\Core\Response\ResponseInterface;
class Null<PERSON><PERSON>Logger implements ApiLoggerInterface
{
    /**
     * @inheritDoc
     */
    public function logRequest(RequestInterface $request) : void
    {
        // noop
    }
    /**
     * @inheritDoc
     */
    public function logResponse(ResponseInterface $response) : void
    {
        // noop
    }
}
