<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'Attribute' => $vendorDir . '/symfony/polyfill-php80/Resources/stubs/Attribute.php',
    'Composer\\InstalledVersions' => $vendorDir . '/composer/InstalledVersions.php',
    'PhpToken' => $vendorDir . '/symfony/polyfill-php80/Resources/stubs/PhpToken.php',
    'Stringable' => $vendorDir . '/symfony/polyfill-php80/Resources/stubs/Stringable.php',
    'UnhandledMatchError' => $vendorDir . '/symfony/polyfill-php80/Resources/stubs/UnhandledMatchError.php',
    'ValueError' => $vendorDir . '/symfony/polyfill-php80/Resources/stubs/ValueError.php',
    'WPForms\\Vendor\\Address' => $baseDir . '/vendor_prefixed/apimatic/jsonmapper/example/Address.php',
    'WPForms\\Vendor\\ClassWithCtor' => $baseDir . '/vendor_prefixed/apimatic/jsonmapper/tests/JsonMapperTest/ClassWithCtor.php',
    'WPForms\\Vendor\\ComplexClassWithCtor' => $baseDir . '/vendor_prefixed/apimatic/jsonmapper/tests/JsonMapperTest/ComplexClassWithCtor.php',
    'WPForms\\Vendor\\Contact' => $baseDir . '/vendor_prefixed/apimatic/jsonmapper/example/Contact.php',
    'WPForms\\Vendor\\CoreInterfaces\\Core\\Authentication\\AuthGroup' => $baseDir . '/vendor_prefixed/apimatic/core-interfaces/src/Core/Authentication/AuthGroup.php',
    'WPForms\\Vendor\\CoreInterfaces\\Core\\Authentication\\AuthInterface' => $baseDir . '/vendor_prefixed/apimatic/core-interfaces/src/Core/Authentication/AuthInterface.php',
    'WPForms\\Vendor\\CoreInterfaces\\Core\\ContextInterface' => $baseDir . '/vendor_prefixed/apimatic/core-interfaces/src/Core/ContextInterface.php',
    'WPForms\\Vendor\\CoreInterfaces\\Core\\Format' => $baseDir . '/vendor_prefixed/apimatic/core-interfaces/src/Core/Format.php',
    'WPForms\\Vendor\\CoreInterfaces\\Core\\Logger\\ApiLoggerInterface' => $baseDir . '/vendor_prefixed/apimatic/core-interfaces/src/Core/Logger/ApiLoggerInterface.php',
    'WPForms\\Vendor\\CoreInterfaces\\Core\\Request\\NonEmptyParamInterface' => $baseDir . '/vendor_prefixed/apimatic/core-interfaces/src/Core/Request/NonEmptyParamInterface.php',
    'WPForms\\Vendor\\CoreInterfaces\\Core\\Request\\ParamInterface' => $baseDir . '/vendor_prefixed/apimatic/core-interfaces/src/Core/Request/ParamInterface.php',
    'WPForms\\Vendor\\CoreInterfaces\\Core\\Request\\RequestArraySerialization' => $baseDir . '/vendor_prefixed/apimatic/core-interfaces/src/Core/Request/RequestArraySerialization.php',
    'WPForms\\Vendor\\CoreInterfaces\\Core\\Request\\RequestInterface' => $baseDir . '/vendor_prefixed/apimatic/core-interfaces/src/Core/Request/RequestInterface.php',
    'WPForms\\Vendor\\CoreInterfaces\\Core\\Request\\RequestMethod' => $baseDir . '/vendor_prefixed/apimatic/core-interfaces/src/Core/Request/RequestMethod.php',
    'WPForms\\Vendor\\CoreInterfaces\\Core\\Request\\RequestSetterInterface' => $baseDir . '/vendor_prefixed/apimatic/core-interfaces/src/Core/Request/RequestSetterInterface.php',
    'WPForms\\Vendor\\CoreInterfaces\\Core\\Request\\TypeValidatorInterface' => $baseDir . '/vendor_prefixed/apimatic/core-interfaces/src/Core/Request/TypeValidatorInterface.php',
    'WPForms\\Vendor\\CoreInterfaces\\Core\\Response\\ResponseInterface' => $baseDir . '/vendor_prefixed/apimatic/core-interfaces/src/Core/Response/ResponseInterface.php',
    'WPForms\\Vendor\\CoreInterfaces\\Http\\HttpClientInterface' => $baseDir . '/vendor_prefixed/apimatic/core-interfaces/src/Http/HttpClientInterface.php',
    'WPForms\\Vendor\\CoreInterfaces\\Http\\HttpConfigurations' => $baseDir . '/vendor_prefixed/apimatic/core-interfaces/src/Http/HttpConfigurations.php',
    'WPForms\\Vendor\\CoreInterfaces\\Http\\RetryOption' => $baseDir . '/vendor_prefixed/apimatic/core-interfaces/src/Http/RetryOption.php',
    'WPForms\\Vendor\\CoreInterfaces\\Sdk\\ConverterInterface' => $baseDir . '/vendor_prefixed/apimatic/core-interfaces/src/Sdk/ConverterInterface.php',
    'WPForms\\Vendor\\CoreInterfaces\\Sdk\\ExceptionInterface' => $baseDir . '/vendor_prefixed/apimatic/core-interfaces/src/Sdk/ExceptionInterface.php',
    'WPForms\\Vendor\\Core\\ApiCall' => $baseDir . '/vendor_prefixed/apimatic/core/src/ApiCall.php',
    'WPForms\\Vendor\\Core\\Authentication\\Auth' => $baseDir . '/vendor_prefixed/apimatic/core/src/Authentication/Auth.php',
    'WPForms\\Vendor\\Core\\Authentication\\CoreAuth' => $baseDir . '/vendor_prefixed/apimatic/core/src/Authentication/CoreAuth.php',
    'WPForms\\Vendor\\Core\\Client' => $baseDir . '/vendor_prefixed/apimatic/core/src/Client.php',
    'WPForms\\Vendor\\Core\\ClientBuilder' => $baseDir . '/vendor_prefixed/apimatic/core/src/ClientBuilder.php',
    'WPForms\\Vendor\\Core\\Exceptions\\AuthValidationException' => $baseDir . '/vendor_prefixed/apimatic/core/src/Exceptions/AuthValidationException.php',
    'WPForms\\Vendor\\Core\\Logger\\ApiLogger' => $baseDir . '/vendor_prefixed/apimatic/core/src/Logger/ApiLogger.php',
    'WPForms\\Vendor\\Core\\Logger\\Configuration\\BaseHttpLoggingConfiguration' => $baseDir . '/vendor_prefixed/apimatic/core/src/Logger/Configuration/BaseHttpLoggingConfiguration.php',
    'WPForms\\Vendor\\Core\\Logger\\Configuration\\LoggingConfiguration' => $baseDir . '/vendor_prefixed/apimatic/core/src/Logger/Configuration/LoggingConfiguration.php',
    'WPForms\\Vendor\\Core\\Logger\\Configuration\\RequestConfiguration' => $baseDir . '/vendor_prefixed/apimatic/core/src/Logger/Configuration/RequestConfiguration.php',
    'WPForms\\Vendor\\Core\\Logger\\Configuration\\ResponseConfiguration' => $baseDir . '/vendor_prefixed/apimatic/core/src/Logger/Configuration/ResponseConfiguration.php',
    'WPForms\\Vendor\\Core\\Logger\\ConsoleLogger' => $baseDir . '/vendor_prefixed/apimatic/core/src/Logger/ConsoleLogger.php',
    'WPForms\\Vendor\\Core\\Logger\\LoggerConstants' => $baseDir . '/vendor_prefixed/apimatic/core/src/Logger/LoggerConstants.php',
    'WPForms\\Vendor\\Core\\Logger\\NullApiLogger' => $baseDir . '/vendor_prefixed/apimatic/core/src/Logger/NullApiLogger.php',
    'WPForms\\Vendor\\Core\\Request\\Parameters\\AdditionalFormParams' => $baseDir . '/vendor_prefixed/apimatic/core/src/Request/Parameters/AdditionalFormParams.php',
    'WPForms\\Vendor\\Core\\Request\\Parameters\\AdditionalHeaderParams' => $baseDir . '/vendor_prefixed/apimatic/core/src/Request/Parameters/AdditionalHeaderParams.php',
    'WPForms\\Vendor\\Core\\Request\\Parameters\\AdditionalQueryParams' => $baseDir . '/vendor_prefixed/apimatic/core/src/Request/Parameters/AdditionalQueryParams.php',
    'WPForms\\Vendor\\Core\\Request\\Parameters\\BodyParam' => $baseDir . '/vendor_prefixed/apimatic/core/src/Request/Parameters/BodyParam.php',
    'WPForms\\Vendor\\Core\\Request\\Parameters\\EncodedParam' => $baseDir . '/vendor_prefixed/apimatic/core/src/Request/Parameters/EncodedParam.php',
    'WPForms\\Vendor\\Core\\Request\\Parameters\\FormParam' => $baseDir . '/vendor_prefixed/apimatic/core/src/Request/Parameters/FormParam.php',
    'WPForms\\Vendor\\Core\\Request\\Parameters\\HeaderParam' => $baseDir . '/vendor_prefixed/apimatic/core/src/Request/Parameters/HeaderParam.php',
    'WPForms\\Vendor\\Core\\Request\\Parameters\\MultipleParams' => $baseDir . '/vendor_prefixed/apimatic/core/src/Request/Parameters/MultipleParams.php',
    'WPForms\\Vendor\\Core\\Request\\Parameters\\Parameter' => $baseDir . '/vendor_prefixed/apimatic/core/src/Request/Parameters/Parameter.php',
    'WPForms\\Vendor\\Core\\Request\\Parameters\\QueryParam' => $baseDir . '/vendor_prefixed/apimatic/core/src/Request/Parameters/QueryParam.php',
    'WPForms\\Vendor\\Core\\Request\\Parameters\\TemplateParam' => $baseDir . '/vendor_prefixed/apimatic/core/src/Request/Parameters/TemplateParam.php',
    'WPForms\\Vendor\\Core\\Request\\Request' => $baseDir . '/vendor_prefixed/apimatic/core/src/Request/Request.php',
    'WPForms\\Vendor\\Core\\Request\\RequestBuilder' => $baseDir . '/vendor_prefixed/apimatic/core/src/Request/RequestBuilder.php',
    'WPForms\\Vendor\\Core\\Response\\Context' => $baseDir . '/vendor_prefixed/apimatic/core/src/Response/Context.php',
    'WPForms\\Vendor\\Core\\Response\\ResponseError' => $baseDir . '/vendor_prefixed/apimatic/core/src/Response/ResponseError.php',
    'WPForms\\Vendor\\Core\\Response\\ResponseHandler' => $baseDir . '/vendor_prefixed/apimatic/core/src/Response/ResponseHandler.php',
    'WPForms\\Vendor\\Core\\Response\\Types\\DeserializableType' => $baseDir . '/vendor_prefixed/apimatic/core/src/Response/Types/DeserializableType.php',
    'WPForms\\Vendor\\Core\\Response\\Types\\ErrorType' => $baseDir . '/vendor_prefixed/apimatic/core/src/Response/Types/ErrorType.php',
    'WPForms\\Vendor\\Core\\Response\\Types\\ResponseMultiType' => $baseDir . '/vendor_prefixed/apimatic/core/src/Response/Types/ResponseMultiType.php',
    'WPForms\\Vendor\\Core\\Response\\Types\\ResponseType' => $baseDir . '/vendor_prefixed/apimatic/core/src/Response/Types/ResponseType.php',
    'WPForms\\Vendor\\Core\\TestCase\\BodyMatchers\\BodyComparator' => $baseDir . '/vendor_prefixed/apimatic/core/src/TestCase/BodyMatchers/BodyComparator.php',
    'WPForms\\Vendor\\Core\\TestCase\\BodyMatchers\\BodyMatcher' => $baseDir . '/vendor_prefixed/apimatic/core/src/TestCase/BodyMatchers/BodyMatcher.php',
    'WPForms\\Vendor\\Core\\TestCase\\BodyMatchers\\KeysAndValuesBodyMatcher' => $baseDir . '/vendor_prefixed/apimatic/core/src/TestCase/BodyMatchers/KeysAndValuesBodyMatcher.php',
    'WPForms\\Vendor\\Core\\TestCase\\BodyMatchers\\KeysBodyMatcher' => $baseDir . '/vendor_prefixed/apimatic/core/src/TestCase/BodyMatchers/KeysBodyMatcher.php',
    'WPForms\\Vendor\\Core\\TestCase\\BodyMatchers\\NativeBodyMatcher' => $baseDir . '/vendor_prefixed/apimatic/core/src/TestCase/BodyMatchers/NativeBodyMatcher.php',
    'WPForms\\Vendor\\Core\\TestCase\\BodyMatchers\\RawBodyMatcher' => $baseDir . '/vendor_prefixed/apimatic/core/src/TestCase/BodyMatchers/RawBodyMatcher.php',
    'WPForms\\Vendor\\Core\\TestCase\\CoreTestCase' => $baseDir . '/vendor_prefixed/apimatic/core/src/TestCase/CoreTestCase.php',
    'WPForms\\Vendor\\Core\\TestCase\\HeadersMatcher' => $baseDir . '/vendor_prefixed/apimatic/core/src/TestCase/HeadersMatcher.php',
    'WPForms\\Vendor\\Core\\TestCase\\StatusCodeMatcher' => $baseDir . '/vendor_prefixed/apimatic/core/src/TestCase/StatusCodeMatcher.php',
    'WPForms\\Vendor\\Core\\TestCase\\TestParam' => $baseDir . '/vendor_prefixed/apimatic/core/src/TestCase/TestParam.php',
    'WPForms\\Vendor\\Core\\Tests\\ApiCallTest' => $baseDir . '/vendor_prefixed/apimatic/core/tests/ApiCallTest.php',
    'WPForms\\Vendor\\Core\\Tests\\AuthenticationTest' => $baseDir . '/vendor_prefixed/apimatic/core/tests/AuthenticationTest.php',
    'WPForms\\Vendor\\Core\\Tests\\ClientTest' => $baseDir . '/vendor_prefixed/apimatic/core/tests/ClientTest.php',
    'WPForms\\Vendor\\Core\\Tests\\CoreTestCaseTest' => $baseDir . '/vendor_prefixed/apimatic/core/tests/CoreTestCaseTest.php',
    'WPForms\\Vendor\\Core\\Tests\\EndToEndTest' => $baseDir . '/vendor_prefixed/apimatic/core/tests/EndToEndTest.php',
    'WPForms\\Vendor\\Core\\Tests\\LoggerTest' => $baseDir . '/vendor_prefixed/apimatic/core/tests/LoggerTest.php',
    'WPForms\\Vendor\\Core\\Tests\\Mocking\\Authentication\\FormAuthManager' => $baseDir . '/vendor_prefixed/apimatic/core/tests/Mocking/Authentication/FormAuthManager.php',
    'WPForms\\Vendor\\Core\\Tests\\Mocking\\Authentication\\HeaderAuthManager' => $baseDir . '/vendor_prefixed/apimatic/core/tests/Mocking/Authentication/HeaderAuthManager.php',
    'WPForms\\Vendor\\Core\\Tests\\Mocking\\Authentication\\QueryAuthManager' => $baseDir . '/vendor_prefixed/apimatic/core/tests/Mocking/Authentication/QueryAuthManager.php',
    'WPForms\\Vendor\\Core\\Tests\\Mocking\\Logger\\LogEntry' => $baseDir . '/vendor_prefixed/apimatic/core/tests/Mocking/Logger/LogEntry.php',
    'WPForms\\Vendor\\Core\\Tests\\Mocking\\Logger\\MockLogger' => $baseDir . '/vendor_prefixed/apimatic/core/tests/Mocking/Logger/MockLogger.php',
    'WPForms\\Vendor\\Core\\Tests\\Mocking\\Logger\\MockPrinter' => $baseDir . '/vendor_prefixed/apimatic/core/tests/Mocking/Logger/MockPrinter.php',
    'WPForms\\Vendor\\Core\\Tests\\Mocking\\MockConverter' => $baseDir . '/vendor_prefixed/apimatic/core/tests/Mocking/MockConverter.php',
    'WPForms\\Vendor\\Core\\Tests\\Mocking\\MockHelper' => $baseDir . '/vendor_prefixed/apimatic/core/tests/Mocking/MockHelper.php',
    'WPForms\\Vendor\\Core\\Tests\\Mocking\\MockHttpClient' => $baseDir . '/vendor_prefixed/apimatic/core/tests/Mocking/MockHttpClient.php',
    'WPForms\\Vendor\\Core\\Tests\\Mocking\\Other\\Customer' => $baseDir . '/vendor_prefixed/apimatic/core/tests/Mocking/Other/Customer.php',
    'WPForms\\Vendor\\Core\\Tests\\Mocking\\Other\\MockChild1' => $baseDir . '/vendor_prefixed/apimatic/core/tests/Mocking/Other/MockChild1.php',
    'WPForms\\Vendor\\Core\\Tests\\Mocking\\Other\\MockChild2' => $baseDir . '/vendor_prefixed/apimatic/core/tests/Mocking/Other/MockChild2.php',
    'WPForms\\Vendor\\Core\\Tests\\Mocking\\Other\\MockChild3' => $baseDir . '/vendor_prefixed/apimatic/core/tests/Mocking/Other/MockChild3.php',
    'WPForms\\Vendor\\Core\\Tests\\Mocking\\Other\\MockClass' => $baseDir . '/vendor_prefixed/apimatic/core/tests/Mocking/Other/MockClass.php',
    'WPForms\\Vendor\\Core\\Tests\\Mocking\\Other\\MockException' => $baseDir . '/vendor_prefixed/apimatic/core/tests/Mocking/Other/MockException.php',
    'WPForms\\Vendor\\Core\\Tests\\Mocking\\Other\\MockException1' => $baseDir . '/vendor_prefixed/apimatic/core/tests/Mocking/Other/MockException1.php',
    'WPForms\\Vendor\\Core\\Tests\\Mocking\\Other\\MockException2' => $baseDir . '/vendor_prefixed/apimatic/core/tests/Mocking/Other/MockException2.php',
    'WPForms\\Vendor\\Core\\Tests\\Mocking\\Other\\MockException3' => $baseDir . '/vendor_prefixed/apimatic/core/tests/Mocking/Other/MockException3.php',
    'WPForms\\Vendor\\Core\\Tests\\Mocking\\Other\\Order' => $baseDir . '/vendor_prefixed/apimatic/core/tests/Mocking/Other/Order.php',
    'WPForms\\Vendor\\Core\\Tests\\Mocking\\Other\\Person' => $baseDir . '/vendor_prefixed/apimatic/core/tests/Mocking/Other/Person.php',
    'WPForms\\Vendor\\Core\\Tests\\Mocking\\Response\\MockResponse' => $baseDir . '/vendor_prefixed/apimatic/core/tests/Mocking/Response/MockResponse.php',
    'WPForms\\Vendor\\Core\\Tests\\Mocking\\Types\\MockApiResponse' => $baseDir . '/vendor_prefixed/apimatic/core/tests/Mocking/Types/MockApiResponse.php',
    'WPForms\\Vendor\\Core\\Tests\\Mocking\\Types\\MockCallback' => $baseDir . '/vendor_prefixed/apimatic/core/tests/Mocking/Types/MockCallback.php',
    'WPForms\\Vendor\\Core\\Tests\\Mocking\\Types\\MockContext' => $baseDir . '/vendor_prefixed/apimatic/core/tests/Mocking/Types/MockContext.php',
    'WPForms\\Vendor\\Core\\Tests\\Mocking\\Types\\MockCoreResponse' => $baseDir . '/vendor_prefixed/apimatic/core/tests/Mocking/Types/MockCoreResponse.php',
    'WPForms\\Vendor\\Core\\Tests\\Mocking\\Types\\MockFileWrapper' => $baseDir . '/vendor_prefixed/apimatic/core/tests/Mocking/Types/MockFileWrapper.php',
    'WPForms\\Vendor\\Core\\Tests\\Mocking\\Types\\MockRequest' => $baseDir . '/vendor_prefixed/apimatic/core/tests/Mocking/Types/MockRequest.php',
    'WPForms\\Vendor\\Core\\Tests\\TypesTest' => $baseDir . '/vendor_prefixed/apimatic/core/tests/TypesTest.php',
    'WPForms\\Vendor\\Core\\Tests\\UtilsTest' => $baseDir . '/vendor_prefixed/apimatic/core/tests/UtilsTest.php',
    'WPForms\\Vendor\\Core\\Types\\CallbackCatcher' => $baseDir . '/vendor_prefixed/apimatic/core/src/Types/CallbackCatcher.php',
    'WPForms\\Vendor\\Core\\Types\\Sdk\\CoreApiResponse' => $baseDir . '/vendor_prefixed/apimatic/core/src/Types/Sdk/CoreApiResponse.php',
    'WPForms\\Vendor\\Core\\Types\\Sdk\\CoreCallback' => $baseDir . '/vendor_prefixed/apimatic/core/src/Types/Sdk/CoreCallback.php',
    'WPForms\\Vendor\\Core\\Types\\Sdk\\CoreContext' => $baseDir . '/vendor_prefixed/apimatic/core/src/Types/Sdk/CoreContext.php',
    'WPForms\\Vendor\\Core\\Types\\Sdk\\CoreFileWrapper' => $baseDir . '/vendor_prefixed/apimatic/core/src/Types/Sdk/CoreFileWrapper.php',
    'WPForms\\Vendor\\Core\\Types\\Sdk\\CoreRequest' => $baseDir . '/vendor_prefixed/apimatic/core/src/Types/Sdk/CoreRequest.php',
    'WPForms\\Vendor\\Core\\Types\\Sdk\\CoreResponse' => $baseDir . '/vendor_prefixed/apimatic/core/src/Types/Sdk/CoreResponse.php',
    'WPForms\\Vendor\\Core\\Utils\\CoreHelper' => $baseDir . '/vendor_prefixed/apimatic/core/src/Utils/CoreHelper.php',
    'WPForms\\Vendor\\Core\\Utils\\DateHelper' => $baseDir . '/vendor_prefixed/apimatic/core/src/Utils/DateHelper.php',
    'WPForms\\Vendor\\Core\\Utils\\JsonHelper' => $baseDir . '/vendor_prefixed/apimatic/core/src/Utils/JsonHelper.php',
    'WPForms\\Vendor\\Core\\Utils\\XmlDeserializer' => $baseDir . '/vendor_prefixed/apimatic/core/src/Utils/XmlDeserializer.php',
    'WPForms\\Vendor\\Core\\Utils\\XmlSerializer' => $baseDir . '/vendor_prefixed/apimatic/core/src/Utils/XmlSerializer.php',
    'WPForms\\Vendor\\FactoryMethod' => $baseDir . '/vendor_prefixed/apimatic/jsonmapper/tests/JsonMapperTest/FactoryMethod.php',
    'WPForms\\Vendor\\FactoryMethodWithError' => $baseDir . '/vendor_prefixed/apimatic/jsonmapper/tests/JsonMapperTest/FactoryMethodWithError.php',
    'WPForms\\Vendor\\HTML5' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/Lexer/PH5P.php',
    'WPForms\\Vendor\\HTML5TreeConstructer' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/Lexer/PH5P.php',
    'WPForms\\Vendor\\HTMLPurifier' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier.php',
    'WPForms\\Vendor\\HTMLPurifier_Arborize' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/Arborize.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrCollections' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrCollections.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrDef' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrDef_CSS' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/CSS.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrDef_CSS_AlphaValue' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/CSS/AlphaValue.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrDef_CSS_Background' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/CSS/Background.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrDef_CSS_BackgroundPosition' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/CSS/BackgroundPosition.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrDef_CSS_Border' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/CSS/Border.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrDef_CSS_Color' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/CSS/Color.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrDef_CSS_Composite' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/CSS/Composite.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrDef_CSS_DenyElementDecorator' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/CSS/DenyElementDecorator.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrDef_CSS_Filter' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/CSS/Filter.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrDef_CSS_Font' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/CSS/Font.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrDef_CSS_FontFamily' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/CSS/FontFamily.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrDef_CSS_Ident' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/CSS/Ident.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrDef_CSS_ImportantDecorator' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/CSS/ImportantDecorator.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrDef_CSS_Length' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/CSS/Length.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrDef_CSS_ListStyle' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/CSS/ListStyle.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrDef_CSS_Multiple' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/CSS/Multiple.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrDef_CSS_Number' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/CSS/Number.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrDef_CSS_Percentage' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/CSS/Percentage.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrDef_CSS_Ratio' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/CSS/Ratio.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrDef_CSS_TextDecoration' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/CSS/TextDecoration.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrDef_CSS_URI' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/CSS/URI.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrDef_Clone' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/Clone.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrDef_Enum' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/Enum.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrDef_HTML_Bool' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/HTML/Bool.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrDef_HTML_Class' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/HTML/Class.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrDef_HTML_Color' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/HTML/Color.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrDef_HTML_ContentEditable' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/HTML/ContentEditable.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrDef_HTML_FrameTarget' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/HTML/FrameTarget.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrDef_HTML_ID' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/HTML/ID.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrDef_HTML_Length' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/HTML/Length.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrDef_HTML_LinkTypes' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/HTML/LinkTypes.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrDef_HTML_MultiLength' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/HTML/MultiLength.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrDef_HTML_Nmtokens' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/HTML/Nmtokens.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrDef_HTML_Pixels' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/HTML/Pixels.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrDef_Integer' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/Integer.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrDef_Lang' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/Lang.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrDef_Switch' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/Switch.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrDef_Text' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/Text.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrDef_URI' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/URI.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrDef_URI_Email' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/URI/Email.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrDef_URI_Email_SimpleCheck' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/URI/Email/SimpleCheck.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrDef_URI_Host' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/URI/Host.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrDef_URI_IPv4' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/URI/IPv4.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrDef_URI_IPv6' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/URI/IPv6.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrTransform' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrTransform.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrTransform_Background' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrTransform/Background.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrTransform_BdoDir' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrTransform/BdoDir.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrTransform_BgColor' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrTransform/BgColor.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrTransform_BoolToCSS' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrTransform/BoolToCSS.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrTransform_Border' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrTransform/Border.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrTransform_EnumToCSS' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrTransform/EnumToCSS.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrTransform_ImgRequired' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrTransform/ImgRequired.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrTransform_ImgSpace' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrTransform/ImgSpace.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrTransform_Input' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrTransform/Input.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrTransform_Lang' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrTransform/Lang.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrTransform_Length' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrTransform/Length.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrTransform_Name' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrTransform/Name.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrTransform_NameSync' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrTransform/NameSync.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrTransform_Nofollow' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrTransform/Nofollow.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrTransform_SafeEmbed' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrTransform/SafeEmbed.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrTransform_SafeObject' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrTransform/SafeObject.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrTransform_SafeParam' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrTransform/SafeParam.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrTransform_ScriptRequired' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrTransform/ScriptRequired.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrTransform_TargetBlank' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrTransform/TargetBlank.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrTransform_TargetNoopener' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrTransform/TargetNoopener.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrTransform_TargetNoreferrer' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrTransform/TargetNoreferrer.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrTransform_Textarea' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrTransform/Textarea.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrTypes' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrTypes.php',
    'WPForms\\Vendor\\HTMLPurifier_AttrValidator' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/AttrValidator.php',
    'WPForms\\Vendor\\HTMLPurifier_Bootstrap' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/Bootstrap.php',
    'WPForms\\Vendor\\HTMLPurifier_CSSDefinition' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/CSSDefinition.php',
    'WPForms\\Vendor\\HTMLPurifier_ChildDef' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/ChildDef.php',
    'WPForms\\Vendor\\HTMLPurifier_ChildDef_Chameleon' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/ChildDef/Chameleon.php',
    'WPForms\\Vendor\\HTMLPurifier_ChildDef_Custom' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/ChildDef/Custom.php',
    'WPForms\\Vendor\\HTMLPurifier_ChildDef_Empty' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/ChildDef/Empty.php',
    'WPForms\\Vendor\\HTMLPurifier_ChildDef_List' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/ChildDef/List.php',
    'WPForms\\Vendor\\HTMLPurifier_ChildDef_Optional' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/ChildDef/Optional.php',
    'WPForms\\Vendor\\HTMLPurifier_ChildDef_Required' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/ChildDef/Required.php',
    'WPForms\\Vendor\\HTMLPurifier_ChildDef_StrictBlockquote' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/ChildDef/StrictBlockquote.php',
    'WPForms\\Vendor\\HTMLPurifier_ChildDef_Table' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/ChildDef/Table.php',
    'WPForms\\Vendor\\HTMLPurifier_Config' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/Config.php',
    'WPForms\\Vendor\\HTMLPurifier_ConfigSchema' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/ConfigSchema.php',
    'WPForms\\Vendor\\HTMLPurifier_ConfigSchema_Builder_ConfigSchema' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/ConfigSchema/Builder/ConfigSchema.php',
    'WPForms\\Vendor\\HTMLPurifier_ConfigSchema_Builder_Xml' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/ConfigSchema/Builder/Xml.php',
    'WPForms\\Vendor\\HTMLPurifier_ConfigSchema_Exception' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/ConfigSchema/Exception.php',
    'WPForms\\Vendor\\HTMLPurifier_ConfigSchema_Interchange' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/ConfigSchema/Interchange.php',
    'WPForms\\Vendor\\HTMLPurifier_ConfigSchema_InterchangeBuilder' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/ConfigSchema/InterchangeBuilder.php',
    'WPForms\\Vendor\\HTMLPurifier_ConfigSchema_Interchange_Directive' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/ConfigSchema/Interchange/Directive.php',
    'WPForms\\Vendor\\HTMLPurifier_ConfigSchema_Interchange_Id' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/ConfigSchema/Interchange/Id.php',
    'WPForms\\Vendor\\HTMLPurifier_ConfigSchema_Validator' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/ConfigSchema/Validator.php',
    'WPForms\\Vendor\\HTMLPurifier_ConfigSchema_ValidatorAtom' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/ConfigSchema/ValidatorAtom.php',
    'WPForms\\Vendor\\HTMLPurifier_ContentSets' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/ContentSets.php',
    'WPForms\\Vendor\\HTMLPurifier_Context' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/Context.php',
    'WPForms\\Vendor\\HTMLPurifier_Definition' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/Definition.php',
    'WPForms\\Vendor\\HTMLPurifier_DefinitionCache' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/DefinitionCache.php',
    'WPForms\\Vendor\\HTMLPurifier_DefinitionCacheFactory' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/DefinitionCacheFactory.php',
    'WPForms\\Vendor\\HTMLPurifier_DefinitionCache_Decorator' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/DefinitionCache/Decorator.php',
    'WPForms\\Vendor\\HTMLPurifier_DefinitionCache_Decorator_Cleanup' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/DefinitionCache/Decorator/Cleanup.php',
    'WPForms\\Vendor\\HTMLPurifier_DefinitionCache_Decorator_Memory' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/DefinitionCache/Decorator/Memory.php',
    'WPForms\\Vendor\\HTMLPurifier_DefinitionCache_Null' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/DefinitionCache/Null.php',
    'WPForms\\Vendor\\HTMLPurifier_DefinitionCache_Serializer' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/DefinitionCache/Serializer.php',
    'WPForms\\Vendor\\HTMLPurifier_Doctype' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/Doctype.php',
    'WPForms\\Vendor\\HTMLPurifier_DoctypeRegistry' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/DoctypeRegistry.php',
    'WPForms\\Vendor\\HTMLPurifier_ElementDef' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/ElementDef.php',
    'WPForms\\Vendor\\HTMLPurifier_Encoder' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/Encoder.php',
    'WPForms\\Vendor\\HTMLPurifier_EntityLookup' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/EntityLookup.php',
    'WPForms\\Vendor\\HTMLPurifier_EntityParser' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/EntityParser.php',
    'WPForms\\Vendor\\HTMLPurifier_ErrorCollector' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/ErrorCollector.php',
    'WPForms\\Vendor\\HTMLPurifier_ErrorStruct' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/ErrorStruct.php',
    'WPForms\\Vendor\\HTMLPurifier_Exception' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/Exception.php',
    'WPForms\\Vendor\\HTMLPurifier_Filter' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/Filter.php',
    'WPForms\\Vendor\\HTMLPurifier_Filter_ExtractStyleBlocks' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/Filter/ExtractStyleBlocks.php',
    'WPForms\\Vendor\\HTMLPurifier_Filter_YouTube' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/Filter/YouTube.php',
    'WPForms\\Vendor\\HTMLPurifier_Generator' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/Generator.php',
    'WPForms\\Vendor\\HTMLPurifier_HTMLDefinition' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/HTMLDefinition.php',
    'WPForms\\Vendor\\HTMLPurifier_HTMLModule' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule.php',
    'WPForms\\Vendor\\HTMLPurifier_HTMLModuleManager' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModuleManager.php',
    'WPForms\\Vendor\\HTMLPurifier_HTMLModule_Bdo' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule/Bdo.php',
    'WPForms\\Vendor\\HTMLPurifier_HTMLModule_CommonAttributes' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule/CommonAttributes.php',
    'WPForms\\Vendor\\HTMLPurifier_HTMLModule_Edit' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule/Edit.php',
    'WPForms\\Vendor\\HTMLPurifier_HTMLModule_Forms' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule/Forms.php',
    'WPForms\\Vendor\\HTMLPurifier_HTMLModule_Hypertext' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule/Hypertext.php',
    'WPForms\\Vendor\\HTMLPurifier_HTMLModule_Iframe' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule/Iframe.php',
    'WPForms\\Vendor\\HTMLPurifier_HTMLModule_Image' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule/Image.php',
    'WPForms\\Vendor\\HTMLPurifier_HTMLModule_Legacy' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule/Legacy.php',
    'WPForms\\Vendor\\HTMLPurifier_HTMLModule_List' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule/List.php',
    'WPForms\\Vendor\\HTMLPurifier_HTMLModule_Name' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule/Name.php',
    'WPForms\\Vendor\\HTMLPurifier_HTMLModule_Nofollow' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule/Nofollow.php',
    'WPForms\\Vendor\\HTMLPurifier_HTMLModule_NonXMLCommonAttributes' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule/NonXMLCommonAttributes.php',
    'WPForms\\Vendor\\HTMLPurifier_HTMLModule_Object' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule/Object.php',
    'WPForms\\Vendor\\HTMLPurifier_HTMLModule_Presentation' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule/Presentation.php',
    'WPForms\\Vendor\\HTMLPurifier_HTMLModule_Proprietary' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule/Proprietary.php',
    'WPForms\\Vendor\\HTMLPurifier_HTMLModule_Ruby' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule/Ruby.php',
    'WPForms\\Vendor\\HTMLPurifier_HTMLModule_SafeEmbed' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule/SafeEmbed.php',
    'WPForms\\Vendor\\HTMLPurifier_HTMLModule_SafeObject' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule/SafeObject.php',
    'WPForms\\Vendor\\HTMLPurifier_HTMLModule_SafeScripting' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule/SafeScripting.php',
    'WPForms\\Vendor\\HTMLPurifier_HTMLModule_Scripting' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule/Scripting.php',
    'WPForms\\Vendor\\HTMLPurifier_HTMLModule_StyleAttribute' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule/StyleAttribute.php',
    'WPForms\\Vendor\\HTMLPurifier_HTMLModule_Tables' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule/Tables.php',
    'WPForms\\Vendor\\HTMLPurifier_HTMLModule_Target' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule/Target.php',
    'WPForms\\Vendor\\HTMLPurifier_HTMLModule_TargetBlank' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule/TargetBlank.php',
    'WPForms\\Vendor\\HTMLPurifier_HTMLModule_TargetNoopener' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule/TargetNoopener.php',
    'WPForms\\Vendor\\HTMLPurifier_HTMLModule_TargetNoreferrer' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule/TargetNoreferrer.php',
    'WPForms\\Vendor\\HTMLPurifier_HTMLModule_Text' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule/Text.php',
    'WPForms\\Vendor\\HTMLPurifier_HTMLModule_Tidy' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule/Tidy.php',
    'WPForms\\Vendor\\HTMLPurifier_HTMLModule_Tidy_Name' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule/Tidy/Name.php',
    'WPForms\\Vendor\\HTMLPurifier_HTMLModule_Tidy_Proprietary' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule/Tidy/Proprietary.php',
    'WPForms\\Vendor\\HTMLPurifier_HTMLModule_Tidy_Strict' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule/Tidy/Strict.php',
    'WPForms\\Vendor\\HTMLPurifier_HTMLModule_Tidy_Transitional' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule/Tidy/Transitional.php',
    'WPForms\\Vendor\\HTMLPurifier_HTMLModule_Tidy_XHTML' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule/Tidy/XHTML.php',
    'WPForms\\Vendor\\HTMLPurifier_HTMLModule_Tidy_XHTMLAndHTML4' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule/Tidy/XHTMLAndHTML4.php',
    'WPForms\\Vendor\\HTMLPurifier_HTMLModule_XMLCommonAttributes' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule/XMLCommonAttributes.php',
    'WPForms\\Vendor\\HTMLPurifier_IDAccumulator' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/IDAccumulator.php',
    'WPForms\\Vendor\\HTMLPurifier_Injector' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/Injector.php',
    'WPForms\\Vendor\\HTMLPurifier_Injector_AutoParagraph' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/Injector/AutoParagraph.php',
    'WPForms\\Vendor\\HTMLPurifier_Injector_DisplayLinkURI' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/Injector/DisplayLinkURI.php',
    'WPForms\\Vendor\\HTMLPurifier_Injector_Linkify' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/Injector/Linkify.php',
    'WPForms\\Vendor\\HTMLPurifier_Injector_PurifierLinkify' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/Injector/PurifierLinkify.php',
    'WPForms\\Vendor\\HTMLPurifier_Injector_RemoveEmpty' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/Injector/RemoveEmpty.php',
    'WPForms\\Vendor\\HTMLPurifier_Injector_RemoveSpansWithoutAttributes' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/Injector/RemoveSpansWithoutAttributes.php',
    'WPForms\\Vendor\\HTMLPurifier_Injector_SafeObject' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/Injector/SafeObject.php',
    'WPForms\\Vendor\\HTMLPurifier_Language' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/Language.php',
    'WPForms\\Vendor\\HTMLPurifier_LanguageFactory' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/LanguageFactory.php',
    'WPForms\\Vendor\\HTMLPurifier_Length' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/Length.php',
    'WPForms\\Vendor\\HTMLPurifier_Lexer' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/Lexer.php',
    'WPForms\\Vendor\\HTMLPurifier_Lexer_DOMLex' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/Lexer/DOMLex.php',
    'WPForms\\Vendor\\HTMLPurifier_Lexer_DirectLex' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/Lexer/DirectLex.php',
    'WPForms\\Vendor\\HTMLPurifier_Lexer_PH5P' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/Lexer/PH5P.php',
    'WPForms\\Vendor\\HTMLPurifier_Node' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/Node.php',
    'WPForms\\Vendor\\HTMLPurifier_Node_Comment' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/Node/Comment.php',
    'WPForms\\Vendor\\HTMLPurifier_Node_Element' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/Node/Element.php',
    'WPForms\\Vendor\\HTMLPurifier_Node_Text' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/Node/Text.php',
    'WPForms\\Vendor\\HTMLPurifier_PercentEncoder' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/PercentEncoder.php',
    'WPForms\\Vendor\\HTMLPurifier_Printer' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/Printer.php',
    'WPForms\\Vendor\\HTMLPurifier_Printer_CSSDefinition' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/Printer/CSSDefinition.php',
    'WPForms\\Vendor\\HTMLPurifier_Printer_ConfigForm' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/Printer/ConfigForm.php',
    'WPForms\\Vendor\\HTMLPurifier_Printer_ConfigForm_NullDecorator' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/Printer/ConfigForm.php',
    'WPForms\\Vendor\\HTMLPurifier_Printer_ConfigForm_bool' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/Printer/ConfigForm.php',
    'WPForms\\Vendor\\HTMLPurifier_Printer_ConfigForm_default' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/Printer/ConfigForm.php',
    'WPForms\\Vendor\\HTMLPurifier_Printer_HTMLDefinition' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/Printer/HTMLDefinition.php',
    'WPForms\\Vendor\\HTMLPurifier_PropertyList' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/PropertyList.php',
    'WPForms\\Vendor\\HTMLPurifier_PropertyListIterator' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/PropertyListIterator.php',
    'WPForms\\Vendor\\HTMLPurifier_Queue' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/Queue.php',
    'WPForms\\Vendor\\HTMLPurifier_Strategy' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/Strategy.php',
    'WPForms\\Vendor\\HTMLPurifier_Strategy_Composite' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/Strategy/Composite.php',
    'WPForms\\Vendor\\HTMLPurifier_Strategy_Core' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/Strategy/Core.php',
    'WPForms\\Vendor\\HTMLPurifier_Strategy_FixNesting' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/Strategy/FixNesting.php',
    'WPForms\\Vendor\\HTMLPurifier_Strategy_MakeWellFormed' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/Strategy/MakeWellFormed.php',
    'WPForms\\Vendor\\HTMLPurifier_Strategy_RemoveForeignElements' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/Strategy/RemoveForeignElements.php',
    'WPForms\\Vendor\\HTMLPurifier_Strategy_ValidateAttributes' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/Strategy/ValidateAttributes.php',
    'WPForms\\Vendor\\HTMLPurifier_StringHash' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/StringHash.php',
    'WPForms\\Vendor\\HTMLPurifier_StringHashParser' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/StringHashParser.php',
    'WPForms\\Vendor\\HTMLPurifier_TagTransform' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/TagTransform.php',
    'WPForms\\Vendor\\HTMLPurifier_TagTransform_Font' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/TagTransform/Font.php',
    'WPForms\\Vendor\\HTMLPurifier_TagTransform_Simple' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/TagTransform/Simple.php',
    'WPForms\\Vendor\\HTMLPurifier_Token' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/Token.php',
    'WPForms\\Vendor\\HTMLPurifier_TokenFactory' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/TokenFactory.php',
    'WPForms\\Vendor\\HTMLPurifier_Token_Comment' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/Token/Comment.php',
    'WPForms\\Vendor\\HTMLPurifier_Token_Empty' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/Token/Empty.php',
    'WPForms\\Vendor\\HTMLPurifier_Token_End' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/Token/End.php',
    'WPForms\\Vendor\\HTMLPurifier_Token_Start' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/Token/Start.php',
    'WPForms\\Vendor\\HTMLPurifier_Token_Tag' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/Token/Tag.php',
    'WPForms\\Vendor\\HTMLPurifier_Token_Text' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/Token/Text.php',
    'WPForms\\Vendor\\HTMLPurifier_URI' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/URI.php',
    'WPForms\\Vendor\\HTMLPurifier_URIDefinition' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/URIDefinition.php',
    'WPForms\\Vendor\\HTMLPurifier_URIFilter' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/URIFilter.php',
    'WPForms\\Vendor\\HTMLPurifier_URIFilter_DisableExternal' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/URIFilter/DisableExternal.php',
    'WPForms\\Vendor\\HTMLPurifier_URIFilter_DisableExternalResources' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/URIFilter/DisableExternalResources.php',
    'WPForms\\Vendor\\HTMLPurifier_URIFilter_DisableResources' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/URIFilter/DisableResources.php',
    'WPForms\\Vendor\\HTMLPurifier_URIFilter_HostBlacklist' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/URIFilter/HostBlacklist.php',
    'WPForms\\Vendor\\HTMLPurifier_URIFilter_MakeAbsolute' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/URIFilter/MakeAbsolute.php',
    'WPForms\\Vendor\\HTMLPurifier_URIFilter_Munge' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/URIFilter/Munge.php',
    'WPForms\\Vendor\\HTMLPurifier_URIFilter_SafeIframe' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/URIFilter/SafeIframe.php',
    'WPForms\\Vendor\\HTMLPurifier_URIParser' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/URIParser.php',
    'WPForms\\Vendor\\HTMLPurifier_URIScheme' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/URIScheme.php',
    'WPForms\\Vendor\\HTMLPurifier_URISchemeRegistry' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/URISchemeRegistry.php',
    'WPForms\\Vendor\\HTMLPurifier_URIScheme_data' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/URIScheme/data.php',
    'WPForms\\Vendor\\HTMLPurifier_URIScheme_file' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/URIScheme/file.php',
    'WPForms\\Vendor\\HTMLPurifier_URIScheme_ftp' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/URIScheme/ftp.php',
    'WPForms\\Vendor\\HTMLPurifier_URIScheme_http' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/URIScheme/http.php',
    'WPForms\\Vendor\\HTMLPurifier_URIScheme_https' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/URIScheme/https.php',
    'WPForms\\Vendor\\HTMLPurifier_URIScheme_mailto' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/URIScheme/mailto.php',
    'WPForms\\Vendor\\HTMLPurifier_URIScheme_news' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/URIScheme/news.php',
    'WPForms\\Vendor\\HTMLPurifier_URIScheme_nntp' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/URIScheme/nntp.php',
    'WPForms\\Vendor\\HTMLPurifier_URIScheme_tel' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/URIScheme/tel.php',
    'WPForms\\Vendor\\HTMLPurifier_UnitConverter' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/UnitConverter.php',
    'WPForms\\Vendor\\HTMLPurifier_VarParser' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/VarParser.php',
    'WPForms\\Vendor\\HTMLPurifier_VarParserException' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/VarParserException.php',
    'WPForms\\Vendor\\HTMLPurifier_VarParser_Flexible' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/VarParser/Flexible.php',
    'WPForms\\Vendor\\HTMLPurifier_VarParser_Native' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/VarParser/Native.php',
    'WPForms\\Vendor\\HTMLPurifier_Zipper' => $baseDir . '/vendor_prefixed/ezyang/htmlpurifier/library/HTMLPurifier/Zipper.php',
    'WPForms\\Vendor\\JsonMapperCommentsDiscardedException' => $baseDir . '/vendor_prefixed/apimatic/jsonmapper/tests/JsonMapperTest/JsonMapperCommentsDiscardedException.php',
    'WPForms\\Vendor\\JsonMapperForCheckingAllowedPaths' => $baseDir . '/vendor_prefixed/apimatic/jsonmapper/tests/JsonMapperTest/JsonMapperForCheckingAllowedPaths.php',
    'WPForms\\Vendor\\JsonMapperTest' => $baseDir . '/vendor_prefixed/apimatic/jsonmapper/tests/JsonMapperTest.php',
    'WPForms\\Vendor\\JsonMapperTest_Array' => $baseDir . '/vendor_prefixed/apimatic/jsonmapper/tests/JsonMapperTest/Array.php',
    'WPForms\\Vendor\\JsonMapperTest_Broken' => $baseDir . '/vendor_prefixed/apimatic/jsonmapper/tests/JsonMapperTest/Broken.php',
    'WPForms\\Vendor\\JsonMapperTest_DependencyInjector' => $baseDir . '/vendor_prefixed/apimatic/jsonmapper/tests/JsonMapperTest/DependencyInjector.php',
    'WPForms\\Vendor\\JsonMapperTest_DerivedClass' => $baseDir . '/vendor_prefixed/apimatic/jsonmapper/tests/JsonMapperTest/DerivedClass.php',
    'WPForms\\Vendor\\JsonMapperTest_DerivedClass2' => $baseDir . '/vendor_prefixed/apimatic/jsonmapper/tests/JsonMapperTest/DerivedClass2.php',
    'WPForms\\Vendor\\JsonMapperTest_Logger' => $baseDir . '/vendor_prefixed/apimatic/jsonmapper/tests/JsonMapperTest/Logger.php',
    'WPForms\\Vendor\\JsonMapperTest_Simple' => $baseDir . '/vendor_prefixed/apimatic/jsonmapper/tests/JsonMapperTest/Simple.php',
    'WPForms\\Vendor\\JsonMapperTest_SimpleBase' => $baseDir . '/vendor_prefixed/apimatic/jsonmapper/tests/JsonMapperTest/SimpleBase.php',
    'WPForms\\Vendor\\JsonMapperTest_SimpleBaseWithMissingDiscrimType' => $baseDir . '/vendor_prefixed/apimatic/jsonmapper/tests/JsonMapperTest/SimpleBaseWithMissingDiscrimType.php',
    'WPForms\\Vendor\\JsonMapperTest_ValueObject' => $baseDir . '/vendor_prefixed/apimatic/jsonmapper/tests/JsonMapperTest/ValueObject.php',
    'WPForms\\Vendor\\MapsWithSetters' => $baseDir . '/vendor_prefixed/apimatic/jsonmapper/tests/JsonMapperTest/MapsWithSetters.php',
    'WPForms\\Vendor\\Php7TypedClass' => $baseDir . '/vendor_prefixed/apimatic/jsonmapper/tests/JsonMapperTest/Php7TypedClass.php',
    'WPForms\\Vendor\\Php7_1TypedClass' => $baseDir . '/vendor_prefixed/apimatic/jsonmapper/tests/JsonMapperTest/Php7_1TypedClass.php',
    'WPForms\\Vendor\\PrivateWithSetter' => $baseDir . '/vendor_prefixed/apimatic/jsonmapper/tests/JsonMapperTest/PrivateWithSetter.php',
    'WPForms\\Vendor\\Rs\\Json\\Pointer' => $baseDir . '/vendor_prefixed/php-jsonpointer/php-jsonpointer/src/Rs/Json/Pointer.php',
    'WPForms\\Vendor\\Rs\\Json\\Pointer\\InvalidJsonException' => $baseDir . '/vendor_prefixed/php-jsonpointer/php-jsonpointer/src/Rs/Json/Pointer/InvalidJsonException.php',
    'WPForms\\Vendor\\Rs\\Json\\Pointer\\InvalidPointerException' => $baseDir . '/vendor_prefixed/php-jsonpointer/php-jsonpointer/src/Rs/Json/Pointer/InvalidPointerException.php',
    'WPForms\\Vendor\\Rs\\Json\\Pointer\\NonWalkableJsonException' => $baseDir . '/vendor_prefixed/php-jsonpointer/php-jsonpointer/src/Rs/Json/Pointer/NonWalkableJsonException.php',
    'WPForms\\Vendor\\Rs\\Json\\Pointer\\NonexistentValueReferencedException' => $baseDir . '/vendor_prefixed/php-jsonpointer/php-jsonpointer/src/Rs/Json/Pointer/NonexistentValueReferencedException.php',
    'WPForms\\Vendor\\Square\\ApiHelper' => $baseDir . '/vendor_prefixed/square/square/src/ApiHelper.php',
    'WPForms\\Vendor\\Square\\Apis\\ApplePayApi' => $baseDir . '/vendor_prefixed/square/square/src/Apis/ApplePayApi.php',
    'WPForms\\Vendor\\Square\\Apis\\BankAccountsApi' => $baseDir . '/vendor_prefixed/square/square/src/Apis/BankAccountsApi.php',
    'WPForms\\Vendor\\Square\\Apis\\BaseApi' => $baseDir . '/vendor_prefixed/square/square/src/Apis/BaseApi.php',
    'WPForms\\Vendor\\Square\\Apis\\BookingCustomAttributesApi' => $baseDir . '/vendor_prefixed/square/square/src/Apis/BookingCustomAttributesApi.php',
    'WPForms\\Vendor\\Square\\Apis\\BookingsApi' => $baseDir . '/vendor_prefixed/square/square/src/Apis/BookingsApi.php',
    'WPForms\\Vendor\\Square\\Apis\\CardsApi' => $baseDir . '/vendor_prefixed/square/square/src/Apis/CardsApi.php',
    'WPForms\\Vendor\\Square\\Apis\\CashDrawersApi' => $baseDir . '/vendor_prefixed/square/square/src/Apis/CashDrawersApi.php',
    'WPForms\\Vendor\\Square\\Apis\\CatalogApi' => $baseDir . '/vendor_prefixed/square/square/src/Apis/CatalogApi.php',
    'WPForms\\Vendor\\Square\\Apis\\CheckoutApi' => $baseDir . '/vendor_prefixed/square/square/src/Apis/CheckoutApi.php',
    'WPForms\\Vendor\\Square\\Apis\\CustomerCustomAttributesApi' => $baseDir . '/vendor_prefixed/square/square/src/Apis/CustomerCustomAttributesApi.php',
    'WPForms\\Vendor\\Square\\Apis\\CustomerGroupsApi' => $baseDir . '/vendor_prefixed/square/square/src/Apis/CustomerGroupsApi.php',
    'WPForms\\Vendor\\Square\\Apis\\CustomerSegmentsApi' => $baseDir . '/vendor_prefixed/square/square/src/Apis/CustomerSegmentsApi.php',
    'WPForms\\Vendor\\Square\\Apis\\CustomersApi' => $baseDir . '/vendor_prefixed/square/square/src/Apis/CustomersApi.php',
    'WPForms\\Vendor\\Square\\Apis\\DevicesApi' => $baseDir . '/vendor_prefixed/square/square/src/Apis/DevicesApi.php',
    'WPForms\\Vendor\\Square\\Apis\\DisputesApi' => $baseDir . '/vendor_prefixed/square/square/src/Apis/DisputesApi.php',
    'WPForms\\Vendor\\Square\\Apis\\EmployeesApi' => $baseDir . '/vendor_prefixed/square/square/src/Apis/EmployeesApi.php',
    'WPForms\\Vendor\\Square\\Apis\\EventsApi' => $baseDir . '/vendor_prefixed/square/square/src/Apis/EventsApi.php',
    'WPForms\\Vendor\\Square\\Apis\\GiftCardActivitiesApi' => $baseDir . '/vendor_prefixed/square/square/src/Apis/GiftCardActivitiesApi.php',
    'WPForms\\Vendor\\Square\\Apis\\GiftCardsApi' => $baseDir . '/vendor_prefixed/square/square/src/Apis/GiftCardsApi.php',
    'WPForms\\Vendor\\Square\\Apis\\InventoryApi' => $baseDir . '/vendor_prefixed/square/square/src/Apis/InventoryApi.php',
    'WPForms\\Vendor\\Square\\Apis\\InvoicesApi' => $baseDir . '/vendor_prefixed/square/square/src/Apis/InvoicesApi.php',
    'WPForms\\Vendor\\Square\\Apis\\LaborApi' => $baseDir . '/vendor_prefixed/square/square/src/Apis/LaborApi.php',
    'WPForms\\Vendor\\Square\\Apis\\LocationCustomAttributesApi' => $baseDir . '/vendor_prefixed/square/square/src/Apis/LocationCustomAttributesApi.php',
    'WPForms\\Vendor\\Square\\Apis\\LocationsApi' => $baseDir . '/vendor_prefixed/square/square/src/Apis/LocationsApi.php',
    'WPForms\\Vendor\\Square\\Apis\\LoyaltyApi' => $baseDir . '/vendor_prefixed/square/square/src/Apis/LoyaltyApi.php',
    'WPForms\\Vendor\\Square\\Apis\\MerchantCustomAttributesApi' => $baseDir . '/vendor_prefixed/square/square/src/Apis/MerchantCustomAttributesApi.php',
    'WPForms\\Vendor\\Square\\Apis\\MerchantsApi' => $baseDir . '/vendor_prefixed/square/square/src/Apis/MerchantsApi.php',
    'WPForms\\Vendor\\Square\\Apis\\MobileAuthorizationApi' => $baseDir . '/vendor_prefixed/square/square/src/Apis/MobileAuthorizationApi.php',
    'WPForms\\Vendor\\Square\\Apis\\OAuthApi' => $baseDir . '/vendor_prefixed/square/square/src/Apis/OAuthApi.php',
    'WPForms\\Vendor\\Square\\Apis\\OrderCustomAttributesApi' => $baseDir . '/vendor_prefixed/square/square/src/Apis/OrderCustomAttributesApi.php',
    'WPForms\\Vendor\\Square\\Apis\\OrdersApi' => $baseDir . '/vendor_prefixed/square/square/src/Apis/OrdersApi.php',
    'WPForms\\Vendor\\Square\\Apis\\PaymentsApi' => $baseDir . '/vendor_prefixed/square/square/src/Apis/PaymentsApi.php',
    'WPForms\\Vendor\\Square\\Apis\\PayoutsApi' => $baseDir . '/vendor_prefixed/square/square/src/Apis/PayoutsApi.php',
    'WPForms\\Vendor\\Square\\Apis\\RefundsApi' => $baseDir . '/vendor_prefixed/square/square/src/Apis/RefundsApi.php',
    'WPForms\\Vendor\\Square\\Apis\\SitesApi' => $baseDir . '/vendor_prefixed/square/square/src/Apis/SitesApi.php',
    'WPForms\\Vendor\\Square\\Apis\\SnippetsApi' => $baseDir . '/vendor_prefixed/square/square/src/Apis/SnippetsApi.php',
    'WPForms\\Vendor\\Square\\Apis\\SubscriptionsApi' => $baseDir . '/vendor_prefixed/square/square/src/Apis/SubscriptionsApi.php',
    'WPForms\\Vendor\\Square\\Apis\\TeamApi' => $baseDir . '/vendor_prefixed/square/square/src/Apis/TeamApi.php',
    'WPForms\\Vendor\\Square\\Apis\\TerminalApi' => $baseDir . '/vendor_prefixed/square/square/src/Apis/TerminalApi.php',
    'WPForms\\Vendor\\Square\\Apis\\TransactionsApi' => $baseDir . '/vendor_prefixed/square/square/src/Apis/TransactionsApi.php',
    'WPForms\\Vendor\\Square\\Apis\\V1TransactionsApi' => $baseDir . '/vendor_prefixed/square/square/src/Apis/V1TransactionsApi.php',
    'WPForms\\Vendor\\Square\\Apis\\VendorsApi' => $baseDir . '/vendor_prefixed/square/square/src/Apis/VendorsApi.php',
    'WPForms\\Vendor\\Square\\Apis\\WebhookSubscriptionsApi' => $baseDir . '/vendor_prefixed/square/square/src/Apis/WebhookSubscriptionsApi.php',
    'WPForms\\Vendor\\Square\\Authentication\\BearerAuthCredentialsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Authentication/BearerAuthCredentialsBuilder.php',
    'WPForms\\Vendor\\Square\\Authentication\\BearerAuthManager' => $baseDir . '/vendor_prefixed/square/square/src/Authentication/BearerAuthManager.php',
    'WPForms\\Vendor\\Square\\BearerAuthCredentials' => $baseDir . '/vendor_prefixed/square/square/src/BearerAuthCredentials.php',
    'WPForms\\Vendor\\Square\\ConfigurationDefaults' => $baseDir . '/vendor_prefixed/square/square/src/ConfigurationDefaults.php',
    'WPForms\\Vendor\\Square\\ConfigurationInterface' => $baseDir . '/vendor_prefixed/square/square/src/ConfigurationInterface.php',
    'WPForms\\Vendor\\Square\\Environment' => $baseDir . '/vendor_prefixed/square/square/src/Environment.php',
    'WPForms\\Vendor\\Square\\Exceptions\\ApiException' => $baseDir . '/vendor_prefixed/square/square/src/Exceptions/ApiException.php',
    'WPForms\\Vendor\\Square\\Http\\ApiResponse' => $baseDir . '/vendor_prefixed/square/square/src/Http/ApiResponse.php',
    'WPForms\\Vendor\\Square\\Http\\HttpCallBack' => $baseDir . '/vendor_prefixed/square/square/src/Http/HttpCallBack.php',
    'WPForms\\Vendor\\Square\\Http\\HttpContext' => $baseDir . '/vendor_prefixed/square/square/src/Http/HttpContext.php',
    'WPForms\\Vendor\\Square\\Http\\HttpMethod' => $baseDir . '/vendor_prefixed/square/square/src/Http/HttpMethod.php',
    'WPForms\\Vendor\\Square\\Http\\HttpRequest' => $baseDir . '/vendor_prefixed/square/square/src/Http/HttpRequest.php',
    'WPForms\\Vendor\\Square\\Http\\HttpResponse' => $baseDir . '/vendor_prefixed/square/square/src/Http/HttpResponse.php',
    'WPForms\\Vendor\\Square\\Models\\ACHDetails' => $baseDir . '/vendor_prefixed/square/square/src/Models/ACHDetails.php',
    'WPForms\\Vendor\\Square\\Models\\AcceptDisputeResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/AcceptDisputeResponse.php',
    'WPForms\\Vendor\\Square\\Models\\AcceptedPaymentMethods' => $baseDir . '/vendor_prefixed/square/square/src/Models/AcceptedPaymentMethods.php',
    'WPForms\\Vendor\\Square\\Models\\AccumulateLoyaltyPointsRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/AccumulateLoyaltyPointsRequest.php',
    'WPForms\\Vendor\\Square\\Models\\AccumulateLoyaltyPointsResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/AccumulateLoyaltyPointsResponse.php',
    'WPForms\\Vendor\\Square\\Models\\ActionCancelReason' => $baseDir . '/vendor_prefixed/square/square/src/Models/ActionCancelReason.php',
    'WPForms\\Vendor\\Square\\Models\\ActivityType' => $baseDir . '/vendor_prefixed/square/square/src/Models/ActivityType.php',
    'WPForms\\Vendor\\Square\\Models\\AddGroupToCustomerResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/AddGroupToCustomerResponse.php',
    'WPForms\\Vendor\\Square\\Models\\AdditionalRecipient' => $baseDir . '/vendor_prefixed/square/square/src/Models/AdditionalRecipient.php',
    'WPForms\\Vendor\\Square\\Models\\Address' => $baseDir . '/vendor_prefixed/square/square/src/Models/Address.php',
    'WPForms\\Vendor\\Square\\Models\\AdjustLoyaltyPointsRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/AdjustLoyaltyPointsRequest.php',
    'WPForms\\Vendor\\Square\\Models\\AdjustLoyaltyPointsResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/AdjustLoyaltyPointsResponse.php',
    'WPForms\\Vendor\\Square\\Models\\AfterpayDetails' => $baseDir . '/vendor_prefixed/square/square/src/Models/AfterpayDetails.php',
    'WPForms\\Vendor\\Square\\Models\\ApplicationDetails' => $baseDir . '/vendor_prefixed/square/square/src/Models/ApplicationDetails.php',
    'WPForms\\Vendor\\Square\\Models\\ApplicationDetailsExternalSquareProduct' => $baseDir . '/vendor_prefixed/square/square/src/Models/ApplicationDetailsExternalSquareProduct.php',
    'WPForms\\Vendor\\Square\\Models\\ApplicationType' => $baseDir . '/vendor_prefixed/square/square/src/Models/ApplicationType.php',
    'WPForms\\Vendor\\Square\\Models\\AppointmentSegment' => $baseDir . '/vendor_prefixed/square/square/src/Models/AppointmentSegment.php',
    'WPForms\\Vendor\\Square\\Models\\ArchivedState' => $baseDir . '/vendor_prefixed/square/square/src/Models/ArchivedState.php',
    'WPForms\\Vendor\\Square\\Models\\Availability' => $baseDir . '/vendor_prefixed/square/square/src/Models/Availability.php',
    'WPForms\\Vendor\\Square\\Models\\BankAccount' => $baseDir . '/vendor_prefixed/square/square/src/Models/BankAccount.php',
    'WPForms\\Vendor\\Square\\Models\\BankAccountPaymentDetails' => $baseDir . '/vendor_prefixed/square/square/src/Models/BankAccountPaymentDetails.php',
    'WPForms\\Vendor\\Square\\Models\\BankAccountStatus' => $baseDir . '/vendor_prefixed/square/square/src/Models/BankAccountStatus.php',
    'WPForms\\Vendor\\Square\\Models\\BankAccountType' => $baseDir . '/vendor_prefixed/square/square/src/Models/BankAccountType.php',
    'WPForms\\Vendor\\Square\\Models\\BatchChangeInventoryRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/BatchChangeInventoryRequest.php',
    'WPForms\\Vendor\\Square\\Models\\BatchChangeInventoryResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/BatchChangeInventoryResponse.php',
    'WPForms\\Vendor\\Square\\Models\\BatchDeleteCatalogObjectsRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/BatchDeleteCatalogObjectsRequest.php',
    'WPForms\\Vendor\\Square\\Models\\BatchDeleteCatalogObjectsResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/BatchDeleteCatalogObjectsResponse.php',
    'WPForms\\Vendor\\Square\\Models\\BatchRetrieveCatalogObjectsRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/BatchRetrieveCatalogObjectsRequest.php',
    'WPForms\\Vendor\\Square\\Models\\BatchRetrieveCatalogObjectsResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/BatchRetrieveCatalogObjectsResponse.php',
    'WPForms\\Vendor\\Square\\Models\\BatchRetrieveInventoryChangesRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/BatchRetrieveInventoryChangesRequest.php',
    'WPForms\\Vendor\\Square\\Models\\BatchRetrieveInventoryChangesResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/BatchRetrieveInventoryChangesResponse.php',
    'WPForms\\Vendor\\Square\\Models\\BatchRetrieveInventoryCountsRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/BatchRetrieveInventoryCountsRequest.php',
    'WPForms\\Vendor\\Square\\Models\\BatchRetrieveInventoryCountsResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/BatchRetrieveInventoryCountsResponse.php',
    'WPForms\\Vendor\\Square\\Models\\BatchRetrieveOrdersRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/BatchRetrieveOrdersRequest.php',
    'WPForms\\Vendor\\Square\\Models\\BatchRetrieveOrdersResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/BatchRetrieveOrdersResponse.php',
    'WPForms\\Vendor\\Square\\Models\\BatchUpsertCatalogObjectsRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/BatchUpsertCatalogObjectsRequest.php',
    'WPForms\\Vendor\\Square\\Models\\BatchUpsertCatalogObjectsResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/BatchUpsertCatalogObjectsResponse.php',
    'WPForms\\Vendor\\Square\\Models\\Booking' => $baseDir . '/vendor_prefixed/square/square/src/Models/Booking.php',
    'WPForms\\Vendor\\Square\\Models\\BookingBookingSource' => $baseDir . '/vendor_prefixed/square/square/src/Models/BookingBookingSource.php',
    'WPForms\\Vendor\\Square\\Models\\BookingCreatorDetails' => $baseDir . '/vendor_prefixed/square/square/src/Models/BookingCreatorDetails.php',
    'WPForms\\Vendor\\Square\\Models\\BookingCreatorDetailsCreatorType' => $baseDir . '/vendor_prefixed/square/square/src/Models/BookingCreatorDetailsCreatorType.php',
    'WPForms\\Vendor\\Square\\Models\\BookingCustomAttributeDeleteRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/BookingCustomAttributeDeleteRequest.php',
    'WPForms\\Vendor\\Square\\Models\\BookingCustomAttributeDeleteResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/BookingCustomAttributeDeleteResponse.php',
    'WPForms\\Vendor\\Square\\Models\\BookingCustomAttributeUpsertRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/BookingCustomAttributeUpsertRequest.php',
    'WPForms\\Vendor\\Square\\Models\\BookingCustomAttributeUpsertResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/BookingCustomAttributeUpsertResponse.php',
    'WPForms\\Vendor\\Square\\Models\\BookingStatus' => $baseDir . '/vendor_prefixed/square/square/src/Models/BookingStatus.php',
    'WPForms\\Vendor\\Square\\Models\\BreakType' => $baseDir . '/vendor_prefixed/square/square/src/Models/BreakType.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ACHDetailsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ACHDetailsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\AcceptDisputeResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/AcceptDisputeResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\AcceptedPaymentMethodsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/AcceptedPaymentMethodsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\AccumulateLoyaltyPointsRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/AccumulateLoyaltyPointsRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\AccumulateLoyaltyPointsResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/AccumulateLoyaltyPointsResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\AddGroupToCustomerResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/AddGroupToCustomerResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\AdditionalRecipientBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/AdditionalRecipientBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\AddressBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/AddressBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\AdjustLoyaltyPointsRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/AdjustLoyaltyPointsRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\AdjustLoyaltyPointsResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/AdjustLoyaltyPointsResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\AfterpayDetailsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/AfterpayDetailsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ApplicationDetailsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ApplicationDetailsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\AppointmentSegmentBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/AppointmentSegmentBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\AvailabilityBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/AvailabilityBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BankAccountBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BankAccountBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BankAccountPaymentDetailsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BankAccountPaymentDetailsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BatchChangeInventoryRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BatchChangeInventoryRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BatchChangeInventoryResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BatchChangeInventoryResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BatchDeleteCatalogObjectsRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BatchDeleteCatalogObjectsRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BatchDeleteCatalogObjectsResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BatchDeleteCatalogObjectsResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BatchRetrieveCatalogObjectsRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BatchRetrieveCatalogObjectsRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BatchRetrieveCatalogObjectsResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BatchRetrieveCatalogObjectsResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BatchRetrieveInventoryChangesRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BatchRetrieveInventoryChangesRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BatchRetrieveInventoryChangesResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BatchRetrieveInventoryChangesResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BatchRetrieveInventoryCountsRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BatchRetrieveInventoryCountsRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BatchRetrieveInventoryCountsResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BatchRetrieveInventoryCountsResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BatchRetrieveOrdersRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BatchRetrieveOrdersRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BatchRetrieveOrdersResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BatchRetrieveOrdersResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BatchUpsertCatalogObjectsRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BatchUpsertCatalogObjectsRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BatchUpsertCatalogObjectsResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BatchUpsertCatalogObjectsResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BookingBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BookingBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BookingCreatorDetailsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BookingCreatorDetailsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BookingCustomAttributeDeleteRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BookingCustomAttributeDeleteRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BookingCustomAttributeDeleteResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BookingCustomAttributeDeleteResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BookingCustomAttributeUpsertRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BookingCustomAttributeUpsertRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BookingCustomAttributeUpsertResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BookingCustomAttributeUpsertResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BreakTypeBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BreakTypeBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BulkCreateCustomerDataBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BulkCreateCustomerDataBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BulkCreateCustomersRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BulkCreateCustomersRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BulkCreateCustomersResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BulkCreateCustomersResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BulkCreateTeamMembersRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BulkCreateTeamMembersRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BulkCreateTeamMembersResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BulkCreateTeamMembersResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BulkCreateVendorsRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BulkCreateVendorsRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BulkCreateVendorsResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BulkCreateVendorsResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BulkDeleteBookingCustomAttributesRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BulkDeleteBookingCustomAttributesRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BulkDeleteBookingCustomAttributesResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BulkDeleteBookingCustomAttributesResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BulkDeleteCustomersRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BulkDeleteCustomersRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BulkDeleteCustomersResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BulkDeleteCustomersResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BulkDeleteLocationCustomAttributesRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BulkDeleteLocationCustomAttributesRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BulkDeleteLocationCustomAttributesRequestLocationCustomAttributeDeleteRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BulkDeleteLocationCustomAttributesRequestLocationCustomAttributeDeleteRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BulkDeleteLocationCustomAttributesResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BulkDeleteLocationCustomAttributesResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BulkDeleteLocationCustomAttributesResponseLocationCustomAttributeDeleteResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BulkDeleteLocationCustomAttributesResponseLocationCustomAttributeDeleteResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BulkDeleteMerchantCustomAttributesRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BulkDeleteMerchantCustomAttributesRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BulkDeleteMerchantCustomAttributesRequestMerchantCustomAttributeDeleteRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BulkDeleteMerchantCustomAttributesRequestMerchantCustomAttributeDeleteRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BulkDeleteMerchantCustomAttributesResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BulkDeleteMerchantCustomAttributesResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BulkDeleteMerchantCustomAttributesResponseMerchantCustomAttributeDeleteResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BulkDeleteMerchantCustomAttributesResponseMerchantCustomAttributeDeleteResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BulkDeleteOrderCustomAttributesRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BulkDeleteOrderCustomAttributesRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BulkDeleteOrderCustomAttributesRequestDeleteCustomAttributeBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BulkDeleteOrderCustomAttributesRequestDeleteCustomAttributeBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BulkDeleteOrderCustomAttributesResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BulkDeleteOrderCustomAttributesResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BulkRetrieveBookingsRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BulkRetrieveBookingsRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BulkRetrieveBookingsResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BulkRetrieveBookingsResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BulkRetrieveCustomersRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BulkRetrieveCustomersRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BulkRetrieveCustomersResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BulkRetrieveCustomersResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BulkRetrieveTeamMemberBookingProfilesRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BulkRetrieveTeamMemberBookingProfilesRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BulkRetrieveTeamMemberBookingProfilesResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BulkRetrieveTeamMemberBookingProfilesResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BulkRetrieveVendorsRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BulkRetrieveVendorsRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BulkRetrieveVendorsResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BulkRetrieveVendorsResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BulkSwapPlanRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BulkSwapPlanRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BulkSwapPlanResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BulkSwapPlanResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BulkUpdateCustomerDataBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BulkUpdateCustomerDataBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BulkUpdateCustomersRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BulkUpdateCustomersRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BulkUpdateCustomersResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BulkUpdateCustomersResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BulkUpdateTeamMembersRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BulkUpdateTeamMembersRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BulkUpdateTeamMembersResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BulkUpdateTeamMembersResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BulkUpdateVendorsRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BulkUpdateVendorsRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BulkUpdateVendorsResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BulkUpdateVendorsResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BulkUpsertBookingCustomAttributesRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BulkUpsertBookingCustomAttributesRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BulkUpsertBookingCustomAttributesResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BulkUpsertBookingCustomAttributesResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BulkUpsertCustomerCustomAttributesRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BulkUpsertCustomerCustomAttributesRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BulkUpsertCustomerCustomAttributesRequestCustomerCustomAttributeUpsertRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BulkUpsertCustomerCustomAttributesRequestCustomerCustomAttributeUpsertRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BulkUpsertCustomerCustomAttributesResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BulkUpsertCustomerCustomAttributesResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BulkUpsertCustomerCustomAttributesResponseCustomerCustomAttributeUpsertResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BulkUpsertCustomerCustomAttributesResponseCustomerCustomAttributeUpsertResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BulkUpsertLocationCustomAttributesRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BulkUpsertLocationCustomAttributesRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BulkUpsertLocationCustomAttributesRequestLocationCustomAttributeUpsertRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BulkUpsertLocationCustomAttributesRequestLocationCustomAttributeUpsertRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BulkUpsertLocationCustomAttributesResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BulkUpsertLocationCustomAttributesResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BulkUpsertLocationCustomAttributesResponseLocationCustomAttributeUpsertResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BulkUpsertLocationCustomAttributesResponseLocationCustomAttributeUpsertResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BulkUpsertMerchantCustomAttributesRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BulkUpsertMerchantCustomAttributesRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BulkUpsertMerchantCustomAttributesRequestMerchantCustomAttributeUpsertRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BulkUpsertMerchantCustomAttributesRequestMerchantCustomAttributeUpsertRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BulkUpsertMerchantCustomAttributesResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BulkUpsertMerchantCustomAttributesResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BulkUpsertMerchantCustomAttributesResponseMerchantCustomAttributeUpsertResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BulkUpsertMerchantCustomAttributesResponseMerchantCustomAttributeUpsertResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BulkUpsertOrderCustomAttributesRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BulkUpsertOrderCustomAttributesRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BulkUpsertOrderCustomAttributesRequestUpsertCustomAttributeBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BulkUpsertOrderCustomAttributesRequestUpsertCustomAttributeBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BulkUpsertOrderCustomAttributesResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BulkUpsertOrderCustomAttributesResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BusinessAppointmentSettingsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BusinessAppointmentSettingsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BusinessBookingProfileBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BusinessBookingProfileBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BusinessHoursBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BusinessHoursBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BusinessHoursPeriodBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BusinessHoursPeriodBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\BuyNowPayLaterDetailsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/BuyNowPayLaterDetailsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CalculateLoyaltyPointsRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CalculateLoyaltyPointsRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CalculateLoyaltyPointsResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CalculateLoyaltyPointsResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CalculateOrderRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CalculateOrderRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CalculateOrderResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CalculateOrderResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CancelBookingRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CancelBookingRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CancelBookingResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CancelBookingResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CancelInvoiceRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CancelInvoiceRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CancelInvoiceResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CancelInvoiceResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CancelLoyaltyPromotionResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CancelLoyaltyPromotionResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CancelPaymentByIdempotencyKeyRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CancelPaymentByIdempotencyKeyRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CancelPaymentByIdempotencyKeyResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CancelPaymentByIdempotencyKeyResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CancelPaymentResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CancelPaymentResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CancelSubscriptionResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CancelSubscriptionResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CancelTerminalActionResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CancelTerminalActionResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CancelTerminalCheckoutResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CancelTerminalCheckoutResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CancelTerminalRefundResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CancelTerminalRefundResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CaptureTransactionResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CaptureTransactionResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CardBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CardBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CardPaymentDetailsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CardPaymentDetailsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CardPaymentTimelineBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CardPaymentTimelineBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CashAppDetailsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CashAppDetailsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CashDrawerDeviceBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CashDrawerDeviceBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CashDrawerShiftBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CashDrawerShiftBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CashDrawerShiftEventBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CashDrawerShiftEventBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CashDrawerShiftSummaryBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CashDrawerShiftSummaryBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CashPaymentDetailsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CashPaymentDetailsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CatalogAvailabilityPeriodBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CatalogAvailabilityPeriodBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CatalogCategoryBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CatalogCategoryBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CatalogCustomAttributeDefinitionBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CatalogCustomAttributeDefinitionBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CatalogCustomAttributeDefinitionNumberConfigBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CatalogCustomAttributeDefinitionNumberConfigBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CatalogCustomAttributeDefinitionSelectionConfigBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CatalogCustomAttributeDefinitionSelectionConfigBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CatalogCustomAttributeDefinitionSelectionConfigCustomAttributeSelectionBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CatalogCustomAttributeDefinitionSelectionConfigCustomAttributeSelectionBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CatalogCustomAttributeDefinitionStringConfigBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CatalogCustomAttributeDefinitionStringConfigBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CatalogCustomAttributeValueBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CatalogCustomAttributeValueBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CatalogDiscountBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CatalogDiscountBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CatalogEcomSeoDataBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CatalogEcomSeoDataBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CatalogIdMappingBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CatalogIdMappingBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CatalogImageBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CatalogImageBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CatalogInfoResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CatalogInfoResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CatalogInfoResponseLimitsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CatalogInfoResponseLimitsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CatalogItemBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CatalogItemBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CatalogItemFoodAndBeverageDetailsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CatalogItemFoodAndBeverageDetailsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CatalogItemFoodAndBeverageDetailsDietaryPreferenceBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CatalogItemFoodAndBeverageDetailsDietaryPreferenceBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CatalogItemFoodAndBeverageDetailsIngredientBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CatalogItemFoodAndBeverageDetailsIngredientBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CatalogItemModifierListInfoBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CatalogItemModifierListInfoBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CatalogItemOptionBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CatalogItemOptionBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CatalogItemOptionForItemBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CatalogItemOptionForItemBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CatalogItemOptionValueBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CatalogItemOptionValueBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CatalogItemOptionValueForItemVariationBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CatalogItemOptionValueForItemVariationBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CatalogItemVariationBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CatalogItemVariationBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CatalogMeasurementUnitBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CatalogMeasurementUnitBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CatalogModifierBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CatalogModifierBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CatalogModifierListBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CatalogModifierListBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CatalogModifierOverrideBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CatalogModifierOverrideBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CatalogObjectBatchBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CatalogObjectBatchBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CatalogObjectBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CatalogObjectBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CatalogObjectCategoryBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CatalogObjectCategoryBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CatalogObjectReferenceBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CatalogObjectReferenceBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CatalogPricingRuleBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CatalogPricingRuleBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CatalogProductSetBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CatalogProductSetBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CatalogQueryBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CatalogQueryBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CatalogQueryExactBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CatalogQueryExactBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CatalogQueryItemVariationsForItemOptionValuesBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CatalogQueryItemVariationsForItemOptionValuesBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CatalogQueryItemsForItemOptionsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CatalogQueryItemsForItemOptionsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CatalogQueryItemsForModifierListBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CatalogQueryItemsForModifierListBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CatalogQueryItemsForTaxBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CatalogQueryItemsForTaxBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CatalogQueryPrefixBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CatalogQueryPrefixBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CatalogQueryRangeBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CatalogQueryRangeBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CatalogQuerySetBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CatalogQuerySetBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CatalogQuerySortedAttributeBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CatalogQuerySortedAttributeBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CatalogQueryTextBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CatalogQueryTextBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CatalogQuickAmountBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CatalogQuickAmountBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CatalogQuickAmountsSettingsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CatalogQuickAmountsSettingsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CatalogStockConversionBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CatalogStockConversionBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CatalogSubscriptionPlanBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CatalogSubscriptionPlanBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CatalogSubscriptionPlanVariationBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CatalogSubscriptionPlanVariationBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CatalogTaxBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CatalogTaxBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CatalogTimePeriodBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CatalogTimePeriodBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CatalogV1IdBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CatalogV1IdBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CategoryPathToRootNodeBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CategoryPathToRootNodeBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ChangeBillingAnchorDateRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ChangeBillingAnchorDateRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ChangeBillingAnchorDateResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ChangeBillingAnchorDateResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ChargeRequestAdditionalRecipientBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ChargeRequestAdditionalRecipientBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ChargeRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ChargeRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ChargeResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ChargeResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CheckoutBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CheckoutBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CheckoutLocationSettingsBrandingBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CheckoutLocationSettingsBrandingBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CheckoutLocationSettingsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CheckoutLocationSettingsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CheckoutLocationSettingsCouponsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CheckoutLocationSettingsCouponsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CheckoutLocationSettingsPolicyBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CheckoutLocationSettingsPolicyBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CheckoutLocationSettingsTippingBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CheckoutLocationSettingsTippingBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CheckoutMerchantSettingsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CheckoutMerchantSettingsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CheckoutMerchantSettingsPaymentMethodsAfterpayClearpayBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CheckoutMerchantSettingsPaymentMethodsAfterpayClearpayBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CheckoutMerchantSettingsPaymentMethodsAfterpayClearpayEligibilityRangeBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CheckoutMerchantSettingsPaymentMethodsAfterpayClearpayEligibilityRangeBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CheckoutMerchantSettingsPaymentMethodsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CheckoutMerchantSettingsPaymentMethodsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CheckoutMerchantSettingsPaymentMethodsPaymentMethodBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CheckoutMerchantSettingsPaymentMethodsPaymentMethodBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CheckoutOptionsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CheckoutOptionsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ClearpayDetailsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ClearpayDetailsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CloneOrderRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CloneOrderRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CloneOrderResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CloneOrderResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CollectedDataBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CollectedDataBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CompletePaymentRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CompletePaymentRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CompletePaymentResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CompletePaymentResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ComponentBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ComponentBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ConfirmationDecisionBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ConfirmationDecisionBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ConfirmationOptionsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ConfirmationOptionsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CoordinatesBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CoordinatesBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateBookingCustomAttributeDefinitionRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateBookingCustomAttributeDefinitionRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateBookingCustomAttributeDefinitionResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateBookingCustomAttributeDefinitionResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateBookingRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateBookingRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateBookingResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateBookingResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateBreakTypeRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateBreakTypeRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateBreakTypeResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateBreakTypeResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateCardRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateCardRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateCardResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateCardResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateCatalogImageRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateCatalogImageRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateCatalogImageResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateCatalogImageResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateCheckoutRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateCheckoutRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateCheckoutResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateCheckoutResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateCustomerCardRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateCustomerCardRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateCustomerCardResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateCustomerCardResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateCustomerCustomAttributeDefinitionRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateCustomerCustomAttributeDefinitionRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateCustomerCustomAttributeDefinitionResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateCustomerCustomAttributeDefinitionResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateCustomerGroupRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateCustomerGroupRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateCustomerGroupResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateCustomerGroupResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateCustomerRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateCustomerRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateCustomerResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateCustomerResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateDeviceCodeRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateDeviceCodeRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateDeviceCodeResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateDeviceCodeResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateDisputeEvidenceFileRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateDisputeEvidenceFileRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateDisputeEvidenceFileResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateDisputeEvidenceFileResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateDisputeEvidenceTextRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateDisputeEvidenceTextRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateDisputeEvidenceTextResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateDisputeEvidenceTextResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateGiftCardActivityRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateGiftCardActivityRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateGiftCardActivityResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateGiftCardActivityResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateGiftCardRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateGiftCardRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateGiftCardResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateGiftCardResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateInvoiceAttachmentRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateInvoiceAttachmentRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateInvoiceAttachmentResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateInvoiceAttachmentResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateInvoiceRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateInvoiceRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateInvoiceResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateInvoiceResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateJobRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateJobRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateJobResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateJobResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateLocationCustomAttributeDefinitionRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateLocationCustomAttributeDefinitionRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateLocationCustomAttributeDefinitionResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateLocationCustomAttributeDefinitionResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateLocationRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateLocationRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateLocationResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateLocationResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateLoyaltyAccountRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateLoyaltyAccountRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateLoyaltyAccountResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateLoyaltyAccountResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateLoyaltyPromotionRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateLoyaltyPromotionRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateLoyaltyPromotionResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateLoyaltyPromotionResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateLoyaltyRewardRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateLoyaltyRewardRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateLoyaltyRewardResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateLoyaltyRewardResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateMerchantCustomAttributeDefinitionRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateMerchantCustomAttributeDefinitionRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateMerchantCustomAttributeDefinitionResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateMerchantCustomAttributeDefinitionResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateMobileAuthorizationCodeRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateMobileAuthorizationCodeRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateMobileAuthorizationCodeResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateMobileAuthorizationCodeResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateOrderCustomAttributeDefinitionRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateOrderCustomAttributeDefinitionRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateOrderCustomAttributeDefinitionResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateOrderCustomAttributeDefinitionResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateOrderRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateOrderRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateOrderResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateOrderResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreatePaymentLinkRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreatePaymentLinkRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreatePaymentLinkResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreatePaymentLinkResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreatePaymentRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreatePaymentRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreatePaymentResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreatePaymentResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateRefundRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateRefundRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateRefundResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateRefundResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateShiftRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateShiftRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateShiftResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateShiftResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateSubscriptionRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateSubscriptionRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateSubscriptionResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateSubscriptionResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateTeamMemberRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateTeamMemberRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateTeamMemberResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateTeamMemberResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateTerminalActionRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateTerminalActionRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateTerminalActionResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateTerminalActionResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateTerminalCheckoutRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateTerminalCheckoutRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateTerminalCheckoutResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateTerminalCheckoutResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateTerminalRefundRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateTerminalRefundRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateTerminalRefundResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateTerminalRefundResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateVendorRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateVendorRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateVendorResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateVendorResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateWebhookSubscriptionRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateWebhookSubscriptionRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CreateWebhookSubscriptionResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CreateWebhookSubscriptionResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CustomAttributeBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CustomAttributeBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CustomAttributeDefinitionBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CustomAttributeDefinitionBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CustomAttributeFilterBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CustomAttributeFilterBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CustomFieldBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CustomFieldBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CustomerAddressFilterBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CustomerAddressFilterBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CustomerBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CustomerBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CustomerCreationSourceFilterBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CustomerCreationSourceFilterBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CustomerCustomAttributeFilterBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CustomerCustomAttributeFilterBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CustomerCustomAttributeFilterValueBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CustomerCustomAttributeFilterValueBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CustomerCustomAttributeFiltersBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CustomerCustomAttributeFiltersBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CustomerDetailsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CustomerDetailsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CustomerFilterBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CustomerFilterBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CustomerGroupBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CustomerGroupBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CustomerPreferencesBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CustomerPreferencesBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CustomerQueryBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CustomerQueryBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CustomerSegmentBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CustomerSegmentBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CustomerSortBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CustomerSortBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CustomerTaxIdsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CustomerTaxIdsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\CustomerTextFilterBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/CustomerTextFilterBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\DataCollectionOptionsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/DataCollectionOptionsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\DateRangeBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/DateRangeBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\DeleteBookingCustomAttributeDefinitionResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/DeleteBookingCustomAttributeDefinitionResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\DeleteBookingCustomAttributeResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/DeleteBookingCustomAttributeResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\DeleteBreakTypeResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/DeleteBreakTypeResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\DeleteCatalogObjectResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/DeleteCatalogObjectResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\DeleteCustomerCardResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/DeleteCustomerCardResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\DeleteCustomerCustomAttributeDefinitionResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/DeleteCustomerCustomAttributeDefinitionResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\DeleteCustomerCustomAttributeResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/DeleteCustomerCustomAttributeResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\DeleteCustomerGroupResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/DeleteCustomerGroupResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\DeleteCustomerRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/DeleteCustomerRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\DeleteCustomerResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/DeleteCustomerResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\DeleteDisputeEvidenceResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/DeleteDisputeEvidenceResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\DeleteInvoiceAttachmentResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/DeleteInvoiceAttachmentResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\DeleteInvoiceRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/DeleteInvoiceRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\DeleteInvoiceResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/DeleteInvoiceResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\DeleteLocationCustomAttributeDefinitionResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/DeleteLocationCustomAttributeDefinitionResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\DeleteLocationCustomAttributeResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/DeleteLocationCustomAttributeResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\DeleteLoyaltyRewardResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/DeleteLoyaltyRewardResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\DeleteMerchantCustomAttributeDefinitionResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/DeleteMerchantCustomAttributeDefinitionResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\DeleteMerchantCustomAttributeResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/DeleteMerchantCustomAttributeResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\DeleteOrderCustomAttributeDefinitionResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/DeleteOrderCustomAttributeDefinitionResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\DeleteOrderCustomAttributeResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/DeleteOrderCustomAttributeResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\DeletePaymentLinkResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/DeletePaymentLinkResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\DeleteShiftResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/DeleteShiftResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\DeleteSnippetResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/DeleteSnippetResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\DeleteSubscriptionActionResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/DeleteSubscriptionActionResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\DeleteWebhookSubscriptionResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/DeleteWebhookSubscriptionResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\DeprecatedCreateDisputeEvidenceFileRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/DeprecatedCreateDisputeEvidenceFileRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\DeprecatedCreateDisputeEvidenceFileResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/DeprecatedCreateDisputeEvidenceFileResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\DeprecatedCreateDisputeEvidenceTextRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/DeprecatedCreateDisputeEvidenceTextRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\DeprecatedCreateDisputeEvidenceTextResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/DeprecatedCreateDisputeEvidenceTextResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\DestinationBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/DestinationBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\DestinationDetailsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/DestinationDetailsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\DestinationDetailsCardRefundDetailsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/DestinationDetailsCardRefundDetailsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\DestinationDetailsCashRefundDetailsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/DestinationDetailsCashRefundDetailsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\DestinationDetailsExternalRefundDetailsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/DestinationDetailsExternalRefundDetailsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\DeviceAttributesBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/DeviceAttributesBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\DeviceBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/DeviceBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\DeviceCheckoutOptionsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/DeviceCheckoutOptionsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\DeviceCodeBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/DeviceCodeBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\DeviceComponentDetailsApplicationDetailsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/DeviceComponentDetailsApplicationDetailsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\DeviceComponentDetailsBatteryDetailsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/DeviceComponentDetailsBatteryDetailsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\DeviceComponentDetailsCardReaderDetailsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/DeviceComponentDetailsCardReaderDetailsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\DeviceComponentDetailsEthernetDetailsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/DeviceComponentDetailsEthernetDetailsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\DeviceComponentDetailsMeasurementBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/DeviceComponentDetailsMeasurementBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\DeviceComponentDetailsNetworkInterfaceDetailsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/DeviceComponentDetailsNetworkInterfaceDetailsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\DeviceComponentDetailsWiFiDetailsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/DeviceComponentDetailsWiFiDetailsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\DeviceDetailsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/DeviceDetailsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\DeviceMetadataBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/DeviceMetadataBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\DeviceStatusBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/DeviceStatusBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\DigitalWalletDetailsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/DigitalWalletDetailsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\DisableCardResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/DisableCardResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\DisableEventsResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/DisableEventsResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\DismissTerminalActionResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/DismissTerminalActionResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\DismissTerminalCheckoutResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/DismissTerminalCheckoutResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\DismissTerminalRefundResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/DismissTerminalRefundResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\DisputeBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/DisputeBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\DisputeEvidenceBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/DisputeEvidenceBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\DisputeEvidenceFileBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/DisputeEvidenceFileBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\DisputedPaymentBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/DisputedPaymentBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\EmployeeBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/EmployeeBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\EmployeeWageBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/EmployeeWageBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\EnableEventsResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/EnableEventsResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ErrorBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ErrorBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\EventBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/EventBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\EventDataBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/EventDataBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\EventMetadataBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/EventMetadataBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\EventTypeMetadataBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/EventTypeMetadataBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ExternalPaymentDetailsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ExternalPaymentDetailsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\FilterValueBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/FilterValueBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\FloatNumberRangeBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/FloatNumberRangeBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\FulfillmentBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/FulfillmentBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\FulfillmentDeliveryDetailsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/FulfillmentDeliveryDetailsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\FulfillmentFulfillmentEntryBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/FulfillmentFulfillmentEntryBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\FulfillmentPickupDetailsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/FulfillmentPickupDetailsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\FulfillmentPickupDetailsCurbsidePickupDetailsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/FulfillmentPickupDetailsCurbsidePickupDetailsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\FulfillmentRecipientBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/FulfillmentRecipientBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\FulfillmentShipmentDetailsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/FulfillmentShipmentDetailsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\GetBankAccountByV1IdResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/GetBankAccountByV1IdResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\GetBankAccountResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/GetBankAccountResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\GetBreakTypeResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/GetBreakTypeResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\GetDeviceCodeResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/GetDeviceCodeResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\GetDeviceResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/GetDeviceResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\GetEmployeeWageResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/GetEmployeeWageResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\GetInvoiceResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/GetInvoiceResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\GetPaymentRefundResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/GetPaymentRefundResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\GetPaymentResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/GetPaymentResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\GetPayoutResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/GetPayoutResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\GetShiftResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/GetShiftResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\GetTeamMemberWageResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/GetTeamMemberWageResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\GetTerminalActionResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/GetTerminalActionResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\GetTerminalCheckoutResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/GetTerminalCheckoutResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\GetTerminalRefundResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/GetTerminalRefundResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\GiftCardActivityActivateBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/GiftCardActivityActivateBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\GiftCardActivityAdjustDecrementBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/GiftCardActivityAdjustDecrementBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\GiftCardActivityAdjustIncrementBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/GiftCardActivityAdjustIncrementBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\GiftCardActivityBlockBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/GiftCardActivityBlockBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\GiftCardActivityBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/GiftCardActivityBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\GiftCardActivityClearBalanceBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/GiftCardActivityClearBalanceBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\GiftCardActivityDeactivateBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/GiftCardActivityDeactivateBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\GiftCardActivityImportBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/GiftCardActivityImportBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\GiftCardActivityImportReversalBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/GiftCardActivityImportReversalBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\GiftCardActivityLoadBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/GiftCardActivityLoadBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\GiftCardActivityRedeemBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/GiftCardActivityRedeemBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\GiftCardActivityRefundBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/GiftCardActivityRefundBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\GiftCardActivityTransferBalanceFromBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/GiftCardActivityTransferBalanceFromBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\GiftCardActivityTransferBalanceToBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/GiftCardActivityTransferBalanceToBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\GiftCardActivityUnblockBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/GiftCardActivityUnblockBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\GiftCardActivityUnlinkedActivityRefundBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/GiftCardActivityUnlinkedActivityRefundBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\GiftCardBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/GiftCardBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\InventoryAdjustmentBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/InventoryAdjustmentBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\InventoryAdjustmentGroupBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/InventoryAdjustmentGroupBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\InventoryChangeBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/InventoryChangeBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\InventoryCountBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/InventoryCountBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\InventoryPhysicalCountBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/InventoryPhysicalCountBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\InventoryTransferBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/InventoryTransferBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\InvoiceAcceptedPaymentMethodsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/InvoiceAcceptedPaymentMethodsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\InvoiceAttachmentBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/InvoiceAttachmentBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\InvoiceBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/InvoiceBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\InvoiceCustomFieldBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/InvoiceCustomFieldBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\InvoiceFilterBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/InvoiceFilterBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\InvoicePaymentReminderBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/InvoicePaymentReminderBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\InvoicePaymentRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/InvoicePaymentRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\InvoiceQueryBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/InvoiceQueryBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\InvoiceRecipientBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/InvoiceRecipientBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\InvoiceRecipientTaxIdsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/InvoiceRecipientTaxIdsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\InvoiceSortBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/InvoiceSortBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ItemVariationLocationOverridesBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ItemVariationLocationOverridesBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\JobAssignmentBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/JobAssignmentBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\JobBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/JobBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\LinkCustomerToGiftCardRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/LinkCustomerToGiftCardRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\LinkCustomerToGiftCardResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/LinkCustomerToGiftCardResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListBankAccountsRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListBankAccountsRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListBankAccountsResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListBankAccountsResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListBookingCustomAttributeDefinitionsRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListBookingCustomAttributeDefinitionsRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListBookingCustomAttributeDefinitionsResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListBookingCustomAttributeDefinitionsResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListBookingCustomAttributesRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListBookingCustomAttributesRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListBookingCustomAttributesResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListBookingCustomAttributesResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListBookingsRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListBookingsRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListBookingsResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListBookingsResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListBreakTypesRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListBreakTypesRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListBreakTypesResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListBreakTypesResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListCardsRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListCardsRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListCardsResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListCardsResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListCashDrawerShiftEventsRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListCashDrawerShiftEventsRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListCashDrawerShiftEventsResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListCashDrawerShiftEventsResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListCashDrawerShiftsRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListCashDrawerShiftsRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListCashDrawerShiftsResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListCashDrawerShiftsResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListCatalogRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListCatalogRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListCatalogResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListCatalogResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListCustomerCustomAttributeDefinitionsRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListCustomerCustomAttributeDefinitionsRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListCustomerCustomAttributeDefinitionsResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListCustomerCustomAttributeDefinitionsResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListCustomerCustomAttributesRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListCustomerCustomAttributesRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListCustomerCustomAttributesResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListCustomerCustomAttributesResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListCustomerGroupsRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListCustomerGroupsRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListCustomerGroupsResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListCustomerGroupsResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListCustomerSegmentsRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListCustomerSegmentsRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListCustomerSegmentsResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListCustomerSegmentsResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListCustomersRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListCustomersRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListCustomersResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListCustomersResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListDeviceCodesRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListDeviceCodesRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListDeviceCodesResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListDeviceCodesResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListDevicesRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListDevicesRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListDevicesResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListDevicesResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListDisputeEvidenceRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListDisputeEvidenceRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListDisputeEvidenceResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListDisputeEvidenceResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListDisputesRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListDisputesRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListDisputesResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListDisputesResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListEmployeeWagesRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListEmployeeWagesRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListEmployeeWagesResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListEmployeeWagesResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListEmployeesRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListEmployeesRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListEmployeesResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListEmployeesResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListEventTypesRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListEventTypesRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListEventTypesResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListEventTypesResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListGiftCardActivitiesRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListGiftCardActivitiesRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListGiftCardActivitiesResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListGiftCardActivitiesResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListGiftCardsRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListGiftCardsRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListGiftCardsResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListGiftCardsResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListInvoicesRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListInvoicesRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListInvoicesResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListInvoicesResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListJobsRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListJobsRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListJobsResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListJobsResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListLocationBookingProfilesRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListLocationBookingProfilesRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListLocationBookingProfilesResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListLocationBookingProfilesResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListLocationCustomAttributeDefinitionsRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListLocationCustomAttributeDefinitionsRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListLocationCustomAttributeDefinitionsResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListLocationCustomAttributeDefinitionsResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListLocationCustomAttributesRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListLocationCustomAttributesRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListLocationCustomAttributesResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListLocationCustomAttributesResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListLocationsResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListLocationsResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListLoyaltyProgramsResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListLoyaltyProgramsResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListLoyaltyPromotionsRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListLoyaltyPromotionsRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListLoyaltyPromotionsResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListLoyaltyPromotionsResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListMerchantCustomAttributeDefinitionsRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListMerchantCustomAttributeDefinitionsRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListMerchantCustomAttributeDefinitionsResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListMerchantCustomAttributeDefinitionsResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListMerchantCustomAttributesRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListMerchantCustomAttributesRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListMerchantCustomAttributesResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListMerchantCustomAttributesResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListMerchantsRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListMerchantsRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListMerchantsResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListMerchantsResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListOrderCustomAttributeDefinitionsRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListOrderCustomAttributeDefinitionsRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListOrderCustomAttributeDefinitionsResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListOrderCustomAttributeDefinitionsResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListOrderCustomAttributesRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListOrderCustomAttributesRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListOrderCustomAttributesResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListOrderCustomAttributesResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListPaymentLinksRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListPaymentLinksRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListPaymentLinksResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListPaymentLinksResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListPaymentRefundsRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListPaymentRefundsRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListPaymentRefundsResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListPaymentRefundsResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListPaymentsRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListPaymentsRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListPaymentsResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListPaymentsResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListPayoutEntriesRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListPayoutEntriesRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListPayoutEntriesResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListPayoutEntriesResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListPayoutsRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListPayoutsRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListPayoutsResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListPayoutsResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListRefundsRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListRefundsRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListRefundsResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListRefundsResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListSitesResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListSitesResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListSubscriptionEventsRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListSubscriptionEventsRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListSubscriptionEventsResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListSubscriptionEventsResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListTeamMemberBookingProfilesRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListTeamMemberBookingProfilesRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListTeamMemberBookingProfilesResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListTeamMemberBookingProfilesResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListTeamMemberWagesRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListTeamMemberWagesRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListTeamMemberWagesResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListTeamMemberWagesResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListTransactionsRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListTransactionsRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListTransactionsResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListTransactionsResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListWebhookEventTypesRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListWebhookEventTypesRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListWebhookEventTypesResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListWebhookEventTypesResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListWebhookSubscriptionsRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListWebhookSubscriptionsRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListWebhookSubscriptionsResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListWebhookSubscriptionsResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListWorkweekConfigsRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListWorkweekConfigsRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ListWorkweekConfigsResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ListWorkweekConfigsResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\LocationBookingProfileBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/LocationBookingProfileBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\LocationBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/LocationBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\LoyaltyAccountBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/LoyaltyAccountBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\LoyaltyAccountExpiringPointDeadlineBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/LoyaltyAccountExpiringPointDeadlineBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\LoyaltyAccountMappingBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/LoyaltyAccountMappingBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\LoyaltyEventAccumulatePointsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/LoyaltyEventAccumulatePointsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\LoyaltyEventAccumulatePromotionPointsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/LoyaltyEventAccumulatePromotionPointsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\LoyaltyEventAdjustPointsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/LoyaltyEventAdjustPointsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\LoyaltyEventBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/LoyaltyEventBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\LoyaltyEventCreateRewardBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/LoyaltyEventCreateRewardBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\LoyaltyEventDateTimeFilterBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/LoyaltyEventDateTimeFilterBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\LoyaltyEventDeleteRewardBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/LoyaltyEventDeleteRewardBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\LoyaltyEventExpirePointsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/LoyaltyEventExpirePointsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\LoyaltyEventFilterBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/LoyaltyEventFilterBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\LoyaltyEventLocationFilterBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/LoyaltyEventLocationFilterBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\LoyaltyEventLoyaltyAccountFilterBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/LoyaltyEventLoyaltyAccountFilterBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\LoyaltyEventOrderFilterBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/LoyaltyEventOrderFilterBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\LoyaltyEventOtherBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/LoyaltyEventOtherBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\LoyaltyEventQueryBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/LoyaltyEventQueryBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\LoyaltyEventRedeemRewardBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/LoyaltyEventRedeemRewardBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\LoyaltyEventTypeFilterBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/LoyaltyEventTypeFilterBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\LoyaltyProgramAccrualRuleBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/LoyaltyProgramAccrualRuleBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\LoyaltyProgramAccrualRuleCategoryDataBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/LoyaltyProgramAccrualRuleCategoryDataBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\LoyaltyProgramAccrualRuleItemVariationDataBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/LoyaltyProgramAccrualRuleItemVariationDataBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\LoyaltyProgramAccrualRuleSpendDataBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/LoyaltyProgramAccrualRuleSpendDataBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\LoyaltyProgramAccrualRuleVisitDataBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/LoyaltyProgramAccrualRuleVisitDataBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\LoyaltyProgramBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/LoyaltyProgramBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\LoyaltyProgramExpirationPolicyBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/LoyaltyProgramExpirationPolicyBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\LoyaltyProgramRewardDefinitionBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/LoyaltyProgramRewardDefinitionBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\LoyaltyProgramRewardTierBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/LoyaltyProgramRewardTierBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\LoyaltyProgramTerminologyBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/LoyaltyProgramTerminologyBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\LoyaltyPromotionAvailableTimeDataBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/LoyaltyPromotionAvailableTimeDataBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\LoyaltyPromotionBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/LoyaltyPromotionBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\LoyaltyPromotionIncentiveBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/LoyaltyPromotionIncentiveBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\LoyaltyPromotionIncentivePointsAdditionDataBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/LoyaltyPromotionIncentivePointsAdditionDataBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\LoyaltyPromotionIncentivePointsMultiplierDataBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/LoyaltyPromotionIncentivePointsMultiplierDataBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\LoyaltyPromotionTriggerLimitBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/LoyaltyPromotionTriggerLimitBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\LoyaltyRewardBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/LoyaltyRewardBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\MBreakBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/MBreakBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\MeasurementUnitBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/MeasurementUnitBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\MeasurementUnitCustomBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/MeasurementUnitCustomBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\MerchantBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/MerchantBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ModifierLocationOverridesBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ModifierLocationOverridesBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\MoneyBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/MoneyBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ObtainTokenRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ObtainTokenRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ObtainTokenResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ObtainTokenResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\OfflinePaymentDetailsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/OfflinePaymentDetailsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\OrderBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/OrderBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\OrderCreatedBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/OrderCreatedBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\OrderCreatedObjectBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/OrderCreatedObjectBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\OrderEntryBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/OrderEntryBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\OrderFulfillmentBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/OrderFulfillmentBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\OrderFulfillmentDeliveryDetailsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/OrderFulfillmentDeliveryDetailsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\OrderFulfillmentFulfillmentEntryBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/OrderFulfillmentFulfillmentEntryBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\OrderFulfillmentPickupDetailsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/OrderFulfillmentPickupDetailsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\OrderFulfillmentPickupDetailsCurbsidePickupDetailsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/OrderFulfillmentPickupDetailsCurbsidePickupDetailsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\OrderFulfillmentRecipientBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/OrderFulfillmentRecipientBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\OrderFulfillmentShipmentDetailsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/OrderFulfillmentShipmentDetailsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\OrderFulfillmentUpdatedBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/OrderFulfillmentUpdatedBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\OrderFulfillmentUpdatedObjectBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/OrderFulfillmentUpdatedObjectBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\OrderFulfillmentUpdatedUpdateBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/OrderFulfillmentUpdatedUpdateBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\OrderLineItemAppliedDiscountBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/OrderLineItemAppliedDiscountBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\OrderLineItemAppliedServiceChargeBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/OrderLineItemAppliedServiceChargeBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\OrderLineItemAppliedTaxBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/OrderLineItemAppliedTaxBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\OrderLineItemBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/OrderLineItemBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\OrderLineItemDiscountBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/OrderLineItemDiscountBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\OrderLineItemModifierBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/OrderLineItemModifierBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\OrderLineItemPricingBlocklistsBlockedDiscountBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/OrderLineItemPricingBlocklistsBlockedDiscountBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\OrderLineItemPricingBlocklistsBlockedTaxBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/OrderLineItemPricingBlocklistsBlockedTaxBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\OrderLineItemPricingBlocklistsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/OrderLineItemPricingBlocklistsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\OrderLineItemTaxBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/OrderLineItemTaxBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\OrderMoneyAmountsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/OrderMoneyAmountsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\OrderPricingOptionsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/OrderPricingOptionsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\OrderQuantityUnitBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/OrderQuantityUnitBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\OrderReturnBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/OrderReturnBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\OrderReturnDiscountBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/OrderReturnDiscountBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\OrderReturnLineItemBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/OrderReturnLineItemBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\OrderReturnLineItemModifierBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/OrderReturnLineItemModifierBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\OrderReturnServiceChargeBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/OrderReturnServiceChargeBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\OrderReturnTaxBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/OrderReturnTaxBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\OrderReturnTipBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/OrderReturnTipBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\OrderRewardBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/OrderRewardBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\OrderRoundingAdjustmentBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/OrderRoundingAdjustmentBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\OrderServiceChargeBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/OrderServiceChargeBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\OrderSourceBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/OrderSourceBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\OrderUpdatedBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/OrderUpdatedBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\OrderUpdatedObjectBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/OrderUpdatedObjectBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\PaginationCursorBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/PaginationCursorBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\PauseSubscriptionRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/PauseSubscriptionRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\PauseSubscriptionResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/PauseSubscriptionResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\PayOrderRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/PayOrderRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\PayOrderResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/PayOrderResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\PaymentBalanceActivityAppFeeRefundDetailBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/PaymentBalanceActivityAppFeeRefundDetailBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\PaymentBalanceActivityAppFeeRevenueDetailBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/PaymentBalanceActivityAppFeeRevenueDetailBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\PaymentBalanceActivityAutomaticSavingsDetailBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/PaymentBalanceActivityAutomaticSavingsDetailBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\PaymentBalanceActivityAutomaticSavingsReversedDetailBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/PaymentBalanceActivityAutomaticSavingsReversedDetailBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\PaymentBalanceActivityChargeDetailBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/PaymentBalanceActivityChargeDetailBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\PaymentBalanceActivityDepositFeeDetailBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/PaymentBalanceActivityDepositFeeDetailBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\PaymentBalanceActivityDepositFeeReversedDetailBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/PaymentBalanceActivityDepositFeeReversedDetailBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\PaymentBalanceActivityDisputeDetailBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/PaymentBalanceActivityDisputeDetailBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\PaymentBalanceActivityFeeDetailBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/PaymentBalanceActivityFeeDetailBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\PaymentBalanceActivityFreeProcessingDetailBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/PaymentBalanceActivityFreeProcessingDetailBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\PaymentBalanceActivityHoldAdjustmentDetailBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/PaymentBalanceActivityHoldAdjustmentDetailBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\PaymentBalanceActivityOpenDisputeDetailBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/PaymentBalanceActivityOpenDisputeDetailBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\PaymentBalanceActivityOtherAdjustmentDetailBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/PaymentBalanceActivityOtherAdjustmentDetailBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\PaymentBalanceActivityOtherDetailBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/PaymentBalanceActivityOtherDetailBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\PaymentBalanceActivityRefundDetailBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/PaymentBalanceActivityRefundDetailBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\PaymentBalanceActivityReleaseAdjustmentDetailBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/PaymentBalanceActivityReleaseAdjustmentDetailBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\PaymentBalanceActivityReserveHoldDetailBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/PaymentBalanceActivityReserveHoldDetailBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\PaymentBalanceActivityReserveReleaseDetailBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/PaymentBalanceActivityReserveReleaseDetailBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\PaymentBalanceActivitySquareCapitalPaymentDetailBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/PaymentBalanceActivitySquareCapitalPaymentDetailBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\PaymentBalanceActivitySquareCapitalReversedPaymentDetailBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/PaymentBalanceActivitySquareCapitalReversedPaymentDetailBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\PaymentBalanceActivitySquarePayrollTransferDetailBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/PaymentBalanceActivitySquarePayrollTransferDetailBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\PaymentBalanceActivitySquarePayrollTransferReversedDetailBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/PaymentBalanceActivitySquarePayrollTransferReversedDetailBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\PaymentBalanceActivityTaxOnFeeDetailBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/PaymentBalanceActivityTaxOnFeeDetailBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\PaymentBalanceActivityThirdPartyFeeDetailBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/PaymentBalanceActivityThirdPartyFeeDetailBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\PaymentBalanceActivityThirdPartyFeeRefundDetailBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/PaymentBalanceActivityThirdPartyFeeRefundDetailBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\PaymentBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/PaymentBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\PaymentLinkBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/PaymentLinkBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\PaymentLinkRelatedResourcesBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/PaymentLinkRelatedResourcesBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\PaymentOptionsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/PaymentOptionsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\PaymentRefundBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/PaymentRefundBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\PayoutBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/PayoutBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\PayoutEntryBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/PayoutEntryBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\PayoutFeeBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/PayoutFeeBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\PhaseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/PhaseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\PhaseInputBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/PhaseInputBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\PrePopulatedDataBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/PrePopulatedDataBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ProcessingFeeBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ProcessingFeeBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\PublishInvoiceRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/PublishInvoiceRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\PublishInvoiceResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/PublishInvoiceResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\QrCodeOptionsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/QrCodeOptionsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\QuantityRatioBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/QuantityRatioBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\QuickPayBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/QuickPayBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RangeBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RangeBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ReceiptOptionsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ReceiptOptionsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RedeemLoyaltyRewardRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RedeemLoyaltyRewardRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RedeemLoyaltyRewardResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RedeemLoyaltyRewardResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RefundBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RefundBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RefundPaymentRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RefundPaymentRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RefundPaymentResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RefundPaymentResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RegisterDomainRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RegisterDomainRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RegisterDomainResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RegisterDomainResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RemoveGroupFromCustomerResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RemoveGroupFromCustomerResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ResumeSubscriptionRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ResumeSubscriptionRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ResumeSubscriptionResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ResumeSubscriptionResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RetrieveBookingCustomAttributeDefinitionRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RetrieveBookingCustomAttributeDefinitionRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RetrieveBookingCustomAttributeDefinitionResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RetrieveBookingCustomAttributeDefinitionResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RetrieveBookingCustomAttributeRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RetrieveBookingCustomAttributeRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RetrieveBookingCustomAttributeResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RetrieveBookingCustomAttributeResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RetrieveBookingResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RetrieveBookingResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RetrieveBusinessBookingProfileResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RetrieveBusinessBookingProfileResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RetrieveCardResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RetrieveCardResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RetrieveCashDrawerShiftRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RetrieveCashDrawerShiftRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RetrieveCashDrawerShiftResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RetrieveCashDrawerShiftResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RetrieveCatalogObjectRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RetrieveCatalogObjectRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RetrieveCatalogObjectResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RetrieveCatalogObjectResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RetrieveCustomerCustomAttributeDefinitionRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RetrieveCustomerCustomAttributeDefinitionRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RetrieveCustomerCustomAttributeDefinitionResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RetrieveCustomerCustomAttributeDefinitionResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RetrieveCustomerCustomAttributeRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RetrieveCustomerCustomAttributeRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RetrieveCustomerCustomAttributeResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RetrieveCustomerCustomAttributeResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RetrieveCustomerGroupResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RetrieveCustomerGroupResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RetrieveCustomerResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RetrieveCustomerResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RetrieveCustomerSegmentResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RetrieveCustomerSegmentResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RetrieveDisputeEvidenceResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RetrieveDisputeEvidenceResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RetrieveDisputeResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RetrieveDisputeResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RetrieveEmployeeResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RetrieveEmployeeResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RetrieveGiftCardFromGANRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RetrieveGiftCardFromGANRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RetrieveGiftCardFromGANResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RetrieveGiftCardFromGANResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RetrieveGiftCardFromNonceRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RetrieveGiftCardFromNonceRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RetrieveGiftCardFromNonceResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RetrieveGiftCardFromNonceResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RetrieveGiftCardResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RetrieveGiftCardResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RetrieveInventoryAdjustmentResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RetrieveInventoryAdjustmentResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RetrieveInventoryChangesRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RetrieveInventoryChangesRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RetrieveInventoryChangesResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RetrieveInventoryChangesResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RetrieveInventoryCountRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RetrieveInventoryCountRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RetrieveInventoryCountResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RetrieveInventoryCountResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RetrieveInventoryPhysicalCountResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RetrieveInventoryPhysicalCountResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RetrieveInventoryTransferResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RetrieveInventoryTransferResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RetrieveJobResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RetrieveJobResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RetrieveLocationBookingProfileResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RetrieveLocationBookingProfileResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RetrieveLocationCustomAttributeDefinitionRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RetrieveLocationCustomAttributeDefinitionRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RetrieveLocationCustomAttributeDefinitionResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RetrieveLocationCustomAttributeDefinitionResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RetrieveLocationCustomAttributeRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RetrieveLocationCustomAttributeRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RetrieveLocationCustomAttributeResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RetrieveLocationCustomAttributeResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RetrieveLocationResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RetrieveLocationResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RetrieveLocationSettingsResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RetrieveLocationSettingsResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RetrieveLoyaltyAccountResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RetrieveLoyaltyAccountResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RetrieveLoyaltyProgramResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RetrieveLoyaltyProgramResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RetrieveLoyaltyPromotionResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RetrieveLoyaltyPromotionResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RetrieveLoyaltyRewardResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RetrieveLoyaltyRewardResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RetrieveMerchantCustomAttributeDefinitionRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RetrieveMerchantCustomAttributeDefinitionRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RetrieveMerchantCustomAttributeDefinitionResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RetrieveMerchantCustomAttributeDefinitionResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RetrieveMerchantCustomAttributeRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RetrieveMerchantCustomAttributeRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RetrieveMerchantCustomAttributeResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RetrieveMerchantCustomAttributeResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RetrieveMerchantResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RetrieveMerchantResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RetrieveMerchantSettingsResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RetrieveMerchantSettingsResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RetrieveOrderCustomAttributeDefinitionRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RetrieveOrderCustomAttributeDefinitionRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RetrieveOrderCustomAttributeDefinitionResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RetrieveOrderCustomAttributeDefinitionResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RetrieveOrderCustomAttributeRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RetrieveOrderCustomAttributeRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RetrieveOrderCustomAttributeResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RetrieveOrderCustomAttributeResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RetrieveOrderResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RetrieveOrderResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RetrievePaymentLinkResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RetrievePaymentLinkResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RetrieveSnippetResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RetrieveSnippetResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RetrieveSubscriptionRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RetrieveSubscriptionRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RetrieveSubscriptionResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RetrieveSubscriptionResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RetrieveTeamMemberBookingProfileResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RetrieveTeamMemberBookingProfileResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RetrieveTeamMemberResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RetrieveTeamMemberResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RetrieveTokenStatusResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RetrieveTokenStatusResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RetrieveTransactionResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RetrieveTransactionResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RetrieveVendorResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RetrieveVendorResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RetrieveWageSettingResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RetrieveWageSettingResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RetrieveWebhookSubscriptionResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RetrieveWebhookSubscriptionResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RevokeTokenRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RevokeTokenRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RevokeTokenResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RevokeTokenResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\RiskEvaluationBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/RiskEvaluationBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SaveCardOptionsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SaveCardOptionsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SearchAvailabilityFilterBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SearchAvailabilityFilterBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SearchAvailabilityQueryBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SearchAvailabilityQueryBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SearchAvailabilityRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SearchAvailabilityRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SearchAvailabilityResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SearchAvailabilityResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SearchCatalogItemsRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SearchCatalogItemsRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SearchCatalogItemsResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SearchCatalogItemsResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SearchCatalogObjectsRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SearchCatalogObjectsRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SearchCatalogObjectsResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SearchCatalogObjectsResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SearchCustomersRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SearchCustomersRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SearchCustomersResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SearchCustomersResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SearchEventsFilterBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SearchEventsFilterBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SearchEventsQueryBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SearchEventsQueryBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SearchEventsRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SearchEventsRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SearchEventsResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SearchEventsResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SearchEventsSortBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SearchEventsSortBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SearchInvoicesRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SearchInvoicesRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SearchInvoicesResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SearchInvoicesResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SearchLoyaltyAccountsRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SearchLoyaltyAccountsRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SearchLoyaltyAccountsRequestLoyaltyAccountQueryBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SearchLoyaltyAccountsRequestLoyaltyAccountQueryBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SearchLoyaltyAccountsResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SearchLoyaltyAccountsResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SearchLoyaltyEventsRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SearchLoyaltyEventsRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SearchLoyaltyEventsResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SearchLoyaltyEventsResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SearchLoyaltyRewardsRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SearchLoyaltyRewardsRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SearchLoyaltyRewardsRequestLoyaltyRewardQueryBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SearchLoyaltyRewardsRequestLoyaltyRewardQueryBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SearchLoyaltyRewardsResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SearchLoyaltyRewardsResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SearchOrdersCustomerFilterBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SearchOrdersCustomerFilterBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SearchOrdersDateTimeFilterBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SearchOrdersDateTimeFilterBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SearchOrdersFilterBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SearchOrdersFilterBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SearchOrdersFulfillmentFilterBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SearchOrdersFulfillmentFilterBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SearchOrdersQueryBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SearchOrdersQueryBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SearchOrdersRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SearchOrdersRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SearchOrdersResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SearchOrdersResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SearchOrdersSortBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SearchOrdersSortBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SearchOrdersSourceFilterBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SearchOrdersSourceFilterBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SearchOrdersStateFilterBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SearchOrdersStateFilterBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SearchShiftsRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SearchShiftsRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SearchShiftsResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SearchShiftsResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SearchSubscriptionsFilterBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SearchSubscriptionsFilterBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SearchSubscriptionsQueryBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SearchSubscriptionsQueryBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SearchSubscriptionsRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SearchSubscriptionsRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SearchSubscriptionsResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SearchSubscriptionsResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SearchTeamMembersFilterBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SearchTeamMembersFilterBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SearchTeamMembersQueryBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SearchTeamMembersQueryBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SearchTeamMembersRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SearchTeamMembersRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SearchTeamMembersResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SearchTeamMembersResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SearchTerminalActionsRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SearchTerminalActionsRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SearchTerminalActionsResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SearchTerminalActionsResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SearchTerminalCheckoutsRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SearchTerminalCheckoutsRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SearchTerminalCheckoutsResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SearchTerminalCheckoutsResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SearchTerminalRefundsRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SearchTerminalRefundsRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SearchTerminalRefundsResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SearchTerminalRefundsResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SearchVendorsRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SearchVendorsRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SearchVendorsRequestFilterBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SearchVendorsRequestFilterBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SearchVendorsRequestSortBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SearchVendorsRequestSortBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SearchVendorsResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SearchVendorsResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SegmentFilterBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SegmentFilterBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SelectOptionBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SelectOptionBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SelectOptionsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SelectOptionsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ShiftBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ShiftBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ShiftFilterBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ShiftFilterBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ShiftQueryBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ShiftQueryBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ShiftSortBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ShiftSortBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ShiftWageBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ShiftWageBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ShiftWorkdayBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ShiftWorkdayBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\ShippingFeeBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/ShippingFeeBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SignatureImageBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SignatureImageBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SignatureOptionsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SignatureOptionsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SiteBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SiteBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SnippetBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SnippetBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SnippetResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SnippetResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SourceApplicationBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SourceApplicationBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SquareAccountDetailsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SquareAccountDetailsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\StandardUnitDescriptionBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/StandardUnitDescriptionBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\StandardUnitDescriptionGroupBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/StandardUnitDescriptionGroupBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SubmitEvidenceResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SubmitEvidenceResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SubscriptionActionBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SubscriptionActionBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SubscriptionBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SubscriptionBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SubscriptionEventBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SubscriptionEventBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SubscriptionEventInfoBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SubscriptionEventInfoBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SubscriptionPhaseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SubscriptionPhaseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SubscriptionPricingBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SubscriptionPricingBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SubscriptionSourceBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SubscriptionSourceBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SubscriptionTestResultBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SubscriptionTestResultBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SwapPlanRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SwapPlanRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\SwapPlanResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/SwapPlanResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\TaxIdsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/TaxIdsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\TeamMemberAssignedLocationsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/TeamMemberAssignedLocationsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\TeamMemberBookingProfileBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/TeamMemberBookingProfileBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\TeamMemberBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/TeamMemberBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\TeamMemberWageBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/TeamMemberWageBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\TenderBankAccountDetailsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/TenderBankAccountDetailsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\TenderBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/TenderBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\TenderBuyNowPayLaterDetailsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/TenderBuyNowPayLaterDetailsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\TenderCardDetailsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/TenderCardDetailsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\TenderCashDetailsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/TenderCashDetailsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\TenderSquareAccountDetailsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/TenderSquareAccountDetailsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\TerminalActionBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/TerminalActionBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\TerminalActionQueryBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/TerminalActionQueryBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\TerminalActionQueryFilterBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/TerminalActionQueryFilterBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\TerminalActionQuerySortBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/TerminalActionQuerySortBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\TerminalCheckoutBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/TerminalCheckoutBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\TerminalCheckoutQueryBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/TerminalCheckoutQueryBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\TerminalCheckoutQueryFilterBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/TerminalCheckoutQueryFilterBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\TerminalCheckoutQuerySortBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/TerminalCheckoutQuerySortBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\TerminalRefundBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/TerminalRefundBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\TerminalRefundQueryBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/TerminalRefundQueryBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\TerminalRefundQueryFilterBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/TerminalRefundQueryFilterBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\TerminalRefundQuerySortBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/TerminalRefundQuerySortBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\TestWebhookSubscriptionRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/TestWebhookSubscriptionRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\TestWebhookSubscriptionResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/TestWebhookSubscriptionResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\TimeRangeBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/TimeRangeBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\TipSettingsBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/TipSettingsBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\TransactionBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/TransactionBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UnlinkCustomerFromGiftCardRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UnlinkCustomerFromGiftCardRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UnlinkCustomerFromGiftCardResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UnlinkCustomerFromGiftCardResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UpdateBookingCustomAttributeDefinitionRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UpdateBookingCustomAttributeDefinitionRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UpdateBookingCustomAttributeDefinitionResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UpdateBookingCustomAttributeDefinitionResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UpdateBookingRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UpdateBookingRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UpdateBookingResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UpdateBookingResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UpdateBreakTypeRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UpdateBreakTypeRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UpdateBreakTypeResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UpdateBreakTypeResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UpdateCatalogImageRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UpdateCatalogImageRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UpdateCatalogImageResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UpdateCatalogImageResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UpdateCustomerCustomAttributeDefinitionRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UpdateCustomerCustomAttributeDefinitionRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UpdateCustomerCustomAttributeDefinitionResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UpdateCustomerCustomAttributeDefinitionResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UpdateCustomerGroupRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UpdateCustomerGroupRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UpdateCustomerGroupResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UpdateCustomerGroupResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UpdateCustomerRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UpdateCustomerRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UpdateCustomerResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UpdateCustomerResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UpdateInvoiceRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UpdateInvoiceRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UpdateInvoiceResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UpdateInvoiceResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UpdateItemModifierListsRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UpdateItemModifierListsRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UpdateItemModifierListsResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UpdateItemModifierListsResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UpdateItemTaxesRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UpdateItemTaxesRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UpdateItemTaxesResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UpdateItemTaxesResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UpdateJobRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UpdateJobRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UpdateJobResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UpdateJobResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UpdateLocationCustomAttributeDefinitionRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UpdateLocationCustomAttributeDefinitionRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UpdateLocationCustomAttributeDefinitionResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UpdateLocationCustomAttributeDefinitionResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UpdateLocationRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UpdateLocationRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UpdateLocationResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UpdateLocationResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UpdateLocationSettingsRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UpdateLocationSettingsRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UpdateLocationSettingsResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UpdateLocationSettingsResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UpdateMerchantCustomAttributeDefinitionRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UpdateMerchantCustomAttributeDefinitionRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UpdateMerchantCustomAttributeDefinitionResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UpdateMerchantCustomAttributeDefinitionResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UpdateMerchantSettingsRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UpdateMerchantSettingsRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UpdateMerchantSettingsResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UpdateMerchantSettingsResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UpdateOrderCustomAttributeDefinitionRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UpdateOrderCustomAttributeDefinitionRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UpdateOrderCustomAttributeDefinitionResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UpdateOrderCustomAttributeDefinitionResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UpdateOrderRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UpdateOrderRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UpdateOrderResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UpdateOrderResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UpdatePaymentLinkRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UpdatePaymentLinkRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UpdatePaymentLinkResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UpdatePaymentLinkResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UpdatePaymentRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UpdatePaymentRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UpdatePaymentResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UpdatePaymentResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UpdateShiftRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UpdateShiftRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UpdateShiftResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UpdateShiftResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UpdateSubscriptionRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UpdateSubscriptionRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UpdateSubscriptionResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UpdateSubscriptionResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UpdateTeamMemberRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UpdateTeamMemberRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UpdateTeamMemberResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UpdateTeamMemberResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UpdateVendorRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UpdateVendorRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UpdateVendorResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UpdateVendorResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UpdateWageSettingRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UpdateWageSettingRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UpdateWageSettingResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UpdateWageSettingResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UpdateWebhookSubscriptionRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UpdateWebhookSubscriptionRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UpdateWebhookSubscriptionResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UpdateWebhookSubscriptionResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UpdateWebhookSubscriptionSignatureKeyRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UpdateWebhookSubscriptionSignatureKeyRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UpdateWebhookSubscriptionSignatureKeyResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UpdateWebhookSubscriptionSignatureKeyResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UpdateWorkweekConfigRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UpdateWorkweekConfigRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UpdateWorkweekConfigResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UpdateWorkweekConfigResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UpsertBookingCustomAttributeRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UpsertBookingCustomAttributeRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UpsertBookingCustomAttributeResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UpsertBookingCustomAttributeResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UpsertCatalogObjectRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UpsertCatalogObjectRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UpsertCatalogObjectResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UpsertCatalogObjectResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UpsertCustomerCustomAttributeRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UpsertCustomerCustomAttributeRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UpsertCustomerCustomAttributeResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UpsertCustomerCustomAttributeResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UpsertLocationCustomAttributeRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UpsertLocationCustomAttributeRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UpsertLocationCustomAttributeResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UpsertLocationCustomAttributeResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UpsertMerchantCustomAttributeRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UpsertMerchantCustomAttributeRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UpsertMerchantCustomAttributeResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UpsertMerchantCustomAttributeResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UpsertOrderCustomAttributeRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UpsertOrderCustomAttributeRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UpsertOrderCustomAttributeResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UpsertOrderCustomAttributeResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UpsertSnippetRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UpsertSnippetRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\UpsertSnippetResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/UpsertSnippetResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\V1DeviceBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/V1DeviceBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\V1ListOrdersRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/V1ListOrdersRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\V1ListOrdersResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/V1ListOrdersResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\V1MoneyBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/V1MoneyBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\V1OrderBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/V1OrderBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\V1OrderHistoryEntryBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/V1OrderHistoryEntryBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\V1PhoneNumberBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/V1PhoneNumberBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\V1TenderBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/V1TenderBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\V1UpdateOrderRequestBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/V1UpdateOrderRequestBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\VendorBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/VendorBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\VendorContactBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/VendorContactBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\VoidTransactionResponseBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/VoidTransactionResponseBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\WageSettingBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/WageSettingBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\WebhookSubscriptionBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/WebhookSubscriptionBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\Builders\\WorkweekConfigBuilder' => $baseDir . '/vendor_prefixed/square/square/src/Models/Builders/WorkweekConfigBuilder.php',
    'WPForms\\Vendor\\Square\\Models\\BulkCreateCustomerData' => $baseDir . '/vendor_prefixed/square/square/src/Models/BulkCreateCustomerData.php',
    'WPForms\\Vendor\\Square\\Models\\BulkCreateCustomersRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/BulkCreateCustomersRequest.php',
    'WPForms\\Vendor\\Square\\Models\\BulkCreateCustomersResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/BulkCreateCustomersResponse.php',
    'WPForms\\Vendor\\Square\\Models\\BulkCreateTeamMembersRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/BulkCreateTeamMembersRequest.php',
    'WPForms\\Vendor\\Square\\Models\\BulkCreateTeamMembersResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/BulkCreateTeamMembersResponse.php',
    'WPForms\\Vendor\\Square\\Models\\BulkCreateVendorsRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/BulkCreateVendorsRequest.php',
    'WPForms\\Vendor\\Square\\Models\\BulkCreateVendorsResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/BulkCreateVendorsResponse.php',
    'WPForms\\Vendor\\Square\\Models\\BulkDeleteBookingCustomAttributesRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/BulkDeleteBookingCustomAttributesRequest.php',
    'WPForms\\Vendor\\Square\\Models\\BulkDeleteBookingCustomAttributesResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/BulkDeleteBookingCustomAttributesResponse.php',
    'WPForms\\Vendor\\Square\\Models\\BulkDeleteCustomersRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/BulkDeleteCustomersRequest.php',
    'WPForms\\Vendor\\Square\\Models\\BulkDeleteCustomersResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/BulkDeleteCustomersResponse.php',
    'WPForms\\Vendor\\Square\\Models\\BulkDeleteLocationCustomAttributesRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/BulkDeleteLocationCustomAttributesRequest.php',
    'WPForms\\Vendor\\Square\\Models\\BulkDeleteLocationCustomAttributesRequestLocationCustomAttributeDeleteRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/BulkDeleteLocationCustomAttributesRequestLocationCustomAttributeDeleteRequest.php',
    'WPForms\\Vendor\\Square\\Models\\BulkDeleteLocationCustomAttributesResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/BulkDeleteLocationCustomAttributesResponse.php',
    'WPForms\\Vendor\\Square\\Models\\BulkDeleteLocationCustomAttributesResponseLocationCustomAttributeDeleteResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/BulkDeleteLocationCustomAttributesResponseLocationCustomAttributeDeleteResponse.php',
    'WPForms\\Vendor\\Square\\Models\\BulkDeleteMerchantCustomAttributesRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/BulkDeleteMerchantCustomAttributesRequest.php',
    'WPForms\\Vendor\\Square\\Models\\BulkDeleteMerchantCustomAttributesRequestMerchantCustomAttributeDeleteRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/BulkDeleteMerchantCustomAttributesRequestMerchantCustomAttributeDeleteRequest.php',
    'WPForms\\Vendor\\Square\\Models\\BulkDeleteMerchantCustomAttributesResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/BulkDeleteMerchantCustomAttributesResponse.php',
    'WPForms\\Vendor\\Square\\Models\\BulkDeleteMerchantCustomAttributesResponseMerchantCustomAttributeDeleteResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/BulkDeleteMerchantCustomAttributesResponseMerchantCustomAttributeDeleteResponse.php',
    'WPForms\\Vendor\\Square\\Models\\BulkDeleteOrderCustomAttributesRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/BulkDeleteOrderCustomAttributesRequest.php',
    'WPForms\\Vendor\\Square\\Models\\BulkDeleteOrderCustomAttributesRequestDeleteCustomAttribute' => $baseDir . '/vendor_prefixed/square/square/src/Models/BulkDeleteOrderCustomAttributesRequestDeleteCustomAttribute.php',
    'WPForms\\Vendor\\Square\\Models\\BulkDeleteOrderCustomAttributesResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/BulkDeleteOrderCustomAttributesResponse.php',
    'WPForms\\Vendor\\Square\\Models\\BulkRetrieveBookingsRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/BulkRetrieveBookingsRequest.php',
    'WPForms\\Vendor\\Square\\Models\\BulkRetrieveBookingsResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/BulkRetrieveBookingsResponse.php',
    'WPForms\\Vendor\\Square\\Models\\BulkRetrieveCustomersRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/BulkRetrieveCustomersRequest.php',
    'WPForms\\Vendor\\Square\\Models\\BulkRetrieveCustomersResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/BulkRetrieveCustomersResponse.php',
    'WPForms\\Vendor\\Square\\Models\\BulkRetrieveTeamMemberBookingProfilesRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/BulkRetrieveTeamMemberBookingProfilesRequest.php',
    'WPForms\\Vendor\\Square\\Models\\BulkRetrieveTeamMemberBookingProfilesResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/BulkRetrieveTeamMemberBookingProfilesResponse.php',
    'WPForms\\Vendor\\Square\\Models\\BulkRetrieveVendorsRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/BulkRetrieveVendorsRequest.php',
    'WPForms\\Vendor\\Square\\Models\\BulkRetrieveVendorsResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/BulkRetrieveVendorsResponse.php',
    'WPForms\\Vendor\\Square\\Models\\BulkSwapPlanRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/BulkSwapPlanRequest.php',
    'WPForms\\Vendor\\Square\\Models\\BulkSwapPlanResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/BulkSwapPlanResponse.php',
    'WPForms\\Vendor\\Square\\Models\\BulkUpdateCustomerData' => $baseDir . '/vendor_prefixed/square/square/src/Models/BulkUpdateCustomerData.php',
    'WPForms\\Vendor\\Square\\Models\\BulkUpdateCustomersRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/BulkUpdateCustomersRequest.php',
    'WPForms\\Vendor\\Square\\Models\\BulkUpdateCustomersResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/BulkUpdateCustomersResponse.php',
    'WPForms\\Vendor\\Square\\Models\\BulkUpdateTeamMembersRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/BulkUpdateTeamMembersRequest.php',
    'WPForms\\Vendor\\Square\\Models\\BulkUpdateTeamMembersResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/BulkUpdateTeamMembersResponse.php',
    'WPForms\\Vendor\\Square\\Models\\BulkUpdateVendorsRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/BulkUpdateVendorsRequest.php',
    'WPForms\\Vendor\\Square\\Models\\BulkUpdateVendorsResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/BulkUpdateVendorsResponse.php',
    'WPForms\\Vendor\\Square\\Models\\BulkUpsertBookingCustomAttributesRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/BulkUpsertBookingCustomAttributesRequest.php',
    'WPForms\\Vendor\\Square\\Models\\BulkUpsertBookingCustomAttributesResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/BulkUpsertBookingCustomAttributesResponse.php',
    'WPForms\\Vendor\\Square\\Models\\BulkUpsertCustomerCustomAttributesRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/BulkUpsertCustomerCustomAttributesRequest.php',
    'WPForms\\Vendor\\Square\\Models\\BulkUpsertCustomerCustomAttributesRequestCustomerCustomAttributeUpsertRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/BulkUpsertCustomerCustomAttributesRequestCustomerCustomAttributeUpsertRequest.php',
    'WPForms\\Vendor\\Square\\Models\\BulkUpsertCustomerCustomAttributesResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/BulkUpsertCustomerCustomAttributesResponse.php',
    'WPForms\\Vendor\\Square\\Models\\BulkUpsertCustomerCustomAttributesResponseCustomerCustomAttributeUpsertResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/BulkUpsertCustomerCustomAttributesResponseCustomerCustomAttributeUpsertResponse.php',
    'WPForms\\Vendor\\Square\\Models\\BulkUpsertLocationCustomAttributesRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/BulkUpsertLocationCustomAttributesRequest.php',
    'WPForms\\Vendor\\Square\\Models\\BulkUpsertLocationCustomAttributesRequestLocationCustomAttributeUpsertRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/BulkUpsertLocationCustomAttributesRequestLocationCustomAttributeUpsertRequest.php',
    'WPForms\\Vendor\\Square\\Models\\BulkUpsertLocationCustomAttributesResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/BulkUpsertLocationCustomAttributesResponse.php',
    'WPForms\\Vendor\\Square\\Models\\BulkUpsertLocationCustomAttributesResponseLocationCustomAttributeUpsertResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/BulkUpsertLocationCustomAttributesResponseLocationCustomAttributeUpsertResponse.php',
    'WPForms\\Vendor\\Square\\Models\\BulkUpsertMerchantCustomAttributesRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/BulkUpsertMerchantCustomAttributesRequest.php',
    'WPForms\\Vendor\\Square\\Models\\BulkUpsertMerchantCustomAttributesRequestMerchantCustomAttributeUpsertRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/BulkUpsertMerchantCustomAttributesRequestMerchantCustomAttributeUpsertRequest.php',
    'WPForms\\Vendor\\Square\\Models\\BulkUpsertMerchantCustomAttributesResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/BulkUpsertMerchantCustomAttributesResponse.php',
    'WPForms\\Vendor\\Square\\Models\\BulkUpsertMerchantCustomAttributesResponseMerchantCustomAttributeUpsertResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/BulkUpsertMerchantCustomAttributesResponseMerchantCustomAttributeUpsertResponse.php',
    'WPForms\\Vendor\\Square\\Models\\BulkUpsertOrderCustomAttributesRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/BulkUpsertOrderCustomAttributesRequest.php',
    'WPForms\\Vendor\\Square\\Models\\BulkUpsertOrderCustomAttributesRequestUpsertCustomAttribute' => $baseDir . '/vendor_prefixed/square/square/src/Models/BulkUpsertOrderCustomAttributesRequestUpsertCustomAttribute.php',
    'WPForms\\Vendor\\Square\\Models\\BulkUpsertOrderCustomAttributesResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/BulkUpsertOrderCustomAttributesResponse.php',
    'WPForms\\Vendor\\Square\\Models\\BusinessAppointmentSettings' => $baseDir . '/vendor_prefixed/square/square/src/Models/BusinessAppointmentSettings.php',
    'WPForms\\Vendor\\Square\\Models\\BusinessAppointmentSettingsAlignmentTime' => $baseDir . '/vendor_prefixed/square/square/src/Models/BusinessAppointmentSettingsAlignmentTime.php',
    'WPForms\\Vendor\\Square\\Models\\BusinessAppointmentSettingsBookingLocationType' => $baseDir . '/vendor_prefixed/square/square/src/Models/BusinessAppointmentSettingsBookingLocationType.php',
    'WPForms\\Vendor\\Square\\Models\\BusinessAppointmentSettingsCancellationPolicy' => $baseDir . '/vendor_prefixed/square/square/src/Models/BusinessAppointmentSettingsCancellationPolicy.php',
    'WPForms\\Vendor\\Square\\Models\\BusinessAppointmentSettingsMaxAppointmentsPerDayLimitType' => $baseDir . '/vendor_prefixed/square/square/src/Models/BusinessAppointmentSettingsMaxAppointmentsPerDayLimitType.php',
    'WPForms\\Vendor\\Square\\Models\\BusinessBookingProfile' => $baseDir . '/vendor_prefixed/square/square/src/Models/BusinessBookingProfile.php',
    'WPForms\\Vendor\\Square\\Models\\BusinessBookingProfileBookingPolicy' => $baseDir . '/vendor_prefixed/square/square/src/Models/BusinessBookingProfileBookingPolicy.php',
    'WPForms\\Vendor\\Square\\Models\\BusinessBookingProfileCustomerTimezoneChoice' => $baseDir . '/vendor_prefixed/square/square/src/Models/BusinessBookingProfileCustomerTimezoneChoice.php',
    'WPForms\\Vendor\\Square\\Models\\BusinessHours' => $baseDir . '/vendor_prefixed/square/square/src/Models/BusinessHours.php',
    'WPForms\\Vendor\\Square\\Models\\BusinessHoursPeriod' => $baseDir . '/vendor_prefixed/square/square/src/Models/BusinessHoursPeriod.php',
    'WPForms\\Vendor\\Square\\Models\\BuyNowPayLaterDetails' => $baseDir . '/vendor_prefixed/square/square/src/Models/BuyNowPayLaterDetails.php',
    'WPForms\\Vendor\\Square\\Models\\CalculateLoyaltyPointsRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/CalculateLoyaltyPointsRequest.php',
    'WPForms\\Vendor\\Square\\Models\\CalculateLoyaltyPointsResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/CalculateLoyaltyPointsResponse.php',
    'WPForms\\Vendor\\Square\\Models\\CalculateOrderRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/CalculateOrderRequest.php',
    'WPForms\\Vendor\\Square\\Models\\CalculateOrderResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/CalculateOrderResponse.php',
    'WPForms\\Vendor\\Square\\Models\\CancelBookingRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/CancelBookingRequest.php',
    'WPForms\\Vendor\\Square\\Models\\CancelBookingResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/CancelBookingResponse.php',
    'WPForms\\Vendor\\Square\\Models\\CancelInvoiceRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/CancelInvoiceRequest.php',
    'WPForms\\Vendor\\Square\\Models\\CancelInvoiceResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/CancelInvoiceResponse.php',
    'WPForms\\Vendor\\Square\\Models\\CancelLoyaltyPromotionResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/CancelLoyaltyPromotionResponse.php',
    'WPForms\\Vendor\\Square\\Models\\CancelPaymentByIdempotencyKeyRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/CancelPaymentByIdempotencyKeyRequest.php',
    'WPForms\\Vendor\\Square\\Models\\CancelPaymentByIdempotencyKeyResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/CancelPaymentByIdempotencyKeyResponse.php',
    'WPForms\\Vendor\\Square\\Models\\CancelPaymentResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/CancelPaymentResponse.php',
    'WPForms\\Vendor\\Square\\Models\\CancelSubscriptionResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/CancelSubscriptionResponse.php',
    'WPForms\\Vendor\\Square\\Models\\CancelTerminalActionResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/CancelTerminalActionResponse.php',
    'WPForms\\Vendor\\Square\\Models\\CancelTerminalCheckoutResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/CancelTerminalCheckoutResponse.php',
    'WPForms\\Vendor\\Square\\Models\\CancelTerminalRefundResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/CancelTerminalRefundResponse.php',
    'WPForms\\Vendor\\Square\\Models\\CaptureTransactionResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/CaptureTransactionResponse.php',
    'WPForms\\Vendor\\Square\\Models\\Card' => $baseDir . '/vendor_prefixed/square/square/src/Models/Card.php',
    'WPForms\\Vendor\\Square\\Models\\CardBrand' => $baseDir . '/vendor_prefixed/square/square/src/Models/CardBrand.php',
    'WPForms\\Vendor\\Square\\Models\\CardCoBrand' => $baseDir . '/vendor_prefixed/square/square/src/Models/CardCoBrand.php',
    'WPForms\\Vendor\\Square\\Models\\CardPaymentDetails' => $baseDir . '/vendor_prefixed/square/square/src/Models/CardPaymentDetails.php',
    'WPForms\\Vendor\\Square\\Models\\CardPaymentTimeline' => $baseDir . '/vendor_prefixed/square/square/src/Models/CardPaymentTimeline.php',
    'WPForms\\Vendor\\Square\\Models\\CardPrepaidType' => $baseDir . '/vendor_prefixed/square/square/src/Models/CardPrepaidType.php',
    'WPForms\\Vendor\\Square\\Models\\CardType' => $baseDir . '/vendor_prefixed/square/square/src/Models/CardType.php',
    'WPForms\\Vendor\\Square\\Models\\CashAppDetails' => $baseDir . '/vendor_prefixed/square/square/src/Models/CashAppDetails.php',
    'WPForms\\Vendor\\Square\\Models\\CashDrawerDevice' => $baseDir . '/vendor_prefixed/square/square/src/Models/CashDrawerDevice.php',
    'WPForms\\Vendor\\Square\\Models\\CashDrawerEventType' => $baseDir . '/vendor_prefixed/square/square/src/Models/CashDrawerEventType.php',
    'WPForms\\Vendor\\Square\\Models\\CashDrawerShift' => $baseDir . '/vendor_prefixed/square/square/src/Models/CashDrawerShift.php',
    'WPForms\\Vendor\\Square\\Models\\CashDrawerShiftEvent' => $baseDir . '/vendor_prefixed/square/square/src/Models/CashDrawerShiftEvent.php',
    'WPForms\\Vendor\\Square\\Models\\CashDrawerShiftState' => $baseDir . '/vendor_prefixed/square/square/src/Models/CashDrawerShiftState.php',
    'WPForms\\Vendor\\Square\\Models\\CashDrawerShiftSummary' => $baseDir . '/vendor_prefixed/square/square/src/Models/CashDrawerShiftSummary.php',
    'WPForms\\Vendor\\Square\\Models\\CashPaymentDetails' => $baseDir . '/vendor_prefixed/square/square/src/Models/CashPaymentDetails.php',
    'WPForms\\Vendor\\Square\\Models\\CatalogAvailabilityPeriod' => $baseDir . '/vendor_prefixed/square/square/src/Models/CatalogAvailabilityPeriod.php',
    'WPForms\\Vendor\\Square\\Models\\CatalogCategory' => $baseDir . '/vendor_prefixed/square/square/src/Models/CatalogCategory.php',
    'WPForms\\Vendor\\Square\\Models\\CatalogCategoryType' => $baseDir . '/vendor_prefixed/square/square/src/Models/CatalogCategoryType.php',
    'WPForms\\Vendor\\Square\\Models\\CatalogCustomAttributeDefinition' => $baseDir . '/vendor_prefixed/square/square/src/Models/CatalogCustomAttributeDefinition.php',
    'WPForms\\Vendor\\Square\\Models\\CatalogCustomAttributeDefinitionAppVisibility' => $baseDir . '/vendor_prefixed/square/square/src/Models/CatalogCustomAttributeDefinitionAppVisibility.php',
    'WPForms\\Vendor\\Square\\Models\\CatalogCustomAttributeDefinitionNumberConfig' => $baseDir . '/vendor_prefixed/square/square/src/Models/CatalogCustomAttributeDefinitionNumberConfig.php',
    'WPForms\\Vendor\\Square\\Models\\CatalogCustomAttributeDefinitionSelectionConfig' => $baseDir . '/vendor_prefixed/square/square/src/Models/CatalogCustomAttributeDefinitionSelectionConfig.php',
    'WPForms\\Vendor\\Square\\Models\\CatalogCustomAttributeDefinitionSelectionConfigCustomAttributeSelection' => $baseDir . '/vendor_prefixed/square/square/src/Models/CatalogCustomAttributeDefinitionSelectionConfigCustomAttributeSelection.php',
    'WPForms\\Vendor\\Square\\Models\\CatalogCustomAttributeDefinitionSellerVisibility' => $baseDir . '/vendor_prefixed/square/square/src/Models/CatalogCustomAttributeDefinitionSellerVisibility.php',
    'WPForms\\Vendor\\Square\\Models\\CatalogCustomAttributeDefinitionStringConfig' => $baseDir . '/vendor_prefixed/square/square/src/Models/CatalogCustomAttributeDefinitionStringConfig.php',
    'WPForms\\Vendor\\Square\\Models\\CatalogCustomAttributeDefinitionType' => $baseDir . '/vendor_prefixed/square/square/src/Models/CatalogCustomAttributeDefinitionType.php',
    'WPForms\\Vendor\\Square\\Models\\CatalogCustomAttributeValue' => $baseDir . '/vendor_prefixed/square/square/src/Models/CatalogCustomAttributeValue.php',
    'WPForms\\Vendor\\Square\\Models\\CatalogDiscount' => $baseDir . '/vendor_prefixed/square/square/src/Models/CatalogDiscount.php',
    'WPForms\\Vendor\\Square\\Models\\CatalogDiscountModifyTaxBasis' => $baseDir . '/vendor_prefixed/square/square/src/Models/CatalogDiscountModifyTaxBasis.php',
    'WPForms\\Vendor\\Square\\Models\\CatalogDiscountType' => $baseDir . '/vendor_prefixed/square/square/src/Models/CatalogDiscountType.php',
    'WPForms\\Vendor\\Square\\Models\\CatalogEcomSeoData' => $baseDir . '/vendor_prefixed/square/square/src/Models/CatalogEcomSeoData.php',
    'WPForms\\Vendor\\Square\\Models\\CatalogIdMapping' => $baseDir . '/vendor_prefixed/square/square/src/Models/CatalogIdMapping.php',
    'WPForms\\Vendor\\Square\\Models\\CatalogImage' => $baseDir . '/vendor_prefixed/square/square/src/Models/CatalogImage.php',
    'WPForms\\Vendor\\Square\\Models\\CatalogInfoResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/CatalogInfoResponse.php',
    'WPForms\\Vendor\\Square\\Models\\CatalogInfoResponseLimits' => $baseDir . '/vendor_prefixed/square/square/src/Models/CatalogInfoResponseLimits.php',
    'WPForms\\Vendor\\Square\\Models\\CatalogItem' => $baseDir . '/vendor_prefixed/square/square/src/Models/CatalogItem.php',
    'WPForms\\Vendor\\Square\\Models\\CatalogItemFoodAndBeverageDetails' => $baseDir . '/vendor_prefixed/square/square/src/Models/CatalogItemFoodAndBeverageDetails.php',
    'WPForms\\Vendor\\Square\\Models\\CatalogItemFoodAndBeverageDetailsDietaryPreference' => $baseDir . '/vendor_prefixed/square/square/src/Models/CatalogItemFoodAndBeverageDetailsDietaryPreference.php',
    'WPForms\\Vendor\\Square\\Models\\CatalogItemFoodAndBeverageDetailsDietaryPreferenceStandardDietaryPreference' => $baseDir . '/vendor_prefixed/square/square/src/Models/CatalogItemFoodAndBeverageDetailsDietaryPreferenceStandardDietaryPreference.php',
    'WPForms\\Vendor\\Square\\Models\\CatalogItemFoodAndBeverageDetailsDietaryPreferenceType' => $baseDir . '/vendor_prefixed/square/square/src/Models/CatalogItemFoodAndBeverageDetailsDietaryPreferenceType.php',
    'WPForms\\Vendor\\Square\\Models\\CatalogItemFoodAndBeverageDetailsIngredient' => $baseDir . '/vendor_prefixed/square/square/src/Models/CatalogItemFoodAndBeverageDetailsIngredient.php',
    'WPForms\\Vendor\\Square\\Models\\CatalogItemFoodAndBeverageDetailsIngredientStandardIngredient' => $baseDir . '/vendor_prefixed/square/square/src/Models/CatalogItemFoodAndBeverageDetailsIngredientStandardIngredient.php',
    'WPForms\\Vendor\\Square\\Models\\CatalogItemModifierListInfo' => $baseDir . '/vendor_prefixed/square/square/src/Models/CatalogItemModifierListInfo.php',
    'WPForms\\Vendor\\Square\\Models\\CatalogItemOption' => $baseDir . '/vendor_prefixed/square/square/src/Models/CatalogItemOption.php',
    'WPForms\\Vendor\\Square\\Models\\CatalogItemOptionForItem' => $baseDir . '/vendor_prefixed/square/square/src/Models/CatalogItemOptionForItem.php',
    'WPForms\\Vendor\\Square\\Models\\CatalogItemOptionValue' => $baseDir . '/vendor_prefixed/square/square/src/Models/CatalogItemOptionValue.php',
    'WPForms\\Vendor\\Square\\Models\\CatalogItemOptionValueForItemVariation' => $baseDir . '/vendor_prefixed/square/square/src/Models/CatalogItemOptionValueForItemVariation.php',
    'WPForms\\Vendor\\Square\\Models\\CatalogItemProductType' => $baseDir . '/vendor_prefixed/square/square/src/Models/CatalogItemProductType.php',
    'WPForms\\Vendor\\Square\\Models\\CatalogItemVariation' => $baseDir . '/vendor_prefixed/square/square/src/Models/CatalogItemVariation.php',
    'WPForms\\Vendor\\Square\\Models\\CatalogMeasurementUnit' => $baseDir . '/vendor_prefixed/square/square/src/Models/CatalogMeasurementUnit.php',
    'WPForms\\Vendor\\Square\\Models\\CatalogModifier' => $baseDir . '/vendor_prefixed/square/square/src/Models/CatalogModifier.php',
    'WPForms\\Vendor\\Square\\Models\\CatalogModifierList' => $baseDir . '/vendor_prefixed/square/square/src/Models/CatalogModifierList.php',
    'WPForms\\Vendor\\Square\\Models\\CatalogModifierListModifierType' => $baseDir . '/vendor_prefixed/square/square/src/Models/CatalogModifierListModifierType.php',
    'WPForms\\Vendor\\Square\\Models\\CatalogModifierListSelectionType' => $baseDir . '/vendor_prefixed/square/square/src/Models/CatalogModifierListSelectionType.php',
    'WPForms\\Vendor\\Square\\Models\\CatalogModifierOverride' => $baseDir . '/vendor_prefixed/square/square/src/Models/CatalogModifierOverride.php',
    'WPForms\\Vendor\\Square\\Models\\CatalogObject' => $baseDir . '/vendor_prefixed/square/square/src/Models/CatalogObject.php',
    'WPForms\\Vendor\\Square\\Models\\CatalogObjectBatch' => $baseDir . '/vendor_prefixed/square/square/src/Models/CatalogObjectBatch.php',
    'WPForms\\Vendor\\Square\\Models\\CatalogObjectCategory' => $baseDir . '/vendor_prefixed/square/square/src/Models/CatalogObjectCategory.php',
    'WPForms\\Vendor\\Square\\Models\\CatalogObjectReference' => $baseDir . '/vendor_prefixed/square/square/src/Models/CatalogObjectReference.php',
    'WPForms\\Vendor\\Square\\Models\\CatalogObjectType' => $baseDir . '/vendor_prefixed/square/square/src/Models/CatalogObjectType.php',
    'WPForms\\Vendor\\Square\\Models\\CatalogPricingRule' => $baseDir . '/vendor_prefixed/square/square/src/Models/CatalogPricingRule.php',
    'WPForms\\Vendor\\Square\\Models\\CatalogPricingType' => $baseDir . '/vendor_prefixed/square/square/src/Models/CatalogPricingType.php',
    'WPForms\\Vendor\\Square\\Models\\CatalogProductSet' => $baseDir . '/vendor_prefixed/square/square/src/Models/CatalogProductSet.php',
    'WPForms\\Vendor\\Square\\Models\\CatalogQuery' => $baseDir . '/vendor_prefixed/square/square/src/Models/CatalogQuery.php',
    'WPForms\\Vendor\\Square\\Models\\CatalogQueryExact' => $baseDir . '/vendor_prefixed/square/square/src/Models/CatalogQueryExact.php',
    'WPForms\\Vendor\\Square\\Models\\CatalogQueryItemVariationsForItemOptionValues' => $baseDir . '/vendor_prefixed/square/square/src/Models/CatalogQueryItemVariationsForItemOptionValues.php',
    'WPForms\\Vendor\\Square\\Models\\CatalogQueryItemsForItemOptions' => $baseDir . '/vendor_prefixed/square/square/src/Models/CatalogQueryItemsForItemOptions.php',
    'WPForms\\Vendor\\Square\\Models\\CatalogQueryItemsForModifierList' => $baseDir . '/vendor_prefixed/square/square/src/Models/CatalogQueryItemsForModifierList.php',
    'WPForms\\Vendor\\Square\\Models\\CatalogQueryItemsForTax' => $baseDir . '/vendor_prefixed/square/square/src/Models/CatalogQueryItemsForTax.php',
    'WPForms\\Vendor\\Square\\Models\\CatalogQueryPrefix' => $baseDir . '/vendor_prefixed/square/square/src/Models/CatalogQueryPrefix.php',
    'WPForms\\Vendor\\Square\\Models\\CatalogQueryRange' => $baseDir . '/vendor_prefixed/square/square/src/Models/CatalogQueryRange.php',
    'WPForms\\Vendor\\Square\\Models\\CatalogQuerySet' => $baseDir . '/vendor_prefixed/square/square/src/Models/CatalogQuerySet.php',
    'WPForms\\Vendor\\Square\\Models\\CatalogQuerySortedAttribute' => $baseDir . '/vendor_prefixed/square/square/src/Models/CatalogQuerySortedAttribute.php',
    'WPForms\\Vendor\\Square\\Models\\CatalogQueryText' => $baseDir . '/vendor_prefixed/square/square/src/Models/CatalogQueryText.php',
    'WPForms\\Vendor\\Square\\Models\\CatalogQuickAmount' => $baseDir . '/vendor_prefixed/square/square/src/Models/CatalogQuickAmount.php',
    'WPForms\\Vendor\\Square\\Models\\CatalogQuickAmountType' => $baseDir . '/vendor_prefixed/square/square/src/Models/CatalogQuickAmountType.php',
    'WPForms\\Vendor\\Square\\Models\\CatalogQuickAmountsSettings' => $baseDir . '/vendor_prefixed/square/square/src/Models/CatalogQuickAmountsSettings.php',
    'WPForms\\Vendor\\Square\\Models\\CatalogQuickAmountsSettingsOption' => $baseDir . '/vendor_prefixed/square/square/src/Models/CatalogQuickAmountsSettingsOption.php',
    'WPForms\\Vendor\\Square\\Models\\CatalogStockConversion' => $baseDir . '/vendor_prefixed/square/square/src/Models/CatalogStockConversion.php',
    'WPForms\\Vendor\\Square\\Models\\CatalogSubscriptionPlan' => $baseDir . '/vendor_prefixed/square/square/src/Models/CatalogSubscriptionPlan.php',
    'WPForms\\Vendor\\Square\\Models\\CatalogSubscriptionPlanVariation' => $baseDir . '/vendor_prefixed/square/square/src/Models/CatalogSubscriptionPlanVariation.php',
    'WPForms\\Vendor\\Square\\Models\\CatalogTax' => $baseDir . '/vendor_prefixed/square/square/src/Models/CatalogTax.php',
    'WPForms\\Vendor\\Square\\Models\\CatalogTimePeriod' => $baseDir . '/vendor_prefixed/square/square/src/Models/CatalogTimePeriod.php',
    'WPForms\\Vendor\\Square\\Models\\CatalogV1Id' => $baseDir . '/vendor_prefixed/square/square/src/Models/CatalogV1Id.php',
    'WPForms\\Vendor\\Square\\Models\\CategoryPathToRootNode' => $baseDir . '/vendor_prefixed/square/square/src/Models/CategoryPathToRootNode.php',
    'WPForms\\Vendor\\Square\\Models\\ChangeBillingAnchorDateRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/ChangeBillingAnchorDateRequest.php',
    'WPForms\\Vendor\\Square\\Models\\ChangeBillingAnchorDateResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/ChangeBillingAnchorDateResponse.php',
    'WPForms\\Vendor\\Square\\Models\\ChangeTiming' => $baseDir . '/vendor_prefixed/square/square/src/Models/ChangeTiming.php',
    'WPForms\\Vendor\\Square\\Models\\ChargeRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/ChargeRequest.php',
    'WPForms\\Vendor\\Square\\Models\\ChargeRequestAdditionalRecipient' => $baseDir . '/vendor_prefixed/square/square/src/Models/ChargeRequestAdditionalRecipient.php',
    'WPForms\\Vendor\\Square\\Models\\ChargeResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/ChargeResponse.php',
    'WPForms\\Vendor\\Square\\Models\\Checkout' => $baseDir . '/vendor_prefixed/square/square/src/Models/Checkout.php',
    'WPForms\\Vendor\\Square\\Models\\CheckoutLocationSettings' => $baseDir . '/vendor_prefixed/square/square/src/Models/CheckoutLocationSettings.php',
    'WPForms\\Vendor\\Square\\Models\\CheckoutLocationSettingsBranding' => $baseDir . '/vendor_prefixed/square/square/src/Models/CheckoutLocationSettingsBranding.php',
    'WPForms\\Vendor\\Square\\Models\\CheckoutLocationSettingsBrandingButtonShape' => $baseDir . '/vendor_prefixed/square/square/src/Models/CheckoutLocationSettingsBrandingButtonShape.php',
    'WPForms\\Vendor\\Square\\Models\\CheckoutLocationSettingsBrandingHeaderType' => $baseDir . '/vendor_prefixed/square/square/src/Models/CheckoutLocationSettingsBrandingHeaderType.php',
    'WPForms\\Vendor\\Square\\Models\\CheckoutLocationSettingsCoupons' => $baseDir . '/vendor_prefixed/square/square/src/Models/CheckoutLocationSettingsCoupons.php',
    'WPForms\\Vendor\\Square\\Models\\CheckoutLocationSettingsPolicy' => $baseDir . '/vendor_prefixed/square/square/src/Models/CheckoutLocationSettingsPolicy.php',
    'WPForms\\Vendor\\Square\\Models\\CheckoutLocationSettingsTipping' => $baseDir . '/vendor_prefixed/square/square/src/Models/CheckoutLocationSettingsTipping.php',
    'WPForms\\Vendor\\Square\\Models\\CheckoutMerchantSettings' => $baseDir . '/vendor_prefixed/square/square/src/Models/CheckoutMerchantSettings.php',
    'WPForms\\Vendor\\Square\\Models\\CheckoutMerchantSettingsPaymentMethods' => $baseDir . '/vendor_prefixed/square/square/src/Models/CheckoutMerchantSettingsPaymentMethods.php',
    'WPForms\\Vendor\\Square\\Models\\CheckoutMerchantSettingsPaymentMethodsAfterpayClearpay' => $baseDir . '/vendor_prefixed/square/square/src/Models/CheckoutMerchantSettingsPaymentMethodsAfterpayClearpay.php',
    'WPForms\\Vendor\\Square\\Models\\CheckoutMerchantSettingsPaymentMethodsAfterpayClearpayEligibilityRange' => $baseDir . '/vendor_prefixed/square/square/src/Models/CheckoutMerchantSettingsPaymentMethodsAfterpayClearpayEligibilityRange.php',
    'WPForms\\Vendor\\Square\\Models\\CheckoutMerchantSettingsPaymentMethodsPaymentMethod' => $baseDir . '/vendor_prefixed/square/square/src/Models/CheckoutMerchantSettingsPaymentMethodsPaymentMethod.php',
    'WPForms\\Vendor\\Square\\Models\\CheckoutOptions' => $baseDir . '/vendor_prefixed/square/square/src/Models/CheckoutOptions.php',
    'WPForms\\Vendor\\Square\\Models\\CheckoutOptionsPaymentType' => $baseDir . '/vendor_prefixed/square/square/src/Models/CheckoutOptionsPaymentType.php',
    'WPForms\\Vendor\\Square\\Models\\ClearpayDetails' => $baseDir . '/vendor_prefixed/square/square/src/Models/ClearpayDetails.php',
    'WPForms\\Vendor\\Square\\Models\\CloneOrderRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/CloneOrderRequest.php',
    'WPForms\\Vendor\\Square\\Models\\CloneOrderResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/CloneOrderResponse.php',
    'WPForms\\Vendor\\Square\\Models\\CollectedData' => $baseDir . '/vendor_prefixed/square/square/src/Models/CollectedData.php',
    'WPForms\\Vendor\\Square\\Models\\CompletePaymentRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/CompletePaymentRequest.php',
    'WPForms\\Vendor\\Square\\Models\\CompletePaymentResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/CompletePaymentResponse.php',
    'WPForms\\Vendor\\Square\\Models\\Component' => $baseDir . '/vendor_prefixed/square/square/src/Models/Component.php',
    'WPForms\\Vendor\\Square\\Models\\ComponentComponentType' => $baseDir . '/vendor_prefixed/square/square/src/Models/ComponentComponentType.php',
    'WPForms\\Vendor\\Square\\Models\\ConfirmationDecision' => $baseDir . '/vendor_prefixed/square/square/src/Models/ConfirmationDecision.php',
    'WPForms\\Vendor\\Square\\Models\\ConfirmationOptions' => $baseDir . '/vendor_prefixed/square/square/src/Models/ConfirmationOptions.php',
    'WPForms\\Vendor\\Square\\Models\\Coordinates' => $baseDir . '/vendor_prefixed/square/square/src/Models/Coordinates.php',
    'WPForms\\Vendor\\Square\\Models\\Country' => $baseDir . '/vendor_prefixed/square/square/src/Models/Country.php',
    'WPForms\\Vendor\\Square\\Models\\CreateBookingCustomAttributeDefinitionRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateBookingCustomAttributeDefinitionRequest.php',
    'WPForms\\Vendor\\Square\\Models\\CreateBookingCustomAttributeDefinitionResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateBookingCustomAttributeDefinitionResponse.php',
    'WPForms\\Vendor\\Square\\Models\\CreateBookingRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateBookingRequest.php',
    'WPForms\\Vendor\\Square\\Models\\CreateBookingResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateBookingResponse.php',
    'WPForms\\Vendor\\Square\\Models\\CreateBreakTypeRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateBreakTypeRequest.php',
    'WPForms\\Vendor\\Square\\Models\\CreateBreakTypeResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateBreakTypeResponse.php',
    'WPForms\\Vendor\\Square\\Models\\CreateCardRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateCardRequest.php',
    'WPForms\\Vendor\\Square\\Models\\CreateCardResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateCardResponse.php',
    'WPForms\\Vendor\\Square\\Models\\CreateCatalogImageRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateCatalogImageRequest.php',
    'WPForms\\Vendor\\Square\\Models\\CreateCatalogImageResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateCatalogImageResponse.php',
    'WPForms\\Vendor\\Square\\Models\\CreateCheckoutRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateCheckoutRequest.php',
    'WPForms\\Vendor\\Square\\Models\\CreateCheckoutResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateCheckoutResponse.php',
    'WPForms\\Vendor\\Square\\Models\\CreateCustomerCardRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateCustomerCardRequest.php',
    'WPForms\\Vendor\\Square\\Models\\CreateCustomerCardResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateCustomerCardResponse.php',
    'WPForms\\Vendor\\Square\\Models\\CreateCustomerCustomAttributeDefinitionRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateCustomerCustomAttributeDefinitionRequest.php',
    'WPForms\\Vendor\\Square\\Models\\CreateCustomerCustomAttributeDefinitionResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateCustomerCustomAttributeDefinitionResponse.php',
    'WPForms\\Vendor\\Square\\Models\\CreateCustomerGroupRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateCustomerGroupRequest.php',
    'WPForms\\Vendor\\Square\\Models\\CreateCustomerGroupResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateCustomerGroupResponse.php',
    'WPForms\\Vendor\\Square\\Models\\CreateCustomerRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateCustomerRequest.php',
    'WPForms\\Vendor\\Square\\Models\\CreateCustomerResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateCustomerResponse.php',
    'WPForms\\Vendor\\Square\\Models\\CreateDeviceCodeRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateDeviceCodeRequest.php',
    'WPForms\\Vendor\\Square\\Models\\CreateDeviceCodeResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateDeviceCodeResponse.php',
    'WPForms\\Vendor\\Square\\Models\\CreateDisputeEvidenceFileRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateDisputeEvidenceFileRequest.php',
    'WPForms\\Vendor\\Square\\Models\\CreateDisputeEvidenceFileResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateDisputeEvidenceFileResponse.php',
    'WPForms\\Vendor\\Square\\Models\\CreateDisputeEvidenceTextRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateDisputeEvidenceTextRequest.php',
    'WPForms\\Vendor\\Square\\Models\\CreateDisputeEvidenceTextResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateDisputeEvidenceTextResponse.php',
    'WPForms\\Vendor\\Square\\Models\\CreateGiftCardActivityRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateGiftCardActivityRequest.php',
    'WPForms\\Vendor\\Square\\Models\\CreateGiftCardActivityResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateGiftCardActivityResponse.php',
    'WPForms\\Vendor\\Square\\Models\\CreateGiftCardRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateGiftCardRequest.php',
    'WPForms\\Vendor\\Square\\Models\\CreateGiftCardResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateGiftCardResponse.php',
    'WPForms\\Vendor\\Square\\Models\\CreateInvoiceAttachmentRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateInvoiceAttachmentRequest.php',
    'WPForms\\Vendor\\Square\\Models\\CreateInvoiceAttachmentResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateInvoiceAttachmentResponse.php',
    'WPForms\\Vendor\\Square\\Models\\CreateInvoiceRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateInvoiceRequest.php',
    'WPForms\\Vendor\\Square\\Models\\CreateInvoiceResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateInvoiceResponse.php',
    'WPForms\\Vendor\\Square\\Models\\CreateJobRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateJobRequest.php',
    'WPForms\\Vendor\\Square\\Models\\CreateJobResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateJobResponse.php',
    'WPForms\\Vendor\\Square\\Models\\CreateLocationCustomAttributeDefinitionRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateLocationCustomAttributeDefinitionRequest.php',
    'WPForms\\Vendor\\Square\\Models\\CreateLocationCustomAttributeDefinitionResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateLocationCustomAttributeDefinitionResponse.php',
    'WPForms\\Vendor\\Square\\Models\\CreateLocationRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateLocationRequest.php',
    'WPForms\\Vendor\\Square\\Models\\CreateLocationResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateLocationResponse.php',
    'WPForms\\Vendor\\Square\\Models\\CreateLoyaltyAccountRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateLoyaltyAccountRequest.php',
    'WPForms\\Vendor\\Square\\Models\\CreateLoyaltyAccountResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateLoyaltyAccountResponse.php',
    'WPForms\\Vendor\\Square\\Models\\CreateLoyaltyPromotionRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateLoyaltyPromotionRequest.php',
    'WPForms\\Vendor\\Square\\Models\\CreateLoyaltyPromotionResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateLoyaltyPromotionResponse.php',
    'WPForms\\Vendor\\Square\\Models\\CreateLoyaltyRewardRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateLoyaltyRewardRequest.php',
    'WPForms\\Vendor\\Square\\Models\\CreateLoyaltyRewardResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateLoyaltyRewardResponse.php',
    'WPForms\\Vendor\\Square\\Models\\CreateMerchantCustomAttributeDefinitionRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateMerchantCustomAttributeDefinitionRequest.php',
    'WPForms\\Vendor\\Square\\Models\\CreateMerchantCustomAttributeDefinitionResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateMerchantCustomAttributeDefinitionResponse.php',
    'WPForms\\Vendor\\Square\\Models\\CreateMobileAuthorizationCodeRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateMobileAuthorizationCodeRequest.php',
    'WPForms\\Vendor\\Square\\Models\\CreateMobileAuthorizationCodeResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateMobileAuthorizationCodeResponse.php',
    'WPForms\\Vendor\\Square\\Models\\CreateOrderCustomAttributeDefinitionRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateOrderCustomAttributeDefinitionRequest.php',
    'WPForms\\Vendor\\Square\\Models\\CreateOrderCustomAttributeDefinitionResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateOrderCustomAttributeDefinitionResponse.php',
    'WPForms\\Vendor\\Square\\Models\\CreateOrderRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateOrderRequest.php',
    'WPForms\\Vendor\\Square\\Models\\CreateOrderResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateOrderResponse.php',
    'WPForms\\Vendor\\Square\\Models\\CreatePaymentLinkRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreatePaymentLinkRequest.php',
    'WPForms\\Vendor\\Square\\Models\\CreatePaymentLinkResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreatePaymentLinkResponse.php',
    'WPForms\\Vendor\\Square\\Models\\CreatePaymentRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreatePaymentRequest.php',
    'WPForms\\Vendor\\Square\\Models\\CreatePaymentResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreatePaymentResponse.php',
    'WPForms\\Vendor\\Square\\Models\\CreateRefundRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateRefundRequest.php',
    'WPForms\\Vendor\\Square\\Models\\CreateRefundResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateRefundResponse.php',
    'WPForms\\Vendor\\Square\\Models\\CreateShiftRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateShiftRequest.php',
    'WPForms\\Vendor\\Square\\Models\\CreateShiftResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateShiftResponse.php',
    'WPForms\\Vendor\\Square\\Models\\CreateSubscriptionRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateSubscriptionRequest.php',
    'WPForms\\Vendor\\Square\\Models\\CreateSubscriptionResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateSubscriptionResponse.php',
    'WPForms\\Vendor\\Square\\Models\\CreateTeamMemberRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateTeamMemberRequest.php',
    'WPForms\\Vendor\\Square\\Models\\CreateTeamMemberResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateTeamMemberResponse.php',
    'WPForms\\Vendor\\Square\\Models\\CreateTerminalActionRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateTerminalActionRequest.php',
    'WPForms\\Vendor\\Square\\Models\\CreateTerminalActionResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateTerminalActionResponse.php',
    'WPForms\\Vendor\\Square\\Models\\CreateTerminalCheckoutRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateTerminalCheckoutRequest.php',
    'WPForms\\Vendor\\Square\\Models\\CreateTerminalCheckoutResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateTerminalCheckoutResponse.php',
    'WPForms\\Vendor\\Square\\Models\\CreateTerminalRefundRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateTerminalRefundRequest.php',
    'WPForms\\Vendor\\Square\\Models\\CreateTerminalRefundResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateTerminalRefundResponse.php',
    'WPForms\\Vendor\\Square\\Models\\CreateVendorRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateVendorRequest.php',
    'WPForms\\Vendor\\Square\\Models\\CreateVendorResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateVendorResponse.php',
    'WPForms\\Vendor\\Square\\Models\\CreateWebhookSubscriptionRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateWebhookSubscriptionRequest.php',
    'WPForms\\Vendor\\Square\\Models\\CreateWebhookSubscriptionResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/CreateWebhookSubscriptionResponse.php',
    'WPForms\\Vendor\\Square\\Models\\Currency' => $baseDir . '/vendor_prefixed/square/square/src/Models/Currency.php',
    'WPForms\\Vendor\\Square\\Models\\CustomAttribute' => $baseDir . '/vendor_prefixed/square/square/src/Models/CustomAttribute.php',
    'WPForms\\Vendor\\Square\\Models\\CustomAttributeDefinition' => $baseDir . '/vendor_prefixed/square/square/src/Models/CustomAttributeDefinition.php',
    'WPForms\\Vendor\\Square\\Models\\CustomAttributeDefinitionVisibility' => $baseDir . '/vendor_prefixed/square/square/src/Models/CustomAttributeDefinitionVisibility.php',
    'WPForms\\Vendor\\Square\\Models\\CustomAttributeFilter' => $baseDir . '/vendor_prefixed/square/square/src/Models/CustomAttributeFilter.php',
    'WPForms\\Vendor\\Square\\Models\\CustomField' => $baseDir . '/vendor_prefixed/square/square/src/Models/CustomField.php',
    'WPForms\\Vendor\\Square\\Models\\Customer' => $baseDir . '/vendor_prefixed/square/square/src/Models/Customer.php',
    'WPForms\\Vendor\\Square\\Models\\CustomerAddressFilter' => $baseDir . '/vendor_prefixed/square/square/src/Models/CustomerAddressFilter.php',
    'WPForms\\Vendor\\Square\\Models\\CustomerCreationSource' => $baseDir . '/vendor_prefixed/square/square/src/Models/CustomerCreationSource.php',
    'WPForms\\Vendor\\Square\\Models\\CustomerCreationSourceFilter' => $baseDir . '/vendor_prefixed/square/square/src/Models/CustomerCreationSourceFilter.php',
    'WPForms\\Vendor\\Square\\Models\\CustomerCustomAttributeFilter' => $baseDir . '/vendor_prefixed/square/square/src/Models/CustomerCustomAttributeFilter.php',
    'WPForms\\Vendor\\Square\\Models\\CustomerCustomAttributeFilterValue' => $baseDir . '/vendor_prefixed/square/square/src/Models/CustomerCustomAttributeFilterValue.php',
    'WPForms\\Vendor\\Square\\Models\\CustomerCustomAttributeFilters' => $baseDir . '/vendor_prefixed/square/square/src/Models/CustomerCustomAttributeFilters.php',
    'WPForms\\Vendor\\Square\\Models\\CustomerDetails' => $baseDir . '/vendor_prefixed/square/square/src/Models/CustomerDetails.php',
    'WPForms\\Vendor\\Square\\Models\\CustomerFilter' => $baseDir . '/vendor_prefixed/square/square/src/Models/CustomerFilter.php',
    'WPForms\\Vendor\\Square\\Models\\CustomerGroup' => $baseDir . '/vendor_prefixed/square/square/src/Models/CustomerGroup.php',
    'WPForms\\Vendor\\Square\\Models\\CustomerInclusionExclusion' => $baseDir . '/vendor_prefixed/square/square/src/Models/CustomerInclusionExclusion.php',
    'WPForms\\Vendor\\Square\\Models\\CustomerPreferences' => $baseDir . '/vendor_prefixed/square/square/src/Models/CustomerPreferences.php',
    'WPForms\\Vendor\\Square\\Models\\CustomerQuery' => $baseDir . '/vendor_prefixed/square/square/src/Models/CustomerQuery.php',
    'WPForms\\Vendor\\Square\\Models\\CustomerSegment' => $baseDir . '/vendor_prefixed/square/square/src/Models/CustomerSegment.php',
    'WPForms\\Vendor\\Square\\Models\\CustomerSort' => $baseDir . '/vendor_prefixed/square/square/src/Models/CustomerSort.php',
    'WPForms\\Vendor\\Square\\Models\\CustomerSortField' => $baseDir . '/vendor_prefixed/square/square/src/Models/CustomerSortField.php',
    'WPForms\\Vendor\\Square\\Models\\CustomerTaxIds' => $baseDir . '/vendor_prefixed/square/square/src/Models/CustomerTaxIds.php',
    'WPForms\\Vendor\\Square\\Models\\CustomerTextFilter' => $baseDir . '/vendor_prefixed/square/square/src/Models/CustomerTextFilter.php',
    'WPForms\\Vendor\\Square\\Models\\DataCollectionOptions' => $baseDir . '/vendor_prefixed/square/square/src/Models/DataCollectionOptions.php',
    'WPForms\\Vendor\\Square\\Models\\DataCollectionOptionsInputType' => $baseDir . '/vendor_prefixed/square/square/src/Models/DataCollectionOptionsInputType.php',
    'WPForms\\Vendor\\Square\\Models\\DateRange' => $baseDir . '/vendor_prefixed/square/square/src/Models/DateRange.php',
    'WPForms\\Vendor\\Square\\Models\\DayOfWeek' => $baseDir . '/vendor_prefixed/square/square/src/Models/DayOfWeek.php',
    'WPForms\\Vendor\\Square\\Models\\DeleteBookingCustomAttributeDefinitionResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/DeleteBookingCustomAttributeDefinitionResponse.php',
    'WPForms\\Vendor\\Square\\Models\\DeleteBookingCustomAttributeResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/DeleteBookingCustomAttributeResponse.php',
    'WPForms\\Vendor\\Square\\Models\\DeleteBreakTypeResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/DeleteBreakTypeResponse.php',
    'WPForms\\Vendor\\Square\\Models\\DeleteCatalogObjectResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/DeleteCatalogObjectResponse.php',
    'WPForms\\Vendor\\Square\\Models\\DeleteCustomerCardResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/DeleteCustomerCardResponse.php',
    'WPForms\\Vendor\\Square\\Models\\DeleteCustomerCustomAttributeDefinitionResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/DeleteCustomerCustomAttributeDefinitionResponse.php',
    'WPForms\\Vendor\\Square\\Models\\DeleteCustomerCustomAttributeResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/DeleteCustomerCustomAttributeResponse.php',
    'WPForms\\Vendor\\Square\\Models\\DeleteCustomerGroupResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/DeleteCustomerGroupResponse.php',
    'WPForms\\Vendor\\Square\\Models\\DeleteCustomerRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/DeleteCustomerRequest.php',
    'WPForms\\Vendor\\Square\\Models\\DeleteCustomerResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/DeleteCustomerResponse.php',
    'WPForms\\Vendor\\Square\\Models\\DeleteDisputeEvidenceResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/DeleteDisputeEvidenceResponse.php',
    'WPForms\\Vendor\\Square\\Models\\DeleteInvoiceAttachmentResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/DeleteInvoiceAttachmentResponse.php',
    'WPForms\\Vendor\\Square\\Models\\DeleteInvoiceRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/DeleteInvoiceRequest.php',
    'WPForms\\Vendor\\Square\\Models\\DeleteInvoiceResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/DeleteInvoiceResponse.php',
    'WPForms\\Vendor\\Square\\Models\\DeleteLocationCustomAttributeDefinitionResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/DeleteLocationCustomAttributeDefinitionResponse.php',
    'WPForms\\Vendor\\Square\\Models\\DeleteLocationCustomAttributeResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/DeleteLocationCustomAttributeResponse.php',
    'WPForms\\Vendor\\Square\\Models\\DeleteLoyaltyRewardResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/DeleteLoyaltyRewardResponse.php',
    'WPForms\\Vendor\\Square\\Models\\DeleteMerchantCustomAttributeDefinitionResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/DeleteMerchantCustomAttributeDefinitionResponse.php',
    'WPForms\\Vendor\\Square\\Models\\DeleteMerchantCustomAttributeResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/DeleteMerchantCustomAttributeResponse.php',
    'WPForms\\Vendor\\Square\\Models\\DeleteOrderCustomAttributeDefinitionResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/DeleteOrderCustomAttributeDefinitionResponse.php',
    'WPForms\\Vendor\\Square\\Models\\DeleteOrderCustomAttributeResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/DeleteOrderCustomAttributeResponse.php',
    'WPForms\\Vendor\\Square\\Models\\DeletePaymentLinkResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/DeletePaymentLinkResponse.php',
    'WPForms\\Vendor\\Square\\Models\\DeleteShiftResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/DeleteShiftResponse.php',
    'WPForms\\Vendor\\Square\\Models\\DeleteSnippetResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/DeleteSnippetResponse.php',
    'WPForms\\Vendor\\Square\\Models\\DeleteSubscriptionActionResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/DeleteSubscriptionActionResponse.php',
    'WPForms\\Vendor\\Square\\Models\\DeleteWebhookSubscriptionResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/DeleteWebhookSubscriptionResponse.php',
    'WPForms\\Vendor\\Square\\Models\\DeprecatedCreateDisputeEvidenceFileRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/DeprecatedCreateDisputeEvidenceFileRequest.php',
    'WPForms\\Vendor\\Square\\Models\\DeprecatedCreateDisputeEvidenceFileResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/DeprecatedCreateDisputeEvidenceFileResponse.php',
    'WPForms\\Vendor\\Square\\Models\\DeprecatedCreateDisputeEvidenceTextRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/DeprecatedCreateDisputeEvidenceTextRequest.php',
    'WPForms\\Vendor\\Square\\Models\\DeprecatedCreateDisputeEvidenceTextResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/DeprecatedCreateDisputeEvidenceTextResponse.php',
    'WPForms\\Vendor\\Square\\Models\\Destination' => $baseDir . '/vendor_prefixed/square/square/src/Models/Destination.php',
    'WPForms\\Vendor\\Square\\Models\\DestinationDetails' => $baseDir . '/vendor_prefixed/square/square/src/Models/DestinationDetails.php',
    'WPForms\\Vendor\\Square\\Models\\DestinationDetailsCardRefundDetails' => $baseDir . '/vendor_prefixed/square/square/src/Models/DestinationDetailsCardRefundDetails.php',
    'WPForms\\Vendor\\Square\\Models\\DestinationDetailsCashRefundDetails' => $baseDir . '/vendor_prefixed/square/square/src/Models/DestinationDetailsCashRefundDetails.php',
    'WPForms\\Vendor\\Square\\Models\\DestinationDetailsExternalRefundDetails' => $baseDir . '/vendor_prefixed/square/square/src/Models/DestinationDetailsExternalRefundDetails.php',
    'WPForms\\Vendor\\Square\\Models\\DestinationType' => $baseDir . '/vendor_prefixed/square/square/src/Models/DestinationType.php',
    'WPForms\\Vendor\\Square\\Models\\Device' => $baseDir . '/vendor_prefixed/square/square/src/Models/Device.php',
    'WPForms\\Vendor\\Square\\Models\\DeviceAttributes' => $baseDir . '/vendor_prefixed/square/square/src/Models/DeviceAttributes.php',
    'WPForms\\Vendor\\Square\\Models\\DeviceAttributesDeviceType' => $baseDir . '/vendor_prefixed/square/square/src/Models/DeviceAttributesDeviceType.php',
    'WPForms\\Vendor\\Square\\Models\\DeviceCheckoutOptions' => $baseDir . '/vendor_prefixed/square/square/src/Models/DeviceCheckoutOptions.php',
    'WPForms\\Vendor\\Square\\Models\\DeviceCode' => $baseDir . '/vendor_prefixed/square/square/src/Models/DeviceCode.php',
    'WPForms\\Vendor\\Square\\Models\\DeviceCodeStatus' => $baseDir . '/vendor_prefixed/square/square/src/Models/DeviceCodeStatus.php',
    'WPForms\\Vendor\\Square\\Models\\DeviceComponentDetailsApplicationDetails' => $baseDir . '/vendor_prefixed/square/square/src/Models/DeviceComponentDetailsApplicationDetails.php',
    'WPForms\\Vendor\\Square\\Models\\DeviceComponentDetailsBatteryDetails' => $baseDir . '/vendor_prefixed/square/square/src/Models/DeviceComponentDetailsBatteryDetails.php',
    'WPForms\\Vendor\\Square\\Models\\DeviceComponentDetailsCardReaderDetails' => $baseDir . '/vendor_prefixed/square/square/src/Models/DeviceComponentDetailsCardReaderDetails.php',
    'WPForms\\Vendor\\Square\\Models\\DeviceComponentDetailsEthernetDetails' => $baseDir . '/vendor_prefixed/square/square/src/Models/DeviceComponentDetailsEthernetDetails.php',
    'WPForms\\Vendor\\Square\\Models\\DeviceComponentDetailsExternalPower' => $baseDir . '/vendor_prefixed/square/square/src/Models/DeviceComponentDetailsExternalPower.php',
    'WPForms\\Vendor\\Square\\Models\\DeviceComponentDetailsMeasurement' => $baseDir . '/vendor_prefixed/square/square/src/Models/DeviceComponentDetailsMeasurement.php',
    'WPForms\\Vendor\\Square\\Models\\DeviceComponentDetailsNetworkInterfaceDetails' => $baseDir . '/vendor_prefixed/square/square/src/Models/DeviceComponentDetailsNetworkInterfaceDetails.php',
    'WPForms\\Vendor\\Square\\Models\\DeviceComponentDetailsWiFiDetails' => $baseDir . '/vendor_prefixed/square/square/src/Models/DeviceComponentDetailsWiFiDetails.php',
    'WPForms\\Vendor\\Square\\Models\\DeviceDetails' => $baseDir . '/vendor_prefixed/square/square/src/Models/DeviceDetails.php',
    'WPForms\\Vendor\\Square\\Models\\DeviceMetadata' => $baseDir . '/vendor_prefixed/square/square/src/Models/DeviceMetadata.php',
    'WPForms\\Vendor\\Square\\Models\\DeviceStatus' => $baseDir . '/vendor_prefixed/square/square/src/Models/DeviceStatus.php',
    'WPForms\\Vendor\\Square\\Models\\DeviceStatusCategory' => $baseDir . '/vendor_prefixed/square/square/src/Models/DeviceStatusCategory.php',
    'WPForms\\Vendor\\Square\\Models\\DigitalWalletDetails' => $baseDir . '/vendor_prefixed/square/square/src/Models/DigitalWalletDetails.php',
    'WPForms\\Vendor\\Square\\Models\\DisableCardResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/DisableCardResponse.php',
    'WPForms\\Vendor\\Square\\Models\\DisableEventsResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/DisableEventsResponse.php',
    'WPForms\\Vendor\\Square\\Models\\DismissTerminalActionResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/DismissTerminalActionResponse.php',
    'WPForms\\Vendor\\Square\\Models\\DismissTerminalCheckoutResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/DismissTerminalCheckoutResponse.php',
    'WPForms\\Vendor\\Square\\Models\\DismissTerminalRefundResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/DismissTerminalRefundResponse.php',
    'WPForms\\Vendor\\Square\\Models\\Dispute' => $baseDir . '/vendor_prefixed/square/square/src/Models/Dispute.php',
    'WPForms\\Vendor\\Square\\Models\\DisputeEvidence' => $baseDir . '/vendor_prefixed/square/square/src/Models/DisputeEvidence.php',
    'WPForms\\Vendor\\Square\\Models\\DisputeEvidenceFile' => $baseDir . '/vendor_prefixed/square/square/src/Models/DisputeEvidenceFile.php',
    'WPForms\\Vendor\\Square\\Models\\DisputeEvidenceType' => $baseDir . '/vendor_prefixed/square/square/src/Models/DisputeEvidenceType.php',
    'WPForms\\Vendor\\Square\\Models\\DisputeReason' => $baseDir . '/vendor_prefixed/square/square/src/Models/DisputeReason.php',
    'WPForms\\Vendor\\Square\\Models\\DisputeState' => $baseDir . '/vendor_prefixed/square/square/src/Models/DisputeState.php',
    'WPForms\\Vendor\\Square\\Models\\DisputedPayment' => $baseDir . '/vendor_prefixed/square/square/src/Models/DisputedPayment.php',
    'WPForms\\Vendor\\Square\\Models\\EcomVisibility' => $baseDir . '/vendor_prefixed/square/square/src/Models/EcomVisibility.php',
    'WPForms\\Vendor\\Square\\Models\\Employee' => $baseDir . '/vendor_prefixed/square/square/src/Models/Employee.php',
    'WPForms\\Vendor\\Square\\Models\\EmployeeStatus' => $baseDir . '/vendor_prefixed/square/square/src/Models/EmployeeStatus.php',
    'WPForms\\Vendor\\Square\\Models\\EmployeeWage' => $baseDir . '/vendor_prefixed/square/square/src/Models/EmployeeWage.php',
    'WPForms\\Vendor\\Square\\Models\\EnableEventsResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/EnableEventsResponse.php',
    'WPForms\\Vendor\\Square\\Models\\Error' => $baseDir . '/vendor_prefixed/square/square/src/Models/Error.php',
    'WPForms\\Vendor\\Square\\Models\\ErrorCategory' => $baseDir . '/vendor_prefixed/square/square/src/Models/ErrorCategory.php',
    'WPForms\\Vendor\\Square\\Models\\ErrorCode' => $baseDir . '/vendor_prefixed/square/square/src/Models/ErrorCode.php',
    'WPForms\\Vendor\\Square\\Models\\Event' => $baseDir . '/vendor_prefixed/square/square/src/Models/Event.php',
    'WPForms\\Vendor\\Square\\Models\\EventData' => $baseDir . '/vendor_prefixed/square/square/src/Models/EventData.php',
    'WPForms\\Vendor\\Square\\Models\\EventMetadata' => $baseDir . '/vendor_prefixed/square/square/src/Models/EventMetadata.php',
    'WPForms\\Vendor\\Square\\Models\\EventTypeMetadata' => $baseDir . '/vendor_prefixed/square/square/src/Models/EventTypeMetadata.php',
    'WPForms\\Vendor\\Square\\Models\\ExcludeStrategy' => $baseDir . '/vendor_prefixed/square/square/src/Models/ExcludeStrategy.php',
    'WPForms\\Vendor\\Square\\Models\\ExternalPaymentDetails' => $baseDir . '/vendor_prefixed/square/square/src/Models/ExternalPaymentDetails.php',
    'WPForms\\Vendor\\Square\\Models\\FilterValue' => $baseDir . '/vendor_prefixed/square/square/src/Models/FilterValue.php',
    'WPForms\\Vendor\\Square\\Models\\FloatNumberRange' => $baseDir . '/vendor_prefixed/square/square/src/Models/FloatNumberRange.php',
    'WPForms\\Vendor\\Square\\Models\\Fulfillment' => $baseDir . '/vendor_prefixed/square/square/src/Models/Fulfillment.php',
    'WPForms\\Vendor\\Square\\Models\\FulfillmentDeliveryDetails' => $baseDir . '/vendor_prefixed/square/square/src/Models/FulfillmentDeliveryDetails.php',
    'WPForms\\Vendor\\Square\\Models\\FulfillmentDeliveryDetailsOrderFulfillmentDeliveryDetailsScheduleType' => $baseDir . '/vendor_prefixed/square/square/src/Models/FulfillmentDeliveryDetailsOrderFulfillmentDeliveryDetailsScheduleType.php',
    'WPForms\\Vendor\\Square\\Models\\FulfillmentFulfillmentEntry' => $baseDir . '/vendor_prefixed/square/square/src/Models/FulfillmentFulfillmentEntry.php',
    'WPForms\\Vendor\\Square\\Models\\FulfillmentFulfillmentLineItemApplication' => $baseDir . '/vendor_prefixed/square/square/src/Models/FulfillmentFulfillmentLineItemApplication.php',
    'WPForms\\Vendor\\Square\\Models\\FulfillmentPickupDetails' => $baseDir . '/vendor_prefixed/square/square/src/Models/FulfillmentPickupDetails.php',
    'WPForms\\Vendor\\Square\\Models\\FulfillmentPickupDetailsCurbsidePickupDetails' => $baseDir . '/vendor_prefixed/square/square/src/Models/FulfillmentPickupDetailsCurbsidePickupDetails.php',
    'WPForms\\Vendor\\Square\\Models\\FulfillmentPickupDetailsScheduleType' => $baseDir . '/vendor_prefixed/square/square/src/Models/FulfillmentPickupDetailsScheduleType.php',
    'WPForms\\Vendor\\Square\\Models\\FulfillmentRecipient' => $baseDir . '/vendor_prefixed/square/square/src/Models/FulfillmentRecipient.php',
    'WPForms\\Vendor\\Square\\Models\\FulfillmentShipmentDetails' => $baseDir . '/vendor_prefixed/square/square/src/Models/FulfillmentShipmentDetails.php',
    'WPForms\\Vendor\\Square\\Models\\FulfillmentState' => $baseDir . '/vendor_prefixed/square/square/src/Models/FulfillmentState.php',
    'WPForms\\Vendor\\Square\\Models\\FulfillmentType' => $baseDir . '/vendor_prefixed/square/square/src/Models/FulfillmentType.php',
    'WPForms\\Vendor\\Square\\Models\\GetBankAccountByV1IdResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/GetBankAccountByV1IdResponse.php',
    'WPForms\\Vendor\\Square\\Models\\GetBankAccountResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/GetBankAccountResponse.php',
    'WPForms\\Vendor\\Square\\Models\\GetBreakTypeResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/GetBreakTypeResponse.php',
    'WPForms\\Vendor\\Square\\Models\\GetDeviceCodeResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/GetDeviceCodeResponse.php',
    'WPForms\\Vendor\\Square\\Models\\GetDeviceResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/GetDeviceResponse.php',
    'WPForms\\Vendor\\Square\\Models\\GetEmployeeWageResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/GetEmployeeWageResponse.php',
    'WPForms\\Vendor\\Square\\Models\\GetInvoiceResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/GetInvoiceResponse.php',
    'WPForms\\Vendor\\Square\\Models\\GetPaymentRefundResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/GetPaymentRefundResponse.php',
    'WPForms\\Vendor\\Square\\Models\\GetPaymentResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/GetPaymentResponse.php',
    'WPForms\\Vendor\\Square\\Models\\GetPayoutResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/GetPayoutResponse.php',
    'WPForms\\Vendor\\Square\\Models\\GetShiftResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/GetShiftResponse.php',
    'WPForms\\Vendor\\Square\\Models\\GetTeamMemberWageResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/GetTeamMemberWageResponse.php',
    'WPForms\\Vendor\\Square\\Models\\GetTerminalActionResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/GetTerminalActionResponse.php',
    'WPForms\\Vendor\\Square\\Models\\GetTerminalCheckoutResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/GetTerminalCheckoutResponse.php',
    'WPForms\\Vendor\\Square\\Models\\GetTerminalRefundResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/GetTerminalRefundResponse.php',
    'WPForms\\Vendor\\Square\\Models\\GiftCard' => $baseDir . '/vendor_prefixed/square/square/src/Models/GiftCard.php',
    'WPForms\\Vendor\\Square\\Models\\GiftCardActivity' => $baseDir . '/vendor_prefixed/square/square/src/Models/GiftCardActivity.php',
    'WPForms\\Vendor\\Square\\Models\\GiftCardActivityActivate' => $baseDir . '/vendor_prefixed/square/square/src/Models/GiftCardActivityActivate.php',
    'WPForms\\Vendor\\Square\\Models\\GiftCardActivityAdjustDecrement' => $baseDir . '/vendor_prefixed/square/square/src/Models/GiftCardActivityAdjustDecrement.php',
    'WPForms\\Vendor\\Square\\Models\\GiftCardActivityAdjustDecrementReason' => $baseDir . '/vendor_prefixed/square/square/src/Models/GiftCardActivityAdjustDecrementReason.php',
    'WPForms\\Vendor\\Square\\Models\\GiftCardActivityAdjustIncrement' => $baseDir . '/vendor_prefixed/square/square/src/Models/GiftCardActivityAdjustIncrement.php',
    'WPForms\\Vendor\\Square\\Models\\GiftCardActivityAdjustIncrementReason' => $baseDir . '/vendor_prefixed/square/square/src/Models/GiftCardActivityAdjustIncrementReason.php',
    'WPForms\\Vendor\\Square\\Models\\GiftCardActivityBlock' => $baseDir . '/vendor_prefixed/square/square/src/Models/GiftCardActivityBlock.php',
    'WPForms\\Vendor\\Square\\Models\\GiftCardActivityBlockReason' => $baseDir . '/vendor_prefixed/square/square/src/Models/GiftCardActivityBlockReason.php',
    'WPForms\\Vendor\\Square\\Models\\GiftCardActivityClearBalance' => $baseDir . '/vendor_prefixed/square/square/src/Models/GiftCardActivityClearBalance.php',
    'WPForms\\Vendor\\Square\\Models\\GiftCardActivityClearBalanceReason' => $baseDir . '/vendor_prefixed/square/square/src/Models/GiftCardActivityClearBalanceReason.php',
    'WPForms\\Vendor\\Square\\Models\\GiftCardActivityDeactivate' => $baseDir . '/vendor_prefixed/square/square/src/Models/GiftCardActivityDeactivate.php',
    'WPForms\\Vendor\\Square\\Models\\GiftCardActivityDeactivateReason' => $baseDir . '/vendor_prefixed/square/square/src/Models/GiftCardActivityDeactivateReason.php',
    'WPForms\\Vendor\\Square\\Models\\GiftCardActivityImport' => $baseDir . '/vendor_prefixed/square/square/src/Models/GiftCardActivityImport.php',
    'WPForms\\Vendor\\Square\\Models\\GiftCardActivityImportReversal' => $baseDir . '/vendor_prefixed/square/square/src/Models/GiftCardActivityImportReversal.php',
    'WPForms\\Vendor\\Square\\Models\\GiftCardActivityLoad' => $baseDir . '/vendor_prefixed/square/square/src/Models/GiftCardActivityLoad.php',
    'WPForms\\Vendor\\Square\\Models\\GiftCardActivityRedeem' => $baseDir . '/vendor_prefixed/square/square/src/Models/GiftCardActivityRedeem.php',
    'WPForms\\Vendor\\Square\\Models\\GiftCardActivityRedeemStatus' => $baseDir . '/vendor_prefixed/square/square/src/Models/GiftCardActivityRedeemStatus.php',
    'WPForms\\Vendor\\Square\\Models\\GiftCardActivityRefund' => $baseDir . '/vendor_prefixed/square/square/src/Models/GiftCardActivityRefund.php',
    'WPForms\\Vendor\\Square\\Models\\GiftCardActivityTransferBalanceFrom' => $baseDir . '/vendor_prefixed/square/square/src/Models/GiftCardActivityTransferBalanceFrom.php',
    'WPForms\\Vendor\\Square\\Models\\GiftCardActivityTransferBalanceTo' => $baseDir . '/vendor_prefixed/square/square/src/Models/GiftCardActivityTransferBalanceTo.php',
    'WPForms\\Vendor\\Square\\Models\\GiftCardActivityType' => $baseDir . '/vendor_prefixed/square/square/src/Models/GiftCardActivityType.php',
    'WPForms\\Vendor\\Square\\Models\\GiftCardActivityUnblock' => $baseDir . '/vendor_prefixed/square/square/src/Models/GiftCardActivityUnblock.php',
    'WPForms\\Vendor\\Square\\Models\\GiftCardActivityUnblockReason' => $baseDir . '/vendor_prefixed/square/square/src/Models/GiftCardActivityUnblockReason.php',
    'WPForms\\Vendor\\Square\\Models\\GiftCardActivityUnlinkedActivityRefund' => $baseDir . '/vendor_prefixed/square/square/src/Models/GiftCardActivityUnlinkedActivityRefund.php',
    'WPForms\\Vendor\\Square\\Models\\GiftCardGANSource' => $baseDir . '/vendor_prefixed/square/square/src/Models/GiftCardGANSource.php',
    'WPForms\\Vendor\\Square\\Models\\GiftCardStatus' => $baseDir . '/vendor_prefixed/square/square/src/Models/GiftCardStatus.php',
    'WPForms\\Vendor\\Square\\Models\\GiftCardType' => $baseDir . '/vendor_prefixed/square/square/src/Models/GiftCardType.php',
    'WPForms\\Vendor\\Square\\Models\\InventoryAdjustment' => $baseDir . '/vendor_prefixed/square/square/src/Models/InventoryAdjustment.php',
    'WPForms\\Vendor\\Square\\Models\\InventoryAdjustmentGroup' => $baseDir . '/vendor_prefixed/square/square/src/Models/InventoryAdjustmentGroup.php',
    'WPForms\\Vendor\\Square\\Models\\InventoryAlertType' => $baseDir . '/vendor_prefixed/square/square/src/Models/InventoryAlertType.php',
    'WPForms\\Vendor\\Square\\Models\\InventoryChange' => $baseDir . '/vendor_prefixed/square/square/src/Models/InventoryChange.php',
    'WPForms\\Vendor\\Square\\Models\\InventoryChangeType' => $baseDir . '/vendor_prefixed/square/square/src/Models/InventoryChangeType.php',
    'WPForms\\Vendor\\Square\\Models\\InventoryCount' => $baseDir . '/vendor_prefixed/square/square/src/Models/InventoryCount.php',
    'WPForms\\Vendor\\Square\\Models\\InventoryPhysicalCount' => $baseDir . '/vendor_prefixed/square/square/src/Models/InventoryPhysicalCount.php',
    'WPForms\\Vendor\\Square\\Models\\InventoryState' => $baseDir . '/vendor_prefixed/square/square/src/Models/InventoryState.php',
    'WPForms\\Vendor\\Square\\Models\\InventoryTransfer' => $baseDir . '/vendor_prefixed/square/square/src/Models/InventoryTransfer.php',
    'WPForms\\Vendor\\Square\\Models\\Invoice' => $baseDir . '/vendor_prefixed/square/square/src/Models/Invoice.php',
    'WPForms\\Vendor\\Square\\Models\\InvoiceAcceptedPaymentMethods' => $baseDir . '/vendor_prefixed/square/square/src/Models/InvoiceAcceptedPaymentMethods.php',
    'WPForms\\Vendor\\Square\\Models\\InvoiceAttachment' => $baseDir . '/vendor_prefixed/square/square/src/Models/InvoiceAttachment.php',
    'WPForms\\Vendor\\Square\\Models\\InvoiceAutomaticPaymentSource' => $baseDir . '/vendor_prefixed/square/square/src/Models/InvoiceAutomaticPaymentSource.php',
    'WPForms\\Vendor\\Square\\Models\\InvoiceCustomField' => $baseDir . '/vendor_prefixed/square/square/src/Models/InvoiceCustomField.php',
    'WPForms\\Vendor\\Square\\Models\\InvoiceCustomFieldPlacement' => $baseDir . '/vendor_prefixed/square/square/src/Models/InvoiceCustomFieldPlacement.php',
    'WPForms\\Vendor\\Square\\Models\\InvoiceDeliveryMethod' => $baseDir . '/vendor_prefixed/square/square/src/Models/InvoiceDeliveryMethod.php',
    'WPForms\\Vendor\\Square\\Models\\InvoiceFilter' => $baseDir . '/vendor_prefixed/square/square/src/Models/InvoiceFilter.php',
    'WPForms\\Vendor\\Square\\Models\\InvoicePaymentReminder' => $baseDir . '/vendor_prefixed/square/square/src/Models/InvoicePaymentReminder.php',
    'WPForms\\Vendor\\Square\\Models\\InvoicePaymentReminderStatus' => $baseDir . '/vendor_prefixed/square/square/src/Models/InvoicePaymentReminderStatus.php',
    'WPForms\\Vendor\\Square\\Models\\InvoicePaymentRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/InvoicePaymentRequest.php',
    'WPForms\\Vendor\\Square\\Models\\InvoiceQuery' => $baseDir . '/vendor_prefixed/square/square/src/Models/InvoiceQuery.php',
    'WPForms\\Vendor\\Square\\Models\\InvoiceRecipient' => $baseDir . '/vendor_prefixed/square/square/src/Models/InvoiceRecipient.php',
    'WPForms\\Vendor\\Square\\Models\\InvoiceRecipientTaxIds' => $baseDir . '/vendor_prefixed/square/square/src/Models/InvoiceRecipientTaxIds.php',
    'WPForms\\Vendor\\Square\\Models\\InvoiceRequestMethod' => $baseDir . '/vendor_prefixed/square/square/src/Models/InvoiceRequestMethod.php',
    'WPForms\\Vendor\\Square\\Models\\InvoiceRequestType' => $baseDir . '/vendor_prefixed/square/square/src/Models/InvoiceRequestType.php',
    'WPForms\\Vendor\\Square\\Models\\InvoiceSort' => $baseDir . '/vendor_prefixed/square/square/src/Models/InvoiceSort.php',
    'WPForms\\Vendor\\Square\\Models\\InvoiceSortField' => $baseDir . '/vendor_prefixed/square/square/src/Models/InvoiceSortField.php',
    'WPForms\\Vendor\\Square\\Models\\InvoiceStatus' => $baseDir . '/vendor_prefixed/square/square/src/Models/InvoiceStatus.php',
    'WPForms\\Vendor\\Square\\Models\\ItemVariationLocationOverrides' => $baseDir . '/vendor_prefixed/square/square/src/Models/ItemVariationLocationOverrides.php',
    'WPForms\\Vendor\\Square\\Models\\Job' => $baseDir . '/vendor_prefixed/square/square/src/Models/Job.php',
    'WPForms\\Vendor\\Square\\Models\\JobAssignment' => $baseDir . '/vendor_prefixed/square/square/src/Models/JobAssignment.php',
    'WPForms\\Vendor\\Square\\Models\\JobAssignmentPayType' => $baseDir . '/vendor_prefixed/square/square/src/Models/JobAssignmentPayType.php',
    'WPForms\\Vendor\\Square\\Models\\LinkCustomerToGiftCardRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/LinkCustomerToGiftCardRequest.php',
    'WPForms\\Vendor\\Square\\Models\\LinkCustomerToGiftCardResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/LinkCustomerToGiftCardResponse.php',
    'WPForms\\Vendor\\Square\\Models\\ListBankAccountsRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListBankAccountsRequest.php',
    'WPForms\\Vendor\\Square\\Models\\ListBankAccountsResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListBankAccountsResponse.php',
    'WPForms\\Vendor\\Square\\Models\\ListBookingCustomAttributeDefinitionsRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListBookingCustomAttributeDefinitionsRequest.php',
    'WPForms\\Vendor\\Square\\Models\\ListBookingCustomAttributeDefinitionsResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListBookingCustomAttributeDefinitionsResponse.php',
    'WPForms\\Vendor\\Square\\Models\\ListBookingCustomAttributesRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListBookingCustomAttributesRequest.php',
    'WPForms\\Vendor\\Square\\Models\\ListBookingCustomAttributesResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListBookingCustomAttributesResponse.php',
    'WPForms\\Vendor\\Square\\Models\\ListBookingsRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListBookingsRequest.php',
    'WPForms\\Vendor\\Square\\Models\\ListBookingsResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListBookingsResponse.php',
    'WPForms\\Vendor\\Square\\Models\\ListBreakTypesRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListBreakTypesRequest.php',
    'WPForms\\Vendor\\Square\\Models\\ListBreakTypesResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListBreakTypesResponse.php',
    'WPForms\\Vendor\\Square\\Models\\ListCardsRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListCardsRequest.php',
    'WPForms\\Vendor\\Square\\Models\\ListCardsResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListCardsResponse.php',
    'WPForms\\Vendor\\Square\\Models\\ListCashDrawerShiftEventsRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListCashDrawerShiftEventsRequest.php',
    'WPForms\\Vendor\\Square\\Models\\ListCashDrawerShiftEventsResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListCashDrawerShiftEventsResponse.php',
    'WPForms\\Vendor\\Square\\Models\\ListCashDrawerShiftsRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListCashDrawerShiftsRequest.php',
    'WPForms\\Vendor\\Square\\Models\\ListCashDrawerShiftsResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListCashDrawerShiftsResponse.php',
    'WPForms\\Vendor\\Square\\Models\\ListCatalogRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListCatalogRequest.php',
    'WPForms\\Vendor\\Square\\Models\\ListCatalogResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListCatalogResponse.php',
    'WPForms\\Vendor\\Square\\Models\\ListCustomerCustomAttributeDefinitionsRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListCustomerCustomAttributeDefinitionsRequest.php',
    'WPForms\\Vendor\\Square\\Models\\ListCustomerCustomAttributeDefinitionsResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListCustomerCustomAttributeDefinitionsResponse.php',
    'WPForms\\Vendor\\Square\\Models\\ListCustomerCustomAttributesRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListCustomerCustomAttributesRequest.php',
    'WPForms\\Vendor\\Square\\Models\\ListCustomerCustomAttributesResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListCustomerCustomAttributesResponse.php',
    'WPForms\\Vendor\\Square\\Models\\ListCustomerGroupsRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListCustomerGroupsRequest.php',
    'WPForms\\Vendor\\Square\\Models\\ListCustomerGroupsResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListCustomerGroupsResponse.php',
    'WPForms\\Vendor\\Square\\Models\\ListCustomerSegmentsRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListCustomerSegmentsRequest.php',
    'WPForms\\Vendor\\Square\\Models\\ListCustomerSegmentsResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListCustomerSegmentsResponse.php',
    'WPForms\\Vendor\\Square\\Models\\ListCustomersRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListCustomersRequest.php',
    'WPForms\\Vendor\\Square\\Models\\ListCustomersResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListCustomersResponse.php',
    'WPForms\\Vendor\\Square\\Models\\ListDeviceCodesRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListDeviceCodesRequest.php',
    'WPForms\\Vendor\\Square\\Models\\ListDeviceCodesResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListDeviceCodesResponse.php',
    'WPForms\\Vendor\\Square\\Models\\ListDevicesRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListDevicesRequest.php',
    'WPForms\\Vendor\\Square\\Models\\ListDevicesResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListDevicesResponse.php',
    'WPForms\\Vendor\\Square\\Models\\ListDisputeEvidenceRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListDisputeEvidenceRequest.php',
    'WPForms\\Vendor\\Square\\Models\\ListDisputeEvidenceResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListDisputeEvidenceResponse.php',
    'WPForms\\Vendor\\Square\\Models\\ListDisputesRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListDisputesRequest.php',
    'WPForms\\Vendor\\Square\\Models\\ListDisputesResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListDisputesResponse.php',
    'WPForms\\Vendor\\Square\\Models\\ListEmployeeWagesRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListEmployeeWagesRequest.php',
    'WPForms\\Vendor\\Square\\Models\\ListEmployeeWagesResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListEmployeeWagesResponse.php',
    'WPForms\\Vendor\\Square\\Models\\ListEmployeesRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListEmployeesRequest.php',
    'WPForms\\Vendor\\Square\\Models\\ListEmployeesResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListEmployeesResponse.php',
    'WPForms\\Vendor\\Square\\Models\\ListEventTypesRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListEventTypesRequest.php',
    'WPForms\\Vendor\\Square\\Models\\ListEventTypesResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListEventTypesResponse.php',
    'WPForms\\Vendor\\Square\\Models\\ListGiftCardActivitiesRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListGiftCardActivitiesRequest.php',
    'WPForms\\Vendor\\Square\\Models\\ListGiftCardActivitiesResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListGiftCardActivitiesResponse.php',
    'WPForms\\Vendor\\Square\\Models\\ListGiftCardsRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListGiftCardsRequest.php',
    'WPForms\\Vendor\\Square\\Models\\ListGiftCardsResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListGiftCardsResponse.php',
    'WPForms\\Vendor\\Square\\Models\\ListInvoicesRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListInvoicesRequest.php',
    'WPForms\\Vendor\\Square\\Models\\ListInvoicesResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListInvoicesResponse.php',
    'WPForms\\Vendor\\Square\\Models\\ListJobsRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListJobsRequest.php',
    'WPForms\\Vendor\\Square\\Models\\ListJobsResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListJobsResponse.php',
    'WPForms\\Vendor\\Square\\Models\\ListLocationBookingProfilesRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListLocationBookingProfilesRequest.php',
    'WPForms\\Vendor\\Square\\Models\\ListLocationBookingProfilesResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListLocationBookingProfilesResponse.php',
    'WPForms\\Vendor\\Square\\Models\\ListLocationCustomAttributeDefinitionsRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListLocationCustomAttributeDefinitionsRequest.php',
    'WPForms\\Vendor\\Square\\Models\\ListLocationCustomAttributeDefinitionsResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListLocationCustomAttributeDefinitionsResponse.php',
    'WPForms\\Vendor\\Square\\Models\\ListLocationCustomAttributesRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListLocationCustomAttributesRequest.php',
    'WPForms\\Vendor\\Square\\Models\\ListLocationCustomAttributesResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListLocationCustomAttributesResponse.php',
    'WPForms\\Vendor\\Square\\Models\\ListLocationsResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListLocationsResponse.php',
    'WPForms\\Vendor\\Square\\Models\\ListLoyaltyProgramsResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListLoyaltyProgramsResponse.php',
    'WPForms\\Vendor\\Square\\Models\\ListLoyaltyPromotionsRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListLoyaltyPromotionsRequest.php',
    'WPForms\\Vendor\\Square\\Models\\ListLoyaltyPromotionsResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListLoyaltyPromotionsResponse.php',
    'WPForms\\Vendor\\Square\\Models\\ListMerchantCustomAttributeDefinitionsRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListMerchantCustomAttributeDefinitionsRequest.php',
    'WPForms\\Vendor\\Square\\Models\\ListMerchantCustomAttributeDefinitionsResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListMerchantCustomAttributeDefinitionsResponse.php',
    'WPForms\\Vendor\\Square\\Models\\ListMerchantCustomAttributesRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListMerchantCustomAttributesRequest.php',
    'WPForms\\Vendor\\Square\\Models\\ListMerchantCustomAttributesResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListMerchantCustomAttributesResponse.php',
    'WPForms\\Vendor\\Square\\Models\\ListMerchantsRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListMerchantsRequest.php',
    'WPForms\\Vendor\\Square\\Models\\ListMerchantsResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListMerchantsResponse.php',
    'WPForms\\Vendor\\Square\\Models\\ListOrderCustomAttributeDefinitionsRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListOrderCustomAttributeDefinitionsRequest.php',
    'WPForms\\Vendor\\Square\\Models\\ListOrderCustomAttributeDefinitionsResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListOrderCustomAttributeDefinitionsResponse.php',
    'WPForms\\Vendor\\Square\\Models\\ListOrderCustomAttributesRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListOrderCustomAttributesRequest.php',
    'WPForms\\Vendor\\Square\\Models\\ListOrderCustomAttributesResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListOrderCustomAttributesResponse.php',
    'WPForms\\Vendor\\Square\\Models\\ListPaymentLinksRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListPaymentLinksRequest.php',
    'WPForms\\Vendor\\Square\\Models\\ListPaymentLinksResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListPaymentLinksResponse.php',
    'WPForms\\Vendor\\Square\\Models\\ListPaymentRefundsRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListPaymentRefundsRequest.php',
    'WPForms\\Vendor\\Square\\Models\\ListPaymentRefundsResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListPaymentRefundsResponse.php',
    'WPForms\\Vendor\\Square\\Models\\ListPaymentsRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListPaymentsRequest.php',
    'WPForms\\Vendor\\Square\\Models\\ListPaymentsResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListPaymentsResponse.php',
    'WPForms\\Vendor\\Square\\Models\\ListPayoutEntriesRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListPayoutEntriesRequest.php',
    'WPForms\\Vendor\\Square\\Models\\ListPayoutEntriesResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListPayoutEntriesResponse.php',
    'WPForms\\Vendor\\Square\\Models\\ListPayoutsRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListPayoutsRequest.php',
    'WPForms\\Vendor\\Square\\Models\\ListPayoutsResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListPayoutsResponse.php',
    'WPForms\\Vendor\\Square\\Models\\ListRefundsRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListRefundsRequest.php',
    'WPForms\\Vendor\\Square\\Models\\ListRefundsResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListRefundsResponse.php',
    'WPForms\\Vendor\\Square\\Models\\ListSitesResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListSitesResponse.php',
    'WPForms\\Vendor\\Square\\Models\\ListSubscriptionEventsRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListSubscriptionEventsRequest.php',
    'WPForms\\Vendor\\Square\\Models\\ListSubscriptionEventsResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListSubscriptionEventsResponse.php',
    'WPForms\\Vendor\\Square\\Models\\ListTeamMemberBookingProfilesRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListTeamMemberBookingProfilesRequest.php',
    'WPForms\\Vendor\\Square\\Models\\ListTeamMemberBookingProfilesResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListTeamMemberBookingProfilesResponse.php',
    'WPForms\\Vendor\\Square\\Models\\ListTeamMemberWagesRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListTeamMemberWagesRequest.php',
    'WPForms\\Vendor\\Square\\Models\\ListTeamMemberWagesResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListTeamMemberWagesResponse.php',
    'WPForms\\Vendor\\Square\\Models\\ListTransactionsRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListTransactionsRequest.php',
    'WPForms\\Vendor\\Square\\Models\\ListTransactionsResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListTransactionsResponse.php',
    'WPForms\\Vendor\\Square\\Models\\ListWebhookEventTypesRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListWebhookEventTypesRequest.php',
    'WPForms\\Vendor\\Square\\Models\\ListWebhookEventTypesResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListWebhookEventTypesResponse.php',
    'WPForms\\Vendor\\Square\\Models\\ListWebhookSubscriptionsRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListWebhookSubscriptionsRequest.php',
    'WPForms\\Vendor\\Square\\Models\\ListWebhookSubscriptionsResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListWebhookSubscriptionsResponse.php',
    'WPForms\\Vendor\\Square\\Models\\ListWorkweekConfigsRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListWorkweekConfigsRequest.php',
    'WPForms\\Vendor\\Square\\Models\\ListWorkweekConfigsResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/ListWorkweekConfigsResponse.php',
    'WPForms\\Vendor\\Square\\Models\\Location' => $baseDir . '/vendor_prefixed/square/square/src/Models/Location.php',
    'WPForms\\Vendor\\Square\\Models\\LocationBookingProfile' => $baseDir . '/vendor_prefixed/square/square/src/Models/LocationBookingProfile.php',
    'WPForms\\Vendor\\Square\\Models\\LocationCapability' => $baseDir . '/vendor_prefixed/square/square/src/Models/LocationCapability.php',
    'WPForms\\Vendor\\Square\\Models\\LocationStatus' => $baseDir . '/vendor_prefixed/square/square/src/Models/LocationStatus.php',
    'WPForms\\Vendor\\Square\\Models\\LocationType' => $baseDir . '/vendor_prefixed/square/square/src/Models/LocationType.php',
    'WPForms\\Vendor\\Square\\Models\\LoyaltyAccount' => $baseDir . '/vendor_prefixed/square/square/src/Models/LoyaltyAccount.php',
    'WPForms\\Vendor\\Square\\Models\\LoyaltyAccountExpiringPointDeadline' => $baseDir . '/vendor_prefixed/square/square/src/Models/LoyaltyAccountExpiringPointDeadline.php',
    'WPForms\\Vendor\\Square\\Models\\LoyaltyAccountMapping' => $baseDir . '/vendor_prefixed/square/square/src/Models/LoyaltyAccountMapping.php',
    'WPForms\\Vendor\\Square\\Models\\LoyaltyAccountMappingType' => $baseDir . '/vendor_prefixed/square/square/src/Models/LoyaltyAccountMappingType.php',
    'WPForms\\Vendor\\Square\\Models\\LoyaltyEvent' => $baseDir . '/vendor_prefixed/square/square/src/Models/LoyaltyEvent.php',
    'WPForms\\Vendor\\Square\\Models\\LoyaltyEventAccumulatePoints' => $baseDir . '/vendor_prefixed/square/square/src/Models/LoyaltyEventAccumulatePoints.php',
    'WPForms\\Vendor\\Square\\Models\\LoyaltyEventAccumulatePromotionPoints' => $baseDir . '/vendor_prefixed/square/square/src/Models/LoyaltyEventAccumulatePromotionPoints.php',
    'WPForms\\Vendor\\Square\\Models\\LoyaltyEventAdjustPoints' => $baseDir . '/vendor_prefixed/square/square/src/Models/LoyaltyEventAdjustPoints.php',
    'WPForms\\Vendor\\Square\\Models\\LoyaltyEventCreateReward' => $baseDir . '/vendor_prefixed/square/square/src/Models/LoyaltyEventCreateReward.php',
    'WPForms\\Vendor\\Square\\Models\\LoyaltyEventDateTimeFilter' => $baseDir . '/vendor_prefixed/square/square/src/Models/LoyaltyEventDateTimeFilter.php',
    'WPForms\\Vendor\\Square\\Models\\LoyaltyEventDeleteReward' => $baseDir . '/vendor_prefixed/square/square/src/Models/LoyaltyEventDeleteReward.php',
    'WPForms\\Vendor\\Square\\Models\\LoyaltyEventExpirePoints' => $baseDir . '/vendor_prefixed/square/square/src/Models/LoyaltyEventExpirePoints.php',
    'WPForms\\Vendor\\Square\\Models\\LoyaltyEventFilter' => $baseDir . '/vendor_prefixed/square/square/src/Models/LoyaltyEventFilter.php',
    'WPForms\\Vendor\\Square\\Models\\LoyaltyEventLocationFilter' => $baseDir . '/vendor_prefixed/square/square/src/Models/LoyaltyEventLocationFilter.php',
    'WPForms\\Vendor\\Square\\Models\\LoyaltyEventLoyaltyAccountFilter' => $baseDir . '/vendor_prefixed/square/square/src/Models/LoyaltyEventLoyaltyAccountFilter.php',
    'WPForms\\Vendor\\Square\\Models\\LoyaltyEventOrderFilter' => $baseDir . '/vendor_prefixed/square/square/src/Models/LoyaltyEventOrderFilter.php',
    'WPForms\\Vendor\\Square\\Models\\LoyaltyEventOther' => $baseDir . '/vendor_prefixed/square/square/src/Models/LoyaltyEventOther.php',
    'WPForms\\Vendor\\Square\\Models\\LoyaltyEventQuery' => $baseDir . '/vendor_prefixed/square/square/src/Models/LoyaltyEventQuery.php',
    'WPForms\\Vendor\\Square\\Models\\LoyaltyEventRedeemReward' => $baseDir . '/vendor_prefixed/square/square/src/Models/LoyaltyEventRedeemReward.php',
    'WPForms\\Vendor\\Square\\Models\\LoyaltyEventSource' => $baseDir . '/vendor_prefixed/square/square/src/Models/LoyaltyEventSource.php',
    'WPForms\\Vendor\\Square\\Models\\LoyaltyEventType' => $baseDir . '/vendor_prefixed/square/square/src/Models/LoyaltyEventType.php',
    'WPForms\\Vendor\\Square\\Models\\LoyaltyEventTypeFilter' => $baseDir . '/vendor_prefixed/square/square/src/Models/LoyaltyEventTypeFilter.php',
    'WPForms\\Vendor\\Square\\Models\\LoyaltyProgram' => $baseDir . '/vendor_prefixed/square/square/src/Models/LoyaltyProgram.php',
    'WPForms\\Vendor\\Square\\Models\\LoyaltyProgramAccrualRule' => $baseDir . '/vendor_prefixed/square/square/src/Models/LoyaltyProgramAccrualRule.php',
    'WPForms\\Vendor\\Square\\Models\\LoyaltyProgramAccrualRuleCategoryData' => $baseDir . '/vendor_prefixed/square/square/src/Models/LoyaltyProgramAccrualRuleCategoryData.php',
    'WPForms\\Vendor\\Square\\Models\\LoyaltyProgramAccrualRuleItemVariationData' => $baseDir . '/vendor_prefixed/square/square/src/Models/LoyaltyProgramAccrualRuleItemVariationData.php',
    'WPForms\\Vendor\\Square\\Models\\LoyaltyProgramAccrualRuleSpendData' => $baseDir . '/vendor_prefixed/square/square/src/Models/LoyaltyProgramAccrualRuleSpendData.php',
    'WPForms\\Vendor\\Square\\Models\\LoyaltyProgramAccrualRuleTaxMode' => $baseDir . '/vendor_prefixed/square/square/src/Models/LoyaltyProgramAccrualRuleTaxMode.php',
    'WPForms\\Vendor\\Square\\Models\\LoyaltyProgramAccrualRuleType' => $baseDir . '/vendor_prefixed/square/square/src/Models/LoyaltyProgramAccrualRuleType.php',
    'WPForms\\Vendor\\Square\\Models\\LoyaltyProgramAccrualRuleVisitData' => $baseDir . '/vendor_prefixed/square/square/src/Models/LoyaltyProgramAccrualRuleVisitData.php',
    'WPForms\\Vendor\\Square\\Models\\LoyaltyProgramExpirationPolicy' => $baseDir . '/vendor_prefixed/square/square/src/Models/LoyaltyProgramExpirationPolicy.php',
    'WPForms\\Vendor\\Square\\Models\\LoyaltyProgramRewardDefinition' => $baseDir . '/vendor_prefixed/square/square/src/Models/LoyaltyProgramRewardDefinition.php',
    'WPForms\\Vendor\\Square\\Models\\LoyaltyProgramRewardDefinitionScope' => $baseDir . '/vendor_prefixed/square/square/src/Models/LoyaltyProgramRewardDefinitionScope.php',
    'WPForms\\Vendor\\Square\\Models\\LoyaltyProgramRewardDefinitionType' => $baseDir . '/vendor_prefixed/square/square/src/Models/LoyaltyProgramRewardDefinitionType.php',
    'WPForms\\Vendor\\Square\\Models\\LoyaltyProgramRewardTier' => $baseDir . '/vendor_prefixed/square/square/src/Models/LoyaltyProgramRewardTier.php',
    'WPForms\\Vendor\\Square\\Models\\LoyaltyProgramStatus' => $baseDir . '/vendor_prefixed/square/square/src/Models/LoyaltyProgramStatus.php',
    'WPForms\\Vendor\\Square\\Models\\LoyaltyProgramTerminology' => $baseDir . '/vendor_prefixed/square/square/src/Models/LoyaltyProgramTerminology.php',
    'WPForms\\Vendor\\Square\\Models\\LoyaltyPromotion' => $baseDir . '/vendor_prefixed/square/square/src/Models/LoyaltyPromotion.php',
    'WPForms\\Vendor\\Square\\Models\\LoyaltyPromotionAvailableTimeData' => $baseDir . '/vendor_prefixed/square/square/src/Models/LoyaltyPromotionAvailableTimeData.php',
    'WPForms\\Vendor\\Square\\Models\\LoyaltyPromotionIncentive' => $baseDir . '/vendor_prefixed/square/square/src/Models/LoyaltyPromotionIncentive.php',
    'WPForms\\Vendor\\Square\\Models\\LoyaltyPromotionIncentivePointsAdditionData' => $baseDir . '/vendor_prefixed/square/square/src/Models/LoyaltyPromotionIncentivePointsAdditionData.php',
    'WPForms\\Vendor\\Square\\Models\\LoyaltyPromotionIncentivePointsMultiplierData' => $baseDir . '/vendor_prefixed/square/square/src/Models/LoyaltyPromotionIncentivePointsMultiplierData.php',
    'WPForms\\Vendor\\Square\\Models\\LoyaltyPromotionIncentiveType' => $baseDir . '/vendor_prefixed/square/square/src/Models/LoyaltyPromotionIncentiveType.php',
    'WPForms\\Vendor\\Square\\Models\\LoyaltyPromotionStatus' => $baseDir . '/vendor_prefixed/square/square/src/Models/LoyaltyPromotionStatus.php',
    'WPForms\\Vendor\\Square\\Models\\LoyaltyPromotionTriggerLimit' => $baseDir . '/vendor_prefixed/square/square/src/Models/LoyaltyPromotionTriggerLimit.php',
    'WPForms\\Vendor\\Square\\Models\\LoyaltyPromotionTriggerLimitInterval' => $baseDir . '/vendor_prefixed/square/square/src/Models/LoyaltyPromotionTriggerLimitInterval.php',
    'WPForms\\Vendor\\Square\\Models\\LoyaltyReward' => $baseDir . '/vendor_prefixed/square/square/src/Models/LoyaltyReward.php',
    'WPForms\\Vendor\\Square\\Models\\LoyaltyRewardStatus' => $baseDir . '/vendor_prefixed/square/square/src/Models/LoyaltyRewardStatus.php',
    'WPForms\\Vendor\\Square\\Models\\MBreak' => $baseDir . '/vendor_prefixed/square/square/src/Models/MBreak.php',
    'WPForms\\Vendor\\Square\\Models\\MeasurementUnit' => $baseDir . '/vendor_prefixed/square/square/src/Models/MeasurementUnit.php',
    'WPForms\\Vendor\\Square\\Models\\MeasurementUnitArea' => $baseDir . '/vendor_prefixed/square/square/src/Models/MeasurementUnitArea.php',
    'WPForms\\Vendor\\Square\\Models\\MeasurementUnitCustom' => $baseDir . '/vendor_prefixed/square/square/src/Models/MeasurementUnitCustom.php',
    'WPForms\\Vendor\\Square\\Models\\MeasurementUnitGeneric' => $baseDir . '/vendor_prefixed/square/square/src/Models/MeasurementUnitGeneric.php',
    'WPForms\\Vendor\\Square\\Models\\MeasurementUnitLength' => $baseDir . '/vendor_prefixed/square/square/src/Models/MeasurementUnitLength.php',
    'WPForms\\Vendor\\Square\\Models\\MeasurementUnitTime' => $baseDir . '/vendor_prefixed/square/square/src/Models/MeasurementUnitTime.php',
    'WPForms\\Vendor\\Square\\Models\\MeasurementUnitUnitType' => $baseDir . '/vendor_prefixed/square/square/src/Models/MeasurementUnitUnitType.php',
    'WPForms\\Vendor\\Square\\Models\\MeasurementUnitVolume' => $baseDir . '/vendor_prefixed/square/square/src/Models/MeasurementUnitVolume.php',
    'WPForms\\Vendor\\Square\\Models\\MeasurementUnitWeight' => $baseDir . '/vendor_prefixed/square/square/src/Models/MeasurementUnitWeight.php',
    'WPForms\\Vendor\\Square\\Models\\Merchant' => $baseDir . '/vendor_prefixed/square/square/src/Models/Merchant.php',
    'WPForms\\Vendor\\Square\\Models\\MerchantStatus' => $baseDir . '/vendor_prefixed/square/square/src/Models/MerchantStatus.php',
    'WPForms\\Vendor\\Square\\Models\\ModifierLocationOverrides' => $baseDir . '/vendor_prefixed/square/square/src/Models/ModifierLocationOverrides.php',
    'WPForms\\Vendor\\Square\\Models\\Money' => $baseDir . '/vendor_prefixed/square/square/src/Models/Money.php',
    'WPForms\\Vendor\\Square\\Models\\ObtainTokenRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/ObtainTokenRequest.php',
    'WPForms\\Vendor\\Square\\Models\\ObtainTokenResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/ObtainTokenResponse.php',
    'WPForms\\Vendor\\Square\\Models\\OfflinePaymentDetails' => $baseDir . '/vendor_prefixed/square/square/src/Models/OfflinePaymentDetails.php',
    'WPForms\\Vendor\\Square\\Models\\Order' => $baseDir . '/vendor_prefixed/square/square/src/Models/Order.php',
    'WPForms\\Vendor\\Square\\Models\\OrderCreated' => $baseDir . '/vendor_prefixed/square/square/src/Models/OrderCreated.php',
    'WPForms\\Vendor\\Square\\Models\\OrderCreatedObject' => $baseDir . '/vendor_prefixed/square/square/src/Models/OrderCreatedObject.php',
    'WPForms\\Vendor\\Square\\Models\\OrderEntry' => $baseDir . '/vendor_prefixed/square/square/src/Models/OrderEntry.php',
    'WPForms\\Vendor\\Square\\Models\\OrderFulfillment' => $baseDir . '/vendor_prefixed/square/square/src/Models/OrderFulfillment.php',
    'WPForms\\Vendor\\Square\\Models\\OrderFulfillmentDeliveryDetails' => $baseDir . '/vendor_prefixed/square/square/src/Models/OrderFulfillmentDeliveryDetails.php',
    'WPForms\\Vendor\\Square\\Models\\OrderFulfillmentDeliveryDetailsScheduleType' => $baseDir . '/vendor_prefixed/square/square/src/Models/OrderFulfillmentDeliveryDetailsScheduleType.php',
    'WPForms\\Vendor\\Square\\Models\\OrderFulfillmentFulfillmentEntry' => $baseDir . '/vendor_prefixed/square/square/src/Models/OrderFulfillmentFulfillmentEntry.php',
    'WPForms\\Vendor\\Square\\Models\\OrderFulfillmentFulfillmentLineItemApplication' => $baseDir . '/vendor_prefixed/square/square/src/Models/OrderFulfillmentFulfillmentLineItemApplication.php',
    'WPForms\\Vendor\\Square\\Models\\OrderFulfillmentPickupDetails' => $baseDir . '/vendor_prefixed/square/square/src/Models/OrderFulfillmentPickupDetails.php',
    'WPForms\\Vendor\\Square\\Models\\OrderFulfillmentPickupDetailsCurbsidePickupDetails' => $baseDir . '/vendor_prefixed/square/square/src/Models/OrderFulfillmentPickupDetailsCurbsidePickupDetails.php',
    'WPForms\\Vendor\\Square\\Models\\OrderFulfillmentPickupDetailsScheduleType' => $baseDir . '/vendor_prefixed/square/square/src/Models/OrderFulfillmentPickupDetailsScheduleType.php',
    'WPForms\\Vendor\\Square\\Models\\OrderFulfillmentRecipient' => $baseDir . '/vendor_prefixed/square/square/src/Models/OrderFulfillmentRecipient.php',
    'WPForms\\Vendor\\Square\\Models\\OrderFulfillmentShipmentDetails' => $baseDir . '/vendor_prefixed/square/square/src/Models/OrderFulfillmentShipmentDetails.php',
    'WPForms\\Vendor\\Square\\Models\\OrderFulfillmentState' => $baseDir . '/vendor_prefixed/square/square/src/Models/OrderFulfillmentState.php',
    'WPForms\\Vendor\\Square\\Models\\OrderFulfillmentType' => $baseDir . '/vendor_prefixed/square/square/src/Models/OrderFulfillmentType.php',
    'WPForms\\Vendor\\Square\\Models\\OrderFulfillmentUpdated' => $baseDir . '/vendor_prefixed/square/square/src/Models/OrderFulfillmentUpdated.php',
    'WPForms\\Vendor\\Square\\Models\\OrderFulfillmentUpdatedObject' => $baseDir . '/vendor_prefixed/square/square/src/Models/OrderFulfillmentUpdatedObject.php',
    'WPForms\\Vendor\\Square\\Models\\OrderFulfillmentUpdatedUpdate' => $baseDir . '/vendor_prefixed/square/square/src/Models/OrderFulfillmentUpdatedUpdate.php',
    'WPForms\\Vendor\\Square\\Models\\OrderLineItem' => $baseDir . '/vendor_prefixed/square/square/src/Models/OrderLineItem.php',
    'WPForms\\Vendor\\Square\\Models\\OrderLineItemAppliedDiscount' => $baseDir . '/vendor_prefixed/square/square/src/Models/OrderLineItemAppliedDiscount.php',
    'WPForms\\Vendor\\Square\\Models\\OrderLineItemAppliedServiceCharge' => $baseDir . '/vendor_prefixed/square/square/src/Models/OrderLineItemAppliedServiceCharge.php',
    'WPForms\\Vendor\\Square\\Models\\OrderLineItemAppliedTax' => $baseDir . '/vendor_prefixed/square/square/src/Models/OrderLineItemAppliedTax.php',
    'WPForms\\Vendor\\Square\\Models\\OrderLineItemDiscount' => $baseDir . '/vendor_prefixed/square/square/src/Models/OrderLineItemDiscount.php',
    'WPForms\\Vendor\\Square\\Models\\OrderLineItemDiscountScope' => $baseDir . '/vendor_prefixed/square/square/src/Models/OrderLineItemDiscountScope.php',
    'WPForms\\Vendor\\Square\\Models\\OrderLineItemDiscountType' => $baseDir . '/vendor_prefixed/square/square/src/Models/OrderLineItemDiscountType.php',
    'WPForms\\Vendor\\Square\\Models\\OrderLineItemItemType' => $baseDir . '/vendor_prefixed/square/square/src/Models/OrderLineItemItemType.php',
    'WPForms\\Vendor\\Square\\Models\\OrderLineItemModifier' => $baseDir . '/vendor_prefixed/square/square/src/Models/OrderLineItemModifier.php',
    'WPForms\\Vendor\\Square\\Models\\OrderLineItemPricingBlocklists' => $baseDir . '/vendor_prefixed/square/square/src/Models/OrderLineItemPricingBlocklists.php',
    'WPForms\\Vendor\\Square\\Models\\OrderLineItemPricingBlocklistsBlockedDiscount' => $baseDir . '/vendor_prefixed/square/square/src/Models/OrderLineItemPricingBlocklistsBlockedDiscount.php',
    'WPForms\\Vendor\\Square\\Models\\OrderLineItemPricingBlocklistsBlockedTax' => $baseDir . '/vendor_prefixed/square/square/src/Models/OrderLineItemPricingBlocklistsBlockedTax.php',
    'WPForms\\Vendor\\Square\\Models\\OrderLineItemTax' => $baseDir . '/vendor_prefixed/square/square/src/Models/OrderLineItemTax.php',
    'WPForms\\Vendor\\Square\\Models\\OrderLineItemTaxScope' => $baseDir . '/vendor_prefixed/square/square/src/Models/OrderLineItemTaxScope.php',
    'WPForms\\Vendor\\Square\\Models\\OrderLineItemTaxType' => $baseDir . '/vendor_prefixed/square/square/src/Models/OrderLineItemTaxType.php',
    'WPForms\\Vendor\\Square\\Models\\OrderMoneyAmounts' => $baseDir . '/vendor_prefixed/square/square/src/Models/OrderMoneyAmounts.php',
    'WPForms\\Vendor\\Square\\Models\\OrderPricingOptions' => $baseDir . '/vendor_prefixed/square/square/src/Models/OrderPricingOptions.php',
    'WPForms\\Vendor\\Square\\Models\\OrderQuantityUnit' => $baseDir . '/vendor_prefixed/square/square/src/Models/OrderQuantityUnit.php',
    'WPForms\\Vendor\\Square\\Models\\OrderReturn' => $baseDir . '/vendor_prefixed/square/square/src/Models/OrderReturn.php',
    'WPForms\\Vendor\\Square\\Models\\OrderReturnDiscount' => $baseDir . '/vendor_prefixed/square/square/src/Models/OrderReturnDiscount.php',
    'WPForms\\Vendor\\Square\\Models\\OrderReturnLineItem' => $baseDir . '/vendor_prefixed/square/square/src/Models/OrderReturnLineItem.php',
    'WPForms\\Vendor\\Square\\Models\\OrderReturnLineItemModifier' => $baseDir . '/vendor_prefixed/square/square/src/Models/OrderReturnLineItemModifier.php',
    'WPForms\\Vendor\\Square\\Models\\OrderReturnServiceCharge' => $baseDir . '/vendor_prefixed/square/square/src/Models/OrderReturnServiceCharge.php',
    'WPForms\\Vendor\\Square\\Models\\OrderReturnTax' => $baseDir . '/vendor_prefixed/square/square/src/Models/OrderReturnTax.php',
    'WPForms\\Vendor\\Square\\Models\\OrderReturnTip' => $baseDir . '/vendor_prefixed/square/square/src/Models/OrderReturnTip.php',
    'WPForms\\Vendor\\Square\\Models\\OrderReward' => $baseDir . '/vendor_prefixed/square/square/src/Models/OrderReward.php',
    'WPForms\\Vendor\\Square\\Models\\OrderRoundingAdjustment' => $baseDir . '/vendor_prefixed/square/square/src/Models/OrderRoundingAdjustment.php',
    'WPForms\\Vendor\\Square\\Models\\OrderServiceCharge' => $baseDir . '/vendor_prefixed/square/square/src/Models/OrderServiceCharge.php',
    'WPForms\\Vendor\\Square\\Models\\OrderServiceChargeCalculationPhase' => $baseDir . '/vendor_prefixed/square/square/src/Models/OrderServiceChargeCalculationPhase.php',
    'WPForms\\Vendor\\Square\\Models\\OrderServiceChargeScope' => $baseDir . '/vendor_prefixed/square/square/src/Models/OrderServiceChargeScope.php',
    'WPForms\\Vendor\\Square\\Models\\OrderServiceChargeTreatmentType' => $baseDir . '/vendor_prefixed/square/square/src/Models/OrderServiceChargeTreatmentType.php',
    'WPForms\\Vendor\\Square\\Models\\OrderServiceChargeType' => $baseDir . '/vendor_prefixed/square/square/src/Models/OrderServiceChargeType.php',
    'WPForms\\Vendor\\Square\\Models\\OrderSource' => $baseDir . '/vendor_prefixed/square/square/src/Models/OrderSource.php',
    'WPForms\\Vendor\\Square\\Models\\OrderState' => $baseDir . '/vendor_prefixed/square/square/src/Models/OrderState.php',
    'WPForms\\Vendor\\Square\\Models\\OrderUpdated' => $baseDir . '/vendor_prefixed/square/square/src/Models/OrderUpdated.php',
    'WPForms\\Vendor\\Square\\Models\\OrderUpdatedObject' => $baseDir . '/vendor_prefixed/square/square/src/Models/OrderUpdatedObject.php',
    'WPForms\\Vendor\\Square\\Models\\PaginationCursor' => $baseDir . '/vendor_prefixed/square/square/src/Models/PaginationCursor.php',
    'WPForms\\Vendor\\Square\\Models\\PauseSubscriptionRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/PauseSubscriptionRequest.php',
    'WPForms\\Vendor\\Square\\Models\\PauseSubscriptionResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/PauseSubscriptionResponse.php',
    'WPForms\\Vendor\\Square\\Models\\PayOrderRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/PayOrderRequest.php',
    'WPForms\\Vendor\\Square\\Models\\PayOrderResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/PayOrderResponse.php',
    'WPForms\\Vendor\\Square\\Models\\Payment' => $baseDir . '/vendor_prefixed/square/square/src/Models/Payment.php',
    'WPForms\\Vendor\\Square\\Models\\PaymentBalanceActivityAppFeeRefundDetail' => $baseDir . '/vendor_prefixed/square/square/src/Models/PaymentBalanceActivityAppFeeRefundDetail.php',
    'WPForms\\Vendor\\Square\\Models\\PaymentBalanceActivityAppFeeRevenueDetail' => $baseDir . '/vendor_prefixed/square/square/src/Models/PaymentBalanceActivityAppFeeRevenueDetail.php',
    'WPForms\\Vendor\\Square\\Models\\PaymentBalanceActivityAutomaticSavingsDetail' => $baseDir . '/vendor_prefixed/square/square/src/Models/PaymentBalanceActivityAutomaticSavingsDetail.php',
    'WPForms\\Vendor\\Square\\Models\\PaymentBalanceActivityAutomaticSavingsReversedDetail' => $baseDir . '/vendor_prefixed/square/square/src/Models/PaymentBalanceActivityAutomaticSavingsReversedDetail.php',
    'WPForms\\Vendor\\Square\\Models\\PaymentBalanceActivityChargeDetail' => $baseDir . '/vendor_prefixed/square/square/src/Models/PaymentBalanceActivityChargeDetail.php',
    'WPForms\\Vendor\\Square\\Models\\PaymentBalanceActivityDepositFeeDetail' => $baseDir . '/vendor_prefixed/square/square/src/Models/PaymentBalanceActivityDepositFeeDetail.php',
    'WPForms\\Vendor\\Square\\Models\\PaymentBalanceActivityDepositFeeReversedDetail' => $baseDir . '/vendor_prefixed/square/square/src/Models/PaymentBalanceActivityDepositFeeReversedDetail.php',
    'WPForms\\Vendor\\Square\\Models\\PaymentBalanceActivityDisputeDetail' => $baseDir . '/vendor_prefixed/square/square/src/Models/PaymentBalanceActivityDisputeDetail.php',
    'WPForms\\Vendor\\Square\\Models\\PaymentBalanceActivityFeeDetail' => $baseDir . '/vendor_prefixed/square/square/src/Models/PaymentBalanceActivityFeeDetail.php',
    'WPForms\\Vendor\\Square\\Models\\PaymentBalanceActivityFreeProcessingDetail' => $baseDir . '/vendor_prefixed/square/square/src/Models/PaymentBalanceActivityFreeProcessingDetail.php',
    'WPForms\\Vendor\\Square\\Models\\PaymentBalanceActivityHoldAdjustmentDetail' => $baseDir . '/vendor_prefixed/square/square/src/Models/PaymentBalanceActivityHoldAdjustmentDetail.php',
    'WPForms\\Vendor\\Square\\Models\\PaymentBalanceActivityOpenDisputeDetail' => $baseDir . '/vendor_prefixed/square/square/src/Models/PaymentBalanceActivityOpenDisputeDetail.php',
    'WPForms\\Vendor\\Square\\Models\\PaymentBalanceActivityOtherAdjustmentDetail' => $baseDir . '/vendor_prefixed/square/square/src/Models/PaymentBalanceActivityOtherAdjustmentDetail.php',
    'WPForms\\Vendor\\Square\\Models\\PaymentBalanceActivityOtherDetail' => $baseDir . '/vendor_prefixed/square/square/src/Models/PaymentBalanceActivityOtherDetail.php',
    'WPForms\\Vendor\\Square\\Models\\PaymentBalanceActivityRefundDetail' => $baseDir . '/vendor_prefixed/square/square/src/Models/PaymentBalanceActivityRefundDetail.php',
    'WPForms\\Vendor\\Square\\Models\\PaymentBalanceActivityReleaseAdjustmentDetail' => $baseDir . '/vendor_prefixed/square/square/src/Models/PaymentBalanceActivityReleaseAdjustmentDetail.php',
    'WPForms\\Vendor\\Square\\Models\\PaymentBalanceActivityReserveHoldDetail' => $baseDir . '/vendor_prefixed/square/square/src/Models/PaymentBalanceActivityReserveHoldDetail.php',
    'WPForms\\Vendor\\Square\\Models\\PaymentBalanceActivityReserveReleaseDetail' => $baseDir . '/vendor_prefixed/square/square/src/Models/PaymentBalanceActivityReserveReleaseDetail.php',
    'WPForms\\Vendor\\Square\\Models\\PaymentBalanceActivitySquareCapitalPaymentDetail' => $baseDir . '/vendor_prefixed/square/square/src/Models/PaymentBalanceActivitySquareCapitalPaymentDetail.php',
    'WPForms\\Vendor\\Square\\Models\\PaymentBalanceActivitySquareCapitalReversedPaymentDetail' => $baseDir . '/vendor_prefixed/square/square/src/Models/PaymentBalanceActivitySquareCapitalReversedPaymentDetail.php',
    'WPForms\\Vendor\\Square\\Models\\PaymentBalanceActivitySquarePayrollTransferDetail' => $baseDir . '/vendor_prefixed/square/square/src/Models/PaymentBalanceActivitySquarePayrollTransferDetail.php',
    'WPForms\\Vendor\\Square\\Models\\PaymentBalanceActivitySquarePayrollTransferReversedDetail' => $baseDir . '/vendor_prefixed/square/square/src/Models/PaymentBalanceActivitySquarePayrollTransferReversedDetail.php',
    'WPForms\\Vendor\\Square\\Models\\PaymentBalanceActivityTaxOnFeeDetail' => $baseDir . '/vendor_prefixed/square/square/src/Models/PaymentBalanceActivityTaxOnFeeDetail.php',
    'WPForms\\Vendor\\Square\\Models\\PaymentBalanceActivityThirdPartyFeeDetail' => $baseDir . '/vendor_prefixed/square/square/src/Models/PaymentBalanceActivityThirdPartyFeeDetail.php',
    'WPForms\\Vendor\\Square\\Models\\PaymentBalanceActivityThirdPartyFeeRefundDetail' => $baseDir . '/vendor_prefixed/square/square/src/Models/PaymentBalanceActivityThirdPartyFeeRefundDetail.php',
    'WPForms\\Vendor\\Square\\Models\\PaymentLink' => $baseDir . '/vendor_prefixed/square/square/src/Models/PaymentLink.php',
    'WPForms\\Vendor\\Square\\Models\\PaymentLinkRelatedResources' => $baseDir . '/vendor_prefixed/square/square/src/Models/PaymentLinkRelatedResources.php',
    'WPForms\\Vendor\\Square\\Models\\PaymentOptions' => $baseDir . '/vendor_prefixed/square/square/src/Models/PaymentOptions.php',
    'WPForms\\Vendor\\Square\\Models\\PaymentOptionsDelayAction' => $baseDir . '/vendor_prefixed/square/square/src/Models/PaymentOptionsDelayAction.php',
    'WPForms\\Vendor\\Square\\Models\\PaymentRefund' => $baseDir . '/vendor_prefixed/square/square/src/Models/PaymentRefund.php',
    'WPForms\\Vendor\\Square\\Models\\PaymentSortField' => $baseDir . '/vendor_prefixed/square/square/src/Models/PaymentSortField.php',
    'WPForms\\Vendor\\Square\\Models\\Payout' => $baseDir . '/vendor_prefixed/square/square/src/Models/Payout.php',
    'WPForms\\Vendor\\Square\\Models\\PayoutEntry' => $baseDir . '/vendor_prefixed/square/square/src/Models/PayoutEntry.php',
    'WPForms\\Vendor\\Square\\Models\\PayoutFee' => $baseDir . '/vendor_prefixed/square/square/src/Models/PayoutFee.php',
    'WPForms\\Vendor\\Square\\Models\\PayoutFeeType' => $baseDir . '/vendor_prefixed/square/square/src/Models/PayoutFeeType.php',
    'WPForms\\Vendor\\Square\\Models\\PayoutStatus' => $baseDir . '/vendor_prefixed/square/square/src/Models/PayoutStatus.php',
    'WPForms\\Vendor\\Square\\Models\\PayoutType' => $baseDir . '/vendor_prefixed/square/square/src/Models/PayoutType.php',
    'WPForms\\Vendor\\Square\\Models\\Phase' => $baseDir . '/vendor_prefixed/square/square/src/Models/Phase.php',
    'WPForms\\Vendor\\Square\\Models\\PhaseInput' => $baseDir . '/vendor_prefixed/square/square/src/Models/PhaseInput.php',
    'WPForms\\Vendor\\Square\\Models\\PrePopulatedData' => $baseDir . '/vendor_prefixed/square/square/src/Models/PrePopulatedData.php',
    'WPForms\\Vendor\\Square\\Models\\ProcessingFee' => $baseDir . '/vendor_prefixed/square/square/src/Models/ProcessingFee.php',
    'WPForms\\Vendor\\Square\\Models\\Product' => $baseDir . '/vendor_prefixed/square/square/src/Models/Product.php',
    'WPForms\\Vendor\\Square\\Models\\ProductType' => $baseDir . '/vendor_prefixed/square/square/src/Models/ProductType.php',
    'WPForms\\Vendor\\Square\\Models\\PublishInvoiceRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/PublishInvoiceRequest.php',
    'WPForms\\Vendor\\Square\\Models\\PublishInvoiceResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/PublishInvoiceResponse.php',
    'WPForms\\Vendor\\Square\\Models\\QrCodeOptions' => $baseDir . '/vendor_prefixed/square/square/src/Models/QrCodeOptions.php',
    'WPForms\\Vendor\\Square\\Models\\QuantityRatio' => $baseDir . '/vendor_prefixed/square/square/src/Models/QuantityRatio.php',
    'WPForms\\Vendor\\Square\\Models\\QuickPay' => $baseDir . '/vendor_prefixed/square/square/src/Models/QuickPay.php',
    'WPForms\\Vendor\\Square\\Models\\Range' => $baseDir . '/vendor_prefixed/square/square/src/Models/Range.php',
    'WPForms\\Vendor\\Square\\Models\\ReceiptOptions' => $baseDir . '/vendor_prefixed/square/square/src/Models/ReceiptOptions.php',
    'WPForms\\Vendor\\Square\\Models\\RedeemLoyaltyRewardRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/RedeemLoyaltyRewardRequest.php',
    'WPForms\\Vendor\\Square\\Models\\RedeemLoyaltyRewardResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/RedeemLoyaltyRewardResponse.php',
    'WPForms\\Vendor\\Square\\Models\\Refund' => $baseDir . '/vendor_prefixed/square/square/src/Models/Refund.php',
    'WPForms\\Vendor\\Square\\Models\\RefundPaymentRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/RefundPaymentRequest.php',
    'WPForms\\Vendor\\Square\\Models\\RefundPaymentResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/RefundPaymentResponse.php',
    'WPForms\\Vendor\\Square\\Models\\RefundStatus' => $baseDir . '/vendor_prefixed/square/square/src/Models/RefundStatus.php',
    'WPForms\\Vendor\\Square\\Models\\RegisterDomainRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/RegisterDomainRequest.php',
    'WPForms\\Vendor\\Square\\Models\\RegisterDomainResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/RegisterDomainResponse.php',
    'WPForms\\Vendor\\Square\\Models\\RegisterDomainResponseStatus' => $baseDir . '/vendor_prefixed/square/square/src/Models/RegisterDomainResponseStatus.php',
    'WPForms\\Vendor\\Square\\Models\\RemoveGroupFromCustomerResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/RemoveGroupFromCustomerResponse.php',
    'WPForms\\Vendor\\Square\\Models\\ResumeSubscriptionRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/ResumeSubscriptionRequest.php',
    'WPForms\\Vendor\\Square\\Models\\ResumeSubscriptionResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/ResumeSubscriptionResponse.php',
    'WPForms\\Vendor\\Square\\Models\\RetrieveBookingCustomAttributeDefinitionRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/RetrieveBookingCustomAttributeDefinitionRequest.php',
    'WPForms\\Vendor\\Square\\Models\\RetrieveBookingCustomAttributeDefinitionResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/RetrieveBookingCustomAttributeDefinitionResponse.php',
    'WPForms\\Vendor\\Square\\Models\\RetrieveBookingCustomAttributeRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/RetrieveBookingCustomAttributeRequest.php',
    'WPForms\\Vendor\\Square\\Models\\RetrieveBookingCustomAttributeResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/RetrieveBookingCustomAttributeResponse.php',
    'WPForms\\Vendor\\Square\\Models\\RetrieveBookingResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/RetrieveBookingResponse.php',
    'WPForms\\Vendor\\Square\\Models\\RetrieveBusinessBookingProfileResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/RetrieveBusinessBookingProfileResponse.php',
    'WPForms\\Vendor\\Square\\Models\\RetrieveCardResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/RetrieveCardResponse.php',
    'WPForms\\Vendor\\Square\\Models\\RetrieveCashDrawerShiftRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/RetrieveCashDrawerShiftRequest.php',
    'WPForms\\Vendor\\Square\\Models\\RetrieveCashDrawerShiftResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/RetrieveCashDrawerShiftResponse.php',
    'WPForms\\Vendor\\Square\\Models\\RetrieveCatalogObjectRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/RetrieveCatalogObjectRequest.php',
    'WPForms\\Vendor\\Square\\Models\\RetrieveCatalogObjectResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/RetrieveCatalogObjectResponse.php',
    'WPForms\\Vendor\\Square\\Models\\RetrieveCustomerCustomAttributeDefinitionRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/RetrieveCustomerCustomAttributeDefinitionRequest.php',
    'WPForms\\Vendor\\Square\\Models\\RetrieveCustomerCustomAttributeDefinitionResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/RetrieveCustomerCustomAttributeDefinitionResponse.php',
    'WPForms\\Vendor\\Square\\Models\\RetrieveCustomerCustomAttributeRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/RetrieveCustomerCustomAttributeRequest.php',
    'WPForms\\Vendor\\Square\\Models\\RetrieveCustomerCustomAttributeResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/RetrieveCustomerCustomAttributeResponse.php',
    'WPForms\\Vendor\\Square\\Models\\RetrieveCustomerGroupResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/RetrieveCustomerGroupResponse.php',
    'WPForms\\Vendor\\Square\\Models\\RetrieveCustomerResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/RetrieveCustomerResponse.php',
    'WPForms\\Vendor\\Square\\Models\\RetrieveCustomerSegmentResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/RetrieveCustomerSegmentResponse.php',
    'WPForms\\Vendor\\Square\\Models\\RetrieveDisputeEvidenceResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/RetrieveDisputeEvidenceResponse.php',
    'WPForms\\Vendor\\Square\\Models\\RetrieveDisputeResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/RetrieveDisputeResponse.php',
    'WPForms\\Vendor\\Square\\Models\\RetrieveEmployeeResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/RetrieveEmployeeResponse.php',
    'WPForms\\Vendor\\Square\\Models\\RetrieveGiftCardFromGANRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/RetrieveGiftCardFromGANRequest.php',
    'WPForms\\Vendor\\Square\\Models\\RetrieveGiftCardFromGANResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/RetrieveGiftCardFromGANResponse.php',
    'WPForms\\Vendor\\Square\\Models\\RetrieveGiftCardFromNonceRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/RetrieveGiftCardFromNonceRequest.php',
    'WPForms\\Vendor\\Square\\Models\\RetrieveGiftCardFromNonceResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/RetrieveGiftCardFromNonceResponse.php',
    'WPForms\\Vendor\\Square\\Models\\RetrieveGiftCardResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/RetrieveGiftCardResponse.php',
    'WPForms\\Vendor\\Square\\Models\\RetrieveInventoryAdjustmentResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/RetrieveInventoryAdjustmentResponse.php',
    'WPForms\\Vendor\\Square\\Models\\RetrieveInventoryChangesRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/RetrieveInventoryChangesRequest.php',
    'WPForms\\Vendor\\Square\\Models\\RetrieveInventoryChangesResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/RetrieveInventoryChangesResponse.php',
    'WPForms\\Vendor\\Square\\Models\\RetrieveInventoryCountRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/RetrieveInventoryCountRequest.php',
    'WPForms\\Vendor\\Square\\Models\\RetrieveInventoryCountResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/RetrieveInventoryCountResponse.php',
    'WPForms\\Vendor\\Square\\Models\\RetrieveInventoryPhysicalCountResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/RetrieveInventoryPhysicalCountResponse.php',
    'WPForms\\Vendor\\Square\\Models\\RetrieveInventoryTransferResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/RetrieveInventoryTransferResponse.php',
    'WPForms\\Vendor\\Square\\Models\\RetrieveJobResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/RetrieveJobResponse.php',
    'WPForms\\Vendor\\Square\\Models\\RetrieveLocationBookingProfileResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/RetrieveLocationBookingProfileResponse.php',
    'WPForms\\Vendor\\Square\\Models\\RetrieveLocationCustomAttributeDefinitionRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/RetrieveLocationCustomAttributeDefinitionRequest.php',
    'WPForms\\Vendor\\Square\\Models\\RetrieveLocationCustomAttributeDefinitionResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/RetrieveLocationCustomAttributeDefinitionResponse.php',
    'WPForms\\Vendor\\Square\\Models\\RetrieveLocationCustomAttributeRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/RetrieveLocationCustomAttributeRequest.php',
    'WPForms\\Vendor\\Square\\Models\\RetrieveLocationCustomAttributeResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/RetrieveLocationCustomAttributeResponse.php',
    'WPForms\\Vendor\\Square\\Models\\RetrieveLocationResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/RetrieveLocationResponse.php',
    'WPForms\\Vendor\\Square\\Models\\RetrieveLocationSettingsResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/RetrieveLocationSettingsResponse.php',
    'WPForms\\Vendor\\Square\\Models\\RetrieveLoyaltyAccountResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/RetrieveLoyaltyAccountResponse.php',
    'WPForms\\Vendor\\Square\\Models\\RetrieveLoyaltyProgramResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/RetrieveLoyaltyProgramResponse.php',
    'WPForms\\Vendor\\Square\\Models\\RetrieveLoyaltyPromotionResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/RetrieveLoyaltyPromotionResponse.php',
    'WPForms\\Vendor\\Square\\Models\\RetrieveLoyaltyRewardResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/RetrieveLoyaltyRewardResponse.php',
    'WPForms\\Vendor\\Square\\Models\\RetrieveMerchantCustomAttributeDefinitionRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/RetrieveMerchantCustomAttributeDefinitionRequest.php',
    'WPForms\\Vendor\\Square\\Models\\RetrieveMerchantCustomAttributeDefinitionResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/RetrieveMerchantCustomAttributeDefinitionResponse.php',
    'WPForms\\Vendor\\Square\\Models\\RetrieveMerchantCustomAttributeRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/RetrieveMerchantCustomAttributeRequest.php',
    'WPForms\\Vendor\\Square\\Models\\RetrieveMerchantCustomAttributeResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/RetrieveMerchantCustomAttributeResponse.php',
    'WPForms\\Vendor\\Square\\Models\\RetrieveMerchantResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/RetrieveMerchantResponse.php',
    'WPForms\\Vendor\\Square\\Models\\RetrieveMerchantSettingsResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/RetrieveMerchantSettingsResponse.php',
    'WPForms\\Vendor\\Square\\Models\\RetrieveOrderCustomAttributeDefinitionRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/RetrieveOrderCustomAttributeDefinitionRequest.php',
    'WPForms\\Vendor\\Square\\Models\\RetrieveOrderCustomAttributeDefinitionResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/RetrieveOrderCustomAttributeDefinitionResponse.php',
    'WPForms\\Vendor\\Square\\Models\\RetrieveOrderCustomAttributeRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/RetrieveOrderCustomAttributeRequest.php',
    'WPForms\\Vendor\\Square\\Models\\RetrieveOrderCustomAttributeResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/RetrieveOrderCustomAttributeResponse.php',
    'WPForms\\Vendor\\Square\\Models\\RetrieveOrderResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/RetrieveOrderResponse.php',
    'WPForms\\Vendor\\Square\\Models\\RetrievePaymentLinkResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/RetrievePaymentLinkResponse.php',
    'WPForms\\Vendor\\Square\\Models\\RetrieveSnippetResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/RetrieveSnippetResponse.php',
    'WPForms\\Vendor\\Square\\Models\\RetrieveSubscriptionRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/RetrieveSubscriptionRequest.php',
    'WPForms\\Vendor\\Square\\Models\\RetrieveSubscriptionResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/RetrieveSubscriptionResponse.php',
    'WPForms\\Vendor\\Square\\Models\\RetrieveTeamMemberBookingProfileResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/RetrieveTeamMemberBookingProfileResponse.php',
    'WPForms\\Vendor\\Square\\Models\\RetrieveTeamMemberResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/RetrieveTeamMemberResponse.php',
    'WPForms\\Vendor\\Square\\Models\\RetrieveTokenStatusResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/RetrieveTokenStatusResponse.php',
    'WPForms\\Vendor\\Square\\Models\\RetrieveTransactionResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/RetrieveTransactionResponse.php',
    'WPForms\\Vendor\\Square\\Models\\RetrieveVendorResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/RetrieveVendorResponse.php',
    'WPForms\\Vendor\\Square\\Models\\RetrieveWageSettingResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/RetrieveWageSettingResponse.php',
    'WPForms\\Vendor\\Square\\Models\\RetrieveWebhookSubscriptionResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/RetrieveWebhookSubscriptionResponse.php',
    'WPForms\\Vendor\\Square\\Models\\RevokeTokenRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/RevokeTokenRequest.php',
    'WPForms\\Vendor\\Square\\Models\\RevokeTokenResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/RevokeTokenResponse.php',
    'WPForms\\Vendor\\Square\\Models\\RiskEvaluation' => $baseDir . '/vendor_prefixed/square/square/src/Models/RiskEvaluation.php',
    'WPForms\\Vendor\\Square\\Models\\RiskEvaluationRiskLevel' => $baseDir . '/vendor_prefixed/square/square/src/Models/RiskEvaluationRiskLevel.php',
    'WPForms\\Vendor\\Square\\Models\\SaveCardOptions' => $baseDir . '/vendor_prefixed/square/square/src/Models/SaveCardOptions.php',
    'WPForms\\Vendor\\Square\\Models\\SearchAvailabilityFilter' => $baseDir . '/vendor_prefixed/square/square/src/Models/SearchAvailabilityFilter.php',
    'WPForms\\Vendor\\Square\\Models\\SearchAvailabilityQuery' => $baseDir . '/vendor_prefixed/square/square/src/Models/SearchAvailabilityQuery.php',
    'WPForms\\Vendor\\Square\\Models\\SearchAvailabilityRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/SearchAvailabilityRequest.php',
    'WPForms\\Vendor\\Square\\Models\\SearchAvailabilityResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/SearchAvailabilityResponse.php',
    'WPForms\\Vendor\\Square\\Models\\SearchCatalogItemsRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/SearchCatalogItemsRequest.php',
    'WPForms\\Vendor\\Square\\Models\\SearchCatalogItemsRequestStockLevel' => $baseDir . '/vendor_prefixed/square/square/src/Models/SearchCatalogItemsRequestStockLevel.php',
    'WPForms\\Vendor\\Square\\Models\\SearchCatalogItemsResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/SearchCatalogItemsResponse.php',
    'WPForms\\Vendor\\Square\\Models\\SearchCatalogObjectsRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/SearchCatalogObjectsRequest.php',
    'WPForms\\Vendor\\Square\\Models\\SearchCatalogObjectsResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/SearchCatalogObjectsResponse.php',
    'WPForms\\Vendor\\Square\\Models\\SearchCustomersRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/SearchCustomersRequest.php',
    'WPForms\\Vendor\\Square\\Models\\SearchCustomersResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/SearchCustomersResponse.php',
    'WPForms\\Vendor\\Square\\Models\\SearchEventsFilter' => $baseDir . '/vendor_prefixed/square/square/src/Models/SearchEventsFilter.php',
    'WPForms\\Vendor\\Square\\Models\\SearchEventsQuery' => $baseDir . '/vendor_prefixed/square/square/src/Models/SearchEventsQuery.php',
    'WPForms\\Vendor\\Square\\Models\\SearchEventsRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/SearchEventsRequest.php',
    'WPForms\\Vendor\\Square\\Models\\SearchEventsResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/SearchEventsResponse.php',
    'WPForms\\Vendor\\Square\\Models\\SearchEventsSort' => $baseDir . '/vendor_prefixed/square/square/src/Models/SearchEventsSort.php',
    'WPForms\\Vendor\\Square\\Models\\SearchEventsSortField' => $baseDir . '/vendor_prefixed/square/square/src/Models/SearchEventsSortField.php',
    'WPForms\\Vendor\\Square\\Models\\SearchInvoicesRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/SearchInvoicesRequest.php',
    'WPForms\\Vendor\\Square\\Models\\SearchInvoicesResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/SearchInvoicesResponse.php',
    'WPForms\\Vendor\\Square\\Models\\SearchLoyaltyAccountsRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/SearchLoyaltyAccountsRequest.php',
    'WPForms\\Vendor\\Square\\Models\\SearchLoyaltyAccountsRequestLoyaltyAccountQuery' => $baseDir . '/vendor_prefixed/square/square/src/Models/SearchLoyaltyAccountsRequestLoyaltyAccountQuery.php',
    'WPForms\\Vendor\\Square\\Models\\SearchLoyaltyAccountsResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/SearchLoyaltyAccountsResponse.php',
    'WPForms\\Vendor\\Square\\Models\\SearchLoyaltyEventsRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/SearchLoyaltyEventsRequest.php',
    'WPForms\\Vendor\\Square\\Models\\SearchLoyaltyEventsResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/SearchLoyaltyEventsResponse.php',
    'WPForms\\Vendor\\Square\\Models\\SearchLoyaltyRewardsRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/SearchLoyaltyRewardsRequest.php',
    'WPForms\\Vendor\\Square\\Models\\SearchLoyaltyRewardsRequestLoyaltyRewardQuery' => $baseDir . '/vendor_prefixed/square/square/src/Models/SearchLoyaltyRewardsRequestLoyaltyRewardQuery.php',
    'WPForms\\Vendor\\Square\\Models\\SearchLoyaltyRewardsResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/SearchLoyaltyRewardsResponse.php',
    'WPForms\\Vendor\\Square\\Models\\SearchOrdersCustomerFilter' => $baseDir . '/vendor_prefixed/square/square/src/Models/SearchOrdersCustomerFilter.php',
    'WPForms\\Vendor\\Square\\Models\\SearchOrdersDateTimeFilter' => $baseDir . '/vendor_prefixed/square/square/src/Models/SearchOrdersDateTimeFilter.php',
    'WPForms\\Vendor\\Square\\Models\\SearchOrdersFilter' => $baseDir . '/vendor_prefixed/square/square/src/Models/SearchOrdersFilter.php',
    'WPForms\\Vendor\\Square\\Models\\SearchOrdersFulfillmentFilter' => $baseDir . '/vendor_prefixed/square/square/src/Models/SearchOrdersFulfillmentFilter.php',
    'WPForms\\Vendor\\Square\\Models\\SearchOrdersQuery' => $baseDir . '/vendor_prefixed/square/square/src/Models/SearchOrdersQuery.php',
    'WPForms\\Vendor\\Square\\Models\\SearchOrdersRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/SearchOrdersRequest.php',
    'WPForms\\Vendor\\Square\\Models\\SearchOrdersResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/SearchOrdersResponse.php',
    'WPForms\\Vendor\\Square\\Models\\SearchOrdersSort' => $baseDir . '/vendor_prefixed/square/square/src/Models/SearchOrdersSort.php',
    'WPForms\\Vendor\\Square\\Models\\SearchOrdersSortField' => $baseDir . '/vendor_prefixed/square/square/src/Models/SearchOrdersSortField.php',
    'WPForms\\Vendor\\Square\\Models\\SearchOrdersSourceFilter' => $baseDir . '/vendor_prefixed/square/square/src/Models/SearchOrdersSourceFilter.php',
    'WPForms\\Vendor\\Square\\Models\\SearchOrdersStateFilter' => $baseDir . '/vendor_prefixed/square/square/src/Models/SearchOrdersStateFilter.php',
    'WPForms\\Vendor\\Square\\Models\\SearchShiftsRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/SearchShiftsRequest.php',
    'WPForms\\Vendor\\Square\\Models\\SearchShiftsResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/SearchShiftsResponse.php',
    'WPForms\\Vendor\\Square\\Models\\SearchSubscriptionsFilter' => $baseDir . '/vendor_prefixed/square/square/src/Models/SearchSubscriptionsFilter.php',
    'WPForms\\Vendor\\Square\\Models\\SearchSubscriptionsQuery' => $baseDir . '/vendor_prefixed/square/square/src/Models/SearchSubscriptionsQuery.php',
    'WPForms\\Vendor\\Square\\Models\\SearchSubscriptionsRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/SearchSubscriptionsRequest.php',
    'WPForms\\Vendor\\Square\\Models\\SearchSubscriptionsResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/SearchSubscriptionsResponse.php',
    'WPForms\\Vendor\\Square\\Models\\SearchTeamMembersFilter' => $baseDir . '/vendor_prefixed/square/square/src/Models/SearchTeamMembersFilter.php',
    'WPForms\\Vendor\\Square\\Models\\SearchTeamMembersQuery' => $baseDir . '/vendor_prefixed/square/square/src/Models/SearchTeamMembersQuery.php',
    'WPForms\\Vendor\\Square\\Models\\SearchTeamMembersRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/SearchTeamMembersRequest.php',
    'WPForms\\Vendor\\Square\\Models\\SearchTeamMembersResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/SearchTeamMembersResponse.php',
    'WPForms\\Vendor\\Square\\Models\\SearchTerminalActionsRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/SearchTerminalActionsRequest.php',
    'WPForms\\Vendor\\Square\\Models\\SearchTerminalActionsResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/SearchTerminalActionsResponse.php',
    'WPForms\\Vendor\\Square\\Models\\SearchTerminalCheckoutsRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/SearchTerminalCheckoutsRequest.php',
    'WPForms\\Vendor\\Square\\Models\\SearchTerminalCheckoutsResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/SearchTerminalCheckoutsResponse.php',
    'WPForms\\Vendor\\Square\\Models\\SearchTerminalRefundsRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/SearchTerminalRefundsRequest.php',
    'WPForms\\Vendor\\Square\\Models\\SearchTerminalRefundsResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/SearchTerminalRefundsResponse.php',
    'WPForms\\Vendor\\Square\\Models\\SearchVendorsRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/SearchVendorsRequest.php',
    'WPForms\\Vendor\\Square\\Models\\SearchVendorsRequestFilter' => $baseDir . '/vendor_prefixed/square/square/src/Models/SearchVendorsRequestFilter.php',
    'WPForms\\Vendor\\Square\\Models\\SearchVendorsRequestSort' => $baseDir . '/vendor_prefixed/square/square/src/Models/SearchVendorsRequestSort.php',
    'WPForms\\Vendor\\Square\\Models\\SearchVendorsRequestSortField' => $baseDir . '/vendor_prefixed/square/square/src/Models/SearchVendorsRequestSortField.php',
    'WPForms\\Vendor\\Square\\Models\\SearchVendorsResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/SearchVendorsResponse.php',
    'WPForms\\Vendor\\Square\\Models\\SegmentFilter' => $baseDir . '/vendor_prefixed/square/square/src/Models/SegmentFilter.php',
    'WPForms\\Vendor\\Square\\Models\\SelectOption' => $baseDir . '/vendor_prefixed/square/square/src/Models/SelectOption.php',
    'WPForms\\Vendor\\Square\\Models\\SelectOptions' => $baseDir . '/vendor_prefixed/square/square/src/Models/SelectOptions.php',
    'WPForms\\Vendor\\Square\\Models\\Shift' => $baseDir . '/vendor_prefixed/square/square/src/Models/Shift.php',
    'WPForms\\Vendor\\Square\\Models\\ShiftFilter' => $baseDir . '/vendor_prefixed/square/square/src/Models/ShiftFilter.php',
    'WPForms\\Vendor\\Square\\Models\\ShiftFilterStatus' => $baseDir . '/vendor_prefixed/square/square/src/Models/ShiftFilterStatus.php',
    'WPForms\\Vendor\\Square\\Models\\ShiftQuery' => $baseDir . '/vendor_prefixed/square/square/src/Models/ShiftQuery.php',
    'WPForms\\Vendor\\Square\\Models\\ShiftSort' => $baseDir . '/vendor_prefixed/square/square/src/Models/ShiftSort.php',
    'WPForms\\Vendor\\Square\\Models\\ShiftSortField' => $baseDir . '/vendor_prefixed/square/square/src/Models/ShiftSortField.php',
    'WPForms\\Vendor\\Square\\Models\\ShiftStatus' => $baseDir . '/vendor_prefixed/square/square/src/Models/ShiftStatus.php',
    'WPForms\\Vendor\\Square\\Models\\ShiftWage' => $baseDir . '/vendor_prefixed/square/square/src/Models/ShiftWage.php',
    'WPForms\\Vendor\\Square\\Models\\ShiftWorkday' => $baseDir . '/vendor_prefixed/square/square/src/Models/ShiftWorkday.php',
    'WPForms\\Vendor\\Square\\Models\\ShiftWorkdayMatcher' => $baseDir . '/vendor_prefixed/square/square/src/Models/ShiftWorkdayMatcher.php',
    'WPForms\\Vendor\\Square\\Models\\ShippingFee' => $baseDir . '/vendor_prefixed/square/square/src/Models/ShippingFee.php',
    'WPForms\\Vendor\\Square\\Models\\SignatureImage' => $baseDir . '/vendor_prefixed/square/square/src/Models/SignatureImage.php',
    'WPForms\\Vendor\\Square\\Models\\SignatureOptions' => $baseDir . '/vendor_prefixed/square/square/src/Models/SignatureOptions.php',
    'WPForms\\Vendor\\Square\\Models\\Site' => $baseDir . '/vendor_prefixed/square/square/src/Models/Site.php',
    'WPForms\\Vendor\\Square\\Models\\Snippet' => $baseDir . '/vendor_prefixed/square/square/src/Models/Snippet.php',
    'WPForms\\Vendor\\Square\\Models\\SnippetResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/SnippetResponse.php',
    'WPForms\\Vendor\\Square\\Models\\SortOrder' => $baseDir . '/vendor_prefixed/square/square/src/Models/SortOrder.php',
    'WPForms\\Vendor\\Square\\Models\\SourceApplication' => $baseDir . '/vendor_prefixed/square/square/src/Models/SourceApplication.php',
    'WPForms\\Vendor\\Square\\Models\\SquareAccountDetails' => $baseDir . '/vendor_prefixed/square/square/src/Models/SquareAccountDetails.php',
    'WPForms\\Vendor\\Square\\Models\\StandardUnitDescription' => $baseDir . '/vendor_prefixed/square/square/src/Models/StandardUnitDescription.php',
    'WPForms\\Vendor\\Square\\Models\\StandardUnitDescriptionGroup' => $baseDir . '/vendor_prefixed/square/square/src/Models/StandardUnitDescriptionGroup.php',
    'WPForms\\Vendor\\Square\\Models\\SubmitEvidenceResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/SubmitEvidenceResponse.php',
    'WPForms\\Vendor\\Square\\Models\\Subscription' => $baseDir . '/vendor_prefixed/square/square/src/Models/Subscription.php',
    'WPForms\\Vendor\\Square\\Models\\SubscriptionAction' => $baseDir . '/vendor_prefixed/square/square/src/Models/SubscriptionAction.php',
    'WPForms\\Vendor\\Square\\Models\\SubscriptionActionType' => $baseDir . '/vendor_prefixed/square/square/src/Models/SubscriptionActionType.php',
    'WPForms\\Vendor\\Square\\Models\\SubscriptionCadence' => $baseDir . '/vendor_prefixed/square/square/src/Models/SubscriptionCadence.php',
    'WPForms\\Vendor\\Square\\Models\\SubscriptionEvent' => $baseDir . '/vendor_prefixed/square/square/src/Models/SubscriptionEvent.php',
    'WPForms\\Vendor\\Square\\Models\\SubscriptionEventInfo' => $baseDir . '/vendor_prefixed/square/square/src/Models/SubscriptionEventInfo.php',
    'WPForms\\Vendor\\Square\\Models\\SubscriptionEventInfoCode' => $baseDir . '/vendor_prefixed/square/square/src/Models/SubscriptionEventInfoCode.php',
    'WPForms\\Vendor\\Square\\Models\\SubscriptionEventSubscriptionEventType' => $baseDir . '/vendor_prefixed/square/square/src/Models/SubscriptionEventSubscriptionEventType.php',
    'WPForms\\Vendor\\Square\\Models\\SubscriptionPhase' => $baseDir . '/vendor_prefixed/square/square/src/Models/SubscriptionPhase.php',
    'WPForms\\Vendor\\Square\\Models\\SubscriptionPricing' => $baseDir . '/vendor_prefixed/square/square/src/Models/SubscriptionPricing.php',
    'WPForms\\Vendor\\Square\\Models\\SubscriptionPricingType' => $baseDir . '/vendor_prefixed/square/square/src/Models/SubscriptionPricingType.php',
    'WPForms\\Vendor\\Square\\Models\\SubscriptionSource' => $baseDir . '/vendor_prefixed/square/square/src/Models/SubscriptionSource.php',
    'WPForms\\Vendor\\Square\\Models\\SubscriptionStatus' => $baseDir . '/vendor_prefixed/square/square/src/Models/SubscriptionStatus.php',
    'WPForms\\Vendor\\Square\\Models\\SubscriptionTestResult' => $baseDir . '/vendor_prefixed/square/square/src/Models/SubscriptionTestResult.php',
    'WPForms\\Vendor\\Square\\Models\\SwapPlanRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/SwapPlanRequest.php',
    'WPForms\\Vendor\\Square\\Models\\SwapPlanResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/SwapPlanResponse.php',
    'WPForms\\Vendor\\Square\\Models\\TaxCalculationPhase' => $baseDir . '/vendor_prefixed/square/square/src/Models/TaxCalculationPhase.php',
    'WPForms\\Vendor\\Square\\Models\\TaxIds' => $baseDir . '/vendor_prefixed/square/square/src/Models/TaxIds.php',
    'WPForms\\Vendor\\Square\\Models\\TaxInclusionType' => $baseDir . '/vendor_prefixed/square/square/src/Models/TaxInclusionType.php',
    'WPForms\\Vendor\\Square\\Models\\TeamMember' => $baseDir . '/vendor_prefixed/square/square/src/Models/TeamMember.php',
    'WPForms\\Vendor\\Square\\Models\\TeamMemberAssignedLocations' => $baseDir . '/vendor_prefixed/square/square/src/Models/TeamMemberAssignedLocations.php',
    'WPForms\\Vendor\\Square\\Models\\TeamMemberAssignedLocationsAssignmentType' => $baseDir . '/vendor_prefixed/square/square/src/Models/TeamMemberAssignedLocationsAssignmentType.php',
    'WPForms\\Vendor\\Square\\Models\\TeamMemberBookingProfile' => $baseDir . '/vendor_prefixed/square/square/src/Models/TeamMemberBookingProfile.php',
    'WPForms\\Vendor\\Square\\Models\\TeamMemberInvitationStatus' => $baseDir . '/vendor_prefixed/square/square/src/Models/TeamMemberInvitationStatus.php',
    'WPForms\\Vendor\\Square\\Models\\TeamMemberStatus' => $baseDir . '/vendor_prefixed/square/square/src/Models/TeamMemberStatus.php',
    'WPForms\\Vendor\\Square\\Models\\TeamMemberWage' => $baseDir . '/vendor_prefixed/square/square/src/Models/TeamMemberWage.php',
    'WPForms\\Vendor\\Square\\Models\\Tender' => $baseDir . '/vendor_prefixed/square/square/src/Models/Tender.php',
    'WPForms\\Vendor\\Square\\Models\\TenderBankAccountDetails' => $baseDir . '/vendor_prefixed/square/square/src/Models/TenderBankAccountDetails.php',
    'WPForms\\Vendor\\Square\\Models\\TenderBankAccountDetailsStatus' => $baseDir . '/vendor_prefixed/square/square/src/Models/TenderBankAccountDetailsStatus.php',
    'WPForms\\Vendor\\Square\\Models\\TenderBuyNowPayLaterDetails' => $baseDir . '/vendor_prefixed/square/square/src/Models/TenderBuyNowPayLaterDetails.php',
    'WPForms\\Vendor\\Square\\Models\\TenderBuyNowPayLaterDetailsBrand' => $baseDir . '/vendor_prefixed/square/square/src/Models/TenderBuyNowPayLaterDetailsBrand.php',
    'WPForms\\Vendor\\Square\\Models\\TenderBuyNowPayLaterDetailsStatus' => $baseDir . '/vendor_prefixed/square/square/src/Models/TenderBuyNowPayLaterDetailsStatus.php',
    'WPForms\\Vendor\\Square\\Models\\TenderCardDetails' => $baseDir . '/vendor_prefixed/square/square/src/Models/TenderCardDetails.php',
    'WPForms\\Vendor\\Square\\Models\\TenderCardDetailsEntryMethod' => $baseDir . '/vendor_prefixed/square/square/src/Models/TenderCardDetailsEntryMethod.php',
    'WPForms\\Vendor\\Square\\Models\\TenderCardDetailsStatus' => $baseDir . '/vendor_prefixed/square/square/src/Models/TenderCardDetailsStatus.php',
    'WPForms\\Vendor\\Square\\Models\\TenderCashDetails' => $baseDir . '/vendor_prefixed/square/square/src/Models/TenderCashDetails.php',
    'WPForms\\Vendor\\Square\\Models\\TenderSquareAccountDetails' => $baseDir . '/vendor_prefixed/square/square/src/Models/TenderSquareAccountDetails.php',
    'WPForms\\Vendor\\Square\\Models\\TenderSquareAccountDetailsStatus' => $baseDir . '/vendor_prefixed/square/square/src/Models/TenderSquareAccountDetailsStatus.php',
    'WPForms\\Vendor\\Square\\Models\\TenderType' => $baseDir . '/vendor_prefixed/square/square/src/Models/TenderType.php',
    'WPForms\\Vendor\\Square\\Models\\TerminalAction' => $baseDir . '/vendor_prefixed/square/square/src/Models/TerminalAction.php',
    'WPForms\\Vendor\\Square\\Models\\TerminalActionActionType' => $baseDir . '/vendor_prefixed/square/square/src/Models/TerminalActionActionType.php',
    'WPForms\\Vendor\\Square\\Models\\TerminalActionQuery' => $baseDir . '/vendor_prefixed/square/square/src/Models/TerminalActionQuery.php',
    'WPForms\\Vendor\\Square\\Models\\TerminalActionQueryFilter' => $baseDir . '/vendor_prefixed/square/square/src/Models/TerminalActionQueryFilter.php',
    'WPForms\\Vendor\\Square\\Models\\TerminalActionQuerySort' => $baseDir . '/vendor_prefixed/square/square/src/Models/TerminalActionQuerySort.php',
    'WPForms\\Vendor\\Square\\Models\\TerminalCheckout' => $baseDir . '/vendor_prefixed/square/square/src/Models/TerminalCheckout.php',
    'WPForms\\Vendor\\Square\\Models\\TerminalCheckoutQuery' => $baseDir . '/vendor_prefixed/square/square/src/Models/TerminalCheckoutQuery.php',
    'WPForms\\Vendor\\Square\\Models\\TerminalCheckoutQueryFilter' => $baseDir . '/vendor_prefixed/square/square/src/Models/TerminalCheckoutQueryFilter.php',
    'WPForms\\Vendor\\Square\\Models\\TerminalCheckoutQuerySort' => $baseDir . '/vendor_prefixed/square/square/src/Models/TerminalCheckoutQuerySort.php',
    'WPForms\\Vendor\\Square\\Models\\TerminalRefund' => $baseDir . '/vendor_prefixed/square/square/src/Models/TerminalRefund.php',
    'WPForms\\Vendor\\Square\\Models\\TerminalRefundQuery' => $baseDir . '/vendor_prefixed/square/square/src/Models/TerminalRefundQuery.php',
    'WPForms\\Vendor\\Square\\Models\\TerminalRefundQueryFilter' => $baseDir . '/vendor_prefixed/square/square/src/Models/TerminalRefundQueryFilter.php',
    'WPForms\\Vendor\\Square\\Models\\TerminalRefundQuerySort' => $baseDir . '/vendor_prefixed/square/square/src/Models/TerminalRefundQuerySort.php',
    'WPForms\\Vendor\\Square\\Models\\TestWebhookSubscriptionRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/TestWebhookSubscriptionRequest.php',
    'WPForms\\Vendor\\Square\\Models\\TestWebhookSubscriptionResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/TestWebhookSubscriptionResponse.php',
    'WPForms\\Vendor\\Square\\Models\\TimeRange' => $baseDir . '/vendor_prefixed/square/square/src/Models/TimeRange.php',
    'WPForms\\Vendor\\Square\\Models\\TipSettings' => $baseDir . '/vendor_prefixed/square/square/src/Models/TipSettings.php',
    'WPForms\\Vendor\\Square\\Models\\Transaction' => $baseDir . '/vendor_prefixed/square/square/src/Models/Transaction.php',
    'WPForms\\Vendor\\Square\\Models\\TransactionProduct' => $baseDir . '/vendor_prefixed/square/square/src/Models/TransactionProduct.php',
    'WPForms\\Vendor\\Square\\Models\\TransactionType' => $baseDir . '/vendor_prefixed/square/square/src/Models/TransactionType.php',
    'WPForms\\Vendor\\Square\\Models\\UnlinkCustomerFromGiftCardRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/UnlinkCustomerFromGiftCardRequest.php',
    'WPForms\\Vendor\\Square\\Models\\UnlinkCustomerFromGiftCardResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/UnlinkCustomerFromGiftCardResponse.php',
    'WPForms\\Vendor\\Square\\Models\\UpdateBookingCustomAttributeDefinitionRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/UpdateBookingCustomAttributeDefinitionRequest.php',
    'WPForms\\Vendor\\Square\\Models\\UpdateBookingCustomAttributeDefinitionResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/UpdateBookingCustomAttributeDefinitionResponse.php',
    'WPForms\\Vendor\\Square\\Models\\UpdateBookingRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/UpdateBookingRequest.php',
    'WPForms\\Vendor\\Square\\Models\\UpdateBookingResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/UpdateBookingResponse.php',
    'WPForms\\Vendor\\Square\\Models\\UpdateBreakTypeRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/UpdateBreakTypeRequest.php',
    'WPForms\\Vendor\\Square\\Models\\UpdateBreakTypeResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/UpdateBreakTypeResponse.php',
    'WPForms\\Vendor\\Square\\Models\\UpdateCatalogImageRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/UpdateCatalogImageRequest.php',
    'WPForms\\Vendor\\Square\\Models\\UpdateCatalogImageResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/UpdateCatalogImageResponse.php',
    'WPForms\\Vendor\\Square\\Models\\UpdateCustomerCustomAttributeDefinitionRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/UpdateCustomerCustomAttributeDefinitionRequest.php',
    'WPForms\\Vendor\\Square\\Models\\UpdateCustomerCustomAttributeDefinitionResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/UpdateCustomerCustomAttributeDefinitionResponse.php',
    'WPForms\\Vendor\\Square\\Models\\UpdateCustomerGroupRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/UpdateCustomerGroupRequest.php',
    'WPForms\\Vendor\\Square\\Models\\UpdateCustomerGroupResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/UpdateCustomerGroupResponse.php',
    'WPForms\\Vendor\\Square\\Models\\UpdateCustomerRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/UpdateCustomerRequest.php',
    'WPForms\\Vendor\\Square\\Models\\UpdateCustomerResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/UpdateCustomerResponse.php',
    'WPForms\\Vendor\\Square\\Models\\UpdateInvoiceRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/UpdateInvoiceRequest.php',
    'WPForms\\Vendor\\Square\\Models\\UpdateInvoiceResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/UpdateInvoiceResponse.php',
    'WPForms\\Vendor\\Square\\Models\\UpdateItemModifierListsRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/UpdateItemModifierListsRequest.php',
    'WPForms\\Vendor\\Square\\Models\\UpdateItemModifierListsResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/UpdateItemModifierListsResponse.php',
    'WPForms\\Vendor\\Square\\Models\\UpdateItemTaxesRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/UpdateItemTaxesRequest.php',
    'WPForms\\Vendor\\Square\\Models\\UpdateItemTaxesResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/UpdateItemTaxesResponse.php',
    'WPForms\\Vendor\\Square\\Models\\UpdateJobRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/UpdateJobRequest.php',
    'WPForms\\Vendor\\Square\\Models\\UpdateJobResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/UpdateJobResponse.php',
    'WPForms\\Vendor\\Square\\Models\\UpdateLocationCustomAttributeDefinitionRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/UpdateLocationCustomAttributeDefinitionRequest.php',
    'WPForms\\Vendor\\Square\\Models\\UpdateLocationCustomAttributeDefinitionResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/UpdateLocationCustomAttributeDefinitionResponse.php',
    'WPForms\\Vendor\\Square\\Models\\UpdateLocationRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/UpdateLocationRequest.php',
    'WPForms\\Vendor\\Square\\Models\\UpdateLocationResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/UpdateLocationResponse.php',
    'WPForms\\Vendor\\Square\\Models\\UpdateLocationSettingsRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/UpdateLocationSettingsRequest.php',
    'WPForms\\Vendor\\Square\\Models\\UpdateLocationSettingsResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/UpdateLocationSettingsResponse.php',
    'WPForms\\Vendor\\Square\\Models\\UpdateMerchantCustomAttributeDefinitionRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/UpdateMerchantCustomAttributeDefinitionRequest.php',
    'WPForms\\Vendor\\Square\\Models\\UpdateMerchantCustomAttributeDefinitionResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/UpdateMerchantCustomAttributeDefinitionResponse.php',
    'WPForms\\Vendor\\Square\\Models\\UpdateMerchantSettingsRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/UpdateMerchantSettingsRequest.php',
    'WPForms\\Vendor\\Square\\Models\\UpdateMerchantSettingsResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/UpdateMerchantSettingsResponse.php',
    'WPForms\\Vendor\\Square\\Models\\UpdateOrderCustomAttributeDefinitionRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/UpdateOrderCustomAttributeDefinitionRequest.php',
    'WPForms\\Vendor\\Square\\Models\\UpdateOrderCustomAttributeDefinitionResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/UpdateOrderCustomAttributeDefinitionResponse.php',
    'WPForms\\Vendor\\Square\\Models\\UpdateOrderRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/UpdateOrderRequest.php',
    'WPForms\\Vendor\\Square\\Models\\UpdateOrderResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/UpdateOrderResponse.php',
    'WPForms\\Vendor\\Square\\Models\\UpdatePaymentLinkRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/UpdatePaymentLinkRequest.php',
    'WPForms\\Vendor\\Square\\Models\\UpdatePaymentLinkResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/UpdatePaymentLinkResponse.php',
    'WPForms\\Vendor\\Square\\Models\\UpdatePaymentRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/UpdatePaymentRequest.php',
    'WPForms\\Vendor\\Square\\Models\\UpdatePaymentResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/UpdatePaymentResponse.php',
    'WPForms\\Vendor\\Square\\Models\\UpdateShiftRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/UpdateShiftRequest.php',
    'WPForms\\Vendor\\Square\\Models\\UpdateShiftResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/UpdateShiftResponse.php',
    'WPForms\\Vendor\\Square\\Models\\UpdateSubscriptionRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/UpdateSubscriptionRequest.php',
    'WPForms\\Vendor\\Square\\Models\\UpdateSubscriptionResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/UpdateSubscriptionResponse.php',
    'WPForms\\Vendor\\Square\\Models\\UpdateTeamMemberRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/UpdateTeamMemberRequest.php',
    'WPForms\\Vendor\\Square\\Models\\UpdateTeamMemberResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/UpdateTeamMemberResponse.php',
    'WPForms\\Vendor\\Square\\Models\\UpdateVendorRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/UpdateVendorRequest.php',
    'WPForms\\Vendor\\Square\\Models\\UpdateVendorResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/UpdateVendorResponse.php',
    'WPForms\\Vendor\\Square\\Models\\UpdateWageSettingRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/UpdateWageSettingRequest.php',
    'WPForms\\Vendor\\Square\\Models\\UpdateWageSettingResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/UpdateWageSettingResponse.php',
    'WPForms\\Vendor\\Square\\Models\\UpdateWebhookSubscriptionRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/UpdateWebhookSubscriptionRequest.php',
    'WPForms\\Vendor\\Square\\Models\\UpdateWebhookSubscriptionResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/UpdateWebhookSubscriptionResponse.php',
    'WPForms\\Vendor\\Square\\Models\\UpdateWebhookSubscriptionSignatureKeyRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/UpdateWebhookSubscriptionSignatureKeyRequest.php',
    'WPForms\\Vendor\\Square\\Models\\UpdateWebhookSubscriptionSignatureKeyResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/UpdateWebhookSubscriptionSignatureKeyResponse.php',
    'WPForms\\Vendor\\Square\\Models\\UpdateWorkweekConfigRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/UpdateWorkweekConfigRequest.php',
    'WPForms\\Vendor\\Square\\Models\\UpdateWorkweekConfigResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/UpdateWorkweekConfigResponse.php',
    'WPForms\\Vendor\\Square\\Models\\UpsertBookingCustomAttributeRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/UpsertBookingCustomAttributeRequest.php',
    'WPForms\\Vendor\\Square\\Models\\UpsertBookingCustomAttributeResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/UpsertBookingCustomAttributeResponse.php',
    'WPForms\\Vendor\\Square\\Models\\UpsertCatalogObjectRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/UpsertCatalogObjectRequest.php',
    'WPForms\\Vendor\\Square\\Models\\UpsertCatalogObjectResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/UpsertCatalogObjectResponse.php',
    'WPForms\\Vendor\\Square\\Models\\UpsertCustomerCustomAttributeRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/UpsertCustomerCustomAttributeRequest.php',
    'WPForms\\Vendor\\Square\\Models\\UpsertCustomerCustomAttributeResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/UpsertCustomerCustomAttributeResponse.php',
    'WPForms\\Vendor\\Square\\Models\\UpsertLocationCustomAttributeRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/UpsertLocationCustomAttributeRequest.php',
    'WPForms\\Vendor\\Square\\Models\\UpsertLocationCustomAttributeResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/UpsertLocationCustomAttributeResponse.php',
    'WPForms\\Vendor\\Square\\Models\\UpsertMerchantCustomAttributeRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/UpsertMerchantCustomAttributeRequest.php',
    'WPForms\\Vendor\\Square\\Models\\UpsertMerchantCustomAttributeResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/UpsertMerchantCustomAttributeResponse.php',
    'WPForms\\Vendor\\Square\\Models\\UpsertOrderCustomAttributeRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/UpsertOrderCustomAttributeRequest.php',
    'WPForms\\Vendor\\Square\\Models\\UpsertOrderCustomAttributeResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/UpsertOrderCustomAttributeResponse.php',
    'WPForms\\Vendor\\Square\\Models\\UpsertSnippetRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/UpsertSnippetRequest.php',
    'WPForms\\Vendor\\Square\\Models\\UpsertSnippetResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/UpsertSnippetResponse.php',
    'WPForms\\Vendor\\Square\\Models\\V1Device' => $baseDir . '/vendor_prefixed/square/square/src/Models/V1Device.php',
    'WPForms\\Vendor\\Square\\Models\\V1ListOrdersRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/V1ListOrdersRequest.php',
    'WPForms\\Vendor\\Square\\Models\\V1ListOrdersResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/V1ListOrdersResponse.php',
    'WPForms\\Vendor\\Square\\Models\\V1Money' => $baseDir . '/vendor_prefixed/square/square/src/Models/V1Money.php',
    'WPForms\\Vendor\\Square\\Models\\V1Order' => $baseDir . '/vendor_prefixed/square/square/src/Models/V1Order.php',
    'WPForms\\Vendor\\Square\\Models\\V1OrderHistoryEntry' => $baseDir . '/vendor_prefixed/square/square/src/Models/V1OrderHistoryEntry.php',
    'WPForms\\Vendor\\Square\\Models\\V1OrderHistoryEntryAction' => $baseDir . '/vendor_prefixed/square/square/src/Models/V1OrderHistoryEntryAction.php',
    'WPForms\\Vendor\\Square\\Models\\V1OrderState' => $baseDir . '/vendor_prefixed/square/square/src/Models/V1OrderState.php',
    'WPForms\\Vendor\\Square\\Models\\V1PhoneNumber' => $baseDir . '/vendor_prefixed/square/square/src/Models/V1PhoneNumber.php',
    'WPForms\\Vendor\\Square\\Models\\V1Tender' => $baseDir . '/vendor_prefixed/square/square/src/Models/V1Tender.php',
    'WPForms\\Vendor\\Square\\Models\\V1TenderCardBrand' => $baseDir . '/vendor_prefixed/square/square/src/Models/V1TenderCardBrand.php',
    'WPForms\\Vendor\\Square\\Models\\V1TenderEntryMethod' => $baseDir . '/vendor_prefixed/square/square/src/Models/V1TenderEntryMethod.php',
    'WPForms\\Vendor\\Square\\Models\\V1TenderType' => $baseDir . '/vendor_prefixed/square/square/src/Models/V1TenderType.php',
    'WPForms\\Vendor\\Square\\Models\\V1UpdateOrderRequest' => $baseDir . '/vendor_prefixed/square/square/src/Models/V1UpdateOrderRequest.php',
    'WPForms\\Vendor\\Square\\Models\\V1UpdateOrderRequestAction' => $baseDir . '/vendor_prefixed/square/square/src/Models/V1UpdateOrderRequestAction.php',
    'WPForms\\Vendor\\Square\\Models\\Vendor' => $baseDir . '/vendor_prefixed/square/square/src/Models/Vendor.php',
    'WPForms\\Vendor\\Square\\Models\\VendorContact' => $baseDir . '/vendor_prefixed/square/square/src/Models/VendorContact.php',
    'WPForms\\Vendor\\Square\\Models\\VendorStatus' => $baseDir . '/vendor_prefixed/square/square/src/Models/VendorStatus.php',
    'WPForms\\Vendor\\Square\\Models\\VisibilityFilter' => $baseDir . '/vendor_prefixed/square/square/src/Models/VisibilityFilter.php',
    'WPForms\\Vendor\\Square\\Models\\VoidTransactionResponse' => $baseDir . '/vendor_prefixed/square/square/src/Models/VoidTransactionResponse.php',
    'WPForms\\Vendor\\Square\\Models\\WageSetting' => $baseDir . '/vendor_prefixed/square/square/src/Models/WageSetting.php',
    'WPForms\\Vendor\\Square\\Models\\WebhookSubscription' => $baseDir . '/vendor_prefixed/square/square/src/Models/WebhookSubscription.php',
    'WPForms\\Vendor\\Square\\Models\\Weekday' => $baseDir . '/vendor_prefixed/square/square/src/Models/Weekday.php',
    'WPForms\\Vendor\\Square\\Models\\WorkweekConfig' => $baseDir . '/vendor_prefixed/square/square/src/Models/WorkweekConfig.php',
    'WPForms\\Vendor\\Square\\Server' => $baseDir . '/vendor_prefixed/square/square/src/Server.php',
    'WPForms\\Vendor\\Square\\SquareClient' => $baseDir . '/vendor_prefixed/square/square/src/SquareClient.php',
    'WPForms\\Vendor\\Square\\SquareClientBuilder' => $baseDir . '/vendor_prefixed/square/square/src/SquareClientBuilder.php',
    'WPForms\\Vendor\\Square\\Tests\\Apis\\BaseTestController' => $baseDir . '/vendor_prefixed/square/square/tests/Apis/BaseTestController.php',
    'WPForms\\Vendor\\Square\\Tests\\Apis\\LocationsApiTest' => $baseDir . '/vendor_prefixed/square/square/tests/Apis/LocationsApiTest.php',
    'WPForms\\Vendor\\Square\\Tests\\CatalogTest' => $baseDir . '/vendor_prefixed/square/square/tests/Flows/CatalogTest.php',
    'WPForms\\Vendor\\Square\\Tests\\ClientFactory' => $baseDir . '/vendor_prefixed/square/square/tests/ClientFactory.php',
    'WPForms\\Vendor\\Square\\Tests\\CustomAttributeDefinitionTest' => $baseDir . '/vendor_prefixed/square/square/tests/Models/CustomAttributeDefinitionTest.php',
    'WPForms\\Vendor\\Square\\Tests\\CustomAttributeTest' => $baseDir . '/vendor_prefixed/square/square/tests/Models/CustomAttributeTest.php',
    'WPForms\\Vendor\\Square\\Tests\\CustomerGroupsTest' => $baseDir . '/vendor_prefixed/square/square/tests/Flows/CustomerGroupsTest.php',
    'WPForms\\Vendor\\Square\\Tests\\CustomersTest' => $baseDir . '/vendor_prefixed/square/square/tests/Flows/CustomersTest.php',
    'WPForms\\Vendor\\Square\\Tests\\DisputesTest' => $baseDir . '/vendor_prefixed/square/square/tests/Flows/DisputesTest.php',
    'WPForms\\Vendor\\Square\\Tests\\ErrorsTest' => $baseDir . '/vendor_prefixed/square/square/tests/Flows/ErrorsTest.php',
    'WPForms\\Vendor\\Square\\Tests\\JsonSerializeTest' => $baseDir . '/vendor_prefixed/square/square/tests/Flows/JsonSerializeTest.php',
    'WPForms\\Vendor\\Square\\Tests\\MarchantsTest' => $baseDir . '/vendor_prefixed/square/square/tests/Flows/MerchantsTest.php',
    'WPForms\\Vendor\\Square\\Tests\\MobileAuthorizationTest' => $baseDir . '/vendor_prefixed/square/square/tests/Flows/MobileAuthorizationTest.php',
    'WPForms\\Vendor\\Square\\Tests\\OrdersTest' => $baseDir . '/vendor_prefixed/square/square/tests/Flows/OrdersTest.php',
    'WPForms\\Vendor\\Square\\Tests\\PaymentsTest' => $baseDir . '/vendor_prefixed/square/square/tests/Flows/PaymentsTest.php',
    'WPForms\\Vendor\\Square\\Tests\\RefundsTest' => $baseDir . '/vendor_prefixed/square/square/tests/Flows/RefundsTest.php',
    'WPForms\\Vendor\\Square\\Tests\\WebhooksHelperTest' => $baseDir . '/vendor_prefixed/square/square/tests/Flows/WebhooksHelperTest.php',
    'WPForms\\Vendor\\Square\\Utils\\CompatibilityConverter' => $baseDir . '/vendor_prefixed/square/square/src/Utils/CompatibilityConverter.php',
    'WPForms\\Vendor\\Square\\Utils\\FileWrapper' => $baseDir . '/vendor_prefixed/square/square/src/Utils/FileWrapper.php',
    'WPForms\\Vendor\\Square\\Utils\\WebhooksHelper' => $baseDir . '/vendor_prefixed/square/square/src/Utils/WebhooksHelper.php',
    'WPForms\\Vendor\\Stripe\\Account' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Account.php',
    'WPForms\\Vendor\\Stripe\\AccountLink' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/AccountLink.php',
    'WPForms\\Vendor\\Stripe\\AccountSession' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/AccountSession.php',
    'WPForms\\Vendor\\Stripe\\ApiOperations\\All' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/ApiOperations/All.php',
    'WPForms\\Vendor\\Stripe\\ApiOperations\\Create' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/ApiOperations/Create.php',
    'WPForms\\Vendor\\Stripe\\ApiOperations\\Delete' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/ApiOperations/Delete.php',
    'WPForms\\Vendor\\Stripe\\ApiOperations\\NestedResource' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/ApiOperations/NestedResource.php',
    'WPForms\\Vendor\\Stripe\\ApiOperations\\Request' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/ApiOperations/Request.php',
    'WPForms\\Vendor\\Stripe\\ApiOperations\\Retrieve' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/ApiOperations/Retrieve.php',
    'WPForms\\Vendor\\Stripe\\ApiOperations\\Search' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/ApiOperations/Search.php',
    'WPForms\\Vendor\\Stripe\\ApiOperations\\SingletonRetrieve' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/ApiOperations/SingletonRetrieve.php',
    'WPForms\\Vendor\\Stripe\\ApiOperations\\Update' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/ApiOperations/Update.php',
    'WPForms\\Vendor\\Stripe\\ApiRequestor' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/ApiRequestor.php',
    'WPForms\\Vendor\\Stripe\\ApiResource' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/ApiResource.php',
    'WPForms\\Vendor\\Stripe\\ApiResponse' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/ApiResponse.php',
    'WPForms\\Vendor\\Stripe\\ApplePayDomain' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/ApplePayDomain.php',
    'WPForms\\Vendor\\Stripe\\Application' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Application.php',
    'WPForms\\Vendor\\Stripe\\ApplicationFee' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/ApplicationFee.php',
    'WPForms\\Vendor\\Stripe\\ApplicationFeeRefund' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/ApplicationFeeRefund.php',
    'WPForms\\Vendor\\Stripe\\Apps\\Secret' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Apps/Secret.php',
    'WPForms\\Vendor\\Stripe\\Balance' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Balance.php',
    'WPForms\\Vendor\\Stripe\\BalanceTransaction' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/BalanceTransaction.php',
    'WPForms\\Vendor\\Stripe\\BankAccount' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/BankAccount.php',
    'WPForms\\Vendor\\Stripe\\BaseStripeClient' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/BaseStripeClient.php',
    'WPForms\\Vendor\\Stripe\\BaseStripeClientInterface' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/BaseStripeClientInterface.php',
    'WPForms\\Vendor\\Stripe\\BillingPortal\\Configuration' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/BillingPortal/Configuration.php',
    'WPForms\\Vendor\\Stripe\\BillingPortal\\Session' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/BillingPortal/Session.php',
    'WPForms\\Vendor\\Stripe\\Billing\\Alert' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Billing/Alert.php',
    'WPForms\\Vendor\\Stripe\\Billing\\AlertTriggered' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Billing/AlertTriggered.php',
    'WPForms\\Vendor\\Stripe\\Billing\\CreditBalanceSummary' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Billing/CreditBalanceSummary.php',
    'WPForms\\Vendor\\Stripe\\Billing\\CreditBalanceTransaction' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Billing/CreditBalanceTransaction.php',
    'WPForms\\Vendor\\Stripe\\Billing\\CreditGrant' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Billing/CreditGrant.php',
    'WPForms\\Vendor\\Stripe\\Billing\\Meter' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Billing/Meter.php',
    'WPForms\\Vendor\\Stripe\\Billing\\MeterEvent' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Billing/MeterEvent.php',
    'WPForms\\Vendor\\Stripe\\Billing\\MeterEventAdjustment' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Billing/MeterEventAdjustment.php',
    'WPForms\\Vendor\\Stripe\\Billing\\MeterEventSummary' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Billing/MeterEventSummary.php',
    'WPForms\\Vendor\\Stripe\\Capability' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Capability.php',
    'WPForms\\Vendor\\Stripe\\Card' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Card.php',
    'WPForms\\Vendor\\Stripe\\CashBalance' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/CashBalance.php',
    'WPForms\\Vendor\\Stripe\\Charge' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Charge.php',
    'WPForms\\Vendor\\Stripe\\Checkout\\Session' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Checkout/Session.php',
    'WPForms\\Vendor\\Stripe\\Climate\\Order' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Climate/Order.php',
    'WPForms\\Vendor\\Stripe\\Climate\\Product' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Climate/Product.php',
    'WPForms\\Vendor\\Stripe\\Climate\\Supplier' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Climate/Supplier.php',
    'WPForms\\Vendor\\Stripe\\Collection' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Collection.php',
    'WPForms\\Vendor\\Stripe\\ConfirmationToken' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/ConfirmationToken.php',
    'WPForms\\Vendor\\Stripe\\ConnectCollectionTransfer' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/ConnectCollectionTransfer.php',
    'WPForms\\Vendor\\Stripe\\CountrySpec' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/CountrySpec.php',
    'WPForms\\Vendor\\Stripe\\Coupon' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Coupon.php',
    'WPForms\\Vendor\\Stripe\\CreditNote' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/CreditNote.php',
    'WPForms\\Vendor\\Stripe\\CreditNoteLineItem' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/CreditNoteLineItem.php',
    'WPForms\\Vendor\\Stripe\\Customer' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Customer.php',
    'WPForms\\Vendor\\Stripe\\CustomerBalanceTransaction' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/CustomerBalanceTransaction.php',
    'WPForms\\Vendor\\Stripe\\CustomerCashBalanceTransaction' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/CustomerCashBalanceTransaction.php',
    'WPForms\\Vendor\\Stripe\\CustomerSession' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/CustomerSession.php',
    'WPForms\\Vendor\\Stripe\\Discount' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Discount.php',
    'WPForms\\Vendor\\Stripe\\Dispute' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Dispute.php',
    'WPForms\\Vendor\\Stripe\\Entitlements\\ActiveEntitlement' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Entitlements/ActiveEntitlement.php',
    'WPForms\\Vendor\\Stripe\\Entitlements\\ActiveEntitlementSummary' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Entitlements/ActiveEntitlementSummary.php',
    'WPForms\\Vendor\\Stripe\\Entitlements\\Feature' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Entitlements/Feature.php',
    'WPForms\\Vendor\\Stripe\\EphemeralKey' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/EphemeralKey.php',
    'WPForms\\Vendor\\Stripe\\ErrorObject' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/ErrorObject.php',
    'WPForms\\Vendor\\Stripe\\Event' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Event.php',
    'WPForms\\Vendor\\Stripe\\EventData\\V1BillingMeterErrorReportTriggeredEventData' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/EventData/V1BillingMeterErrorReportTriggeredEventData.php',
    'WPForms\\Vendor\\Stripe\\EventData\\V1BillingMeterNoMeterFoundEventData' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/EventData/V1BillingMeterNoMeterFoundEventData.php',
    'WPForms\\Vendor\\Stripe\\Events\\V1BillingMeterErrorReportTriggeredEvent' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Events/V1BillingMeterErrorReportTriggeredEvent.php',
    'WPForms\\Vendor\\Stripe\\Events\\V1BillingMeterNoMeterFoundEvent' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Events/V1BillingMeterNoMeterFoundEvent.php',
    'WPForms\\Vendor\\Stripe\\Exception\\ApiConnectionException' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Exception/ApiConnectionException.php',
    'WPForms\\Vendor\\Stripe\\Exception\\ApiErrorException' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Exception/ApiErrorException.php',
    'WPForms\\Vendor\\Stripe\\Exception\\AuthenticationException' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Exception/AuthenticationException.php',
    'WPForms\\Vendor\\Stripe\\Exception\\BadMethodCallException' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Exception/BadMethodCallException.php',
    'WPForms\\Vendor\\Stripe\\Exception\\CardException' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Exception/CardException.php',
    'WPForms\\Vendor\\Stripe\\Exception\\ExceptionInterface' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Exception/ExceptionInterface.php',
    'WPForms\\Vendor\\Stripe\\Exception\\IdempotencyException' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Exception/IdempotencyException.php',
    'WPForms\\Vendor\\Stripe\\Exception\\InvalidArgumentException' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Exception/InvalidArgumentException.php',
    'WPForms\\Vendor\\Stripe\\Exception\\InvalidRequestException' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Exception/InvalidRequestException.php',
    'WPForms\\Vendor\\Stripe\\Exception\\OAuth\\ExceptionInterface' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Exception/OAuth/ExceptionInterface.php',
    'WPForms\\Vendor\\Stripe\\Exception\\OAuth\\InvalidClientException' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Exception/OAuth/InvalidClientException.php',
    'WPForms\\Vendor\\Stripe\\Exception\\OAuth\\InvalidGrantException' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Exception/OAuth/InvalidGrantException.php',
    'WPForms\\Vendor\\Stripe\\Exception\\OAuth\\InvalidRequestException' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Exception/OAuth/InvalidRequestException.php',
    'WPForms\\Vendor\\Stripe\\Exception\\OAuth\\InvalidScopeException' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Exception/OAuth/InvalidScopeException.php',
    'WPForms\\Vendor\\Stripe\\Exception\\OAuth\\OAuthErrorException' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Exception/OAuth/OAuthErrorException.php',
    'WPForms\\Vendor\\Stripe\\Exception\\OAuth\\UnknownOAuthErrorException' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Exception/OAuth/UnknownOAuthErrorException.php',
    'WPForms\\Vendor\\Stripe\\Exception\\OAuth\\UnsupportedGrantTypeException' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Exception/OAuth/UnsupportedGrantTypeException.php',
    'WPForms\\Vendor\\Stripe\\Exception\\OAuth\\UnsupportedResponseTypeException' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Exception/OAuth/UnsupportedResponseTypeException.php',
    'WPForms\\Vendor\\Stripe\\Exception\\PermissionException' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Exception/PermissionException.php',
    'WPForms\\Vendor\\Stripe\\Exception\\RateLimitException' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Exception/RateLimitException.php',
    'WPForms\\Vendor\\Stripe\\Exception\\SignatureVerificationException' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Exception/SignatureVerificationException.php',
    'WPForms\\Vendor\\Stripe\\Exception\\TemporarySessionExpiredException' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Exception/TemporarySessionExpiredException.php',
    'WPForms\\Vendor\\Stripe\\Exception\\UnexpectedValueException' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Exception/UnexpectedValueException.php',
    'WPForms\\Vendor\\Stripe\\Exception\\UnknownApiErrorException' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Exception/UnknownApiErrorException.php',
    'WPForms\\Vendor\\Stripe\\ExchangeRate' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/ExchangeRate.php',
    'WPForms\\Vendor\\Stripe\\File' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/File.php',
    'WPForms\\Vendor\\Stripe\\FileLink' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/FileLink.php',
    'WPForms\\Vendor\\Stripe\\FinancialConnections\\Account' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/FinancialConnections/Account.php',
    'WPForms\\Vendor\\Stripe\\FinancialConnections\\AccountOwner' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/FinancialConnections/AccountOwner.php',
    'WPForms\\Vendor\\Stripe\\FinancialConnections\\AccountOwnership' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/FinancialConnections/AccountOwnership.php',
    'WPForms\\Vendor\\Stripe\\FinancialConnections\\Session' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/FinancialConnections/Session.php',
    'WPForms\\Vendor\\Stripe\\FinancialConnections\\Transaction' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/FinancialConnections/Transaction.php',
    'WPForms\\Vendor\\Stripe\\Forwarding\\Request' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Forwarding/Request.php',
    'WPForms\\Vendor\\Stripe\\FundingInstructions' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/FundingInstructions.php',
    'WPForms\\Vendor\\Stripe\\HttpClient\\ClientInterface' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/HttpClient/ClientInterface.php',
    'WPForms\\Vendor\\Stripe\\HttpClient\\CurlClient' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/HttpClient/CurlClient.php',
    'WPForms\\Vendor\\Stripe\\HttpClient\\StreamingClientInterface' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/HttpClient/StreamingClientInterface.php',
    'WPForms\\Vendor\\Stripe\\Identity\\VerificationReport' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Identity/VerificationReport.php',
    'WPForms\\Vendor\\Stripe\\Identity\\VerificationSession' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Identity/VerificationSession.php',
    'WPForms\\Vendor\\Stripe\\Invoice' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Invoice.php',
    'WPForms\\Vendor\\Stripe\\InvoiceItem' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/InvoiceItem.php',
    'WPForms\\Vendor\\Stripe\\InvoiceLineItem' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/InvoiceLineItem.php',
    'WPForms\\Vendor\\Stripe\\InvoiceRenderingTemplate' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/InvoiceRenderingTemplate.php',
    'WPForms\\Vendor\\Stripe\\Issuing\\Authorization' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Issuing/Authorization.php',
    'WPForms\\Vendor\\Stripe\\Issuing\\Card' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Issuing/Card.php',
    'WPForms\\Vendor\\Stripe\\Issuing\\CardDetails' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Issuing/CardDetails.php',
    'WPForms\\Vendor\\Stripe\\Issuing\\Cardholder' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Issuing/Cardholder.php',
    'WPForms\\Vendor\\Stripe\\Issuing\\Dispute' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Issuing/Dispute.php',
    'WPForms\\Vendor\\Stripe\\Issuing\\PersonalizationDesign' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Issuing/PersonalizationDesign.php',
    'WPForms\\Vendor\\Stripe\\Issuing\\PhysicalBundle' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Issuing/PhysicalBundle.php',
    'WPForms\\Vendor\\Stripe\\Issuing\\Token' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Issuing/Token.php',
    'WPForms\\Vendor\\Stripe\\Issuing\\Transaction' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Issuing/Transaction.php',
    'WPForms\\Vendor\\Stripe\\LineItem' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/LineItem.php',
    'WPForms\\Vendor\\Stripe\\LoginLink' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/LoginLink.php',
    'WPForms\\Vendor\\Stripe\\Mandate' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Mandate.php',
    'WPForms\\Vendor\\Stripe\\OAuth' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/OAuth.php',
    'WPForms\\Vendor\\Stripe\\OAuthErrorObject' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/OAuthErrorObject.php',
    'WPForms\\Vendor\\Stripe\\PaymentIntent' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/PaymentIntent.php',
    'WPForms\\Vendor\\Stripe\\PaymentLink' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/PaymentLink.php',
    'WPForms\\Vendor\\Stripe\\PaymentMethod' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/PaymentMethod.php',
    'WPForms\\Vendor\\Stripe\\PaymentMethodConfiguration' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/PaymentMethodConfiguration.php',
    'WPForms\\Vendor\\Stripe\\PaymentMethodDomain' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/PaymentMethodDomain.php',
    'WPForms\\Vendor\\Stripe\\Payout' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Payout.php',
    'WPForms\\Vendor\\Stripe\\Person' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Person.php',
    'WPForms\\Vendor\\Stripe\\Plan' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Plan.php',
    'WPForms\\Vendor\\Stripe\\Price' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Price.php',
    'WPForms\\Vendor\\Stripe\\Product' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Product.php',
    'WPForms\\Vendor\\Stripe\\ProductFeature' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/ProductFeature.php',
    'WPForms\\Vendor\\Stripe\\PromotionCode' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/PromotionCode.php',
    'WPForms\\Vendor\\Stripe\\Quote' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Quote.php',
    'WPForms\\Vendor\\Stripe\\Radar\\EarlyFraudWarning' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Radar/EarlyFraudWarning.php',
    'WPForms\\Vendor\\Stripe\\Radar\\ValueList' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Radar/ValueList.php',
    'WPForms\\Vendor\\Stripe\\Radar\\ValueListItem' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Radar/ValueListItem.php',
    'WPForms\\Vendor\\Stripe\\Reason' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Reason.php',
    'WPForms\\Vendor\\Stripe\\RecipientTransfer' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/RecipientTransfer.php',
    'WPForms\\Vendor\\Stripe\\Refund' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Refund.php',
    'WPForms\\Vendor\\Stripe\\RelatedObject' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/RelatedObject.php',
    'WPForms\\Vendor\\Stripe\\Reporting\\ReportRun' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Reporting/ReportRun.php',
    'WPForms\\Vendor\\Stripe\\Reporting\\ReportType' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Reporting/ReportType.php',
    'WPForms\\Vendor\\Stripe\\RequestTelemetry' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/RequestTelemetry.php',
    'WPForms\\Vendor\\Stripe\\ReserveTransaction' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/ReserveTransaction.php',
    'WPForms\\Vendor\\Stripe\\Review' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Review.php',
    'WPForms\\Vendor\\Stripe\\SearchResult' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/SearchResult.php',
    'WPForms\\Vendor\\Stripe\\Service\\AbstractService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/AbstractService.php',
    'WPForms\\Vendor\\Stripe\\Service\\AbstractServiceFactory' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/AbstractServiceFactory.php',
    'WPForms\\Vendor\\Stripe\\Service\\AccountLinkService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/AccountLinkService.php',
    'WPForms\\Vendor\\Stripe\\Service\\AccountService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/AccountService.php',
    'WPForms\\Vendor\\Stripe\\Service\\AccountSessionService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/AccountSessionService.php',
    'WPForms\\Vendor\\Stripe\\Service\\ApplePayDomainService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/ApplePayDomainService.php',
    'WPForms\\Vendor\\Stripe\\Service\\ApplicationFeeService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/ApplicationFeeService.php',
    'WPForms\\Vendor\\Stripe\\Service\\Apps\\AppsServiceFactory' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/Apps/AppsServiceFactory.php',
    'WPForms\\Vendor\\Stripe\\Service\\Apps\\SecretService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/Apps/SecretService.php',
    'WPForms\\Vendor\\Stripe\\Service\\BalanceService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/BalanceService.php',
    'WPForms\\Vendor\\Stripe\\Service\\BalanceTransactionService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/BalanceTransactionService.php',
    'WPForms\\Vendor\\Stripe\\Service\\BillingPortal\\BillingPortalServiceFactory' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/BillingPortal/BillingPortalServiceFactory.php',
    'WPForms\\Vendor\\Stripe\\Service\\BillingPortal\\ConfigurationService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/BillingPortal/ConfigurationService.php',
    'WPForms\\Vendor\\Stripe\\Service\\BillingPortal\\SessionService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/BillingPortal/SessionService.php',
    'WPForms\\Vendor\\Stripe\\Service\\Billing\\AlertService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/Billing/AlertService.php',
    'WPForms\\Vendor\\Stripe\\Service\\Billing\\BillingServiceFactory' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/Billing/BillingServiceFactory.php',
    'WPForms\\Vendor\\Stripe\\Service\\Billing\\CreditBalanceSummaryService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/Billing/CreditBalanceSummaryService.php',
    'WPForms\\Vendor\\Stripe\\Service\\Billing\\CreditBalanceTransactionService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/Billing/CreditBalanceTransactionService.php',
    'WPForms\\Vendor\\Stripe\\Service\\Billing\\CreditGrantService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/Billing/CreditGrantService.php',
    'WPForms\\Vendor\\Stripe\\Service\\Billing\\MeterEventAdjustmentService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/Billing/MeterEventAdjustmentService.php',
    'WPForms\\Vendor\\Stripe\\Service\\Billing\\MeterEventService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/Billing/MeterEventService.php',
    'WPForms\\Vendor\\Stripe\\Service\\Billing\\MeterService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/Billing/MeterService.php',
    'WPForms\\Vendor\\Stripe\\Service\\ChargeService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/ChargeService.php',
    'WPForms\\Vendor\\Stripe\\Service\\Checkout\\CheckoutServiceFactory' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/Checkout/CheckoutServiceFactory.php',
    'WPForms\\Vendor\\Stripe\\Service\\Checkout\\SessionService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/Checkout/SessionService.php',
    'WPForms\\Vendor\\Stripe\\Service\\Climate\\ClimateServiceFactory' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/Climate/ClimateServiceFactory.php',
    'WPForms\\Vendor\\Stripe\\Service\\Climate\\OrderService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/Climate/OrderService.php',
    'WPForms\\Vendor\\Stripe\\Service\\Climate\\ProductService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/Climate/ProductService.php',
    'WPForms\\Vendor\\Stripe\\Service\\Climate\\SupplierService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/Climate/SupplierService.php',
    'WPForms\\Vendor\\Stripe\\Service\\ConfirmationTokenService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/ConfirmationTokenService.php',
    'WPForms\\Vendor\\Stripe\\Service\\CoreServiceFactory' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/CoreServiceFactory.php',
    'WPForms\\Vendor\\Stripe\\Service\\CountrySpecService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/CountrySpecService.php',
    'WPForms\\Vendor\\Stripe\\Service\\CouponService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/CouponService.php',
    'WPForms\\Vendor\\Stripe\\Service\\CreditNoteService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/CreditNoteService.php',
    'WPForms\\Vendor\\Stripe\\Service\\CustomerService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/CustomerService.php',
    'WPForms\\Vendor\\Stripe\\Service\\CustomerSessionService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/CustomerSessionService.php',
    'WPForms\\Vendor\\Stripe\\Service\\DisputeService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/DisputeService.php',
    'WPForms\\Vendor\\Stripe\\Service\\Entitlements\\ActiveEntitlementService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/Entitlements/ActiveEntitlementService.php',
    'WPForms\\Vendor\\Stripe\\Service\\Entitlements\\EntitlementsServiceFactory' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/Entitlements/EntitlementsServiceFactory.php',
    'WPForms\\Vendor\\Stripe\\Service\\Entitlements\\FeatureService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/Entitlements/FeatureService.php',
    'WPForms\\Vendor\\Stripe\\Service\\EphemeralKeyService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/EphemeralKeyService.php',
    'WPForms\\Vendor\\Stripe\\Service\\EventService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/EventService.php',
    'WPForms\\Vendor\\Stripe\\Service\\ExchangeRateService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/ExchangeRateService.php',
    'WPForms\\Vendor\\Stripe\\Service\\FileLinkService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/FileLinkService.php',
    'WPForms\\Vendor\\Stripe\\Service\\FileService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/FileService.php',
    'WPForms\\Vendor\\Stripe\\Service\\FinancialConnections\\AccountService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/FinancialConnections/AccountService.php',
    'WPForms\\Vendor\\Stripe\\Service\\FinancialConnections\\FinancialConnectionsServiceFactory' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/FinancialConnections/FinancialConnectionsServiceFactory.php',
    'WPForms\\Vendor\\Stripe\\Service\\FinancialConnections\\SessionService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/FinancialConnections/SessionService.php',
    'WPForms\\Vendor\\Stripe\\Service\\FinancialConnections\\TransactionService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/FinancialConnections/TransactionService.php',
    'WPForms\\Vendor\\Stripe\\Service\\Forwarding\\ForwardingServiceFactory' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/Forwarding/ForwardingServiceFactory.php',
    'WPForms\\Vendor\\Stripe\\Service\\Forwarding\\RequestService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/Forwarding/RequestService.php',
    'WPForms\\Vendor\\Stripe\\Service\\Identity\\IdentityServiceFactory' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/Identity/IdentityServiceFactory.php',
    'WPForms\\Vendor\\Stripe\\Service\\Identity\\VerificationReportService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/Identity/VerificationReportService.php',
    'WPForms\\Vendor\\Stripe\\Service\\Identity\\VerificationSessionService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/Identity/VerificationSessionService.php',
    'WPForms\\Vendor\\Stripe\\Service\\InvoiceItemService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/InvoiceItemService.php',
    'WPForms\\Vendor\\Stripe\\Service\\InvoiceRenderingTemplateService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/InvoiceRenderingTemplateService.php',
    'WPForms\\Vendor\\Stripe\\Service\\InvoiceService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/InvoiceService.php',
    'WPForms\\Vendor\\Stripe\\Service\\Issuing\\AuthorizationService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/Issuing/AuthorizationService.php',
    'WPForms\\Vendor\\Stripe\\Service\\Issuing\\CardService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/Issuing/CardService.php',
    'WPForms\\Vendor\\Stripe\\Service\\Issuing\\CardholderService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/Issuing/CardholderService.php',
    'WPForms\\Vendor\\Stripe\\Service\\Issuing\\DisputeService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/Issuing/DisputeService.php',
    'WPForms\\Vendor\\Stripe\\Service\\Issuing\\IssuingServiceFactory' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/Issuing/IssuingServiceFactory.php',
    'WPForms\\Vendor\\Stripe\\Service\\Issuing\\PersonalizationDesignService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/Issuing/PersonalizationDesignService.php',
    'WPForms\\Vendor\\Stripe\\Service\\Issuing\\PhysicalBundleService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/Issuing/PhysicalBundleService.php',
    'WPForms\\Vendor\\Stripe\\Service\\Issuing\\TokenService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/Issuing/TokenService.php',
    'WPForms\\Vendor\\Stripe\\Service\\Issuing\\TransactionService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/Issuing/TransactionService.php',
    'WPForms\\Vendor\\Stripe\\Service\\MandateService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/MandateService.php',
    'WPForms\\Vendor\\Stripe\\Service\\OAuthService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/OAuthService.php',
    'WPForms\\Vendor\\Stripe\\Service\\PaymentIntentService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/PaymentIntentService.php',
    'WPForms\\Vendor\\Stripe\\Service\\PaymentLinkService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/PaymentLinkService.php',
    'WPForms\\Vendor\\Stripe\\Service\\PaymentMethodConfigurationService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/PaymentMethodConfigurationService.php',
    'WPForms\\Vendor\\Stripe\\Service\\PaymentMethodDomainService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/PaymentMethodDomainService.php',
    'WPForms\\Vendor\\Stripe\\Service\\PaymentMethodService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/PaymentMethodService.php',
    'WPForms\\Vendor\\Stripe\\Service\\PayoutService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/PayoutService.php',
    'WPForms\\Vendor\\Stripe\\Service\\PlanService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/PlanService.php',
    'WPForms\\Vendor\\Stripe\\Service\\PriceService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/PriceService.php',
    'WPForms\\Vendor\\Stripe\\Service\\ProductService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/ProductService.php',
    'WPForms\\Vendor\\Stripe\\Service\\PromotionCodeService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/PromotionCodeService.php',
    'WPForms\\Vendor\\Stripe\\Service\\QuoteService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/QuoteService.php',
    'WPForms\\Vendor\\Stripe\\Service\\Radar\\EarlyFraudWarningService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/Radar/EarlyFraudWarningService.php',
    'WPForms\\Vendor\\Stripe\\Service\\Radar\\RadarServiceFactory' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/Radar/RadarServiceFactory.php',
    'WPForms\\Vendor\\Stripe\\Service\\Radar\\ValueListItemService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/Radar/ValueListItemService.php',
    'WPForms\\Vendor\\Stripe\\Service\\Radar\\ValueListService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/Radar/ValueListService.php',
    'WPForms\\Vendor\\Stripe\\Service\\RefundService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/RefundService.php',
    'WPForms\\Vendor\\Stripe\\Service\\Reporting\\ReportRunService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/Reporting/ReportRunService.php',
    'WPForms\\Vendor\\Stripe\\Service\\Reporting\\ReportTypeService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/Reporting/ReportTypeService.php',
    'WPForms\\Vendor\\Stripe\\Service\\Reporting\\ReportingServiceFactory' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/Reporting/ReportingServiceFactory.php',
    'WPForms\\Vendor\\Stripe\\Service\\ReviewService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/ReviewService.php',
    'WPForms\\Vendor\\Stripe\\Service\\ServiceNavigatorTrait' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/ServiceNavigatorTrait.php',
    'WPForms\\Vendor\\Stripe\\Service\\SetupAttemptService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/SetupAttemptService.php',
    'WPForms\\Vendor\\Stripe\\Service\\SetupIntentService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/SetupIntentService.php',
    'WPForms\\Vendor\\Stripe\\Service\\ShippingRateService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/ShippingRateService.php',
    'WPForms\\Vendor\\Stripe\\Service\\Sigma\\ScheduledQueryRunService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/Sigma/ScheduledQueryRunService.php',
    'WPForms\\Vendor\\Stripe\\Service\\Sigma\\SigmaServiceFactory' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/Sigma/SigmaServiceFactory.php',
    'WPForms\\Vendor\\Stripe\\Service\\SourceService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/SourceService.php',
    'WPForms\\Vendor\\Stripe\\Service\\SubscriptionItemService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/SubscriptionItemService.php',
    'WPForms\\Vendor\\Stripe\\Service\\SubscriptionScheduleService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/SubscriptionScheduleService.php',
    'WPForms\\Vendor\\Stripe\\Service\\SubscriptionService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/SubscriptionService.php',
    'WPForms\\Vendor\\Stripe\\Service\\TaxCodeService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/TaxCodeService.php',
    'WPForms\\Vendor\\Stripe\\Service\\TaxIdService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/TaxIdService.php',
    'WPForms\\Vendor\\Stripe\\Service\\TaxRateService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/TaxRateService.php',
    'WPForms\\Vendor\\Stripe\\Service\\Tax\\CalculationService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/Tax/CalculationService.php',
    'WPForms\\Vendor\\Stripe\\Service\\Tax\\RegistrationService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/Tax/RegistrationService.php',
    'WPForms\\Vendor\\Stripe\\Service\\Tax\\SettingsService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/Tax/SettingsService.php',
    'WPForms\\Vendor\\Stripe\\Service\\Tax\\TaxServiceFactory' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/Tax/TaxServiceFactory.php',
    'WPForms\\Vendor\\Stripe\\Service\\Tax\\TransactionService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/Tax/TransactionService.php',
    'WPForms\\Vendor\\Stripe\\Service\\Terminal\\ConfigurationService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/Terminal/ConfigurationService.php',
    'WPForms\\Vendor\\Stripe\\Service\\Terminal\\ConnectionTokenService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/Terminal/ConnectionTokenService.php',
    'WPForms\\Vendor\\Stripe\\Service\\Terminal\\LocationService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/Terminal/LocationService.php',
    'WPForms\\Vendor\\Stripe\\Service\\Terminal\\ReaderService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/Terminal/ReaderService.php',
    'WPForms\\Vendor\\Stripe\\Service\\Terminal\\TerminalServiceFactory' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/Terminal/TerminalServiceFactory.php',
    'WPForms\\Vendor\\Stripe\\Service\\TestHelpers\\ConfirmationTokenService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/TestHelpers/ConfirmationTokenService.php',
    'WPForms\\Vendor\\Stripe\\Service\\TestHelpers\\CustomerService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/TestHelpers/CustomerService.php',
    'WPForms\\Vendor\\Stripe\\Service\\TestHelpers\\Issuing\\AuthorizationService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/TestHelpers/Issuing/AuthorizationService.php',
    'WPForms\\Vendor\\Stripe\\Service\\TestHelpers\\Issuing\\CardService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/TestHelpers/Issuing/CardService.php',
    'WPForms\\Vendor\\Stripe\\Service\\TestHelpers\\Issuing\\IssuingServiceFactory' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/TestHelpers/Issuing/IssuingServiceFactory.php',
    'WPForms\\Vendor\\Stripe\\Service\\TestHelpers\\Issuing\\PersonalizationDesignService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/TestHelpers/Issuing/PersonalizationDesignService.php',
    'WPForms\\Vendor\\Stripe\\Service\\TestHelpers\\Issuing\\TransactionService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/TestHelpers/Issuing/TransactionService.php',
    'WPForms\\Vendor\\Stripe\\Service\\TestHelpers\\RefundService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/TestHelpers/RefundService.php',
    'WPForms\\Vendor\\Stripe\\Service\\TestHelpers\\Terminal\\ReaderService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/TestHelpers/Terminal/ReaderService.php',
    'WPForms\\Vendor\\Stripe\\Service\\TestHelpers\\Terminal\\TerminalServiceFactory' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/TestHelpers/Terminal/TerminalServiceFactory.php',
    'WPForms\\Vendor\\Stripe\\Service\\TestHelpers\\TestClockService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/TestHelpers/TestClockService.php',
    'WPForms\\Vendor\\Stripe\\Service\\TestHelpers\\TestHelpersServiceFactory' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/TestHelpers/TestHelpersServiceFactory.php',
    'WPForms\\Vendor\\Stripe\\Service\\TestHelpers\\Treasury\\InboundTransferService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/TestHelpers/Treasury/InboundTransferService.php',
    'WPForms\\Vendor\\Stripe\\Service\\TestHelpers\\Treasury\\OutboundPaymentService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/TestHelpers/Treasury/OutboundPaymentService.php',
    'WPForms\\Vendor\\Stripe\\Service\\TestHelpers\\Treasury\\OutboundTransferService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/TestHelpers/Treasury/OutboundTransferService.php',
    'WPForms\\Vendor\\Stripe\\Service\\TestHelpers\\Treasury\\ReceivedCreditService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/TestHelpers/Treasury/ReceivedCreditService.php',
    'WPForms\\Vendor\\Stripe\\Service\\TestHelpers\\Treasury\\ReceivedDebitService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/TestHelpers/Treasury/ReceivedDebitService.php',
    'WPForms\\Vendor\\Stripe\\Service\\TestHelpers\\Treasury\\TreasuryServiceFactory' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/TestHelpers/Treasury/TreasuryServiceFactory.php',
    'WPForms\\Vendor\\Stripe\\Service\\TokenService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/TokenService.php',
    'WPForms\\Vendor\\Stripe\\Service\\TopupService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/TopupService.php',
    'WPForms\\Vendor\\Stripe\\Service\\TransferService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/TransferService.php',
    'WPForms\\Vendor\\Stripe\\Service\\Treasury\\CreditReversalService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/Treasury/CreditReversalService.php',
    'WPForms\\Vendor\\Stripe\\Service\\Treasury\\DebitReversalService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/Treasury/DebitReversalService.php',
    'WPForms\\Vendor\\Stripe\\Service\\Treasury\\FinancialAccountService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/Treasury/FinancialAccountService.php',
    'WPForms\\Vendor\\Stripe\\Service\\Treasury\\InboundTransferService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/Treasury/InboundTransferService.php',
    'WPForms\\Vendor\\Stripe\\Service\\Treasury\\OutboundPaymentService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/Treasury/OutboundPaymentService.php',
    'WPForms\\Vendor\\Stripe\\Service\\Treasury\\OutboundTransferService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/Treasury/OutboundTransferService.php',
    'WPForms\\Vendor\\Stripe\\Service\\Treasury\\ReceivedCreditService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/Treasury/ReceivedCreditService.php',
    'WPForms\\Vendor\\Stripe\\Service\\Treasury\\ReceivedDebitService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/Treasury/ReceivedDebitService.php',
    'WPForms\\Vendor\\Stripe\\Service\\Treasury\\TransactionEntryService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/Treasury/TransactionEntryService.php',
    'WPForms\\Vendor\\Stripe\\Service\\Treasury\\TransactionService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/Treasury/TransactionService.php',
    'WPForms\\Vendor\\Stripe\\Service\\Treasury\\TreasuryServiceFactory' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/Treasury/TreasuryServiceFactory.php',
    'WPForms\\Vendor\\Stripe\\Service\\V2\\Billing\\BillingServiceFactory' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/V2/Billing/BillingServiceFactory.php',
    'WPForms\\Vendor\\Stripe\\Service\\V2\\Billing\\MeterEventAdjustmentService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/V2/Billing/MeterEventAdjustmentService.php',
    'WPForms\\Vendor\\Stripe\\Service\\V2\\Billing\\MeterEventService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/V2/Billing/MeterEventService.php',
    'WPForms\\Vendor\\Stripe\\Service\\V2\\Billing\\MeterEventSessionService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/V2/Billing/MeterEventSessionService.php',
    'WPForms\\Vendor\\Stripe\\Service\\V2\\Billing\\MeterEventStreamService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/V2/Billing/MeterEventStreamService.php',
    'WPForms\\Vendor\\Stripe\\Service\\V2\\Core\\CoreServiceFactory' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/V2/Core/CoreServiceFactory.php',
    'WPForms\\Vendor\\Stripe\\Service\\V2\\Core\\EventDestinationService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/V2/Core/EventDestinationService.php',
    'WPForms\\Vendor\\Stripe\\Service\\V2\\Core\\EventService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/V2/Core/EventService.php',
    'WPForms\\Vendor\\Stripe\\Service\\V2\\V2ServiceFactory' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/V2/V2ServiceFactory.php',
    'WPForms\\Vendor\\Stripe\\Service\\WebhookEndpointService' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Service/WebhookEndpointService.php',
    'WPForms\\Vendor\\Stripe\\SetupAttempt' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/SetupAttempt.php',
    'WPForms\\Vendor\\Stripe\\SetupIntent' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/SetupIntent.php',
    'WPForms\\Vendor\\Stripe\\ShippingRate' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/ShippingRate.php',
    'WPForms\\Vendor\\Stripe\\Sigma\\ScheduledQueryRun' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Sigma/ScheduledQueryRun.php',
    'WPForms\\Vendor\\Stripe\\SingletonApiResource' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/SingletonApiResource.php',
    'WPForms\\Vendor\\Stripe\\Source' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Source.php',
    'WPForms\\Vendor\\Stripe\\SourceMandateNotification' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/SourceMandateNotification.php',
    'WPForms\\Vendor\\Stripe\\SourceTransaction' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/SourceTransaction.php',
    'WPForms\\Vendor\\Stripe\\Stripe' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Stripe.php',
    'WPForms\\Vendor\\Stripe\\StripeClient' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/StripeClient.php',
    'WPForms\\Vendor\\Stripe\\StripeClientInterface' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/StripeClientInterface.php',
    'WPForms\\Vendor\\Stripe\\StripeObject' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/StripeObject.php',
    'WPForms\\Vendor\\Stripe\\StripeStreamingClientInterface' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/StripeStreamingClientInterface.php',
    'WPForms\\Vendor\\Stripe\\Subscription' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Subscription.php',
    'WPForms\\Vendor\\Stripe\\SubscriptionItem' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/SubscriptionItem.php',
    'WPForms\\Vendor\\Stripe\\SubscriptionSchedule' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/SubscriptionSchedule.php',
    'WPForms\\Vendor\\Stripe\\TaxCode' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/TaxCode.php',
    'WPForms\\Vendor\\Stripe\\TaxDeductedAtSource' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/TaxDeductedAtSource.php',
    'WPForms\\Vendor\\Stripe\\TaxId' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/TaxId.php',
    'WPForms\\Vendor\\Stripe\\TaxRate' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/TaxRate.php',
    'WPForms\\Vendor\\Stripe\\Tax\\Calculation' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Tax/Calculation.php',
    'WPForms\\Vendor\\Stripe\\Tax\\CalculationLineItem' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Tax/CalculationLineItem.php',
    'WPForms\\Vendor\\Stripe\\Tax\\Registration' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Tax/Registration.php',
    'WPForms\\Vendor\\Stripe\\Tax\\Settings' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Tax/Settings.php',
    'WPForms\\Vendor\\Stripe\\Tax\\Transaction' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Tax/Transaction.php',
    'WPForms\\Vendor\\Stripe\\Tax\\TransactionLineItem' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Tax/TransactionLineItem.php',
    'WPForms\\Vendor\\Stripe\\Terminal\\Configuration' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Terminal/Configuration.php',
    'WPForms\\Vendor\\Stripe\\Terminal\\ConnectionToken' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Terminal/ConnectionToken.php',
    'WPForms\\Vendor\\Stripe\\Terminal\\Location' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Terminal/Location.php',
    'WPForms\\Vendor\\Stripe\\Terminal\\Reader' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Terminal/Reader.php',
    'WPForms\\Vendor\\Stripe\\TestHelpers\\TestClock' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/TestHelpers/TestClock.php',
    'WPForms\\Vendor\\Stripe\\ThinEvent' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/ThinEvent.php',
    'WPForms\\Vendor\\Stripe\\Token' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Token.php',
    'WPForms\\Vendor\\Stripe\\Topup' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Topup.php',
    'WPForms\\Vendor\\Stripe\\Transfer' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Transfer.php',
    'WPForms\\Vendor\\Stripe\\TransferReversal' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/TransferReversal.php',
    'WPForms\\Vendor\\Stripe\\Treasury\\CreditReversal' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Treasury/CreditReversal.php',
    'WPForms\\Vendor\\Stripe\\Treasury\\DebitReversal' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Treasury/DebitReversal.php',
    'WPForms\\Vendor\\Stripe\\Treasury\\FinancialAccount' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Treasury/FinancialAccount.php',
    'WPForms\\Vendor\\Stripe\\Treasury\\FinancialAccountFeatures' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Treasury/FinancialAccountFeatures.php',
    'WPForms\\Vendor\\Stripe\\Treasury\\InboundTransfer' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Treasury/InboundTransfer.php',
    'WPForms\\Vendor\\Stripe\\Treasury\\OutboundPayment' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Treasury/OutboundPayment.php',
    'WPForms\\Vendor\\Stripe\\Treasury\\OutboundTransfer' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Treasury/OutboundTransfer.php',
    'WPForms\\Vendor\\Stripe\\Treasury\\ReceivedCredit' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Treasury/ReceivedCredit.php',
    'WPForms\\Vendor\\Stripe\\Treasury\\ReceivedDebit' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Treasury/ReceivedDebit.php',
    'WPForms\\Vendor\\Stripe\\Treasury\\Transaction' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Treasury/Transaction.php',
    'WPForms\\Vendor\\Stripe\\Treasury\\TransactionEntry' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Treasury/TransactionEntry.php',
    'WPForms\\Vendor\\Stripe\\UsageRecord' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/UsageRecord.php',
    'WPForms\\Vendor\\Stripe\\UsageRecordSummary' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/UsageRecordSummary.php',
    'WPForms\\Vendor\\Stripe\\Util\\ApiVersion' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Util/ApiVersion.php',
    'WPForms\\Vendor\\Stripe\\Util\\CaseInsensitiveArray' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Util/CaseInsensitiveArray.php',
    'WPForms\\Vendor\\Stripe\\Util\\DefaultLogger' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Util/DefaultLogger.php',
    'WPForms\\Vendor\\Stripe\\Util\\EventTypes' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Util/EventTypes.php',
    'WPForms\\Vendor\\Stripe\\Util\\LoggerInterface' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Util/LoggerInterface.php',
    'WPForms\\Vendor\\Stripe\\Util\\ObjectTypes' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Util/ObjectTypes.php',
    'WPForms\\Vendor\\Stripe\\Util\\RandomGenerator' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Util/RandomGenerator.php',
    'WPForms\\Vendor\\Stripe\\Util\\RequestOptions' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Util/RequestOptions.php',
    'WPForms\\Vendor\\Stripe\\Util\\Set' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Util/Set.php',
    'WPForms\\Vendor\\Stripe\\Util\\Util' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Util/Util.php',
    'WPForms\\Vendor\\Stripe\\V2\\Billing\\MeterEvent' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/V2/Billing/MeterEvent.php',
    'WPForms\\Vendor\\Stripe\\V2\\Billing\\MeterEventAdjustment' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/V2/Billing/MeterEventAdjustment.php',
    'WPForms\\Vendor\\Stripe\\V2\\Billing\\MeterEventSession' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/V2/Billing/MeterEventSession.php',
    'WPForms\\Vendor\\Stripe\\V2\\Collection' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/V2/Collection.php',
    'WPForms\\Vendor\\Stripe\\V2\\Event' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/V2/Event.php',
    'WPForms\\Vendor\\Stripe\\V2\\EventDestination' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/V2/EventDestination.php',
    'WPForms\\Vendor\\Stripe\\Webhook' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/Webhook.php',
    'WPForms\\Vendor\\Stripe\\WebhookEndpoint' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/WebhookEndpoint.php',
    'WPForms\\Vendor\\Stripe\\WebhookSignature' => $baseDir . '/vendor_prefixed/stripe/stripe-php/lib/WebhookSignature.php',
    'WPForms\\Vendor\\Symfony\\Component\\CssSelector\\CssSelectorConverter' => $baseDir . '/vendor_prefixed/symfony/css-selector/CssSelectorConverter.php',
    'WPForms\\Vendor\\Symfony\\Component\\CssSelector\\Exception\\ExceptionInterface' => $baseDir . '/vendor_prefixed/symfony/css-selector/Exception/ExceptionInterface.php',
    'WPForms\\Vendor\\Symfony\\Component\\CssSelector\\Exception\\ExpressionErrorException' => $baseDir . '/vendor_prefixed/symfony/css-selector/Exception/ExpressionErrorException.php',
    'WPForms\\Vendor\\Symfony\\Component\\CssSelector\\Exception\\InternalErrorException' => $baseDir . '/vendor_prefixed/symfony/css-selector/Exception/InternalErrorException.php',
    'WPForms\\Vendor\\Symfony\\Component\\CssSelector\\Exception\\ParseException' => $baseDir . '/vendor_prefixed/symfony/css-selector/Exception/ParseException.php',
    'WPForms\\Vendor\\Symfony\\Component\\CssSelector\\Exception\\SyntaxErrorException' => $baseDir . '/vendor_prefixed/symfony/css-selector/Exception/SyntaxErrorException.php',
    'WPForms\\Vendor\\Symfony\\Component\\CssSelector\\Node\\AbstractNode' => $baseDir . '/vendor_prefixed/symfony/css-selector/Node/AbstractNode.php',
    'WPForms\\Vendor\\Symfony\\Component\\CssSelector\\Node\\AttributeNode' => $baseDir . '/vendor_prefixed/symfony/css-selector/Node/AttributeNode.php',
    'WPForms\\Vendor\\Symfony\\Component\\CssSelector\\Node\\ClassNode' => $baseDir . '/vendor_prefixed/symfony/css-selector/Node/ClassNode.php',
    'WPForms\\Vendor\\Symfony\\Component\\CssSelector\\Node\\CombinedSelectorNode' => $baseDir . '/vendor_prefixed/symfony/css-selector/Node/CombinedSelectorNode.php',
    'WPForms\\Vendor\\Symfony\\Component\\CssSelector\\Node\\ElementNode' => $baseDir . '/vendor_prefixed/symfony/css-selector/Node/ElementNode.php',
    'WPForms\\Vendor\\Symfony\\Component\\CssSelector\\Node\\FunctionNode' => $baseDir . '/vendor_prefixed/symfony/css-selector/Node/FunctionNode.php',
    'WPForms\\Vendor\\Symfony\\Component\\CssSelector\\Node\\HashNode' => $baseDir . '/vendor_prefixed/symfony/css-selector/Node/HashNode.php',
    'WPForms\\Vendor\\Symfony\\Component\\CssSelector\\Node\\NegationNode' => $baseDir . '/vendor_prefixed/symfony/css-selector/Node/NegationNode.php',
    'WPForms\\Vendor\\Symfony\\Component\\CssSelector\\Node\\NodeInterface' => $baseDir . '/vendor_prefixed/symfony/css-selector/Node/NodeInterface.php',
    'WPForms\\Vendor\\Symfony\\Component\\CssSelector\\Node\\PseudoNode' => $baseDir . '/vendor_prefixed/symfony/css-selector/Node/PseudoNode.php',
    'WPForms\\Vendor\\Symfony\\Component\\CssSelector\\Node\\SelectorNode' => $baseDir . '/vendor_prefixed/symfony/css-selector/Node/SelectorNode.php',
    'WPForms\\Vendor\\Symfony\\Component\\CssSelector\\Node\\Specificity' => $baseDir . '/vendor_prefixed/symfony/css-selector/Node/Specificity.php',
    'WPForms\\Vendor\\Symfony\\Component\\CssSelector\\Parser\\Handler\\CommentHandler' => $baseDir . '/vendor_prefixed/symfony/css-selector/Parser/Handler/CommentHandler.php',
    'WPForms\\Vendor\\Symfony\\Component\\CssSelector\\Parser\\Handler\\HandlerInterface' => $baseDir . '/vendor_prefixed/symfony/css-selector/Parser/Handler/HandlerInterface.php',
    'WPForms\\Vendor\\Symfony\\Component\\CssSelector\\Parser\\Handler\\HashHandler' => $baseDir . '/vendor_prefixed/symfony/css-selector/Parser/Handler/HashHandler.php',
    'WPForms\\Vendor\\Symfony\\Component\\CssSelector\\Parser\\Handler\\IdentifierHandler' => $baseDir . '/vendor_prefixed/symfony/css-selector/Parser/Handler/IdentifierHandler.php',
    'WPForms\\Vendor\\Symfony\\Component\\CssSelector\\Parser\\Handler\\NumberHandler' => $baseDir . '/vendor_prefixed/symfony/css-selector/Parser/Handler/NumberHandler.php',
    'WPForms\\Vendor\\Symfony\\Component\\CssSelector\\Parser\\Handler\\StringHandler' => $baseDir . '/vendor_prefixed/symfony/css-selector/Parser/Handler/StringHandler.php',
    'WPForms\\Vendor\\Symfony\\Component\\CssSelector\\Parser\\Handler\\WhitespaceHandler' => $baseDir . '/vendor_prefixed/symfony/css-selector/Parser/Handler/WhitespaceHandler.php',
    'WPForms\\Vendor\\Symfony\\Component\\CssSelector\\Parser\\Parser' => $baseDir . '/vendor_prefixed/symfony/css-selector/Parser/Parser.php',
    'WPForms\\Vendor\\Symfony\\Component\\CssSelector\\Parser\\ParserInterface' => $baseDir . '/vendor_prefixed/symfony/css-selector/Parser/ParserInterface.php',
    'WPForms\\Vendor\\Symfony\\Component\\CssSelector\\Parser\\Reader' => $baseDir . '/vendor_prefixed/symfony/css-selector/Parser/Reader.php',
    'WPForms\\Vendor\\Symfony\\Component\\CssSelector\\Parser\\Shortcut\\ClassParser' => $baseDir . '/vendor_prefixed/symfony/css-selector/Parser/Shortcut/ClassParser.php',
    'WPForms\\Vendor\\Symfony\\Component\\CssSelector\\Parser\\Shortcut\\ElementParser' => $baseDir . '/vendor_prefixed/symfony/css-selector/Parser/Shortcut/ElementParser.php',
    'WPForms\\Vendor\\Symfony\\Component\\CssSelector\\Parser\\Shortcut\\EmptyStringParser' => $baseDir . '/vendor_prefixed/symfony/css-selector/Parser/Shortcut/EmptyStringParser.php',
    'WPForms\\Vendor\\Symfony\\Component\\CssSelector\\Parser\\Shortcut\\HashParser' => $baseDir . '/vendor_prefixed/symfony/css-selector/Parser/Shortcut/HashParser.php',
    'WPForms\\Vendor\\Symfony\\Component\\CssSelector\\Parser\\Token' => $baseDir . '/vendor_prefixed/symfony/css-selector/Parser/Token.php',
    'WPForms\\Vendor\\Symfony\\Component\\CssSelector\\Parser\\TokenStream' => $baseDir . '/vendor_prefixed/symfony/css-selector/Parser/TokenStream.php',
    'WPForms\\Vendor\\Symfony\\Component\\CssSelector\\Parser\\Tokenizer\\Tokenizer' => $baseDir . '/vendor_prefixed/symfony/css-selector/Parser/Tokenizer/Tokenizer.php',
    'WPForms\\Vendor\\Symfony\\Component\\CssSelector\\Parser\\Tokenizer\\TokenizerEscaping' => $baseDir . '/vendor_prefixed/symfony/css-selector/Parser/Tokenizer/TokenizerEscaping.php',
    'WPForms\\Vendor\\Symfony\\Component\\CssSelector\\Parser\\Tokenizer\\TokenizerPatterns' => $baseDir . '/vendor_prefixed/symfony/css-selector/Parser/Tokenizer/TokenizerPatterns.php',
    'WPForms\\Vendor\\Symfony\\Component\\CssSelector\\XPath\\Extension\\AbstractExtension' => $baseDir . '/vendor_prefixed/symfony/css-selector/XPath/Extension/AbstractExtension.php',
    'WPForms\\Vendor\\Symfony\\Component\\CssSelector\\XPath\\Extension\\AttributeMatchingExtension' => $baseDir . '/vendor_prefixed/symfony/css-selector/XPath/Extension/AttributeMatchingExtension.php',
    'WPForms\\Vendor\\Symfony\\Component\\CssSelector\\XPath\\Extension\\CombinationExtension' => $baseDir . '/vendor_prefixed/symfony/css-selector/XPath/Extension/CombinationExtension.php',
    'WPForms\\Vendor\\Symfony\\Component\\CssSelector\\XPath\\Extension\\ExtensionInterface' => $baseDir . '/vendor_prefixed/symfony/css-selector/XPath/Extension/ExtensionInterface.php',
    'WPForms\\Vendor\\Symfony\\Component\\CssSelector\\XPath\\Extension\\FunctionExtension' => $baseDir . '/vendor_prefixed/symfony/css-selector/XPath/Extension/FunctionExtension.php',
    'WPForms\\Vendor\\Symfony\\Component\\CssSelector\\XPath\\Extension\\HtmlExtension' => $baseDir . '/vendor_prefixed/symfony/css-selector/XPath/Extension/HtmlExtension.php',
    'WPForms\\Vendor\\Symfony\\Component\\CssSelector\\XPath\\Extension\\NodeExtension' => $baseDir . '/vendor_prefixed/symfony/css-selector/XPath/Extension/NodeExtension.php',
    'WPForms\\Vendor\\Symfony\\Component\\CssSelector\\XPath\\Extension\\PseudoClassExtension' => $baseDir . '/vendor_prefixed/symfony/css-selector/XPath/Extension/PseudoClassExtension.php',
    'WPForms\\Vendor\\Symfony\\Component\\CssSelector\\XPath\\Translator' => $baseDir . '/vendor_prefixed/symfony/css-selector/XPath/Translator.php',
    'WPForms\\Vendor\\Symfony\\Component\\CssSelector\\XPath\\TranslatorInterface' => $baseDir . '/vendor_prefixed/symfony/css-selector/XPath/TranslatorInterface.php',
    'WPForms\\Vendor\\Symfony\\Component\\CssSelector\\XPath\\XPathExpr' => $baseDir . '/vendor_prefixed/symfony/css-selector/XPath/XPathExpr.php',
    'WPForms\\Vendor\\TijsVerkoyen\\CssToInlineStyles\\CssToInlineStyles' => $baseDir . '/vendor_prefixed/tijsverkoyen/css-to-inline-styles/src/CssToInlineStyles.php',
    'WPForms\\Vendor\\TijsVerkoyen\\CssToInlineStyles\\Css\\Processor' => $baseDir . '/vendor_prefixed/tijsverkoyen/css-to-inline-styles/src/Css/Processor.php',
    'WPForms\\Vendor\\TijsVerkoyen\\CssToInlineStyles\\Css\\Property\\Processor' => $baseDir . '/vendor_prefixed/tijsverkoyen/css-to-inline-styles/src/Css/Property/Processor.php',
    'WPForms\\Vendor\\TijsVerkoyen\\CssToInlineStyles\\Css\\Property\\Property' => $baseDir . '/vendor_prefixed/tijsverkoyen/css-to-inline-styles/src/Css/Property/Property.php',
    'WPForms\\Vendor\\TijsVerkoyen\\CssToInlineStyles\\Css\\Rule\\Processor' => $baseDir . '/vendor_prefixed/tijsverkoyen/css-to-inline-styles/src/Css/Rule/Processor.php',
    'WPForms\\Vendor\\TijsVerkoyen\\CssToInlineStyles\\Css\\Rule\\Rule' => $baseDir . '/vendor_prefixed/tijsverkoyen/css-to-inline-styles/src/Css/Rule/Rule.php',
    'WPForms\\Vendor\\TrueBV\\Exception\\DomainOutOfBoundsException' => $baseDir . '/vendor_prefixed/true/punycode/src/Exception/DomainOutOfBoundsException.php',
    'WPForms\\Vendor\\TrueBV\\Exception\\LabelOutOfBoundsException' => $baseDir . '/vendor_prefixed/true/punycode/src/Exception/LabelOutOfBoundsException.php',
    'WPForms\\Vendor\\TrueBV\\Exception\\OutOfBoundsException' => $baseDir . '/vendor_prefixed/true/punycode/src/Exception/OutOfBoundsException.php',
    'WPForms\\Vendor\\TrueBV\\Punycode' => $baseDir . '/vendor_prefixed/true/punycode/src/Punycode.php',
    'WPForms\\Vendor\\Unirest\\Configuration' => $baseDir . '/vendor_prefixed/apimatic/unirest-php/src/Configuration.php',
    'WPForms\\Vendor\\Unirest\\HttpClient' => $baseDir . '/vendor_prefixed/apimatic/unirest-php/src/HttpClient.php',
    'WPForms\\Vendor\\Unirest\\Request\\Body' => $baseDir . '/vendor_prefixed/apimatic/unirest-php/src/Request/Body.php',
    'WPForms\\Vendor\\Unirest\\Request\\Request' => $baseDir . '/vendor_prefixed/apimatic/unirest-php/src/Request/Request.php',
    'WPForms\\Vendor\\Unirest\\Response' => $baseDir . '/vendor_prefixed/apimatic/unirest-php/src/Response.php',
    'WPForms\\Vendor\\Unirest\\Test\\BodyTest' => $baseDir . '/vendor_prefixed/apimatic/unirest-php/tests/BodyTest.php',
    'WPForms\\Vendor\\Unirest\\Test\\Mocking\\HttpClientChild' => $baseDir . '/vendor_prefixed/apimatic/unirest-php/tests/Mocking/HttpClientChild.php',
    'WPForms\\Vendor\\Unirest\\Test\\RequestTest' => $baseDir . '/vendor_prefixed/apimatic/unirest-php/tests/RequestTest.php',
    'WPForms\\Vendor\\Unirest\\Test\\ResponseTest' => $baseDir . '/vendor_prefixed/apimatic/unirest-php/tests/ResponseTest.php',
    'WPForms\\Vendor\\apimatic\\jsonmapper\\AnyOfValidationException' => $baseDir . '/vendor_prefixed/apimatic/jsonmapper/src/AnyOfValidationException.php',
    'WPForms\\Vendor\\apimatic\\jsonmapper\\JsonMapper' => $baseDir . '/vendor_prefixed/apimatic/jsonmapper/src/JsonMapper.php',
    'WPForms\\Vendor\\apimatic\\jsonmapper\\JsonMapperException' => $baseDir . '/vendor_prefixed/apimatic/jsonmapper/src/JsonMapperException.php',
    'WPForms\\Vendor\\apimatic\\jsonmapper\\OneOfValidationException' => $baseDir . '/vendor_prefixed/apimatic/jsonmapper/src/OneOfValidationException.php',
    'WPForms\\Vendor\\apimatic\\jsonmapper\\TypeCombination' => $baseDir . '/vendor_prefixed/apimatic/jsonmapper/src/TypeCombination.php',
    'WPForms\\Vendor\\multitypetest\\MultiTypeJsonMapper' => $baseDir . '/vendor_prefixed/apimatic/jsonmapper/tests/multitypetest/MultiTypeJsonMapper.php',
    'WPForms\\Vendor\\multitypetest\\MultiTypeTest' => $baseDir . '/vendor_prefixed/apimatic/jsonmapper/tests/multitypetest/MultiTypeTest.php',
    'WPForms\\Vendor\\multitypetest\\model\\Atom' => $baseDir . '/vendor_prefixed/apimatic/jsonmapper/tests/multitypetest/model/Atom.php',
    'WPForms\\Vendor\\multitypetest\\model\\Car' => $baseDir . '/vendor_prefixed/apimatic/jsonmapper/tests/multitypetest/model/Car.php',
    'WPForms\\Vendor\\multitypetest\\model\\ComplexCaseA' => $baseDir . '/vendor_prefixed/apimatic/jsonmapper/tests/multitypetest/model/ComplexCaseA.php',
    'WPForms\\Vendor\\multitypetest\\model\\ComplexCaseB' => $baseDir . '/vendor_prefixed/apimatic/jsonmapper/tests/multitypetest/model/ComplexCaseB.php',
    'WPForms\\Vendor\\multitypetest\\model\\DateTimeHelper' => $baseDir . '/vendor_prefixed/apimatic/jsonmapper/tests/multitypetest/model/DateTimeHelper.php',
    'WPForms\\Vendor\\multitypetest\\model\\DaysEnum' => $baseDir . '/vendor_prefixed/apimatic/jsonmapper/tests/multitypetest/model/DaysEnum.php',
    'WPForms\\Vendor\\multitypetest\\model\\Deer' => $baseDir . '/vendor_prefixed/apimatic/jsonmapper/tests/multitypetest/model/Deer.php',
    'WPForms\\Vendor\\multitypetest\\model\\Employee' => $baseDir . '/vendor_prefixed/apimatic/jsonmapper/tests/multitypetest/model/Employee.php',
    'WPForms\\Vendor\\multitypetest\\model\\Evening' => $baseDir . '/vendor_prefixed/apimatic/jsonmapper/tests/multitypetest/model/Evening.php',
    'WPForms\\Vendor\\multitypetest\\model\\Lion' => $baseDir . '/vendor_prefixed/apimatic/jsonmapper/tests/multitypetest/model/Lion.php',
    'WPForms\\Vendor\\multitypetest\\model\\MonthNameEnum' => $baseDir . '/vendor_prefixed/apimatic/jsonmapper/tests/multitypetest/model/MonthNameEnum.php',
    'WPForms\\Vendor\\multitypetest\\model\\MonthNumberEnum' => $baseDir . '/vendor_prefixed/apimatic/jsonmapper/tests/multitypetest/model/MonthNumberEnum.php',
    'WPForms\\Vendor\\multitypetest\\model\\Morning' => $baseDir . '/vendor_prefixed/apimatic/jsonmapper/tests/multitypetest/model/Morning.php',
    'WPForms\\Vendor\\multitypetest\\model\\Orbit' => $baseDir . '/vendor_prefixed/apimatic/jsonmapper/tests/multitypetest/model/Orbit.php',
    'WPForms\\Vendor\\multitypetest\\model\\OuterArrayCase' => $baseDir . '/vendor_prefixed/apimatic/jsonmapper/tests/multitypetest/model/OuterArrayCase.php',
    'WPForms\\Vendor\\multitypetest\\model\\Person' => $baseDir . '/vendor_prefixed/apimatic/jsonmapper/tests/multitypetest/model/Person.php',
    'WPForms\\Vendor\\multitypetest\\model\\Postman' => $baseDir . '/vendor_prefixed/apimatic/jsonmapper/tests/multitypetest/model/Postman.php',
    'WPForms\\Vendor\\multitypetest\\model\\SimpleCase' => $baseDir . '/vendor_prefixed/apimatic/jsonmapper/tests/multitypetest/model/SimpleCase.php',
    'WPForms\\Vendor\\multitypetest\\model\\SimpleCaseA' => $baseDir . '/vendor_prefixed/apimatic/jsonmapper/tests/multitypetest/model/SimpleCaseA.php',
    'WPForms\\Vendor\\multitypetest\\model\\SimpleCaseB' => $baseDir . '/vendor_prefixed/apimatic/jsonmapper/tests/multitypetest/model/SimpleCaseB.php',
    'WPForms\\Vendor\\multitypetest\\model\\Vehicle' => $baseDir . '/vendor_prefixed/apimatic/jsonmapper/tests/multitypetest/model/Vehicle.php',
    'WPForms\\Vendor\\multitypetest\\model\\Vehicle2' => $baseDir . '/vendor_prefixed/apimatic/jsonmapper/tests/multitypetest/model/Vehicle2.php',
    'WPForms\\Vendor\\namespacetest\\NamespaceTest' => $baseDir . '/vendor_prefixed/apimatic/jsonmapper/tests/namespacetest/NamespaceTest.php',
    'WPForms\\Vendor\\namespacetest\\Unit' => $baseDir . '/vendor_prefixed/apimatic/jsonmapper/tests/namespacetest/Unit.php',
    'WPForms\\Vendor\\namespacetest\\UnitData' => $baseDir . '/vendor_prefixed/apimatic/jsonmapper/tests/namespacetest/UnitData.php',
    'WPForms\\Vendor\\namespacetest\\model\\Group' => $baseDir . '/vendor_prefixed/apimatic/jsonmapper/tests/namespacetest/model/Group.php',
    'WPForms\\Vendor\\namespacetest\\model\\User' => $baseDir . '/vendor_prefixed/apimatic/jsonmapper/tests/namespacetest/model/User.php',
    'WPForms\\Vendor\\namespacetest\\model\\UserList' => $baseDir . '/vendor_prefixed/apimatic/jsonmapper/tests/namespacetest/model/UserList.php',
    'WPForms\\Vendor\\othernamespace\\Foo' => $baseDir . '/vendor_prefixed/apimatic/jsonmapper/tests/othernamespace/Foo.php',
    'WPForms\\Vendor\\othernamespace\\Programmers' => $baseDir . '/vendor_prefixed/apimatic/jsonmapper/tests/othernamespace/Programmers.php',
);
