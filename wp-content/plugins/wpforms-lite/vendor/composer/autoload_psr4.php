<?php

// autoload_psr4.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'WPForms\\Tests\\Unit\\' => array($baseDir . '/tests/unit'),
    'WPForms\\Tests\\Integration\\' => array($baseDir . '/tests/integration'),
    'WPForms\\Scoper\\' => array($baseDir . '/../.php-scoper'),
    'WPForms\\' => array($baseDir . '/src', $baseDir . '/src'),
    'Symfony\\Polyfill\\Php80\\' => array($vendorDir . '/symfony/polyfill-php80'),
    'Symfony\\Polyfill\\Mbstring\\' => array($vendorDir . '/symfony/polyfill-mbstring'),
    'Symfony\\Polyfill\\Iconv\\' => array($vendorDir . '/symfony/polyfill-iconv'),
    'Psr\\Log\\' => array($vendorDir . '/psr/log/Psr/Log'),
);
