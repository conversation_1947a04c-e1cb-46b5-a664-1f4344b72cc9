<?php return array(
    'root' => array(
        'name' => 'awesomemotive/wpforms',
        'pretty_version' => 'dev-master',
        'version' => 'dev-master',
        'reference' => '56d5f0335e68026f69ef92265ba0f8e5ccc4d788',
        'type' => 'library',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        'apimatic/core' => array(
            'pretty_version' => '0.3.14',
            'version' => '0.3.14.0',
            'reference' => 'c3eaad6cf0c00b793ce6d9bee8b87176247da582',
            'type' => 'library',
            'install_path' => __DIR__ . '/../apimatic/core',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'apimatic/core-interfaces' => array(
            'pretty_version' => '0.1.5',
            'version' => '0.1.5.0',
            'reference' => 'b4f1bffc8be79584836f70af33c65e097eec155c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../apimatic/core-interfaces',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'apimatic/jsonmapper' => array(
            'pretty_version' => '3.1.6',
            'version' => '3.1.6.0',
            'reference' => 'c6cc21bd56bfe5d5822bbd08f514be465c0b24e7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../apimatic/jsonmapper',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'apimatic/unirest-php' => array(
            'pretty_version' => '4.0.6',
            'version' => '4.0.6.0',
            'reference' => 'df38c93ffa71e8a1b7a075c2322c28cdfe8fe1f5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../apimatic/unirest-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'awesomemotive/wpforms' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => '56d5f0335e68026f69ef92265ba0f8e5ccc4d788',
            'type' => 'library',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ezyang/htmlpurifier' => array(
            'pretty_version' => 'v4.18.0',
            'version' => '4.18.0.0',
            'reference' => 'cb56001e54359df7ae76dc522d08845dc741621b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ezyang/htmlpurifier',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'mk-j/php_xlsxwriter' => array(
            'pretty_version' => '0.39',
            'version' => '0.39.0.0',
            'reference' => '67541cff96eab25563aa7fcecba33e03368fa464',
            'type' => 'project',
            'install_path' => __DIR__ . '/../mk-j/php_xlsxwriter',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'php-jsonpointer/php-jsonpointer' => array(
            'pretty_version' => 'v3.0.2',
            'version' => '3.0.2.0',
            'reference' => '4428f86c6f23846e9faa5a420c4ef14e485b3afb',
            'type' => 'library',
            'install_path' => __DIR__ . '/../php-jsonpointer/php-jsonpointer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/log' => array(
            'pretty_version' => '1.1.4',
            'version' => '1.1.4.0',
            'reference' => 'd49695b909c3b7628b6289db5479a1c204601f11',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/log',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'roave/security-advisories' => array(
            'pretty_version' => 'dev-latest',
            'version' => 'dev-latest',
            'reference' => '45b01f4e60c350f72a8697056674e449e053935a',
            'type' => 'metapackage',
            'install_path' => null,
            'aliases' => array(
                0 => '9999999-dev',
            ),
            'dev_requirement' => true,
        ),
        'square/square' => array(
            'pretty_version' => '40.0.0.20250123',
            'version' => '40.0.0.20250123',
            'reference' => '4c8c88afbafb476a3e3f5751c987674874361ca9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../square/square',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'stripe/stripe-php' => array(
            'pretty_version' => 'v16.5.0',
            'version' => '********',
            'reference' => '3fb22256317344e049fce02ff289af3b776b0464',
            'type' => 'library',
            'install_path' => __DIR__ . '/../stripe/stripe-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/css-selector' => array(
            'pretty_version' => 'v4.4.44',
            'version' => '********',
            'reference' => 'bd0a6737e48de45b4b0b7b6fc98c78404ddceaed',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/css-selector',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-iconv' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '********',
            'reference' => '48becf00c920479ca2e910c22a5a39e5d47ca956',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-iconv',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-mbstring' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '********',
            'reference' => '85181ba99b2345b0ef10ce42ecac37612d9fd341',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-mbstring',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php80' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '0cc9dd0f17f61d8131e7df6b84bd344899fe2608',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php80',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'tijsverkoyen/css-to-inline-styles' => array(
            'pretty_version' => 'v2.2.7',
            'version' => '2.2.7.0',
            'reference' => '83ee6f38df0a63106a9e4536e3060458b74ccedb',
            'type' => 'library',
            'install_path' => __DIR__ . '/../tijsverkoyen/css-to-inline-styles',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'true/punycode' => array(
            'pretty_version' => 'v2.1.1',
            'version' => '2.1.1.0',
            'reference' => 'a4d0c11a36dd7f4e7cd7096076cab6d3378a071e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../true/punycode',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'woocommerce/action-scheduler' => array(
            'pretty_version' => '3.9.2',
            'version' => '3.9.2.0',
            'reference' => 'efbb7953f72a433086335b249292f280dd43ddfe',
            'type' => 'wordpress-plugin',
            'install_path' => __DIR__ . '/../woocommerce/action-scheduler',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
