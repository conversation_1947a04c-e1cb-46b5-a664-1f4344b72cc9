msgid ""
msgstr ""
"Project-Id-Version: WPForms Lite *******\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/plugin/wpforms-lite\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2025-05-05T10:56:03+00:00\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"X-Generator: WP-CLI 2.11.0\n"
"X-Domain: wpforms-lite\n"

#. Plugin Name of the plugin
#. Author of the plugin
#: wpforms.php
#: includes/admin/class-menu.php:47
#: includes/admin/class-menu.php:48
#: includes/admin/class-menu.php:59
#: includes/admin/class-menu.php:137
#: includes/class-form.php:149
#: includes/integrations.php:41
#: src/Emails/Templates/General.php:94
#: src/Integrations/Divi/WPFormsSelector.php:35
#: src/Integrations/Elementor/Widget.php:53
#: src/Integrations/Gutenberg/FormSelector.php:477
#: src/Lite/Admin/DashboardWidget.php:196
#: templates/admin/dashboard/widget/settings.php:47
msgid "WPForms"
msgstr ""

#. Plugin URI of the plugin
#. Author URI of the plugin
#: wpforms.php
msgid "https://wpforms.com"
msgstr ""

#. Description of the plugin
#: wpforms.php
msgid "Beginner friendly WordPress contact form plugin. Use our Drag & Drop form builder to create your WordPress forms."
msgstr ""

#: includes/admin/admin.php:170
#: includes/admin/admin.php:306
#: includes/fields/class-base.php:3536
#: src/Admin/Forms/Tags.php:179
msgid "Loading..."
msgstr ""

#: includes/admin/admin.php:171
#: includes/admin/builder/class-builder.php:876
#: includes/fields/class-base.php:3537
#: src/Admin/Forms/Tags.php:180
msgid "No results found"
msgstr ""

#: includes/admin/admin.php:172
#: includes/fields/class-base.php:3538
msgid "No choices to choose from"
msgstr ""

#: includes/admin/admin.php:186
#: includes/admin/admin.php:253
#: includes/admin/class-about.php:409
#: includes/functions/education.php:30
#: src/Admin/Builder/Templates.php:186
msgid "Activate"
msgstr ""

#: includes/admin/admin.php:187
#: includes/admin/class-about.php:401
msgid "Activated"
msgstr ""

#: includes/admin/admin.php:188
#: includes/admin/class-about.php:398
#: src/Db/Payments/ValueValidator.php:122
msgid "Active"
msgstr ""

#: includes/admin/admin.php:189
msgid "Deactivate"
msgstr ""

#: includes/admin/admin.php:190
#: includes/admin/class-about.php:406
msgid "Inactive"
msgstr ""

#: includes/admin/admin.php:191
#: src/Forms/Fields/Traits/ProField.php:288
msgid "Install Addon"
msgstr ""

#. translators: %1$s - addon download URL, %2$s - link to manual installation guide, %3$s - link to contact support.
#: includes/admin/admin.php:194
#: includes/admin/ajax-actions.php:801
msgid "Could not install the addon. Please <a href=\"%1$s\" target=\"_blank\" rel=\"noopener noreferrer\">download it from wpforms.com</a> and <a href=\"%2$s\" target=\"_blank\" rel=\"noopener noreferrer\">install it manually</a>, or <a href=\"%3$s\" target=\"_blank\" rel=\"noopener noreferrer\">contact support</a> for assistance."
msgstr ""

#: includes/admin/admin.php:207
msgid "Could not install the plugin automatically. Please download and install it manually."
msgstr ""

#: includes/admin/admin.php:208
msgid "Searching Addons"
msgstr ""

#: includes/admin/admin.php:210
#: includes/admin/builder/class-builder.php:759
#: includes/admin/class-editor.php:162
#: src/Admin/Builder/Templates.php:183
#: src/Admin/Education/StringsTrait.php:25
#: src/Admin/Forms/Tags.php:452
#: src/Admin/Payments/Views/Single.php:411
#: src/Integrations/AI/Admin/Builder/Enqueues.php:135
#: src/Integrations/Elementor/Elementor.php:133
#: src/Lite/Admin/Education/LiteConnect.php:237
#: templates/admin/components/datepicker.php:72
#: templates/admin/forms/bulk-edit-tags.php:30
msgid "Cancel"
msgstr ""

#: includes/admin/admin.php:211
#: src/Forms/IconChoices.php:488
#: templates/admin/challenge/modal.php:74
#: templates/builder/fullscreen/mobile-notice.php:25
msgid "Continue"
msgstr ""

#: includes/admin/admin.php:212
#: includes/admin/builder/class-builder.php:761
#: includes/admin/class-editor.php:108
#: src/Admin/Education/StringsTrait.php:26
#: src/Forms/Locator.php:376
#: src/Integrations/AI/Admin/Builder/Enqueues.php:157
#: src/Lite/Admin/Education/LiteConnect.php:246
#: templates/builder/fullscreen/mobile-notice.php:28
#: templates/builder/help.php:35
msgid "Close"
msgstr ""

#: includes/admin/admin.php:213
msgid "Close and Refresh"
msgstr ""

#: includes/admin/admin.php:214
msgid "Change columns to display"
msgstr ""

#: includes/admin/admin.php:215
msgid "Sorry, there are no form fields that match your criteria."
msgstr ""

#: includes/admin/admin.php:216
msgid "Sorry, there is no entry meta that match your criteria."
msgstr ""

#: includes/admin/admin.php:217
msgid "Are you sure you want to delete this entry? This will also remove all associated files, notes, and logs."
msgstr ""

#: includes/admin/admin.php:218
msgid "Are you sure you want to delete ALL entries? This will also remove all associated files, notes, and logs."
msgstr ""

#. translators: %s - entry count.
#: includes/admin/admin.php:220
msgid "Are you sure you want to delete %s entries? This will also remove all associated files, notes, and logs."
msgstr ""

#: includes/admin/admin.php:223
msgid "Are you sure you want to trash this entry? This will also remove all associated files, notes, and logs."
msgstr ""

#: includes/admin/admin.php:224
msgid "Are you sure you want to trash ALL entries? This will also remove all associated files, notes, and logs."
msgstr ""

#. translators: %s - entry count.
#: includes/admin/admin.php:226
msgid "Are you sure you want to trash %s entries? This will also remove all associated files, notes, and logs."
msgstr ""

#: includes/admin/admin.php:229
msgid "Hide Empty Fields"
msgstr ""

#: includes/admin/admin.php:230
msgid "Show Empty Fields"
msgstr ""

#: includes/admin/admin.php:231
msgid "Are you sure you want to delete this note?"
msgstr ""

#: includes/admin/admin.php:232
msgid "Unstar entry"
msgstr ""

#: includes/admin/admin.php:233
msgid "Star entry"
msgstr ""

#: includes/admin/admin.php:234
msgid "Mark entry read"
msgstr ""

#: includes/admin/admin.php:235
msgid "Mark entry unread"
msgstr ""

#: includes/admin/admin.php:236
msgid "Are you sure you want to delete this form and all its entries?"
msgstr ""

#: includes/admin/admin.php:237
msgid "Are you sure you want to delete this template and all its entries?"
msgstr ""

#: includes/admin/admin.php:238
msgid "Are you sure you want to delete the selected forms and all their entries?"
msgstr ""

#: includes/admin/admin.php:239
msgid "Are you sure you want to delete ALL the forms in the trash and all their entries?"
msgstr ""

#: includes/admin/admin.php:240
msgid "Are you sure you want to duplicate this form?"
msgstr ""

#: includes/admin/admin.php:241
msgid "Are you sure you want to duplicate this template?"
msgstr ""

#: includes/admin/admin.php:242
#: includes/admin/builder/class-builder.php:771
#: src/Admin/Education/Builder/Captcha.php:161
#: src/Admin/Tools/Views/Importer.php:223
#: src/Forms/Preview.php:338
#: src/Integrations/Elementor/Elementor.php:132
#: src/Integrations/Gutenberg/FormSelector.php:535
#: wpforms.php:213
msgid "Heads up!"
msgstr ""

#: includes/admin/admin.php:243
msgid "Please select at least one form to import."
msgstr ""

#: includes/admin/admin.php:246
msgid "Almost Done"
msgstr ""

#: includes/admin/admin.php:247
#: src/Admin/Education/StringsTrait.php:111
msgid "Thanks for your interest in WPForms Pro!"
msgstr ""

#: includes/admin/admin.php:248
msgid "Oops!"
msgstr ""

#: includes/admin/admin.php:249
#: src/Forms/IconChoices.php:490
msgid "Uh oh!"
msgstr ""

#: includes/admin/admin.php:250
#: includes/admin/builder/class-builder.php:760
msgid "OK"
msgstr ""

#: includes/admin/admin.php:251
msgid "Install and Activate"
msgstr ""

#: includes/admin/admin.php:252
msgid "needs to be installed and activated to import its forms. Would you like us to install and activate it for you?"
msgstr ""

#: includes/admin/admin.php:254
msgid "needs to be activated to import its forms. Would you like us to activate it for you?"
msgstr ""

#: includes/admin/admin.php:255
msgid "Are you sure you want to disconnect this account?"
msgstr ""

#: includes/admin/admin.php:256
msgid "Could not disconnect this account."
msgstr ""

#: includes/admin/admin.php:257
msgid "Could not authenticate with the provider."
msgstr ""

#: includes/admin/admin.php:258
msgid "Connecting..."
msgstr ""

#: includes/admin/admin.php:259
msgid "Save and Refresh"
msgstr ""

#: includes/admin/admin.php:260
#: templates/admin/dashboard/widget/settings.php:57
msgid "Save Changes"
msgstr ""

#: includes/admin/admin.php:261
msgid "Unfortunately there was a server connection error."
msgstr ""

#: includes/admin/admin.php:262
msgid "Unknown error."
msgstr ""

#. translators: %s - WPForms.com docs page URL.
#: includes/admin/admin.php:265
msgid "You've selected <strong>Base Styling Only</strong>, which may result in styling issues. <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">Please check out our tutorial</a> for common issues and recommendations."
msgstr ""

#. translators: %s - WPForms.com docs page URL.
#: includes/admin/admin.php:279
msgid "You've selected <strong>No Styling</strong>, which will likely result in significant styling issues and is recommended only for developers. <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">Please check out our tutorial</a> for more details and recommendations."
msgstr ""

#: includes/admin/admin.php:291
msgid "Testing"
msgstr ""

#: includes/admin/admin.php:292
msgid "Recreating"
msgstr ""

#: includes/admin/admin.php:293
msgid "Upgrade was successfully completed!"
msgstr ""

#: includes/admin/admin.php:294
#: includes/admin/builder/class-builder.php:842
msgid "Upload or Choose Your Image"
msgstr ""

#: includes/admin/admin.php:295
#: includes/admin/builder/class-builder.php:843
msgid "Use Image"
msgstr ""

#: includes/admin/admin.php:297
#: includes/admin/builder/class-builder.php:846
msgid "You tried uploading a file type that is not allowed. Please try again."
msgstr ""

#: includes/admin/admin.php:303
msgid "To edit the License Key, please first click the Remove Key button. Please note that removing this key will remove access to updates, addons, and support."
msgstr ""

#: includes/admin/admin.php:304
#: includes/admin/builder/class-builder.php:866
msgid "Something went wrong"
msgstr ""

#: includes/admin/admin.php:305
msgid "Success"
msgstr ""

#: includes/admin/admin.php:307
#: includes/admin/builder/class-builder.php:804
msgid "Use Default Template"
msgstr ""

#: includes/admin/admin.php:308
#: includes/admin/builder/class-builder.php:863
msgid "Something went wrong while applying the form template. Please try again. If the error persists, contact our support team."
msgstr ""

#. translators: %s - link to WPForms.com docs page.
#: includes/admin/admin.php:311
msgid "Something went wrong. Please try again, and if the problem persists, <a href=\"%1$s\" target=\"_blank\" rel=\"noopener noreferrer\">contact our support team</a>."
msgstr ""

#. translators: %1$s - WPForms plugin name; %2$s - WPForms.com URL to a related doc.
#: includes/admin/admin.php:586
msgid "Your site is running an outdated version of PHP that is no longer supported and may cause issues with %1$s. <a href=\"%2$s\" target=\"_blank\" rel=\"noopener noreferrer\">Read more</a> for additional information."
msgstr ""

#: includes/admin/admin.php:600
msgid "<strong>Please Note:</strong> Support for PHP 7.3 and below will be discontinued soon. After this, if no further action is taken, WPForms functionality will be disabled."
msgstr ""

#. translators: %s - WPForms.com contact page URL.
#: includes/admin/admin.php:637
msgid "Thank you for considering upgrading. If you have any questions, please <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">let us know</a>."
msgstr ""

#: includes/admin/admin.php:657
msgid "After upgrading, your license key will remain the same.<br>You may need to do a quick refresh to unlock your new addons. In your WordPress admin, go to <strong>WPForms &raquo; Settings</strong>. If you don't see your updated plan, click <em>refresh</em>."
msgstr ""

#. translators: %s - WPForms.com upgrade from Lite to paid docs page URL.
#: includes/admin/admin.php:668
#: includes/admin/admin.php:718
msgid "Check out <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">our documentation</a> for step-by-step instructions."
msgstr ""

#. translators: %s - WPForms.com contact page URL.
#: includes/admin/admin.php:685
msgid "If you have any questions or issues just <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">let us know</a>."
msgstr ""

#. translators: %s - license level, WPForms Pro or WPForms Elite.
#: includes/admin/admin.php:706
msgid "After purchasing a license, just <strong>enter your license key on the WPForms Settings page</strong>. This will let your site automatically upgrade to %s! (Don't worry, all your forms and settings will be preserved.)"
msgstr ""

#: includes/admin/ajax-actions.php:23
#: includes/admin/ajax-actions.php:925
#: includes/fields/class-base.php:2972
#: src/Admin/FormEmbedWizard.php:448
#: src/Integrations/AI/Admin/Ajax/Choices.php:57
#: src/Integrations/AI/Admin/Ajax/Forms.php:84
#: src/Integrations/AI/Admin/Ajax/Forms.php:113
#: src/Integrations/AI/Admin/Ajax/Forms.php:259
#: src/Integrations/AI/Admin/Ajax/Forms.php:393
msgid "Your session expired. Please reload the builder."
msgstr ""

#: includes/admin/ajax-actions.php:28
#: includes/admin/ajax-actions.php:286
#: includes/fields/class-base.php:2977
#: src/Admin/Builder/Ajax/PanelLoader.php:134
#: src/Admin/FormEmbedWizard.php:279
#: src/Admin/FormEmbedWizard.php:455
#: src/Admin/Forms/Ajax/Tags.php:200
#: src/Admin/Forms/Ajax/Tags.php:240
#: src/Admin/Payments/Views/Overview/Ajax.php:64
#: src/Admin/Payments/Views/Overview/Ajax.php:129
#: src/Integrations/Square/Admin/Payments/SingleActionsHandler.php:151
#: src/Integrations/Stripe/Admin/Payments/SingleActionsHandler.php:99
#: src/Integrations/Stripe/Admin/Payments/SingleActionsHandler.php:159
msgid "You are not allowed to perform this action."
msgstr ""

#: includes/admin/ajax-actions.php:33
msgid "Something went wrong while performing this action."
msgstr ""

#: includes/admin/ajax-actions.php:68
msgid "Something went wrong while saving the form."
msgstr ""

#: includes/admin/ajax-actions.php:180
msgid "No Form Name Provided"
msgstr ""

#: includes/admin/ajax-actions.php:194
#: includes/admin/ajax-actions.php:312
msgid "The template you selected is currently not available, but you can try again later. If you continue to have trouble, please reach out to support."
msgstr ""

#: includes/admin/ajax-actions.php:241
msgid "Error Creating Form"
msgstr ""

#: includes/admin/ajax-actions.php:296
msgid "No Form ID Provided"
msgstr ""

#: includes/admin/ajax-actions.php:409
msgid "Error Updating Template"
msgstr ""

#: includes/admin/ajax-actions.php:525
#: includes/fields/class-base.php:1560
msgid "post type"
msgstr ""

#: includes/admin/ajax-actions.php:556
#: includes/fields/class-base.php:1567
msgid "taxonomy"
msgstr ""

#: includes/admin/ajax-actions.php:616
#: includes/admin/ajax-actions.php:655
msgid "You do not have permission to perform this operation."
msgstr ""

#: includes/admin/ajax-actions.php:626
msgid "Success! Your server can make SSL connections."
msgstr ""

#: includes/admin/ajax-actions.php:633
msgid "There was an error and the connection failed. Please contact your web host with the technical details below."
msgstr ""

#: includes/admin/ajax-actions.php:665
msgid "WPForms custom database tables are recreated."
msgstr ""

#: includes/admin/ajax-actions.php:672
msgid "Error recreating WPForms custom database tables."
msgstr ""

#: includes/admin/ajax-actions.php:692
msgid "Plugin deactivation is disabled for you on this site."
msgstr ""

#: includes/admin/ajax-actions.php:712
msgid "Plugin deactivated."
msgstr ""

#: includes/admin/ajax-actions.php:714
msgid "Addon deactivated."
msgstr ""

#: includes/admin/ajax-actions.php:718
msgid "Could not deactivate the addon. Please deactivate from the Plugins page."
msgstr ""

#: includes/admin/ajax-actions.php:735
msgid "Plugin activation is disabled for you on this site."
msgstr ""

#: includes/admin/ajax-actions.php:739
msgid "Plugin activated."
msgstr ""

#: includes/admin/ajax-actions.php:740
msgid "Addon activated."
msgstr ""

#: includes/admin/ajax-actions.php:743
msgid "Could not activate the plugin. Please activate it on the Plugins page."
msgstr ""

#: includes/admin/ajax-actions.php:744
msgid "Could not activate the addon. Please activate it on the Plugins page."
msgstr ""

#: includes/admin/ajax-actions.php:789
msgid "There was an error while performing your request."
msgstr ""

#: includes/admin/ajax-actions.php:798
msgid "Could not install the plugin. Please download and install it manually."
msgstr ""

#: includes/admin/ajax-actions.php:886
msgid "Plugin installed."
msgstr ""

#: includes/admin/ajax-actions.php:886
msgid "Addon installed."
msgstr ""

#: includes/admin/ajax-actions.php:906
#: src/Lite/Admin/Connect.php:203
#: src/Lite/Admin/Connect.php:217
#: src/Lite/Admin/Connect.php:278
msgid "Plugin installed & activated."
msgstr ""

#: includes/admin/ajax-actions.php:906
msgid "Addon installed & activated."
msgstr ""

#: includes/admin/ajax-actions.php:929
#: src/Admin/FormEmbedWizard.php:461
msgid "Incorrect usage of this operation."
msgstr ""

#: includes/admin/builder/class-builder.php:134
#: includes/admin/builder/class-builder.php:159
#: src/Integrations/AI/Admin/Ajax/Forms.php:293
msgid "It looks like the form you are trying to access is no longer available."
msgstr ""

#: includes/admin/builder/class-builder.php:148
msgid "Sorry, you are not allowed to create new forms."
msgstr ""

#: includes/admin/builder/class-builder.php:152
#: src/Integrations/AI/Admin/Ajax/Forms.php:274
msgid "Sorry, you are not allowed to edit this form."
msgstr ""

#: includes/admin/builder/class-builder.php:164
msgid "You can't edit this form because it's in the trash."
msgstr ""

#: includes/admin/builder/class-builder.php:728
msgid "And"
msgstr ""

#: includes/admin/builder/class-builder.php:730
msgid "Add New Choices"
msgstr ""

#: includes/admin/builder/class-builder.php:731
#: includes/fields/class-base.php:1465
msgid "Bulk Add"
msgstr ""

#: includes/admin/builder/class-builder.php:732
msgid "Are you sure you want to leave? You have unsaved changes"
msgstr ""

#: includes/admin/builder/class-builder.php:733
msgid "Hide Bulk Add"
msgstr ""

#: includes/admin/builder/class-builder.php:734
msgid "Add Choices (one per line)"
msgstr ""

#: includes/admin/builder/class-builder.php:735
msgid ""
"Blue\n"
"Red\n"
"Green"
msgstr ""

#: includes/admin/builder/class-builder.php:736
msgid "Show presets"
msgstr ""

#: includes/admin/builder/class-builder.php:737
msgid "Hide presets"
msgstr ""

#. translators: %1$s - data source name (e.g. Categories, Posts), %2$s - data source type (e.g. post type, taxonomy), %3$s - display limit, %4$s - total number of items.
#: includes/admin/builder/class-builder.php:743
msgid "The %1$s %2$s contains over %3$s items (%4$s). This may make the field difficult for your visitors to use and/or cause the form to be slow."
msgstr ""

#. translators: %1$s - data source name (e.g. Categories, Posts), %2$s - data source type (e.g. post type, taxonomy).
#: includes/admin/builder/class-builder.php:750
#: includes/fields/class-base.php:3745
msgid "This field will not be displayed in your form since there are no %2$s belonging to %1$s."
msgstr ""

#: includes/admin/builder/class-builder.php:755
#: includes/fields/class-base.php:3731
msgid "posts"
msgstr ""

#: includes/admin/builder/class-builder.php:756
#: includes/fields/class-base.php:3736
msgid "terms"
msgstr ""

#: includes/admin/builder/class-builder.php:762
msgid "Due to form changes, conditional logic rules will be removed or updated:"
msgstr ""

#: includes/admin/builder/class-builder.php:763
msgid "Are you sure you want to disable conditional logic? This will remove the rules for this field or setting."
msgstr ""

#: includes/admin/builder/class-builder.php:764
msgid "Field"
msgstr ""

#: includes/admin/builder/class-builder.php:765
msgid "Field Locked"
msgstr ""

#: includes/admin/builder/class-builder.php:766
msgid "This field cannot be deleted or duplicated."
msgstr ""

#: includes/admin/builder/class-builder.php:767
msgid "This field cannot be deleted."
msgstr ""

#: includes/admin/builder/class-builder.php:768
msgid "This field cannot be duplicated."
msgstr ""

#: includes/admin/builder/class-builder.php:769
msgid "Available Fields"
msgstr ""

#: includes/admin/builder/class-builder.php:770
msgid "No fields available"
msgstr ""

#: includes/admin/builder/class-builder.php:775
msgid "No email fields"
msgstr ""

#: includes/admin/builder/class-builder.php:776
msgid "Are you sure you want to delete this notification?"
msgstr ""

#: includes/admin/builder/class-builder.php:777
msgid "Enter a notification name"
msgstr ""

#: includes/admin/builder/class-builder.php:778
msgid "Eg: User Confirmation"
msgstr ""

#: includes/admin/builder/class-builder.php:779
msgid "You must provide a notification name"
msgstr ""

#: includes/admin/builder/class-builder.php:780
#: lite/wpforms-lite.php:197
#: src/Admin/Tools/Importers/PirateForms.php:464
msgid "Default Notification"
msgstr ""

#: includes/admin/builder/class-builder.php:781
msgid "Are you sure you want to delete this confirmation?"
msgstr ""

#: includes/admin/builder/class-builder.php:782
msgid "Enter a confirmation name"
msgstr ""

#: includes/admin/builder/class-builder.php:783
msgid "Eg: Alternative Confirmation"
msgstr ""

#: includes/admin/builder/class-builder.php:784
msgid "You must provide a confirmation name"
msgstr ""

#: includes/admin/builder/class-builder.php:785
#: lite/wpforms-lite.php:501
msgid "Default Confirmation"
msgstr ""

#: includes/admin/builder/class-builder.php:786
#: includes/admin/builder/class-builder.php:1247
msgid "Save"
msgstr ""

#: includes/admin/builder/class-builder.php:787
msgid "Saving"
msgstr ""

#: includes/admin/builder/class-builder.php:788
msgid "Saved!"
msgstr ""

#: includes/admin/builder/class-builder.php:789
msgid "Save and Exit"
msgstr ""

#: includes/admin/builder/class-builder.php:790
msgid "Save and Embed"
msgstr ""

#: includes/admin/builder/class-builder.php:792
#: includes/fields/class-base.php:2145
msgid "Show Layouts"
msgstr ""

#: includes/admin/builder/class-builder.php:793
msgid "Hide Layouts"
msgstr ""

#: includes/admin/builder/class-builder.php:794
msgid "Select your layout"
msgstr ""

#: includes/admin/builder/class-builder.php:795
msgid "Select your column"
msgstr ""

#: includes/admin/builder/class-builder.php:796
#: src/Frontend/Classic.php:389
msgid "Loading"
msgstr ""

#: includes/admin/builder/class-builder.php:802
#: src/Admin/Forms/UserTemplates.php:333
#: src/Admin/Traits/FormTemplates.php:458
msgid "Use Template"
msgstr ""

#: includes/admin/builder/class-builder.php:803
msgid "Changing the template on this form will delete existing fields, reset external connections, and unsaved changes will be lost. Are you sure you want to apply the new template?"
msgstr ""

#: includes/admin/builder/class-builder.php:805
#: includes/admin/builder/class-builder.php:1233
#: includes/admin/builder/class-builder.php:1239
msgid "Embed"
msgstr ""

#: includes/admin/builder/class-builder.php:806
msgid "Exit"
msgstr ""

#: includes/admin/builder/class-builder.php:808
msgid "Your form contains unsaved changes. Would you like to save your changes first."
msgstr ""

#: includes/admin/builder/class-builder.php:809
msgid "Are you sure you want to delete this field?"
msgstr ""

#: includes/admin/builder/class-builder.php:810
msgid "Are you sure you want to delete this choice?"
msgstr ""

#: includes/admin/builder/class-builder.php:811
msgid "Are you sure you want to duplicate this field?"
msgstr ""

#: includes/admin/builder/class-builder.php:812
#: includes/class-form.php:878
#: includes/class-form.php:906
#: includes/class-form.php:998
msgid "(copy)"
msgstr ""

#: includes/admin/builder/class-builder.php:813
msgid "Please enter a form name."
msgstr ""

#: includes/admin/builder/class-builder.php:814
msgid "This item must contain at least one choice."
msgstr ""

#: includes/admin/builder/class-builder.php:815
#: includes/admin/builder/functions.php:468
#: includes/fields/class-base.php:2299
#: src/Admin/Tools/Views/Logs.php:110
#: src/Integrations/Divi/WPFormsSelector.php:78
#: src/Integrations/Divi/WPFormsSelector.php:88
msgid "Off"
msgstr ""

#: includes/admin/builder/class-builder.php:816
#: includes/admin/builder/functions.php:467
#: src/Admin/Tools/Views/Logs.php:110
#: src/Integrations/Divi/WPFormsSelector.php:79
#: src/Integrations/Divi/WPFormsSelector.php:89
msgid "On"
msgstr ""

#: includes/admin/builder/class-builder.php:817
#: includes/functions/utilities.php:337
msgid "or"
msgstr ""

#: includes/admin/builder/class-builder.php:818
msgid "Other"
msgstr ""

#: includes/admin/builder/class-builder.php:819
msgid "is"
msgstr ""

#: includes/admin/builder/class-builder.php:820
msgid "is not"
msgstr ""

#: includes/admin/builder/class-builder.php:821
msgid "empty"
msgstr ""

#: includes/admin/builder/class-builder.php:822
msgid "not empty"
msgstr ""

#: includes/admin/builder/class-builder.php:823
#: src/Admin/Payments/Views/Overview/Table.php:699
msgid "contains"
msgstr ""

#: includes/admin/builder/class-builder.php:824
msgid "does not contain"
msgstr ""

#: includes/admin/builder/class-builder.php:825
#: src/Admin/Payments/Views/Overview/Table.php:701
msgid "starts with"
msgstr ""

#: includes/admin/builder/class-builder.php:826
msgid "ends with"
msgstr ""

#: includes/admin/builder/class-builder.php:827
msgid "greater than"
msgstr ""

#: includes/admin/builder/class-builder.php:828
msgid "less than"
msgstr ""

#: includes/admin/builder/class-builder.php:829
msgid "Entry storage is currently disabled, but is required to accept payments. Please enable in your form settings."
msgstr ""

#: includes/admin/builder/class-builder.php:830
msgid "This form is currently accepting payments. Entry storage is required to accept payments. To disable entry storage, please first disable payments."
msgstr ""

#: includes/admin/builder/class-builder.php:831
#: src/Forms/Fields/Pagebreak/Field.php:465
msgid "Previous"
msgstr ""

#. translators: %s - marketing integration name.
#: includes/admin/builder/class-builder.php:833
msgid "In order to complete your form's %s integration, please check that all required (*) fields have been filled out."
msgstr ""

#: includes/admin/builder/class-builder.php:836
msgid "Create new rule"
msgstr ""

#: includes/admin/builder/class-builder.php:837
msgid "Add New Group"
msgstr ""

#: includes/admin/builder/class-builder.php:838
msgid "Delete rule"
msgstr ""

#: includes/admin/builder/class-builder.php:839
msgid "Smart Tags"
msgstr ""

#: includes/admin/builder/class-builder.php:840
#: src/Providers/Provider/Settings/FormBuilder.php:171
#: src/Providers/Provider/Settings/FormBuilder.php:228
msgid "--- Select Field ---"
msgstr ""

#: includes/admin/builder/class-builder.php:841
msgid "--- Select Choice ---"
msgstr ""

#: includes/admin/builder/class-builder.php:844
#: includes/admin/settings-api.php:532
#: includes/fields/class-base.php:1518
#: includes/fields/class-base.php:1679
#: src/Integrations/Gutenberg/FormSelector.php:509
msgid "Remove Image"
msgstr ""

#: includes/admin/builder/class-builder.php:847
msgid "Add"
msgstr ""

#: includes/admin/builder/class-builder.php:851
msgid "You should enter a valid absolute address to the Confirmation Redirect URL field."
msgstr ""

#: includes/admin/builder/class-builder.php:852
msgid "Add Custom Value"
msgstr ""

#. translators: %s - choice number.
#: includes/admin/builder/class-builder.php:854
#: includes/fields/class-checkbox.php:134
#: includes/fields/class-checkbox.php:775
#: includes/fields/class-radio.php:121
#: includes/fields/class-radio.php:616
msgid "Choice %s"
msgstr ""

#. translators: %s - choice number.
#. translators: %s - item number.
#: includes/admin/builder/class-builder.php:858
#: src/Forms/Fields/PaymentCheckbox/Field.php:119
#: src/Forms/Fields/PaymentCheckbox/Field.php:398
#: src/Forms/Fields/PaymentMultiple/Field.php:386
#: src/Forms/Fields/PaymentSelect/Field.php:439
#: src/Forms/Fields/PaymentTotal/Field.php:690
#: src/SmartTags/SmartTag/OrderSummary.php:200
#: src/SmartTags/SmartTag/OrderSummary.php:275
msgid "Item %s"
msgstr ""

#: includes/admin/builder/class-builder.php:861
msgid "Something went wrong while saving the form. Please reload the page and try again."
msgstr ""

#: includes/admin/builder/class-builder.php:862
msgid "Please contact the plugin support team if this behavior persists."
msgstr ""

#: includes/admin/builder/class-builder.php:864
msgid "Couldn't load the Setup panel."
msgstr ""

#: includes/admin/builder/class-builder.php:865
#: includes/templates/class-blank.php:34
msgid "Blank Form"
msgstr ""

#: includes/admin/builder/class-builder.php:867
msgid "This field cannot be moved."
msgstr ""

#: includes/admin/builder/class-builder.php:868
#: includes/fields/class-base.php:2625
msgid "Empty Label"
msgstr ""

#: includes/admin/builder/class-builder.php:869
#: includes/admin/builder/panels/class-fields.php:198
#: includes/admin/builder/panels/class-settings.php:206
#: includes/class-form.php:595
#: includes/class-form.php:693
#: includes/templates/class-simple-contact-form.php:59
#: src/Admin/Tools/Importers/ContactForm7.php:135
#: src/Admin/Tools/Importers/NinjaForms.php:143
#: src/Frontend/Frontend.php:1316
#: src/Integrations/AI/Admin/Builder/Forms.php:180
msgid "Submit"
msgstr ""

#: includes/admin/builder/class-builder.php:871
#: src/Forms/Fields/Richtext/Field.php:129
msgid "Full"
msgstr ""

#: includes/admin/builder/class-builder.php:872
#: includes/fields/class-name.php:103
#: includes/fields/class-name.php:453
msgid "First"
msgstr ""

#: includes/admin/builder/class-builder.php:873
#: includes/fields/class-name.php:124
#: includes/fields/class-name.php:458
msgid "Middle"
msgstr ""

#: includes/admin/builder/class-builder.php:874
#: includes/fields/class-name.php:145
#: includes/fields/class-name.php:463
msgid "Last"
msgstr ""

#: includes/admin/builder/class-builder.php:877
msgid "Sorry, no results found"
msgstr ""

#: includes/admin/builder/class-builder.php:878
msgid "Search"
msgstr ""

#. translators: %1$s - from value %2$s - to value.
#: includes/admin/builder/class-builder.php:880
msgid "Please enter a valid value or change the Increment. The nearest valid values are %1$s and %2$s."
msgstr ""

#: includes/admin/builder/class-builder.php:887
#: includes/providers/class-base.php:97
msgid "Connection"
msgstr ""

#. translators: %s - link to the WPForms.com doc article.
#: includes/admin/builder/class-builder.php:894
msgid "Disabling entry storage for this form will completely prevent any new submissions from getting saved to your site. If you still intend to keep a record of entries through notification emails, then please <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">test your form</a> to ensure emails send reliably."
msgstr ""

#. translators: %1$s - link to the plugin search page, %2$s - link to the WPForms.com doc article.
#: includes/admin/builder/class-builder.php:914
msgid "This feature cannot be used at this time because the Akismet plugin <a href=\"%1$s\" target=\"_blank\" rel=\"noopener noreferrer\">has not been installed</a>. For information on how to use this feature please <a href=\"%2$s\" target=\"_blank\" rel=\"noopener noreferrer\">refer to our documentation</a>."
msgstr ""

#. translators: %1$s - link to the plugins page, %2$s - link to the WPForms.com doc article.
#: includes/admin/builder/class-builder.php:935
msgid "This feature cannot be used at this time because the Akismet plugin <a href=\"%1$s\" target=\"_blank\" rel=\"noopener noreferrer\">has not been activated</a>. For information on how to use this feature please <a href=\"%2$s\" target=\"_blank\" rel=\"noopener noreferrer\">refer to our documentation</a>."
msgstr ""

#. translators: %1$s - link to the Akismet settings page, %2$s - link to the WPForms.com doc article.
#: includes/admin/builder/class-builder.php:956
msgid "This feature cannot be used at this time because the Akismet plugin <a href=\"%1$s\" target=\"_blank\" rel=\"noopener noreferrer\">has not been properly configured</a>. For information on how to use this feature please <a href=\"%2$s\" target=\"_blank\" rel=\"noopener noreferrer\">refer to our documentation</a>."
msgstr ""

#: includes/admin/builder/class-builder.php:1013
msgid "Countries"
msgstr ""

#: includes/admin/builder/class-builder.php:1017
msgid "Countries Postal Code"
msgstr ""

#: includes/admin/builder/class-builder.php:1021
msgid "States"
msgstr ""

#: includes/admin/builder/class-builder.php:1025
msgid "States Postal Code"
msgstr ""

#: includes/admin/builder/class-builder.php:1029
msgid "Months"
msgstr ""

#: includes/admin/builder/class-builder.php:1033
msgid "Days"
msgstr ""

#: includes/admin/builder/class-builder.php:1172
#: includes/admin/class-welcome.php:143
#: lite/templates/education/builder/lite-connect/ai-modal.php:15
#: lite/templates/education/lite-connect-modal.php:17
#: src/Lite/Admin/DashboardWidget.php:266
#: templates/admin/challenge/modal.php:52
#: templates/builder/fullscreen/mobile-notice.php:15
#: templates/builder/help.php:32
msgid "Sullie the WPForms mascot"
msgstr ""

#: includes/admin/builder/class-builder.php:1180
msgid "Now editing"
msgstr ""

#: includes/admin/builder/class-builder.php:1189
#: src/Admin/Forms/UserTemplates.php:125
#: src/Admin/Settings/Email.php:213
msgid "Template"
msgstr ""

#: includes/admin/builder/class-builder.php:1201
msgid "Help Ctrl+H"
msgstr ""

#: includes/admin/builder/class-builder.php:1204
#: src/Admin/Payments/Views/Overview/Helpers.php:83
msgid "Help"
msgstr ""

#: includes/admin/builder/class-builder.php:1221
msgid "Preview Form Ctrl+P"
msgstr ""

#: includes/admin/builder/class-builder.php:1224
#: includes/admin/settings-api.php:433
#: src/Admin/Forms/Views.php:602
#: src/Admin/Settings/Captcha/Page.php:194
#: src/Admin/Tools/Views/Importer.php:332
#: templates/builder/notifications/email-template-modal.php:44
msgid "Preview"
msgstr ""

#: includes/admin/builder/class-builder.php:1232
msgid "You cannot embed a form template"
msgstr ""

#: includes/admin/builder/class-builder.php:1238
msgid "Embed Form Ctrl+B"
msgstr ""

#: includes/admin/builder/class-builder.php:1246
msgid "Save Form Ctrl+S"
msgstr ""

#: includes/admin/builder/class-builder.php:1252
msgid "Exit Ctrl+Q"
msgstr ""

#: includes/admin/builder/functions.php:78
msgid "Show Smart Tags"
msgstr ""

#. translators: %d - field ID.
#. translators: %d - Field ID.
#: includes/admin/builder/functions.php:291
#: src/Integrations/Square/Process.php:760
#: src/Integrations/Stripe/Fields/PaymentElementCreditCard.php:145
msgid "Field #%d"
msgstr ""

#: includes/admin/builder/panels/class-base.php:211
#: src/Integrations/Square/Admin/Builder/Traits/Content.php:137
#: src/Integrations/Stripe/Admin/Builder/Traits/ContentTrait.php:423
msgid "Plan Name"
msgstr ""

#: includes/admin/builder/panels/class-base.php:212
msgid "You can only use one payment type at a time. If you'd like to enable Recurring Payments, please disable One-Time Payments."
msgstr ""

#: includes/admin/builder/panels/class-base.php:213
msgid "You can only use one payment type at a time. If you'd like to enable One-Time Payments, please disable Recurring Payments."
msgstr ""

#: includes/admin/builder/panels/class-fields.php:22
msgid "Fields"
msgstr ""

#: includes/admin/builder/panels/class-fields.php:84
msgid "Add Fields"
msgstr ""

#: includes/admin/builder/panels/class-fields.php:90
msgid "Field Options"
msgstr ""

#: includes/admin/builder/panels/class-fields.php:117
msgid "You need to <a href=\"#\" class=\"wpforms-panel-switch\" data-panel=\"setup\">setup your form</a> before you can manage the fields."
msgstr ""

#: includes/admin/builder/panels/class-fields.php:191
msgid "Enabled"
msgstr ""

#: includes/admin/builder/panels/class-fields.php:410
#: includes/fields/class-base.php:3061
msgid "Duplicate Field"
msgstr ""

#: includes/admin/builder/panels/class-fields.php:416
#: includes/fields/class-base.php:3064
msgid "Delete Field"
msgstr ""

#: includes/admin/builder/panels/class-fields.php:428
#: includes/fields/class-base.php:3075
msgid "Click to Edit"
msgstr ""

#: includes/admin/builder/panels/class-fields.php:429
#: includes/fields/class-base.php:3076
msgid "Drag to Reorder"
msgstr ""

#: includes/admin/builder/panels/class-fields.php:430
#: includes/fields/class-base.php:3077
msgid "Hide Helper"
msgstr ""

#. translators: %s - unavailable field name.
#: includes/admin/builder/panels/class-fields.php:507
msgid "Unfortunately, the %s field is not available and will be ignored on the front end."
msgstr ""

#: includes/admin/builder/panels/class-fields.php:528
#: includes/admin/class-settings.php:128
#: includes/fields/class-internal-information.php:294
#: includes/fields/class-internal-information.php:448
#: includes/fields/class-internal-information.php:676
#: lite/templates/education/builder/did-you-know.php:27
#: src/Admin/Notifications/EventDriven.php:566
#: src/Admin/Notifications/EventDriven.php:632
#: src/Admin/Notifications/EventDriven.php:685
#: src/Admin/Pages/Analytics.php:479
#: src/Admin/Splash/SplashTrait.php:145
#: src/Integrations/UncannyAutomator/UncannyAutomator.php:172
#: src/Lite/Admin/DashboardWidget.php:277
#: src/Lite/Admin/DashboardWidget.php:443
#: src/Lite/Admin/Education/Admin/DidYouKnow.php:130
#: src/Lite/Admin/Education/Admin/DidYouKnow.php:136
msgid "Learn More"
msgstr ""

#: includes/admin/builder/panels/class-fields.php:529
msgid "Dismiss this message. The field will be deleted as well."
msgstr ""

#: includes/admin/builder/panels/class-fields.php:548
msgid "You don't have any fields yet."
msgstr ""

#: includes/admin/builder/panels/class-fields.php:564
msgid "You don't have any fields yet. Add some!"
msgstr ""

#: includes/admin/builder/panels/class-fields.php:565
msgid "Take your pick from our wide variety of fields and start building out your form!"
msgstr ""

#. translators: %s - total amount of choices.
#: includes/admin/builder/panels/class-fields.php:667
msgid "Showing the first 20 choices.<br> All %s choices will be displayed when viewing the form."
msgstr ""

#: includes/admin/builder/panels/class-fields.php:707
msgid "Search fields:"
msgstr ""

#: includes/admin/builder/panels/class-fields.php:708
msgid "Search fields..."
msgstr ""

#: includes/admin/builder/panels/class-fields.php:720
msgid "Sorry, we didn't find any fields that match your criteria."
msgstr ""

#: includes/admin/builder/panels/class-payments.php:22
#: includes/admin/class-menu.php:89
#: includes/admin/class-menu.php:90
#: src/Admin/AdminBarMenu.php:225
#: src/Admin/AdminBarMenu.php:491
#: src/Admin/Forms/Views.php:592
#: src/Admin/Payments/Payments.php:173
#: src/Admin/Payments/Views/Overview/Page.php:151
#: src/Admin/Settings/Payments.php:47
#: src/Admin/Settings/Payments.php:78
msgid "Payments"
msgstr ""

#: includes/admin/builder/panels/class-payments.php:41
#: includes/admin/builder/panels/class-providers.php:81
#: includes/fields/class-base.php:1951
msgid "Default"
msgstr ""

#: includes/admin/builder/panels/class-payments.php:62
msgid "You need to <a href=\"#\" class=\"wpforms-panel-switch\" data-panel=\"setup\">setup your form</a> before you can manage these settings."
msgstr ""

#: includes/admin/builder/panels/class-payments.php:83
#: includes/admin/builder/panels/class-payments.php:104
msgid "Install Your Payment Integration"
msgstr ""

#. translators: %s - addons page URL.
#: includes/admin/builder/panels/class-payments.php:87
msgid "It seems you do not have any payment addons activated. You can head over to the <a href=\"%s\">Addons page</a> to install and activate the addon for your payment service."
msgstr ""

#: includes/admin/builder/panels/class-payments.php:105
msgid "It seems you don't have any payment addons activated. Click one of the available addons and start accepting payments today!"
msgstr ""

#: includes/admin/builder/panels/class-providers.php:27
msgid "Marketing"
msgstr ""

#: includes/admin/builder/panels/class-providers.php:57
msgid "We need to save your progress to continue to the Marketing panel. Is that OK?"
msgstr ""

#: includes/admin/builder/panels/class-providers.php:58
msgid "Are you sure you want to delete this connection?"
msgstr ""

#. translators: %s - connection type.
#: includes/admin/builder/panels/class-providers.php:60
msgid "Enter a %s nickname"
msgstr ""

#: includes/admin/builder/panels/class-providers.php:61
msgid "Eg: Newsletter Optin"
msgstr ""

#: includes/admin/builder/panels/class-providers.php:62
msgid "You must provide a connection nickname."
msgstr ""

#: includes/admin/builder/panels/class-providers.php:63
msgid "Field required"
msgstr ""

#: includes/admin/builder/panels/class-providers.php:64
#: src/Integrations/ConstantContact/V3/Settings/FormBuilder.php:233
#: src/Integrations/ConstantContact/V3/Settings/FormBuilder.php:239
#: src/Providers/Provider/Settings/FormBuilder.php:193
#: src/Providers/Provider/Settings/FormBuilder.php:248
#: templates/integrations/constant-contact-v3/builder/select-field.php:15
msgid "--- Select Form Field ---"
msgstr ""

#: includes/admin/builder/panels/class-providers.php:101
msgid "You need to <a href=\"#\" class=\"wpforms-panel-switch\" data-panel=\"setup\">set up your form</a> before you can manage these settings."
msgstr ""

#: includes/admin/builder/panels/class-providers.php:124
msgid "Install Your Marketing Integration"
msgstr ""

#. translators: %s - plugin admin area Addons page.
#: includes/admin/builder/panels/class-providers.php:129
msgid "It seems you do not have any marketing addons activated. You can head over to the <a href=\"%s\">Addons page</a> to install and activate the addon for your provider."
msgstr ""

#: includes/admin/builder/panels/class-providers.php:145
msgid "Select Your Marketing Integration"
msgstr ""

#: includes/admin/builder/panels/class-revisions.php:31
msgid "Revisions"
msgstr ""

#: includes/admin/builder/panels/class-revisions.php:38
msgid "Form Template Revisions"
msgstr ""

#: includes/admin/builder/panels/class-revisions.php:39
msgid "Form Revisions"
msgstr ""

#: includes/admin/builder/panels/class-revisions.php:117
msgid "Select a revision to roll back to that version. All changes, including settings, will be reverted."
msgstr ""

#: includes/admin/builder/panels/class-revisions.php:172
msgid "Restore this revision"
msgstr ""

#: includes/admin/builder/panels/class-revisions.php:178
msgid "go back to the current version"
msgstr ""

#. translators: %1$s - revision date, %2$s - revision time, %3$s - "Restore this revision" link, %4$s - "go back to the current version" link.
#: includes/admin/builder/panels/class-revisions.php:182
msgid "You’re currently viewing a form revision from %1$s at %2$s. %3$s or %4$s."
msgstr ""

#: includes/admin/builder/panels/class-settings.php:29
#: includes/admin/class-menu.php:118
#: includes/admin/class-menu.php:369
#: src/Admin/AdminBarMenu.php:534
msgid "Settings"
msgstr ""

#: includes/admin/builder/panels/class-settings.php:58
#: includes/admin/builder/panels/class-settings.php:155
#: includes/admin/class-settings.php:302
#: includes/admin/class-settings.php:379
#: includes/fields/class-base.php:1228
#: src/Admin/AdminBarMenu.php:209
msgid "General"
msgstr ""

#: includes/admin/builder/panels/class-settings.php:59
#: includes/admin/class-about.php:1738
#: src/Admin/Builder/AntiSpam.php:57
msgid "Spam Protection and Security"
msgstr ""

#: includes/admin/builder/panels/class-settings.php:60
#: src/Integrations/Gutenberg/FormSelector.php:488
msgid "Themes"
msgstr ""

#: includes/admin/builder/panels/class-settings.php:61
#: lite/wpforms-lite.php:136
#: src/Admin/AdminBarMenu.php:453
#: templates/admin/notifications.php:23
msgid "Notifications"
msgstr ""

#: includes/admin/builder/panels/class-settings.php:62
#: lite/wpforms-lite.php:489
msgid "Confirmations"
msgstr ""

#: includes/admin/builder/panels/class-settings.php:120
#: src/Admin/Forms/Tags.php:258
msgid "Press Enter or \",\" key to add new tag"
msgstr ""

#: includes/admin/builder/panels/class-settings.php:136
msgid "You need to <a href=\"#\" class=\"wpforms-panel-switch\" data-panel=\"setup\">setup your form</a> before you can manage the settings."
msgstr ""

#: includes/admin/builder/panels/class-settings.php:163
#: src/Integrations/Elementor/Widget.php:234
#: src/SmartTags/SmartTags.php:117
msgid "Form Name"
msgstr ""

#: includes/admin/builder/panels/class-settings.php:173
#: src/Integrations/Elementor/Widget.php:248
msgid "Form Description"
msgstr ""

#: includes/admin/builder/panels/class-settings.php:175
msgid "Enter descriptive text or instructions to help your users understand the requirements of your form."
msgstr ""

#: includes/admin/builder/panels/class-settings.php:190
msgid "Template Description"
msgstr ""

#: includes/admin/builder/panels/class-settings.php:192
msgid "Describe the use case for your template. Only displayed internally."
msgstr ""

#: includes/admin/builder/panels/class-settings.php:204
msgid "Submit Button Text"
msgstr ""

#: includes/admin/builder/panels/class-settings.php:214
msgid "Submit Button Processing Text"
msgstr ""

#: includes/admin/builder/panels/class-settings.php:216
msgid "Enter the submit button text you would like the button display while the form submit is processing."
msgstr ""

#: includes/admin/builder/panels/class-settings.php:294
#: src/Admin/Forms/Table/Facades/Columns.php:70
msgid "Tags"
msgstr ""

#: includes/admin/builder/panels/class-settings.php:299
msgid "Mark form with the tags. To create a new tag, simply type it and press Enter."
msgstr ""

#: includes/admin/builder/panels/class-settings.php:320
msgid "Form CSS Class"
msgstr ""

#: includes/admin/builder/panels/class-settings.php:322
msgid "Enter CSS class names for the form wrapper. Multiple class names should be separated with spaces."
msgstr ""

#: includes/admin/builder/panels/class-settings.php:331
msgid "Submit Button CSS Class"
msgstr ""

#: includes/admin/builder/panels/class-settings.php:333
msgid "Enter CSS class names for the form submit button. Multiple names should be separated with spaces."
msgstr ""

#: includes/admin/builder/panels/class-settings.php:342
msgid "Enable Prefill by URL"
msgstr ""

#: includes/admin/builder/panels/class-settings.php:347
msgid "How to use Prefill by URL"
msgstr ""

#: includes/admin/builder/panels/class-settings.php:357
msgid "Enable AJAX form submission"
msgstr ""

#: includes/admin/builder/panels/class-settings.php:359
msgid "Enables form submission without page reload."
msgstr ""

#: includes/admin/builder/panels/class-settings.php:379
#: includes/fields/class-base.php:2086
#: src/Integrations/Elementor/WidgetModern.php:318
#: src/Integrations/Gutenberg/FormSelector.php:530
#: src/Lite/Admin/Education/Builder/Notifications.php:130
msgid "Advanced"
msgstr ""

#: includes/admin/builder/panels/class-setup.php:33
msgid "Setup"
msgstr ""

#: includes/admin/builder/panels/class-setup.php:72
#: templates/admin/challenge/builder.php:15
#: templates/admin/challenge/modal.php:38
msgid "Name Your Form"
msgstr ""

#: includes/admin/builder/panels/class-setup.php:73
msgid "Enter your form name here&hellip;"
msgstr ""

#: includes/admin/builder/panels/class-setup.php:77
#: src/Admin/Tools/Views/Export.php:207
#: templates/admin/challenge/builder.php:21
#: templates/admin/challenge/modal.php:39
msgid "Select a Template"
msgstr ""

#. translators: %1$s - create a template doc link, %2$s - Contact us page link.
#: includes/admin/builder/panels/class-setup.php:85
msgid "To speed up the process, you can select from one of our pre-made templates, start with a <a href=\"#\" class=\"wpforms-trigger-blank\">blank form</a> or <a href=\"%1$s\" target=\"_blank\" rel=\"noopener noreferrer\">create your own</a>."
msgstr ""

#. translators: %1$s - create a template doc link, %2$s - Contact us page link.
#: includes/admin/builder/panels/class-setup.php:103
msgid "Have a suggestion for a new template? <a href=\"%1$s\" target=\"_blank\" rel=\"noopener noreferrer\">We’d love to hear it</a>!"
msgstr ""

#: includes/admin/class-about.php:111
#: includes/admin/class-menu.php:178
msgid "About Us"
msgstr ""

#: includes/admin/class-about.php:112
#: src/Admin/Builder/Help.php:148
msgid "Getting Started"
msgstr ""

#. translators: %1$s - current license type, %2$s - suggested license type.
#: includes/admin/class-about.php:126
msgid "%1$s vs %2$s"
msgstr ""

#: includes/admin/class-about.php:243
msgid "Hello and welcome to WPForms, the most beginner friendly drag & drop WordPress forms plugin. At WPForms, we build software that helps you create beautiful responsive online forms for your website in minutes."
msgstr ""

#: includes/admin/class-about.php:246
msgid "Over the years, we found that most WordPress contact form plugins were bloated, buggy, slow, and very hard to use. So we started with a simple goal: build a WordPress forms plugin that’s both easy and powerful."
msgstr ""

#: includes/admin/class-about.php:249
msgid "Our goal is to take the pain out of creating online forms and make it easy."
msgstr ""

#. translators: %1$s - WPBeginner URL, %2$s - OptinMonster URL, %3$s - MonsterInsights URL.
#: includes/admin/class-about.php:255
msgid "WPForms is brought to you by the same team that’s behind the largest WordPress resource site, <a href=\"%1$s\" target=\"_blank\" rel=\"noopener noreferrer\">WPBeginner</a>, the most popular lead-generation software, <a href=\"%2$s\" target=\"_blank\" rel=\"noopener noreferrer\">OptinMonster</a>, the best WordPress analytics plugin, <a href=\"%3$s\" target=\"_blank\" rel=\"noopener noreferrer\">MonsterInsights</a>, and more!"
msgstr ""

#: includes/admin/class-about.php:271
msgid "Yup, we know a thing or two about building awesome products that customers love."
msgstr ""

#: includes/admin/class-about.php:277
msgid "The WPForms Team photo"
msgstr ""

#: includes/admin/class-about.php:279
msgid "The WPForms Team"
msgstr ""

#. translators: %s - status label.
#: includes/admin/class-about.php:334
msgid "Status: %s"
msgstr ""

#: includes/admin/class-about.php:347
msgid "WordPress.org"
msgstr ""

#: includes/admin/class-about.php:420
#: src/Admin/Tools/Views/Import.php:258
msgid "Not Installed"
msgstr ""

#: includes/admin/class-about.php:423
msgid "Install Plugin"
msgstr ""

#: includes/admin/class-about.php:458
msgid "Creating Your First Form"
msgstr ""

#: includes/admin/class-about.php:462
msgid "Want to get started creating your first form with WPForms? By following the step by step instructions in this walkthrough, you can easily publish your first form on your site."
msgstr ""

#: includes/admin/class-about.php:466
msgid "To begin, you’ll need to be logged into the WordPress admin area. Once there, click on WPForms in the admin sidebar to go to the Forms Overview page."
msgstr ""

#: includes/admin/class-about.php:470
msgid "In the Forms Overview page, the forms list will be empty because there are no forms yet. To create a new form, click on the Add New button, and this will launch the WPForms Form Builder."
msgstr ""

#: includes/admin/class-about.php:476
msgid "How to Add a New Form"
msgstr ""

#: includes/admin/class-about.php:481
msgid "How to Customize Form Fields"
msgstr ""

#: includes/admin/class-about.php:486
msgid "How to Display Forms on Your Site"
msgstr ""

#: includes/admin/class-about.php:504
#: lite/wpforms-lite.php:699
msgid "Get WPForms Pro and Unlock all the Powerful Features"
msgstr ""

#: includes/admin/class-about.php:510
msgid "Thanks for being a loyal WPForms Lite user. <strong>Upgrade to WPForms Pro</strong> to unlock all the awesome features and experience<br>why WPForms is consistently rated the best WordPress form builder."
msgstr ""

#. translators: %s - stars.
#: includes/admin/class-about.php:523
msgid "We know that you will truly love WPForms. It has over <strong>13,000+ five star ratings</strong> (%s) and is active on over 6 million websites."
msgstr ""

#. translators: %s - number of templates.
#: includes/admin/class-about.php:546
#: lite/wpforms-lite.php:723
msgid "%s customizable form templates"
msgstr ""

#: includes/admin/class-about.php:553
#: lite/wpforms-lite.php:728
msgid "Store and manage form entries in WordPress"
msgstr ""

#: includes/admin/class-about.php:557
#: lite/wpforms-lite.php:729
msgid "Unlock all fields & features, including smart conditional logic"
msgstr ""

#: includes/admin/class-about.php:561
#: lite/wpforms-lite.php:730
msgid "Create powerful custom calculation forms"
msgstr ""

#: includes/admin/class-about.php:565
#: lite/wpforms-lite.php:731
msgid "Make surveys and generate reports"
msgstr ""

#: includes/admin/class-about.php:569
#: lite/wpforms-lite.php:732
msgid "Accept user-submitted content with the Post Submissions addon"
msgstr ""

#: includes/admin/class-about.php:577
#: lite/wpforms-lite.php:735
msgid "7000+ integrations with marketing and payment services"
msgstr ""

#: includes/admin/class-about.php:581
#: lite/wpforms-lite.php:736
msgid "Let users save & resume submissions to prevent abandonment"
msgstr ""

#: includes/admin/class-about.php:585
#: lite/wpforms-lite.php:737
msgid "Take payments with Stripe, PayPal, Square, & Authorize.Net"
msgstr ""

#: includes/admin/class-about.php:589
#: lite/wpforms-lite.php:738
msgid "Export entries to Google Sheets, Excel, and CSV"
msgstr ""

#: includes/admin/class-about.php:593
#: lite/wpforms-lite.php:739
msgid "Collect signatures, geolocation data, and file uploads"
msgstr ""

#: includes/admin/class-about.php:597
#: lite/wpforms-lite.php:740
msgid "Create user registration and login forms"
msgstr ""

#: includes/admin/class-about.php:612
msgid "Get WPForms Pro Today and Unlock all the Powerful Features"
msgstr ""

#: includes/admin/class-about.php:621
#: includes/admin/class-about.php:843
msgid "Bonus: WPForms Lite users get <span class=\"price-20-off\">50% off regular price</span>, automatically applied at checkout."
msgstr ""

#: includes/admin/class-about.php:642
msgid "How to Choose the Right Form Field"
msgstr ""

#: includes/admin/class-about.php:646
msgid "Are you wondering which form fields you have access to in WPForms and what each field does? WPForms has lots of field types to make creating and filling out forms easy. In this tutorial, we’ll cover all of the fields available in WPForms."
msgstr ""

#: includes/admin/class-about.php:650
#: includes/admin/class-about.php:669
#: includes/admin/class-about.php:688
#: includes/admin/class-about.php:707
msgid "Read Documentation"
msgstr ""

#: includes/admin/class-about.php:661
msgid "A Complete Guide to WPForms Settings"
msgstr ""

#: includes/admin/class-about.php:665
msgid "Would you like to learn more about all of the settings available in WPForms? In addition to tons of customization options within the form builder, WPForms has an extensive list of plugin-wide options available. This includes choosing your currency, adding GDPR enhancements, setting up integrations."
msgstr ""

#: includes/admin/class-about.php:680
msgid "How to Create GDPR Compliant Forms"
msgstr ""

#: includes/admin/class-about.php:684
msgid "Do you need to check that your forms are compliant with the European Union’s General Data Protection Regulation? The best way to ensure GDPR compliance for your specific site is always to consult legal counsel. In this guide, we’ll discuss general considerations for GDPR compliance in your WordPress forms."
msgstr ""

#: includes/admin/class-about.php:699
msgid "How to Install and Activate WPForms Addons"
msgstr ""

#: includes/admin/class-about.php:703
msgid "Would you like to access WPForms addons to extend the functionality of your forms? The first thing you need to do is install WPForms. Once that’s done, let’s go ahead and look at the process of activating addons."
msgstr ""

#: includes/admin/class-about.php:754
msgid "Get the most out of WPForms by upgrading to Pro and unlocking all of the powerful features."
msgstr ""

#: includes/admin/class-about.php:763
msgid "Feature"
msgstr ""

#. translators: %s - next license level.
#: includes/admin/class-about.php:832
msgid "Get WPForms %s Today and Unlock all the Powerful Features"
msgstr ""

#: includes/admin/class-about.php:874
msgid "OptinMonster"
msgstr ""

#: includes/admin/class-about.php:875
msgid "Instantly get more subscribers, leads, and sales with the #1 conversion optimization toolkit. Create high converting popups, announcement bars, spin a wheel, and more with smart targeting and personalization."
msgstr ""

#: includes/admin/class-about.php:882
#: src/Admin/Dashboard/Widget.php:188
#: src/Admin/Pages/Analytics.php:414
msgid "MonsterInsights"
msgstr ""

#: includes/admin/class-about.php:883
#: includes/admin/class-about.php:890
msgid "The leading WordPress analytics plugin that shows you how people find and use your website, so you can make data driven decisions to grow your business. Properly set up Google Analytics without writing code."
msgstr ""

#: includes/admin/class-about.php:889
msgid "MonsterInsights Pro"
msgstr ""

#: includes/admin/class-about.php:898
#: src/Admin/Dashboard/Widget.php:212
#: src/Admin/Pages/SMTP.php:402
msgid "WP Mail SMTP"
msgstr ""

#: includes/admin/class-about.php:899
#: includes/admin/class-about.php:906
msgid "Improve your WordPress email deliverability and make sure that your website emails reach user's inbox with the #1 SMTP plugin for WordPress. Over 3 million websites use it to fix WordPress email issues."
msgstr ""

#: includes/admin/class-about.php:905
msgid "WP Mail SMTP Pro"
msgstr ""

#: includes/admin/class-about.php:914
#: src/Admin/Dashboard/Widget.php:196
msgid "AIOSEO"
msgstr ""

#: includes/admin/class-about.php:915
#: includes/admin/class-about.php:922
msgid "The original WordPress SEO plugin and toolkit that improves your website's search rankings. Comes with all the SEO features like Local SEO, WooCommerce SEO, sitemaps, SEO optimizer, schema, and more."
msgstr ""

#: includes/admin/class-about.php:921
msgid "AIOSEO Pro"
msgstr ""

#: includes/admin/class-about.php:930
#: src/Admin/Dashboard/Widget.php:204
msgid "SeedProd"
msgstr ""

#: includes/admin/class-about.php:931
#: includes/admin/class-about.php:938
msgid "The fastest drag & drop landing page builder for WordPress. Create custom landing pages without writing code, connect them with your CRM, collect subscribers, and grow your audience. Trusted by 1 million sites."
msgstr ""

#: includes/admin/class-about.php:937
msgid "SeedProd Pro"
msgstr ""

#: includes/admin/class-about.php:946
msgid "RafflePress"
msgstr ""

#: includes/admin/class-about.php:947
#: includes/admin/class-about.php:954
msgid "Turn your website visitors into brand ambassadors! Easily grow your email list, website traffic, and social media followers with the most powerful giveaways & contests plugin for WordPress."
msgstr ""

#: includes/admin/class-about.php:953
msgid "RafflePress Pro"
msgstr ""

#: includes/admin/class-about.php:962
msgid "PushEngage"
msgstr ""

#: includes/admin/class-about.php:963
msgid "Connect with your visitors after they leave your website with the leading web push notification software. Over 10,000+ businesses worldwide use PushEngage to send 15 billion notifications each month."
msgstr ""

#: includes/admin/class-about.php:970
msgid "Smash Balloon Instagram Feeds"
msgstr ""

#: includes/admin/class-about.php:971
#: includes/admin/class-about.php:978
msgid "Easily display Instagram content on your WordPress site without writing any code. Comes with multiple templates, ability to show content from multiple accounts, hashtags, and more. Trusted by 1 million websites."
msgstr ""

#: includes/admin/class-about.php:977
msgid "Smash Balloon Instagram Feeds Pro"
msgstr ""

#: includes/admin/class-about.php:986
msgid "Smash Balloon Facebook Feeds"
msgstr ""

#: includes/admin/class-about.php:987
#: includes/admin/class-about.php:994
msgid "Easily display Facebook content on your WordPress site without writing any code. Comes with multiple templates, ability to embed albums, group content, reviews, live videos, comments, and reactions."
msgstr ""

#: includes/admin/class-about.php:993
msgid "Smash Balloon Facebook Feeds Pro"
msgstr ""

#: includes/admin/class-about.php:1002
msgid "Smash Balloon YouTube Feeds"
msgstr ""

#: includes/admin/class-about.php:1003
#: includes/admin/class-about.php:1010
msgid "Easily display YouTube videos on your WordPress site without writing any code. Comes with multiple layouts, ability to embed live streams, video filtering, ability to combine multiple channel videos, and more."
msgstr ""

#: includes/admin/class-about.php:1009
msgid "Smash Balloon YouTube Feeds Pro"
msgstr ""

#: includes/admin/class-about.php:1018
msgid "Smash Balloon Twitter Feeds"
msgstr ""

#: includes/admin/class-about.php:1019
#: includes/admin/class-about.php:1026
msgid "Easily display Twitter content in WordPress without writing any code. Comes with multiple layouts, ability to combine multiple Twitter feeds, Twitter card support, tweet moderation, and more."
msgstr ""

#: includes/admin/class-about.php:1025
msgid "Smash Balloon Twitter Feeds Pro"
msgstr ""

#: includes/admin/class-about.php:1034
msgid "TrustPulse"
msgstr ""

#: includes/admin/class-about.php:1035
msgid "Boost your sales and conversions by up to 15% with real-time social proof notifications. TrustPulse helps you show live user activity and purchases to help convince other users to purchase."
msgstr ""

#: includes/admin/class-about.php:1042
msgid "SearchWP"
msgstr ""

#: includes/admin/class-about.php:1043
msgid "The most advanced WordPress search plugin. Customize your WordPress search algorithm, reorder search results, track search metrics, and everything you need to leverage search to grow your business."
msgstr ""

#: includes/admin/class-about.php:1051
msgid "AffiliateWP"
msgstr ""

#: includes/admin/class-about.php:1052
msgid "The #1 affiliate management plugin for WordPress. Easily create an affiliate program for your eCommerce store or membership site within minutes and start growing your sales with the power of referral marketing."
msgstr ""

#: includes/admin/class-about.php:1060
msgid "WP Simple Pay"
msgstr ""

#: includes/admin/class-about.php:1061
#: includes/admin/class-about.php:1068
msgid "The #1 Stripe payments plugin for WordPress. Start accepting one-time and recurring payments on your WordPress site without setting up a shopping cart. No code required."
msgstr ""

#: includes/admin/class-about.php:1067
msgid "WP Simple Pay Pro"
msgstr ""

#: includes/admin/class-about.php:1076
msgid "Easy Digital Downloads"
msgstr ""

#: includes/admin/class-about.php:1077
msgid "The best WordPress eCommerce plugin for selling digital downloads. Start selling eBooks, software, music, digital art, and more within minutes. Accept payments, manage subscriptions, advanced access control, and more."
msgstr ""

#: includes/admin/class-about.php:1084
msgid "Sugar Calendar"
msgstr ""

#: includes/admin/class-about.php:1085
#: includes/admin/class-about.php:1092
msgid "A simple & powerful event calendar plugin for WordPress that comes with all the event management features including payments, scheduling, timezones, ticketing, recurring events, and more."
msgstr ""

#: includes/admin/class-about.php:1091
msgid "Sugar Calendar Pro"
msgstr ""

#: includes/admin/class-about.php:1099
msgid "Charitable"
msgstr ""

#: includes/admin/class-about.php:1100
msgid "Top-rated WordPress donation and fundraising plugin. Over 10,000+ non-profit organizations and website owners use Charitable to create fundraising campaigns and raise more money online."
msgstr ""

#: includes/admin/class-about.php:1106
msgid "WPCode"
msgstr ""

#: includes/admin/class-about.php:1107
msgid "Future proof your WordPress customizations with the most popular code snippet management plugin for WordPress. Trusted by over 1,500,000+ websites for easily adding code to WordPress right from the admin area."
msgstr ""

#: includes/admin/class-about.php:1113
msgid "Duplicator"
msgstr ""

#: includes/admin/class-about.php:1114
msgid "Leading WordPress backup & site migration plugin. Over 1,500,000+ smart website owners use Duplicator to make reliable and secure WordPress backups to protect their websites. It also makes website migration really easy."
msgstr ""

#: includes/admin/class-about.php:1138
msgid "Entries via Email Only"
msgstr ""

#: includes/admin/class-about.php:1144
#: includes/admin/class-about.php:1150
#: includes/admin/class-about.php:1156
msgid "Complete Entry Management inside WordPress"
msgstr ""

#: includes/admin/class-about.php:1164
msgid "Standard and Payment Fields"
msgstr ""

#: includes/admin/class-about.php:1165
msgid "Name, Email, Single Line Text, Paragraph Text, Dropdown, Multiple Choice, Checkboxes, Numbers, Number Slider, and Payment Fields (Single Item, Total, etc.)"
msgstr ""

#: includes/admin/class-about.php:1171
#: includes/admin/class-about.php:1178
#: includes/admin/class-about.php:1185
msgid "Access to all Standard, Fancy, and Payment Fields"
msgstr ""

#: includes/admin/class-about.php:1172
#: includes/admin/class-about.php:1179
#: includes/admin/class-about.php:1186
msgid "Address, Phone, Website / URL, Date / Time, Password, File Upload, Layout, Rich Text, Content, HTML, Pagebreaks, Entry Preview, Section Dividers, Ratings, and Hidden Field"
msgstr ""

#: includes/admin/class-about.php:1194
msgid "Not available"
msgstr ""

#: includes/admin/class-about.php:1200
#: includes/admin/class-about.php:1206
#: includes/admin/class-about.php:1212
msgid "Powerful Form Logic for Building Smart Forms"
msgstr ""

#: includes/admin/class-about.php:1220
#: includes/admin/class-about.php:1226
#: includes/admin/class-about.php:1232
msgid "Basic Form Templates"
msgstr ""

#. translators: %s - number of templates.
#: includes/admin/class-about.php:1240
msgid "All Form Templates including Bonus %s pre-made form templates"
msgstr ""

#: includes/admin/class-about.php:1251
msgid "Basic Anti-Spam Settings"
msgstr ""

#: includes/admin/class-about.php:1252
msgid "Basic Protection, reCAPTCHA, hCaptcha, Cloudflare Turnstile and Akismet"
msgstr ""

#: includes/admin/class-about.php:1258
#: includes/admin/class-about.php:1265
#: includes/admin/class-about.php:1272
msgid "Additional Anti-Spam Settings"
msgstr ""

#: includes/admin/class-about.php:1259
#: includes/admin/class-about.php:1266
#: includes/admin/class-about.php:1273
msgid "Basic Protection, reCAPTCHA, hCaptcha, Cloudflare Turnstile, Akismet, Country Filter, Keyword Filter, and Custom Captcha"
msgstr ""

#: includes/admin/class-about.php:1281
#: includes/admin/class-about.php:1288
msgid "Limited Marketing Integration"
msgstr ""

#: includes/admin/class-about.php:1282
#: includes/admin/class-about.php:1289
msgid "Constant Contact only"
msgstr ""

#: includes/admin/class-about.php:1295
#: includes/admin/class-about.php:1317
msgid "Additional Marketing Integrations"
msgstr ""

#: includes/admin/class-about.php:1337
#: includes/admin/class-about.php:1371
#: includes/admin/class-about.php:1405
#: includes/admin/class-about.php:1439
msgid "<strong>Bonus:</strong> 7000+ integrations with Zapier."
msgstr ""

#: includes/admin/class-about.php:1347
#: includes/admin/class-about.php:1381
#: includes/admin/class-about.php:1415
msgid "All Marketing Integrations"
msgstr ""

#: includes/admin/class-about.php:1451
#: includes/admin/class-about.php:1458
#: includes/admin/class-about.php:1465
msgid "Limited Payment Forms"
msgstr ""

#: includes/admin/class-about.php:1452
#: includes/admin/class-about.php:1459
#: includes/admin/class-about.php:1466
msgid "Accept payments using Stripe and Square only"
msgstr ""

#: includes/admin/class-about.php:1472
#: includes/admin/class-about.php:1479
#: includes/admin/class-about.php:1486
#: includes/admin/class-about.php:1493
msgid "Create Payment Forms"
msgstr ""

#: includes/admin/class-about.php:1473
msgid "Accept payments using PayPal Commerce, Stripe, Square, and PayPal Standard"
msgstr ""

#: includes/admin/class-about.php:1480
#: includes/admin/class-about.php:1487
#: includes/admin/class-about.php:1494
msgid "Accept payments using PayPal Commerce, Stripe, Square, PayPal Standard, and Authorize.Net"
msgstr ""

#: includes/admin/class-about.php:1502
#: includes/admin/class-about.php:1508
#: includes/admin/class-about.php:1514
msgid "Not Available"
msgstr ""

#: includes/admin/class-about.php:1520
msgid "Create interactive Surveys and Polls with beautiful reports"
msgstr ""

#: includes/admin/class-about.php:1528
msgid "No Advanced Features"
msgstr ""

#: includes/admin/class-about.php:1534
#: includes/admin/class-about.php:1541
msgid "Limited Advanced Features"
msgstr ""

#: includes/admin/class-about.php:1535
msgid "Multi-page Forms, File Upload Forms, Multiple Form Notifications, File Upload and CSV Attachments, Conditional Form Confirmation"
msgstr ""

#: includes/admin/class-about.php:1542
msgid "Multi-page Forms, File Upload Forms, Multiple Form Notifications, File Upload and CSV Attachments, Conditional Form Confirmation, Save and Resume Form"
msgstr ""

#: includes/admin/class-about.php:1548
msgid "All Advanced Features"
msgstr ""

#: includes/admin/class-about.php:1549
msgid "Multi-page Forms, File Upload Forms, Multiple Form Notifications, File Upload and CSV Attachments, Conditional Form Confirmation, Custom CAPTCHA, Offline Forms, Signature Forms, Save and Resume Form, Coupons"
msgstr ""

#: includes/admin/class-about.php:1557
#: includes/admin/class-about.php:1563
msgid "No Addons Included"
msgstr ""

#: includes/admin/class-about.php:1569
msgid "Email Marketing Addons included"
msgstr ""

#: includes/admin/class-about.php:1575
msgid "Pro Addons Included"
msgstr ""

#: includes/admin/class-about.php:1576
msgid "Calculations, Form Abandonment, Conversational Forms, Lead Forms, Frontend Post Submission, User Registration, Geolocation, Google Sheets, Coupons, Dropbox, Google Drive, and more (30+ total)"
msgstr ""

#: includes/admin/class-about.php:1582
#: includes/admin/class-about.php:1589
#: includes/admin/class-about.php:1596
msgid "All Addons Included"
msgstr ""

#: includes/admin/class-about.php:1583
#: includes/admin/class-about.php:1590
#: includes/admin/class-about.php:1597
msgid "Calculations, Form Abandonment, Conversational Forms, Lead Forms, Frontend Post Submission, User Registration, Geolocation, Webhooks, Google Sheets, Coupons, Dropbox, Google Drive, and more (35+ total)"
msgstr ""

#: includes/admin/class-about.php:1605
msgid "Limited Support"
msgstr ""

#: includes/admin/class-about.php:1611
#: includes/admin/class-about.php:1617
msgid "Standard Support"
msgstr ""

#: includes/admin/class-about.php:1623
msgid "Priority Support"
msgstr ""

#: includes/admin/class-about.php:1629
#: includes/admin/class-about.php:1635
#: includes/admin/class-about.php:1641
msgid "Premium Support"
msgstr ""

#: includes/admin/class-about.php:1649
msgid "1 Site"
msgstr ""

#: includes/admin/class-about.php:1655
msgid "3 Sites"
msgstr ""

#: includes/admin/class-about.php:1661
msgid "5 Sites"
msgstr ""

#: includes/admin/class-about.php:1667
#: includes/admin/class-about.php:1673
#: includes/admin/class-about.php:1679
msgid "Unlimited Sites"
msgstr ""

#: includes/admin/class-about.php:1735
#: includes/admin/class-menu.php:79
msgid "Form Entries"
msgstr ""

#: includes/admin/class-about.php:1736
msgid "Form Fields"
msgstr ""

#: includes/admin/class-about.php:1737
#: includes/admin/class-menu.php:108
#: includes/admin/class-welcome.php:196
#: src/Admin/Pages/Templates.php:114
msgid "Form Templates"
msgstr ""

#: includes/admin/class-about.php:1739
#: includes/admin/class-welcome.php:208
#: src/Integrations/Square/Admin/Builder/Settings.php:277
#: src/Integrations/Stripe/Admin/Builder/Settings.php:301
#: src/Lite/Admin/Education/Builder/Fields.php:81
msgid "Smart Conditional Logic"
msgstr ""

#: includes/admin/class-about.php:1740
#: includes/admin/class-welcome.php:279
#: src/Admin/Builder/Help.php:152
msgid "Marketing Integrations"
msgstr ""

#: includes/admin/class-about.php:1741
#: includes/admin/class-welcome.php:270
#: src/Admin/Builder/Help.php:153
msgid "Payment Forms"
msgstr ""

#: includes/admin/class-about.php:1742
#: includes/admin/class-welcome.php:271
msgid "Surveys & Polls"
msgstr ""

#: includes/admin/class-about.php:1743
msgid "Advanced Form Features"
msgstr ""

#: includes/admin/class-about.php:1744
#: includes/admin/class-menu.php:147
#: lite/templates/admin/addons.php:21
msgid "WPForms Addons"
msgstr ""

#: includes/admin/class-about.php:1745
msgid "Customer Support"
msgstr ""

#: includes/admin/class-about.php:1746
msgid "Number of Sites"
msgstr ""

#: includes/admin/class-editor.php:52
#: includes/admin/class-editor.php:166
msgid "Add Form"
msgstr ""

#: includes/admin/class-editor.php:107
msgid "Insert Form"
msgstr ""

#. translators: %s - WPForms documentation URL.
#: includes/admin/class-editor.php:117
msgid "Heads up! Don't forget to test your form. <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">Check out our complete guide</a>!"
msgstr ""

#: includes/admin/class-editor.php:133
msgid "Select a form below to insert"
msgstr ""

#: includes/admin/class-editor.php:139
msgid "Show form name"
msgstr ""

#: includes/admin/class-editor.php:140
msgid "Show form description"
msgstr ""

#. translators: %s - WPForms Builder page.
#: includes/admin/class-editor.php:146
msgid "Whoops, you haven't created a form yet. Want to <a href=\"%s\">give it a go</a>?"
msgstr ""

#: includes/admin/class-menu.php:60
#: src/Admin/AdminBarMenu.php:472
msgid "All Forms"
msgstr ""

#: includes/admin/class-menu.php:69
msgid "WPForms Builder"
msgstr ""

#: includes/admin/class-menu.php:70
#: src/Admin/AdminBarMenu.php:515
msgid "Add New Form"
msgstr ""

#: includes/admin/class-menu.php:80
#: src/Admin/Forms/Table/Facades/Columns.php:82
#: src/Admin/Forms/Views.php:571
#: src/Lite/Admin/DashboardWidget.php:177
#: src/Logger/Log.php:103
#: templates/emails/summary-body-plain.php:52
#: templates/emails/summary-body.php:159
msgid "Entries"
msgstr ""

#: includes/admin/class-menu.php:107
msgid "WPForms Templates"
msgstr ""

#: includes/admin/class-menu.php:117
msgid "WPForms Settings"
msgstr ""

#: includes/admin/class-menu.php:127
msgid "WPForms Tools"
msgstr ""

#: includes/admin/class-menu.php:128
#: src/Admin/AdminBarMenu.php:553
msgid "Tools"
msgstr ""

#: includes/admin/class-menu.php:138
#: templates/emails/summary-body.php:280
msgid "Info"
msgstr ""

#: includes/admin/class-menu.php:148
#: src/Integrations/AI/Admin/Builder/Forms.php:218
msgid "Addons"
msgstr ""

#: includes/admin/class-menu.php:157
#: includes/admin/class-menu.php:158
msgid "Analytics"
msgstr ""

#: includes/admin/class-menu.php:167
#: includes/admin/class-menu.php:168
msgid "SMTP"
msgstr ""

#: includes/admin/class-menu.php:177
msgid "About WPForms"
msgstr ""

#: includes/admin/class-menu.php:187
#: includes/admin/class-menu.php:188
#: src/Admin/AdminBarMenu.php:572
#: src/Admin/Pages/Community.php:137
msgid "Community"
msgstr ""

#: includes/admin/class-menu.php:197
#: includes/admin/class-menu.php:198
#: lite/templates/education/builder/did-you-know.php:30
#: lite/wpforms-lite.php:1000
#: src/Admin/Builder/AntiSpam.php:350
#: src/Admin/Builder/Templates.php:1012
#: src/Forms/Fields/Traits/ProField.php:287
#: src/Integrations/AI/Admin/Builder/Forms.php:217
msgid "Upgrade to Pro"
msgstr ""

#: includes/admin/class-menu.php:356
#: includes/admin/class-welcome.php:338
#: includes/functions/education.php:79
#: src/Admin/FlyoutMenu.php:112
#: src/Admin/Payments/Views/Coupons/Education.php:143
#: src/Admin/Tools/Views/Importer.php:229
#: src/Lite/Admin/DashboardWidget.php:324
#: src/Lite/Admin/Settings/Access.php:300
#: templates/builder/help.php:91
#: templates/education/admin/page.php:100
msgid "Upgrade to WPForms Pro"
msgstr ""

#: includes/admin/class-menu.php:357
msgid "Get WPForms Pro"
msgstr ""

#: includes/admin/class-menu.php:368
msgid "Go to WPForms Settings page"
msgstr ""

#: includes/admin/class-menu.php:385
msgid "Read the documentation"
msgstr ""

#: includes/admin/class-menu.php:386
#: includes/admin/class-review.php:297
#: templates/builder/help.php:122
msgid "Docs"
msgstr ""

#: includes/admin/class-review.php:203
msgid "Hey, there! It looks like you enjoy creating forms with WPForms. Would you do us a favor and take a few seconds to give us a 5-star review? We’d love to hear from you."
msgstr ""

#: includes/admin/class-review.php:205
msgid "Ok, you deserve it"
msgstr ""

#: includes/admin/class-review.php:206
msgid "Nope, maybe later"
msgstr ""

#: includes/admin/class-review.php:207
msgid "I already did"
msgstr ""

#. translators: $1$s - WPForms plugin name, $2$s - WP.org review link, $3$s - WP.org review link.
#: includes/admin/class-review.php:231
msgid "Please rate %1$s <a href=\"%2$s\" target=\"_blank\" rel=\"noopener noreferrer\">&#9733;&#9733;&#9733;&#9733;&#9733;</a> on <a href=\"%3$s\" target=\"_blank\" rel=\"noopener\">WordPress.org</a> to help us spread the word."
msgstr ""

#: includes/admin/class-review.php:288
msgid "Support"
msgstr ""

#: includes/admin/class-review.php:302
msgid "VIP Circle"
msgstr ""

#: includes/admin/class-review.php:307
msgid "Free Plugins"
msgstr ""

#: includes/admin/class-review.php:314
msgid "Made with ♥ by the WPForms Team"
msgstr ""

#. translators: %1$s - WPForms.com doc page URL; %2$s - button text.
#: includes/admin/class-settings.php:115
msgid "It looks like you've downgraded to an older version of WPForms. We recommend always using the latest version as some features may not function as expected in older versions. <a href=\"%1$s\" target=\"_blank\" rel=\"noopener\">%2$s</a>"
msgstr ""

#: includes/admin/class-settings.php:273
msgid "Settings were successfully saved."
msgstr ""

#: includes/admin/class-settings.php:277
msgid "You've changed your currency. Please double-check the product prices in your forms and verify that they're correct."
msgstr ""

#: includes/admin/class-settings.php:304
#: includes/admin/class-settings.php:309
#: includes/admin/class-settings.php:324
#: src/Admin/Settings/Captcha/Page.php:124
#: src/Admin/Settings/Email.php:178
#: src/Admin/Settings/Payments.php:49
#: src/Admin/Tools/Views/Logs.php:128
msgid "Save Settings"
msgstr ""

#: includes/admin/class-settings.php:307
#: src/Admin/AdminBarMenu.php:221
msgid "Validation"
msgstr ""

#: includes/admin/class-settings.php:312
#: includes/admin/class-settings.php:560
#: includes/admin/class-settings.php:567
#: src/Admin/AdminBarMenu.php:229
msgid "Integrations"
msgstr ""

#: includes/admin/class-settings.php:317
#: includes/admin/class-welcome.php:276
#: src/Admin/AdminBarMenu.php:233
#: src/Admin/Education/Admin/Settings/Geolocation.php:141
msgid "Geolocation"
msgstr ""

#: includes/admin/class-settings.php:322
#: src/Admin/AdminBarMenu.php:241
msgid "Misc"
msgstr ""

#: includes/admin/class-settings.php:367
msgid "License"
msgstr ""

#: includes/admin/class-settings.php:367
msgid "Your license key provides access to updates and addons."
msgstr ""

#: includes/admin/class-settings.php:374
msgid "License Key"
msgstr ""

#: includes/admin/class-settings.php:386
msgid "Include Form Styling"
msgstr ""

#. translators: %s - WPForms.com form styling setting URL.
#: includes/admin/class-settings.php:389
msgid "Determines which CSS files to load and use for the site. \"Base and Form Theme Styling\" is recommended, unless you are experienced with CSS or instructed by support to change settings. <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\" class=\"wpforms-learn-more\">Learn More</a>"
msgstr ""

#: includes/admin/class-settings.php:405
msgid "Base and form theme styling"
msgstr ""

#: includes/admin/class-settings.php:406
msgid "Base styling only"
msgstr ""

#: includes/admin/class-settings.php:407
msgid "No styling"
msgstr ""

#: includes/admin/class-settings.php:412
msgid "Load Assets Globally"
msgstr ""

#: includes/admin/class-settings.php:413
msgid "Load WPForms assets site-wide. Only check if your site is having compatibility issues or instructed to by support."
msgstr ""

#: includes/admin/class-settings.php:419
msgid "GDPR"
msgstr ""

#: includes/admin/class-settings.php:426
msgid "GDPR Enhancements"
msgstr ""

#. translators: %s - WPForms.com GDPR documentation URL.
#: includes/admin/class-settings.php:429
msgid "Enable GDPR related features and enhancements. <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\" class=\"wpforms-learn-more\">Learn More</a>"
msgstr ""

#. translators: %s - WPForms.com smart tags documentation URL.
#: includes/admin/class-settings.php:450
msgid "%1$s These messages are displayed to the users as they fill out a form in real-time. Messages can include plain text and/or %2$sSmart Tags%3$s."
msgstr ""

#: includes/admin/class-settings.php:451
msgid "Validation Messages"
msgstr ""

#: includes/admin/class-settings.php:462
#: includes/fields/class-base.php:1331
msgid "Required"
msgstr ""

#: includes/admin/class-settings.php:464
#: includes/functions/escape-sanitize.php:451
#: src/Frontend/Frontend.php:1860
msgid "This field is required."
msgstr ""

#: includes/admin/class-settings.php:468
#: includes/fields/class-email.php:50
#: includes/fields/class-email.php:144
#: includes/fields/class-email.php:485
#: includes/templates/class-simple-contact-form.php:43
#: src/Admin/AdminBarMenu.php:213
#: src/Admin/Settings/Email.php:177
#: src/Emails/Preview.php:406
#: src/Integrations/ConstantContact/V3/Settings/FormBuilder.php:210
#: src/Integrations/ConstantContact/V3/Settings/FormBuilder.php:229
#: src/Integrations/ConstantContact/V3/Settings/FormBuilder.php:244
#: src/Integrations/Stripe/Fields/PaymentElementCreditCard.php:221
#: templates/admin/settings/email-heading.php:12
msgid "Email"
msgstr ""

#: includes/admin/class-settings.php:470
#: src/Frontend/Frontend.php:1861
msgid "Please enter a valid email address."
msgstr ""

#: includes/admin/class-settings.php:474
msgid "Email Suggestion"
msgstr ""

#. translators: %s - suggested email address.
#: includes/admin/class-settings.php:477
#: src/Frontend/Frontend.php:1865
msgid "Did you mean %s?"
msgstr ""

#: includes/admin/class-settings.php:483
msgid "Email Restricted"
msgstr ""

#: includes/admin/class-settings.php:485
#: includes/fields/class-email.php:652
#: includes/fields/class-email.php:660
#: src/Frontend/Frontend.php:1870
msgid "This email address is not allowed."
msgstr ""

#: includes/admin/class-settings.php:489
msgid "Number"
msgstr ""

#: includes/admin/class-settings.php:491
#: includes/fields/class-number.php:226
#: src/Frontend/Frontend.php:1871
msgid "Please enter a valid number."
msgstr ""

#: includes/admin/class-settings.php:495
msgid "Number Positive"
msgstr ""

#: includes/admin/class-settings.php:497
#: src/Frontend/Frontend.php:1872
msgid "Please enter a valid positive number."
msgstr ""

#: includes/admin/class-settings.php:501
#: src/Forms/Fields/PaymentSingle/Field.php:418
msgid "Minimum Price"
msgstr ""

#: includes/admin/class-settings.php:503
#: src/Frontend/Frontend.php:1873
msgid "Amount entered is less than the required minimum."
msgstr ""

#: includes/admin/class-settings.php:507
msgid "Confirm Value"
msgstr ""

#: includes/admin/class-settings.php:509
#: src/Frontend/Frontend.php:1874
msgid "Field values do not match."
msgstr ""

#: includes/admin/class-settings.php:513
msgid "Input Mask Incomplete"
msgstr ""

#: includes/admin/class-settings.php:515
#: src/Frontend/Frontend.php:1894
msgid "Please fill out the field in required format."
msgstr ""

#: includes/admin/class-settings.php:519
msgid "Checkbox Selection Limit"
msgstr ""

#: includes/admin/class-settings.php:521
#: includes/fields/class-checkbox.php:653
#: src/Frontend/Frontend.php:1875
msgid "You have exceeded the number of allowed selections: {#}."
msgstr ""

#: includes/admin/class-settings.php:525
msgid "Character Limit"
msgstr ""

#. translators: %1$s - characters limit, %2$s - number of characters left.
#: includes/admin/class-settings.php:528
msgid "Limit is %1$s characters. Characters remaining: %2$s."
msgstr ""

#: includes/admin/class-settings.php:535
msgid "Word Limit"
msgstr ""

#. translators: %1$s - words limit, %2$s - number of words left.
#: includes/admin/class-settings.php:538
msgid "Limit is %1$s words. Words remaining: %2$s."
msgstr ""

#: includes/admin/class-settings.php:545
msgid "Payment Required"
msgstr ""

#: includes/admin/class-settings.php:547
#: src/Forms/Fields/PaymentTotal/Field.php:386
#: src/Frontend/Frontend.php:1994
msgid "Payment is required."
msgstr ""

#: includes/admin/class-settings.php:551
#: src/Forms/Fields/CreditCard/Field.php:25
msgid "Credit Card"
msgstr ""

#: includes/admin/class-settings.php:553
#: src/Frontend/Frontend.php:1995
msgid "Please enter a valid credit card number."
msgstr ""

#: includes/admin/class-settings.php:560
#: includes/admin/class-settings.php:567
msgid "Manage integrations with popular providers such as Constant Contact, Mailchimp, Zapier, and more."
msgstr ""

#: includes/admin/class-settings.php:576
msgid "Miscellaneous"
msgstr ""

#: includes/admin/class-settings.php:583
msgid "Delete Spam Entries"
msgstr ""

#: includes/admin/class-settings.php:584
msgid "Choose the frequency spam entries are automatically deleted."
msgstr ""

#: includes/admin/class-settings.php:589
msgid "7 Days"
msgstr ""

#: includes/admin/class-settings.php:590
msgid "15 Days"
msgstr ""

#: includes/admin/class-settings.php:591
msgid "30 Days"
msgstr ""

#: includes/admin/class-settings.php:592
msgid "90 Days"
msgstr ""

#: includes/admin/class-settings.php:597
msgid "Hide Announcements"
msgstr ""

#: includes/admin/class-settings.php:598
msgid "Hide plugin announcements and update details."
msgstr ""

#: includes/admin/class-settings.php:604
msgid "Hide Admin Bar Menu"
msgstr ""

#: includes/admin/class-settings.php:605
msgid "Hide the WPForms admin bar menu."
msgstr ""

#: includes/admin/class-settings.php:611
msgid "Uninstall WPForms"
msgstr ""

#: includes/admin/class-settings.php:640
#: includes/admin/class-settings.php:644
msgid "Remove ALL WPForms data upon plugin deletion."
msgstr ""

#: includes/admin/class-settings.php:641
msgid "All forms and settings will be unrecoverable."
msgstr ""

#: includes/admin/class-settings.php:645
msgid "All forms, entries, and uploaded files will be unrecoverable."
msgstr ""

#: includes/admin/class-welcome.php:67
#: includes/admin/class-welcome.php:68
#: includes/admin/class-welcome.php:147
msgid "Welcome to WPForms"
msgstr ""

#: includes/admin/class-welcome.php:148
msgid "Thank you for choosing WPForms - the most powerful drag & drop WordPress form builder in the market."
msgstr ""

#: includes/admin/class-welcome.php:151
#: includes/admin/class-welcome.php:152
msgid "Watch how to create your first form"
msgstr ""

#: includes/admin/class-welcome.php:157
msgid "WPForms makes it easy to create forms in WordPress. You can watch the video tutorial or read our guide on how to create your first form."
msgstr ""

#: includes/admin/class-welcome.php:162
#: includes/admin/class-welcome.php:331
msgid "Create Your First Form"
msgstr ""

#: includes/admin/class-welcome.php:168
msgid "Read the Full Guide"
msgstr ""

#: includes/admin/class-welcome.php:183
msgid "WPForms Features &amp; Addons"
msgstr ""

#: includes/admin/class-welcome.php:184
msgid "WPForms is both easy to use and extremely powerful. We have tons of helpful features that allow us to give you everything you need from a form builder."
msgstr ""

#: includes/admin/class-welcome.php:190
msgid "Drag &amp; Drop Form Builder"
msgstr ""

#: includes/admin/class-welcome.php:191
msgid "Easily create an amazing form in just a few minutes without writing any code."
msgstr ""

#: includes/admin/class-welcome.php:197
msgid "Start with pre-built form templates to save even more time."
msgstr ""

#: includes/admin/class-welcome.php:202
msgid "Responsive Mobile Friendly"
msgstr ""

#: includes/admin/class-welcome.php:203
msgid "WPForms is 100% responsive meaning it works on mobile, tablets & desktop."
msgstr ""

#: includes/admin/class-welcome.php:209
msgid "Easily create high performance forms with our smart conditional logic."
msgstr ""

#: includes/admin/class-welcome.php:214
msgid "Instant Notifications"
msgstr ""

#: includes/admin/class-welcome.php:215
msgid "Respond to leads quickly with our instant form notification feature for your team."
msgstr ""

#: includes/admin/class-welcome.php:220
#: includes/admin/class-welcome.php:274
#: src/Admin/Builder/Help.php:150
msgid "Entry Management"
msgstr ""

#: includes/admin/class-welcome.php:221
msgid "View all your leads in one place to streamline your workflow."
msgstr ""

#: includes/admin/class-welcome.php:226
msgid "Payments Made Easy"
msgstr ""

#: includes/admin/class-welcome.php:227
msgid "Easily collect payments, donations, and online orders without hiring a developer."
msgstr ""

#: includes/admin/class-welcome.php:232
msgid "Marketing &amp; Subscriptions"
msgstr ""

#: includes/admin/class-welcome.php:233
msgid "Create subscription forms and connect with your email marketing service."
msgstr ""

#: includes/admin/class-welcome.php:238
msgid "Easy to Embed"
msgstr ""

#: includes/admin/class-welcome.php:239
msgid "Easily embed your forms in blog posts, pages, sidebar widgets, footer, etc."
msgstr ""

#: includes/admin/class-welcome.php:244
msgid "Spam Protection"
msgstr ""

#: includes/admin/class-welcome.php:245
msgid "Our smart captcha and spam protection automatically prevents spam submissions."
msgstr ""

#: includes/admin/class-welcome.php:253
msgid "See All Features"
msgstr ""

#: includes/admin/class-welcome.php:266
msgid "Upgrade to PRO"
msgstr ""

#: includes/admin/class-welcome.php:268
msgid "Advanced Fields"
msgstr ""

#: includes/admin/class-welcome.php:269
#: src/Logger/Log.php:102
msgid "Conditional Logic"
msgstr ""

#: includes/admin/class-welcome.php:272
msgid "Signatures"
msgstr ""

#: includes/admin/class-welcome.php:273
msgid "Form Abandonment"
msgstr ""

#: includes/admin/class-welcome.php:275
msgid "File Uploads"
msgstr ""

#: includes/admin/class-welcome.php:277
msgid "Conversational Forms"
msgstr ""

#: includes/admin/class-welcome.php:278
msgid "User Registration"
msgstr ""

#: includes/admin/class-welcome.php:287
msgid "per year"
msgstr ""

#: includes/admin/class-welcome.php:291
#: lite/templates/admin/addons.php:89
#: src/Admin/Notifications/EventDriven.php:601
#: src/Admin/Notifications/EventDriven.php:608
#: src/Admin/Notifications/EventDriven.php:615
#: src/Admin/Notifications/EventDriven.php:654
#: src/Admin/Notifications/EventDriven.php:661
#: src/Admin/Notifications/EventDriven.php:668
#: src/Admin/Notifications/EventDriven.php:721
#: src/Admin/Notifications/EventDriven.php:728
#: src/Admin/Notifications/EventDriven.php:735
#: src/Admin/Tools/Views/Importer.php:363
#: src/Lite/Admin/Pages/Addons.php:95
msgid "Upgrade Now"
msgstr ""

#: includes/admin/class-welcome.php:303
msgid "Testimonials"
msgstr ""

#: includes/admin/class-welcome.php:307
msgid "WPForms is by far the easiest form plugin to use. My clients love it – it’s one of the few plugins they can use without any training. As a developer I appreciate how fast, modern, clean and extensible it is."
msgstr ""

#: includes/admin/class-welcome.php:314
msgid "As a business owner, time is my most valuable asset. WPForms allow me to create smart online forms with just a few clicks. With their pre-built form templates and the drag & drop builder, I can create a new form that works in less than 2 minutes without writing a single line of code. Well worth the investment."
msgstr ""

#. translators: %s - ID of a setting.
#: includes/admin/settings-api.php:97
msgid "The callback function used for the %s setting is missing."
msgstr ""

#: includes/admin/settings-api.php:129
msgid "You're using WPForms Lite - no license needed. Enjoy!"
msgstr ""

#. translators: %s - WPForms.com upgrade URL.
#: includes/admin/settings-api.php:134
msgid "To unlock more features consider <strong><a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\" class=\"wpforms-upgrade-modal\">upgrading to PRO</a></strong>."
msgstr ""

#: includes/admin/settings-api.php:151
msgid "As a valued WPForms Lite user you receive <strong>50% off</strong>, automatically applied at checkout!"
msgstr ""

#: includes/admin/settings-api.php:158
msgid "Already purchased? Simply enter your license key below to enable WPForms PRO!"
msgstr ""

#: includes/admin/settings-api.php:160
msgid "Paste license key here"
msgstr ""

#: includes/admin/settings-api.php:161
msgid "Verify Key"
msgstr ""

#: includes/admin/settings-api.php:399
#: templates/builder/notifications/email-template-modal.php:31
msgid "Email Templates"
msgstr ""

#: includes/admin/settings-api.php:428
#: templates/builder/notifications/email-template-modal.php:40
msgid "Choose"
msgstr ""

#: includes/admin/settings-api.php:535
#: includes/fields/class-base.php:1528
#: includes/fields/class-base.php:1689
msgid "Upload Image"
msgstr ""

#: includes/admin/settings-api.php:672
msgid "Copy webhook URL"
msgstr ""

#: includes/class-form.php:596
#: includes/templates/class-blank.php:62
#: includes/templates/class-simple-contact-form.php:60
msgid "Sending..."
msgstr ""

#. translators: %s - form name.
#: includes/class-form.php:602
#: lite/wpforms-lite.php:248
#: src/Admin/Tools/Importers/ContactForm7.php:144
#: src/Admin/Tools/Importers/ContactForm7.php:455
#: src/Admin/Tools/Importers/NinjaForms.php:152
#: src/Admin/Tools/Importers/NinjaForms.php:441
#: src/Admin/Tools/Importers/PirateForms.php:467
#: src/Integrations/AI/Admin/Ajax/Forms.php:209
msgid "New Entry: %s"
msgstr ""

#: includes/class-form.php:613
#: includes/class-process.php:1399
#: includes/templates/class-simple-contact-form.php:73
#: lite/wpforms-lite.php:481
#: lite/wpforms-lite.php:543
#: src/Admin/Tools/Importers/ContactForm7.php:156
#: src/Admin/Tools/Importers/NinjaForms.php:164
#: src/Admin/Tools/Importers/PirateForms.php:480
msgid "Thanks for contacting us! We will be in touch with you shortly."
msgstr ""

#: includes/class-form.php:1125
msgid "Zaps Have Been Disabled"
msgstr ""

#. translators: %s - URL the to list of Zaps.
#: includes/class-form.php:1128
msgid "Head over to the Zapier settings in the Marketing tab or visit your <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">Zapier account</a> to restore them."
msgstr ""

#: includes/class-process.php:228
msgid "Invalid form."
msgstr ""

#: includes/class-process.php:253
msgid "Attempt to submit corrupted post data."
msgstr ""

#. translators: %s - error unique ID.
#: includes/class-process.php:280
msgid "Missing form data on form submission process %s"
msgstr ""

#: includes/class-process.php:281
msgid "Form data is not an array in `\\WPForms_Process::process()`. It might be caused by incorrect data returned by `wpforms_process_before_form_data` filter. Verify whether you have a custom code using this filter and debug value it is returning."
msgstr ""

#: includes/class-process.php:288
msgid "Your form has not been submitted because data is missing from the entry."
msgstr ""

#. translators: %s - URL to the WForms Logs admin page.
#: includes/class-process.php:293
msgid "Check the WPForms &raquo; Tools &raquo; <a href=\"%s\">Logs</a> for more details."
msgstr ""

#. translators: %s - error unique ID.
#: includes/class-process.php:308
msgid "Error ID: %s."
msgstr ""

#: includes/class-process.php:432
#: includes/class-process.php:597
#: includes/class-process.php:1594
msgid "Form has not been submitted, please see the errors below."
msgstr ""

#: includes/class-process.php:456
msgid "The form could not be submitted due to a security issue."
msgstr ""

#: includes/class-process.php:707
msgid "Anti-spam Honeypot V2 verification was failed, please try again later."
msgstr ""

#: includes/class-process.php:765
msgid "Direct POST requests are not allowed when the AJAX submission is enabled."
msgstr ""

#: includes/class-process.php:767
msgid "Direct POST request"
msgstr ""

#: includes/class-process.php:955
msgid "Please wait a little longer before submitting. We’re running a quick security check."
msgstr ""

#: includes/class-process.php:1024
msgid "Akismet"
msgstr ""

#. translators: %s - The CAPTCHA provider name.
#: includes/class-process.php:1087
msgid "%s verification failed, please try again later."
msgstr ""

#: includes/class-process.php:1297
msgid "Uploaded files combined size exceeds allowed maximum."
msgstr ""

#: includes/class-process.php:1460
msgid "Redirecting…"
msgstr ""

#. translators: %1$.3f - total size of the selected files in megabytes, %2$.3f - allowed file upload limit in megabytes.
#: includes/class-process.php:1596
msgid "The total size of the selected files %1$.3f MB exceeds the allowed limit %2$.3f MB."
msgstr ""

#. translators: %s - form name.
#: includes/class-process.php:1708
#: lite/wpforms-lite.php:123
msgid "New %s Entry"
msgstr ""

#: includes/class-widget.php:44
msgctxt "Widget"
msgid "Display a form."
msgstr ""

#: includes/class-widget.php:54
msgctxt "Widget"
msgid "WPForms"
msgstr ""

#: includes/class-widget.php:130
msgctxt "Widget"
msgid "Title:"
msgstr ""

#: includes/class-widget.php:136
msgctxt "Widget"
msgid "Form:"
msgstr ""

#: includes/class-widget.php:143
msgctxt "Widget"
msgid "Select your form"
msgstr ""

#: includes/class-widget.php:149
msgctxt "Widget"
msgid "No forms"
msgstr ""

#: includes/class-widget.php:157
msgctxt "Widget"
msgid "Display form name"
msgstr ""

#: includes/class-widget.php:162
msgctxt "Widget"
msgid "Display form description"
msgstr ""

#: includes/emails/class-emails.php:384
msgid "You cannot send emails with WPForms_WP_Emails() until init/admin_init has been reached."
msgstr ""

#: includes/emails/class-emails.php:606
#: src/Admin/Education/Fields.php:101
#: src/Emails/Notifications.php:955
#: src/Forms/Fields/Pagebreak/Field.php:44
#: src/Forms/Fields/Pagebreak/Field.php:472
#: src/Integrations/Gutenberg/FormSelector.php:588
msgid "Page Break"
msgstr ""

#: includes/emails/class-emails.php:610
#: src/Emails/Notifications.php:960
#: src/Forms/Fields/Html/Field.php:191
msgid "HTML / Code Block"
msgstr ""

#: includes/emails/class-emails.php:614
#: includes/integrations.php:44
#: src/Admin/Education/Fields.php:125
#: src/Emails/Notifications.php:965
#: src/Forms/Fields/Content/Field.php:27
#: src/Forms/Fields/Content/Field.php:107
#: src/Integrations/AI/API/Forms.php:290
msgid "Content"
msgstr ""

#: includes/emails/class-emails.php:643
#: includes/emails/class-emails.php:717
#: src/Emails/Notifications.php:546
#: src/Emails/Notifications.php:719
#: src/SmartTags/SmartTag/FieldHtmlId.php:38
msgid "(empty)"
msgstr ""

#. translators: %d - field ID.
#: includes/emails/class-emails.php:649
#: includes/emails/class-emails.php:722
msgid "Field ID #%s"
msgstr ""

#: includes/emails/class-emails.php:734
#: src/Emails/Notifications.php:427
msgid "An empty form was submitted."
msgstr ""

#. translators: %s - link to the site.
#: includes/emails/templates/footer-default.php:39
#: templates/emails/general-footer.php:24
msgid "Sent from %s"
msgstr ""

#. translators: %d - choice number.
#. translators: %d - Choice ID.
#: includes/fields/class-base.php:588
#: includes/fields/class-base.php:626
#: includes/fields/class-base.php:3790
#: src/Integrations/Square/Process.php:801
msgid "Choice %d"
msgstr ""

#: includes/fields/class-base.php:1244
msgid "Enter text for the form field label. Field labels are recommended and can be hidden in the Advanced Settings."
msgstr ""

#: includes/fields/class-base.php:1251
#: src/Forms/Fields/Html/Field.php:73
#: src/Integrations/Elementor/WidgetModern.php:216
#: src/Integrations/Gutenberg/FormSelector.php:570
msgid "Label"
msgstr ""

#: includes/fields/class-base.php:1283
msgid "Enter text for the form field description."
msgstr ""

#: includes/fields/class-base.php:1290
msgid "Description"
msgstr ""

#: includes/fields/class-base.php:1323
msgid "Check this option to mark the field required. A form will not submit unless all required fields are provided."
msgstr ""

#: includes/fields/class-base.php:1354
#: src/Admin/Payments/Views/Overview/Table.php:92
#: src/Admin/Payments/Views/Single.php:358
#: src/Admin/Settings/Captcha/ReCaptcha.php:58
#: src/Admin/Settings/Captcha/Turnstile.php:95
#: src/Forms/Fields/CustomCaptcha/Field.php:129
#: src/Forms/Fields/DateTime/Field.php:234
#: templates/admin/payments/single/payment-history.php:31
#: templates/admin/payments/single/payment-history.php:50
msgid "Type"
msgstr ""

#: includes/fields/class-base.php:1378
msgid "Enter code for the form field."
msgstr ""

#: includes/fields/class-base.php:1385
msgid "Code"
msgstr ""

#: includes/fields/class-base.php:1417
msgid "Choices"
msgstr ""

#: includes/fields/class-base.php:1462
#: includes/fields/class-base.php:1629
msgid "Add choices for the form field."
msgstr ""

#: includes/fields/class-base.php:1580
msgid "Dynamic Choices Active"
msgstr ""

#. translators: %1$s - source name, %2$s - type name.
#: includes/fields/class-base.php:1584
msgid "Choices are dynamically populated from the %1$s %2$s. Go to the Advanced tab to change this."
msgstr ""

#: includes/fields/class-base.php:1628
msgid "Items"
msgstr ""

#: includes/fields/class-base.php:1737
msgid "<h4>Images are not cropped or resized.</h4><p>For best results, they should be the same size and 250x250 pixels or smaller.</p>"
msgstr ""

#: includes/fields/class-base.php:1752
msgid "Use image choices"
msgstr ""

#: includes/fields/class-base.php:1753
msgid "Check this option to enable using images with the choices."
msgstr ""

#: includes/fields/class-base.php:1781
msgid "Image Choice Style"
msgstr ""

#: includes/fields/class-base.php:1782
msgid "Select the style for the image choices."
msgstr ""

#: includes/fields/class-base.php:1795
#: includes/fields/class-base.php:1952
#: includes/fields/class-select.php:323
#: src/Emails/Notifications.php:1301
#: src/Forms/Fields/Addons/LikertScale/Field.php:247
#: src/Forms/Fields/Addons/NetPromoterScore/Field.php:123
#: src/Forms/Fields/FileUpload/Field.php:263
#: src/Forms/Fields/PaymentSelect/Field.php:302
msgid "Modern"
msgstr ""

#: includes/fields/class-base.php:1796
#: includes/fields/class-base.php:1953
#: includes/fields/class-select.php:322
#: src/Emails/Notifications.php:1291
#: src/Forms/Fields/Addons/LikertScale/Field.php:248
#: src/Forms/Fields/Addons/NetPromoterScore/Field.php:124
#: src/Forms/Fields/FileUpload/Field.php:264
#: src/Forms/Fields/PaymentSelect/Field.php:301
msgid "Classic"
msgstr ""

#: includes/fields/class-base.php:1797
#: includes/fields/class-base.php:1954
#: includes/fields/class-email.php:395
#: src/Admin/Settings/Captcha/Page.php:158
#: src/Forms/Fields/FileUpload/Field.php:432
#: src/Forms/Fields/Pagebreak/Field.php:274
#: src/Integrations/Gutenberg/FormSelector.php:561
msgid "None"
msgstr ""

#: includes/fields/class-base.php:1827
msgid "Use icon choices"
msgstr ""

#: includes/fields/class-base.php:1828
msgid "Enable this option to use icons with the choices."
msgstr ""

#: includes/fields/class-base.php:1853
#: src/Forms/Fields/Rating/Field.php:234
msgid "Icon Color"
msgstr ""

#: includes/fields/class-base.php:1854
msgid "Select an accent color for the icon choices."
msgstr ""

#: includes/fields/class-base.php:1893
#: src/Forms/Fields/Rating/Field.php:199
msgid "Icon Size"
msgstr ""

#: includes/fields/class-base.php:1894
msgid "Select icon size."
msgstr ""

#: includes/fields/class-base.php:1937
msgid "Icon Choice Style"
msgstr ""

#: includes/fields/class-base.php:1938
msgid "Select the style for the icon choices."
msgstr ""

#: includes/fields/class-base.php:1981
msgid "Enter text for the default form field value."
msgstr ""

#: includes/fields/class-base.php:1987
#: includes/fields/class-name.php:317
#: includes/fields/class-name.php:343
#: includes/fields/class-name.php:369
#: includes/fields/class-name.php:395
#: src/Forms/Fields/Address/Field.php:219
#: src/Forms/Fields/Address/Field.php:274
#: src/Forms/Fields/Address/Field.php:310
#: src/Forms/Fields/Address/Field.php:345
#: src/Forms/Fields/Address/Field.php:402
#: src/Forms/Fields/Address/Field.php:458
#: src/Forms/Fields/Traits/NumberField.php:248
msgid "Default Value"
msgstr ""

#: includes/fields/class-base.php:2024
msgid "Select the default form field size."
msgstr ""

#: includes/fields/class-base.php:2026
#: src/Admin/Settings/Email.php:367
#: src/Forms/Fields/Rating/Field.php:211
#: src/Forms/IconChoices.php:141
#: src/Integrations/Elementor/WidgetModern.php:64
#: src/Integrations/Gutenberg/FormSelector.php:573
#: templates/builder/field-context-menu.php:86
msgid "Small"
msgstr ""

#: includes/fields/class-base.php:2027
#: src/Admin/Settings/Email.php:368
#: src/Forms/Fields/Password/Field.php:132
#: src/Forms/Fields/Rating/Field.php:212
#: src/Forms/IconChoices.php:137
#: src/Integrations/Elementor/WidgetModern.php:65
#: src/Integrations/Gutenberg/FormSelector.php:574
#: templates/builder/field-context-menu.php:96
msgid "Medium"
msgstr ""

#: includes/fields/class-base.php:2028
#: src/Admin/Settings/Email.php:369
#: src/Forms/Fields/Rating/Field.php:213
#: src/Forms/IconChoices.php:133
#: src/Integrations/Elementor/WidgetModern.php:66
#: src/Integrations/Gutenberg/FormSelector.php:575
#: templates/builder/field-context-menu.php:106
msgid "Large"
msgstr ""

#: includes/fields/class-base.php:2040
#: templates/builder/field-context-menu.php:76
msgid "Field Size"
msgstr ""

#: includes/fields/class-base.php:2101
msgid "Enter text for the form field placeholder."
msgstr ""

#: includes/fields/class-base.php:2108
msgid "Placeholder Text"
msgstr ""

#: includes/fields/class-base.php:2142
msgid "Enter CSS class names for the form field container. Class names should be separated with spaces."
msgstr ""

#: includes/fields/class-base.php:2154
msgid "CSS Classes"
msgstr ""

#: includes/fields/class-base.php:2187
msgid "Check this option to hide the form field label."
msgstr ""

#: includes/fields/class-base.php:2196
#: templates/builder/field-context-menu.php:66
msgid "Hide Label"
msgstr ""

#: includes/fields/class-base.php:2219
msgid "Check this option to hide the form field sublabel."
msgstr ""

#: includes/fields/class-base.php:2228
msgid "Hide Sublabels"
msgstr ""

#: includes/fields/class-base.php:2251
msgid "Select the layout for displaying field choices."
msgstr ""

#: includes/fields/class-base.php:2253
msgid "One Column"
msgstr ""

#: includes/fields/class-base.php:2254
msgid "Two Columns"
msgstr ""

#: includes/fields/class-base.php:2255
msgid "Three Columns"
msgstr ""

#: includes/fields/class-base.php:2256
msgid "Inline"
msgstr ""

#: includes/fields/class-base.php:2264
msgid "Choice Layout"
msgstr ""

#: includes/fields/class-base.php:2297
msgid "Select auto-populate method to use."
msgstr ""

#: includes/fields/class-base.php:2300
#: includes/fields/class-base.php:2350
msgid "Post Type"
msgstr ""

#: includes/fields/class-base.php:2301
#: includes/fields/class-base.php:2360
msgid "Taxonomy"
msgstr ""

#: includes/fields/class-base.php:2309
msgid "Dynamic Choices"
msgstr ""

#. translators: %s - dynamic source type name.
#: includes/fields/class-base.php:2373
msgid "Select %s to use for auto-populating field choices."
msgstr ""

#. translators: %s - dynamic source type name.
#: includes/fields/class-base.php:2376
msgid "Dynamic %s Source"
msgstr ""

#: includes/fields/class-base.php:2437
msgid "Enable quantity for this product to allow customers to purchase more than one."
msgstr ""

#: includes/fields/class-base.php:2438
msgid "Set the minimum and maximum quantity for this product."
msgstr ""

#: includes/fields/class-base.php:2444
msgid "Enable Quantity"
msgstr ""

#: includes/fields/class-base.php:2478
#: src/Forms/Fields/Traits/NumberField.php:169
msgid "Range"
msgstr ""

#: includes/fields/class-base.php:2492
#: src/Forms/Fields/Traits/NumberField.php:223
msgid "Minimum"
msgstr ""

#: includes/fields/class-base.php:2511
#: src/Forms/Fields/Traits/NumberField.php:226
msgid "Maximum"
msgstr ""

#: includes/fields/class-base.php:2626
msgid "Label Hidden"
msgstr ""

#: includes/fields/class-base.php:2627
msgid "To ensure your form is accessible, every field should have a descriptive label. If you'd like to hide the label, you can do so by enabling Hide Label in the Advanced Field Options tab."
msgstr ""

#. translators: %s - total number of choices.
#: includes/fields/class-base.php:2925
msgid "Showing the first %1$s choices.<br> All %2$s choices will be displayed when viewing the form."
msgstr ""

#: includes/fields/class-base.php:2982
msgid "No form ID found"
msgstr ""

#: includes/fields/class-base.php:2987
msgid "No field type found"
msgstr ""

#: includes/fields/class-base.php:3539
msgid "Only unique values can be added"
msgstr ""

#: includes/fields/class-base.php:3540
msgid "Only values matching specific conditions can be added"
msgstr ""

#: includes/fields/class-base.php:3725
msgid "Dynamic choices"
msgstr ""

#: includes/fields/class-base.php:3726
msgid "items"
msgstr ""

#. translators: %d - choice number.
#: includes/fields/class-base.php:3790
msgid "Item %d"
msgstr ""

#: includes/fields/class-checkbox.php:22
msgid "Checkboxes"
msgstr ""

#: includes/fields/class-checkbox.php:23
#: includes/fields/class-select.php:48
msgid "choice"
msgstr ""

#: includes/fields/class-checkbox.php:29
#: includes/fields/class-radio.php:29
#: includes/fields/class-select.php:54
#: src/Integrations/AI/Admin/Builder/Enqueues.php:270
msgid "First Choice"
msgstr ""

#: includes/fields/class-checkbox.php:37
#: includes/fields/class-radio.php:37
#: includes/fields/class-select.php:59
#: src/Integrations/AI/Admin/Builder/Enqueues.php:271
msgid "Second Choice"
msgstr ""

#: includes/fields/class-checkbox.php:45
#: includes/fields/class-radio.php:45
#: includes/fields/class-select.php:64
#: src/Integrations/AI/Admin/Builder/Enqueues.php:272
msgid "Third Choice"
msgstr ""

#: includes/fields/class-checkbox.php:131
#: src/Forms/Fields/PaymentCheckbox/Field.php:116
msgid "Checked"
msgstr ""

#: includes/fields/class-checkbox.php:245
#: includes/fields/class-radio.php:218
#: includes/fields/class-select.php:211
#: src/Integrations/AI/Admin/Builder/Enqueues.php:206
msgid "Generate Choices"
msgstr ""

#: includes/fields/class-checkbox.php:308
#: includes/fields/class-radio.php:281
msgid "Randomize Choices"
msgstr ""

#: includes/fields/class-checkbox.php:309
#: includes/fields/class-radio.php:282
msgid "Check this option to randomize the order of the choices."
msgstr ""

#: includes/fields/class-checkbox.php:330
#: includes/fields/class-radio.php:303
#: includes/fields/class-select.php:253
msgid "Show Values"
msgstr ""

#: includes/fields/class-checkbox.php:331
#: includes/fields/class-radio.php:304
#: includes/fields/class-select.php:254
msgid "Check this option to manually set form field values."
msgstr ""

#: includes/fields/class-checkbox.php:355
msgid "Choice Limit"
msgstr ""

#: includes/fields/class-checkbox.php:356
msgid "Limit the number of checkboxes a user can select. Leave empty for unlimited."
msgstr ""

#: includes/fields/class-checkbox.php:396
msgid "Enable Disclaimer / Terms of Service Display"
msgstr ""

#: includes/fields/class-checkbox.php:397
msgid "Check this option to adjust the field styling to support Disclaimers and Terms of Service type agreements."
msgstr ""

#: includes/fields/class-email.php:51
#: src/Forms/Fields/Password/Field.php:26
msgid "user"
msgstr ""

#: includes/fields/class-email.php:168
#: includes/fields/class-email.php:490
msgid "Confirm Email"
msgstr ""

#: includes/fields/class-email.php:308
msgid "Enable Email Confirmation"
msgstr ""

#: includes/fields/class-email.php:309
msgid "Check this option to ask users to provide an email address twice."
msgstr ""

#: includes/fields/class-email.php:351
#: src/Forms/Fields/Password/Field.php:178
msgid "Confirmation Placeholder Text"
msgstr ""

#: includes/fields/class-email.php:352
#: src/Forms/Fields/Password/Field.php:179
msgid "Enter text for the confirmation field placeholder."
msgstr ""

#: includes/fields/class-email.php:382
msgid "Allowlist / Denylist"
msgstr ""

#: includes/fields/class-email.php:383
msgid "Restrict which email addresses are allowed. Be sure to separate each email address with a comma."
msgstr ""

#: includes/fields/class-email.php:396
msgid "Allowlist"
msgstr ""

#: includes/fields/class-email.php:397
msgid "Denylist"
msgstr ""

#: includes/fields/class-email.php:580
#: includes/fields/class-email.php:630
#: includes/fields/class-email.php:640
#: includes/fields/class-email.php:1088
msgid "The provided email is not valid."
msgstr ""

#: includes/fields/class-email.php:646
msgid "The provided emails do not match."
msgstr ""

#: includes/fields/class-email.php:1080
msgid "We’ve detected the same text in your allowlist and denylist. To prevent a conflict, we’ve removed the following text from the list you’re currently viewing:"
msgstr ""

#: includes/fields/class-email.php:1084
msgid "At least one of the emails in your list contained an error and has been removed."
msgstr ""

#: includes/fields/class-gdpr-checkbox.php:22
msgid "GDPR Agreement"
msgstr ""

#: includes/fields/class-gdpr-checkbox.php:28
msgid "I consent to having this website store my submitted information so they can respond to my inquiry."
msgstr ""

#: includes/fields/class-gdpr-checkbox.php:190
msgid "Agreement"
msgstr ""

#: includes/fields/class-internal-information.php:30
msgid "Internal Information"
msgstr ""

#: includes/fields/class-internal-information.php:30
msgid "This field is not editable"
msgstr ""

#: includes/fields/class-internal-information.php:198
msgid "Heading"
msgstr ""

#: includes/fields/class-internal-information.php:199
msgid "Enter text for the form field heading."
msgstr ""

#: includes/fields/class-internal-information.php:238
msgid "Expanded Content"
msgstr ""

#: includes/fields/class-internal-information.php:239
msgid "Enter text for the form field expanded description."
msgstr ""

#: includes/fields/class-internal-information.php:256
msgid "Adds an expandable content area below the description."
msgstr ""

#: includes/fields/class-internal-information.php:283
msgid "CTA Label"
msgstr ""

#: includes/fields/class-internal-information.php:284
msgid "Enter label for the form field call to action button. The label will be ignored if the field has extended description content: in that case button will be used to expand the description content."
msgstr ""

#: includes/fields/class-internal-information.php:323
msgid "CTA Link"
msgstr ""

#: includes/fields/class-internal-information.php:324
msgid "Enter the URL for the form field call to action button. URL will be ignored if the field has extended description content: in that case button will be used to expand the description content."
msgstr ""

#: includes/fields/class-internal-information.php:341
msgid "CTA is hidden if Expanded Content is used."
msgstr ""

#: includes/fields/class-internal-information.php:674
msgid "You should enter a valid absolute address to the CTA Link field or leave it empty."
msgstr ""

#: includes/fields/class-internal-information.php:675
#: src/Lite/Admin/DashboardWidget.php:446
#: templates/admin/dashboard/widget/welcome.php:30
msgid "Dismiss"
msgstr ""

#: includes/fields/class-internal-information.php:816
msgid "This field is disabled in the editor mode."
msgstr ""

#: includes/fields/class-name.php:22
#: includes/fields/class-name.php:306
#: includes/templates/class-simple-contact-form.php:36
#: src/Admin/Forms/Table/Facades/Columns.php:66
#: src/Emails/Preview.php:401
msgid "Name"
msgstr ""

#: includes/fields/class-name.php:23
msgid "user, first, last"
msgstr ""

#: includes/fields/class-name.php:239
#: src/Forms/Fields/DateTime/Field.php:119
#: src/Forms/Fields/DateTime/Field.php:280
#: src/Forms/Fields/DateTime/Field.php:382
#: src/Forms/Fields/Phone/Field.php:87
msgid "Format"
msgstr ""

#: includes/fields/class-name.php:240
msgid "Select format to use for the name form field"
msgstr ""

#: includes/fields/class-name.php:252
msgid "Simple"
msgstr ""

#: includes/fields/class-name.php:253
msgid "First Last"
msgstr ""

#: includes/fields/class-name.php:254
msgid "First Middle Last"
msgstr ""

#: includes/fields/class-name.php:307
msgid "Name field advanced options."
msgstr ""

#: includes/fields/class-name.php:313
#: includes/fields/class-name.php:339
#: includes/fields/class-name.php:365
#: includes/fields/class-name.php:391
#: src/Forms/Fields/Address/Field.php:215
#: src/Forms/Fields/Address/Field.php:270
#: src/Forms/Fields/Address/Field.php:306
#: src/Forms/Fields/Address/Field.php:341
#: src/Forms/Fields/Address/Field.php:398
#: src/Forms/Fields/Address/Field.php:454
#: src/Forms/Fields/DateTime/Field.php:247
#: src/Forms/Fields/DateTime/Field.php:359
msgid "Placeholder"
msgstr ""

#: includes/fields/class-name.php:332
#: src/Integrations/ConstantContact/V3/ConstantContact.php:156
msgid "First Name"
msgstr ""

#: includes/fields/class-name.php:333
msgid "First name field advanced options."
msgstr ""

#: includes/fields/class-name.php:358
msgid "Middle Name"
msgstr ""

#: includes/fields/class-name.php:359
msgid "Middle name field advanced options."
msgstr ""

#: includes/fields/class-name.php:384
#: src/Integrations/ConstantContact/V3/ConstantContact.php:157
msgid "Last Name"
msgstr ""

#: includes/fields/class-name.php:385
msgid "Last name field advanced options."
msgstr ""

#: includes/fields/class-number-slider.php:47
msgid "Number Slider"
msgstr ""

#: includes/fields/class-number-slider.php:70
msgid "Increment value should be greater than zero. Decimal fractions allowed."
msgstr ""

#. translators: %1$s - Number slider selected value, %2$s - its minimum value, %3$s - its maximum value.
#: includes/fields/class-number-slider.php:102
msgid "%1$s (%2$s min / %3$s max)"
msgstr ""

#: includes/fields/class-number-slider.php:152
msgid "Value Range"
msgstr ""

#: includes/fields/class-number-slider.php:153
msgid "Define the minimum and the maximum values for the slider."
msgstr ""

#: includes/fields/class-number-slider.php:166
msgid "Determines the increment between selectable values on the slider."
msgstr ""

#: includes/fields/class-number-slider.php:209
msgid "Value Display"
msgstr ""

#: includes/fields/class-number-slider.php:210
msgid "Displays the currently selected value below the slider."
msgstr ""

#. translators: %s - value.
#: includes/fields/class-number-slider.php:259
msgid "Selected Value: %s"
msgstr ""

#: includes/fields/class-number-slider.php:311
msgid "Selected Value: {value}"
msgstr ""

#: includes/fields/class-number-slider.php:368
msgid "Please provide a valid value."
msgstr ""

#: includes/fields/class-number.php:26
msgid "Numbers"
msgstr ""

#: includes/fields/class-radio.php:22
msgid "Multiple Choice"
msgstr ""

#: includes/fields/class-radio.php:23
msgid "radio"
msgstr ""

#: includes/fields/class-select.php:47
msgid "Dropdown"
msgstr ""

#: includes/fields/class-select.php:275
msgid "Multiple Options Selection"
msgstr ""

#: includes/fields/class-select.php:276
msgid "Allow users to select multiple choices in this field."
msgstr ""

#. translators: %s - URL to WPForms.com doc article.
#: includes/fields/class-select.php:279
msgid "For details, including how this looks and works for your site's visitors, please check out <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">our doc</a>."
msgstr ""

#: includes/fields/class-select.php:309
#: src/Forms/Fields/Addons/LikertScale/Field.php:235
#: src/Forms/Fields/Addons/NetPromoterScore/Field.php:111
#: src/Forms/Fields/EntryPreview/Field.php:142
#: src/Forms/Fields/FileUpload/Field.php:250
#: src/Forms/Fields/PaymentSelect/Field.php:288
msgid "Style"
msgstr ""

#: includes/fields/class-select.php:310
#: src/Forms/Fields/PaymentSelect/Field.php:289
msgid "Classic style is the default one generated by your browser. Modern has a fresh look and displays all selected options in a single row."
msgstr ""

#: includes/fields/class-text.php:22
msgid "Single Line Text"
msgstr ""

#: includes/fields/class-text.php:287
#: includes/fields/class-textarea.php:125
msgid "Limit Length"
msgstr ""

#: includes/fields/class-text.php:288
#: includes/fields/class-textarea.php:126
msgid "Check this option to limit text length by characters or words count."
msgstr ""

#: includes/fields/class-text.php:319
#: includes/fields/class-textarea.php:157
msgid "Characters"
msgstr ""

#: includes/fields/class-text.php:320
#: includes/fields/class-textarea.php:158
msgid "Words"
msgstr ""

#: includes/fields/class-text.php:343
msgid "Input Mask"
msgstr ""

#: includes/fields/class-text.php:344
msgid "Enter your custom input mask."
msgstr ""

#: includes/fields/class-text.php:345
msgid "See Examples & Docs"
msgstr ""

#. translators: %s - limit characters number.
#: includes/fields/class-text.php:543
#: includes/fields/class-textarea.php:365
msgid "Text can't exceed %d character."
msgid_plural "Text can't exceed %d characters."
msgstr[0] ""
msgstr[1] ""

#. translators: %s - limit words number.
#: includes/fields/class-text.php:550
#: includes/fields/class-textarea.php:372
msgid "Text can't exceed %d word."
msgid_plural "Text can't exceed %d words."
msgstr[0] ""
msgstr[1] ""

#: includes/fields/class-textarea.php:22
msgid "Paragraph Text"
msgstr ""

#: includes/fields/class-textarea.php:23
msgid "textarea"
msgstr ""

#: includes/functions/data-presets.php:18
msgid "Alabama"
msgstr ""

#: includes/functions/data-presets.php:19
msgid "Alaska"
msgstr ""

#: includes/functions/data-presets.php:20
msgid "Arizona"
msgstr ""

#: includes/functions/data-presets.php:21
msgid "Arkansas"
msgstr ""

#: includes/functions/data-presets.php:22
msgid "California"
msgstr ""

#: includes/functions/data-presets.php:23
msgid "Colorado"
msgstr ""

#: includes/functions/data-presets.php:24
msgid "Connecticut"
msgstr ""

#: includes/functions/data-presets.php:25
msgid "Delaware"
msgstr ""

#: includes/functions/data-presets.php:26
msgid "District of Columbia"
msgstr ""

#: includes/functions/data-presets.php:27
msgid "Florida"
msgstr ""

#: includes/functions/data-presets.php:28
msgctxt "US State"
msgid "Georgia"
msgstr ""

#: includes/functions/data-presets.php:29
msgid "Hawaii"
msgstr ""

#: includes/functions/data-presets.php:30
msgid "Idaho"
msgstr ""

#: includes/functions/data-presets.php:31
msgid "Illinois"
msgstr ""

#: includes/functions/data-presets.php:32
msgid "Indiana"
msgstr ""

#: includes/functions/data-presets.php:33
msgid "Iowa"
msgstr ""

#: includes/functions/data-presets.php:34
msgid "Kansas"
msgstr ""

#: includes/functions/data-presets.php:35
msgid "Kentucky"
msgstr ""

#: includes/functions/data-presets.php:36
msgid "Louisiana"
msgstr ""

#: includes/functions/data-presets.php:37
msgid "Maine"
msgstr ""

#: includes/functions/data-presets.php:38
msgid "Maryland"
msgstr ""

#: includes/functions/data-presets.php:39
msgid "Massachusetts"
msgstr ""

#: includes/functions/data-presets.php:40
msgid "Michigan"
msgstr ""

#: includes/functions/data-presets.php:41
msgid "Minnesota"
msgstr ""

#: includes/functions/data-presets.php:42
msgid "Mississippi"
msgstr ""

#: includes/functions/data-presets.php:43
msgid "Missouri"
msgstr ""

#: includes/functions/data-presets.php:44
msgid "Montana"
msgstr ""

#: includes/functions/data-presets.php:45
msgid "Nebraska"
msgstr ""

#: includes/functions/data-presets.php:46
msgid "Nevada"
msgstr ""

#: includes/functions/data-presets.php:47
msgid "New Hampshire"
msgstr ""

#: includes/functions/data-presets.php:48
msgid "New Jersey"
msgstr ""

#: includes/functions/data-presets.php:49
msgid "New Mexico"
msgstr ""

#: includes/functions/data-presets.php:50
msgid "New York"
msgstr ""

#: includes/functions/data-presets.php:51
msgid "North Carolina"
msgstr ""

#: includes/functions/data-presets.php:52
msgid "North Dakota"
msgstr ""

#: includes/functions/data-presets.php:53
msgid "Ohio"
msgstr ""

#: includes/functions/data-presets.php:54
msgid "Oklahoma"
msgstr ""

#: includes/functions/data-presets.php:55
msgid "Oregon"
msgstr ""

#: includes/functions/data-presets.php:56
msgid "Pennsylvania"
msgstr ""

#: includes/functions/data-presets.php:57
msgid "Rhode Island"
msgstr ""

#: includes/functions/data-presets.php:58
msgid "South Carolina"
msgstr ""

#: includes/functions/data-presets.php:59
msgid "South Dakota"
msgstr ""

#: includes/functions/data-presets.php:60
msgid "Tennessee"
msgstr ""

#: includes/functions/data-presets.php:61
msgid "Texas"
msgstr ""

#: includes/functions/data-presets.php:62
msgid "Utah"
msgstr ""

#: includes/functions/data-presets.php:63
msgid "Vermont"
msgstr ""

#: includes/functions/data-presets.php:64
msgid "Virginia"
msgstr ""

#: includes/functions/data-presets.php:65
msgid "Washington"
msgstr ""

#: includes/functions/data-presets.php:66
msgid "West Virginia"
msgstr ""

#: includes/functions/data-presets.php:67
msgid "Wisconsin"
msgstr ""

#: includes/functions/data-presets.php:68
msgid "Wyoming"
msgstr ""

#: includes/functions/data-presets.php:84
msgid "Afghanistan"
msgstr ""

#: includes/functions/data-presets.php:85
msgid "Åland Islands"
msgstr ""

#: includes/functions/data-presets.php:86
msgid "Albania"
msgstr ""

#: includes/functions/data-presets.php:87
msgid "Algeria"
msgstr ""

#: includes/functions/data-presets.php:88
msgid "American Samoa"
msgstr ""

#: includes/functions/data-presets.php:89
msgid "Andorra"
msgstr ""

#: includes/functions/data-presets.php:90
msgid "Angola"
msgstr ""

#: includes/functions/data-presets.php:91
msgid "Anguilla"
msgstr ""

#: includes/functions/data-presets.php:92
msgid "Antarctica"
msgstr ""

#: includes/functions/data-presets.php:93
msgid "Antigua and Barbuda"
msgstr ""

#: includes/functions/data-presets.php:94
msgid "Argentina"
msgstr ""

#: includes/functions/data-presets.php:95
msgid "Armenia"
msgstr ""

#: includes/functions/data-presets.php:96
msgid "Aruba"
msgstr ""

#: includes/functions/data-presets.php:97
msgid "Australia"
msgstr ""

#: includes/functions/data-presets.php:98
msgid "Austria"
msgstr ""

#: includes/functions/data-presets.php:99
msgid "Azerbaijan"
msgstr ""

#: includes/functions/data-presets.php:100
msgid "Bahamas"
msgstr ""

#: includes/functions/data-presets.php:101
msgid "Bahrain"
msgstr ""

#: includes/functions/data-presets.php:102
msgid "Bangladesh"
msgstr ""

#: includes/functions/data-presets.php:103
msgid "Barbados"
msgstr ""

#: includes/functions/data-presets.php:104
msgid "Belarus"
msgstr ""

#: includes/functions/data-presets.php:105
msgid "Belgium"
msgstr ""

#: includes/functions/data-presets.php:106
msgid "Belize"
msgstr ""

#: includes/functions/data-presets.php:107
msgid "Benin"
msgstr ""

#: includes/functions/data-presets.php:108
msgid "Bermuda"
msgstr ""

#: includes/functions/data-presets.php:109
msgid "Bhutan"
msgstr ""

#: includes/functions/data-presets.php:110
msgid "Bolivia (Plurinational State of)"
msgstr ""

#: includes/functions/data-presets.php:111
msgid "Bonaire, Saint Eustatius and Saba"
msgstr ""

#: includes/functions/data-presets.php:112
msgid "Bosnia and Herzegovina"
msgstr ""

#: includes/functions/data-presets.php:113
msgid "Botswana"
msgstr ""

#: includes/functions/data-presets.php:114
msgid "Bouvet Island"
msgstr ""

#: includes/functions/data-presets.php:115
msgid "Brazil"
msgstr ""

#: includes/functions/data-presets.php:116
msgid "British Indian Ocean Territory"
msgstr ""

#: includes/functions/data-presets.php:117
msgid "Brunei Darussalam"
msgstr ""

#: includes/functions/data-presets.php:118
msgid "Bulgaria"
msgstr ""

#: includes/functions/data-presets.php:119
msgid "Burkina Faso"
msgstr ""

#: includes/functions/data-presets.php:120
msgid "Burundi"
msgstr ""

#: includes/functions/data-presets.php:121
msgid "Cabo Verde"
msgstr ""

#: includes/functions/data-presets.php:122
msgid "Cambodia"
msgstr ""

#: includes/functions/data-presets.php:123
msgid "Cameroon"
msgstr ""

#: includes/functions/data-presets.php:124
msgid "Canada"
msgstr ""

#: includes/functions/data-presets.php:125
msgid "Cayman Islands"
msgstr ""

#: includes/functions/data-presets.php:126
msgid "Central African Republic"
msgstr ""

#: includes/functions/data-presets.php:127
msgid "Chad"
msgstr ""

#: includes/functions/data-presets.php:128
msgid "Chile"
msgstr ""

#: includes/functions/data-presets.php:129
msgid "China"
msgstr ""

#: includes/functions/data-presets.php:130
msgid "Christmas Island"
msgstr ""

#: includes/functions/data-presets.php:131
msgid "Cocos (Keeling) Islands"
msgstr ""

#: includes/functions/data-presets.php:132
msgid "Colombia"
msgstr ""

#: includes/functions/data-presets.php:133
msgid "Comoros"
msgstr ""

#: includes/functions/data-presets.php:134
msgid "Congo"
msgstr ""

#: includes/functions/data-presets.php:135
msgid "Congo (Democratic Republic of the)"
msgstr ""

#: includes/functions/data-presets.php:136
msgid "Cook Islands"
msgstr ""

#: includes/functions/data-presets.php:137
msgid "Costa Rica"
msgstr ""

#: includes/functions/data-presets.php:138
msgid "Côte d'Ivoire"
msgstr ""

#: includes/functions/data-presets.php:139
msgid "Croatia"
msgstr ""

#: includes/functions/data-presets.php:140
msgid "Cuba"
msgstr ""

#: includes/functions/data-presets.php:141
msgid "Curaçao"
msgstr ""

#: includes/functions/data-presets.php:142
msgid "Cyprus"
msgstr ""

#: includes/functions/data-presets.php:143
msgid "Czech Republic"
msgstr ""

#: includes/functions/data-presets.php:144
msgid "Denmark"
msgstr ""

#: includes/functions/data-presets.php:145
msgid "Djibouti"
msgstr ""

#: includes/functions/data-presets.php:146
msgid "Dominica"
msgstr ""

#: includes/functions/data-presets.php:147
msgid "Dominican Republic"
msgstr ""

#: includes/functions/data-presets.php:148
msgid "Ecuador"
msgstr ""

#: includes/functions/data-presets.php:149
msgid "Egypt"
msgstr ""

#: includes/functions/data-presets.php:150
msgid "El Salvador"
msgstr ""

#: includes/functions/data-presets.php:151
msgid "Equatorial Guinea"
msgstr ""

#: includes/functions/data-presets.php:152
msgid "Eritrea"
msgstr ""

#: includes/functions/data-presets.php:153
msgid "Estonia"
msgstr ""

#: includes/functions/data-presets.php:154
msgid "Ethiopia"
msgstr ""

#: includes/functions/data-presets.php:155
msgid "Falkland Islands (Malvinas)"
msgstr ""

#: includes/functions/data-presets.php:156
msgid "Faroe Islands"
msgstr ""

#: includes/functions/data-presets.php:157
msgid "Fiji"
msgstr ""

#: includes/functions/data-presets.php:158
msgid "Finland"
msgstr ""

#: includes/functions/data-presets.php:159
msgid "France"
msgstr ""

#: includes/functions/data-presets.php:160
msgid "French Guiana"
msgstr ""

#: includes/functions/data-presets.php:161
msgid "French Polynesia"
msgstr ""

#: includes/functions/data-presets.php:162
msgid "French Southern Territories"
msgstr ""

#: includes/functions/data-presets.php:163
msgid "Gabon"
msgstr ""

#: includes/functions/data-presets.php:164
msgid "Gambia"
msgstr ""

#: includes/functions/data-presets.php:165
msgctxt "Country"
msgid "Georgia"
msgstr ""

#: includes/functions/data-presets.php:166
msgid "Germany"
msgstr ""

#: includes/functions/data-presets.php:167
msgid "Ghana"
msgstr ""

#: includes/functions/data-presets.php:168
msgid "Gibraltar"
msgstr ""

#: includes/functions/data-presets.php:169
msgid "Greece"
msgstr ""

#: includes/functions/data-presets.php:170
msgid "Greenland"
msgstr ""

#: includes/functions/data-presets.php:171
msgid "Grenada"
msgstr ""

#: includes/functions/data-presets.php:172
msgid "Guadeloupe"
msgstr ""

#: includes/functions/data-presets.php:173
msgid "Guam"
msgstr ""

#: includes/functions/data-presets.php:174
msgid "Guatemala"
msgstr ""

#: includes/functions/data-presets.php:175
msgid "Guernsey"
msgstr ""

#: includes/functions/data-presets.php:176
msgid "Guinea"
msgstr ""

#: includes/functions/data-presets.php:177
msgid "Guinea-Bissau"
msgstr ""

#: includes/functions/data-presets.php:178
msgid "Guyana"
msgstr ""

#: includes/functions/data-presets.php:179
msgid "Haiti"
msgstr ""

#: includes/functions/data-presets.php:180
msgid "Heard Island and McDonald Islands"
msgstr ""

#: includes/functions/data-presets.php:181
msgid "Honduras"
msgstr ""

#: includes/functions/data-presets.php:182
msgid "Hong Kong"
msgstr ""

#: includes/functions/data-presets.php:183
msgid "Hungary"
msgstr ""

#: includes/functions/data-presets.php:184
msgid "Iceland"
msgstr ""

#: includes/functions/data-presets.php:185
msgid "India"
msgstr ""

#: includes/functions/data-presets.php:186
msgid "Indonesia"
msgstr ""

#: includes/functions/data-presets.php:187
msgid "Iran (Islamic Republic of)"
msgstr ""

#: includes/functions/data-presets.php:188
msgid "Iraq"
msgstr ""

#: includes/functions/data-presets.php:189
msgid "Ireland (Republic of)"
msgstr ""

#: includes/functions/data-presets.php:190
msgid "Isle of Man"
msgstr ""

#: includes/functions/data-presets.php:191
msgid "Israel"
msgstr ""

#: includes/functions/data-presets.php:192
msgid "Italy"
msgstr ""

#: includes/functions/data-presets.php:193
msgid "Jamaica"
msgstr ""

#: includes/functions/data-presets.php:194
msgid "Japan"
msgstr ""

#: includes/functions/data-presets.php:195
msgid "Jersey"
msgstr ""

#: includes/functions/data-presets.php:196
msgid "Jordan"
msgstr ""

#: includes/functions/data-presets.php:197
msgid "Kazakhstan"
msgstr ""

#: includes/functions/data-presets.php:198
msgid "Kenya"
msgstr ""

#: includes/functions/data-presets.php:199
msgid "Kiribati"
msgstr ""

#: includes/functions/data-presets.php:200
msgid "Korea (Democratic People's Republic of)"
msgstr ""

#: includes/functions/data-presets.php:201
msgid "Korea (Republic of)"
msgstr ""

#: includes/functions/data-presets.php:202
msgid "Kosovo"
msgstr ""

#: includes/functions/data-presets.php:203
msgid "Kuwait"
msgstr ""

#: includes/functions/data-presets.php:204
msgid "Kyrgyzstan"
msgstr ""

#: includes/functions/data-presets.php:205
msgid "Lao People's Democratic Republic"
msgstr ""

#: includes/functions/data-presets.php:206
msgid "Latvia"
msgstr ""

#: includes/functions/data-presets.php:207
msgid "Lebanon"
msgstr ""

#: includes/functions/data-presets.php:208
msgid "Lesotho"
msgstr ""

#: includes/functions/data-presets.php:209
msgid "Liberia"
msgstr ""

#: includes/functions/data-presets.php:210
msgid "Libya"
msgstr ""

#: includes/functions/data-presets.php:211
msgid "Liechtenstein"
msgstr ""

#: includes/functions/data-presets.php:212
msgid "Lithuania"
msgstr ""

#: includes/functions/data-presets.php:213
msgid "Luxembourg"
msgstr ""

#: includes/functions/data-presets.php:214
msgid "Macao"
msgstr ""

#: includes/functions/data-presets.php:215
msgid "North Macedonia (Republic of)"
msgstr ""

#: includes/functions/data-presets.php:216
msgid "Madagascar"
msgstr ""

#: includes/functions/data-presets.php:217
msgid "Malawi"
msgstr ""

#: includes/functions/data-presets.php:218
msgid "Malaysia"
msgstr ""

#: includes/functions/data-presets.php:219
msgid "Maldives"
msgstr ""

#: includes/functions/data-presets.php:220
msgid "Mali"
msgstr ""

#: includes/functions/data-presets.php:221
msgid "Malta"
msgstr ""

#: includes/functions/data-presets.php:222
msgid "Marshall Islands"
msgstr ""

#: includes/functions/data-presets.php:223
msgid "Martinique"
msgstr ""

#: includes/functions/data-presets.php:224
msgid "Mauritania"
msgstr ""

#: includes/functions/data-presets.php:225
msgid "Mauritius"
msgstr ""

#: includes/functions/data-presets.php:226
msgid "Mayotte"
msgstr ""

#: includes/functions/data-presets.php:227
msgid "Mexico"
msgstr ""

#: includes/functions/data-presets.php:228
msgid "Micronesia (Federated States of)"
msgstr ""

#: includes/functions/data-presets.php:229
msgid "Moldova (Republic of)"
msgstr ""

#: includes/functions/data-presets.php:230
msgid "Monaco"
msgstr ""

#: includes/functions/data-presets.php:231
msgid "Mongolia"
msgstr ""

#: includes/functions/data-presets.php:232
msgid "Montenegro"
msgstr ""

#: includes/functions/data-presets.php:233
msgid "Montserrat"
msgstr ""

#: includes/functions/data-presets.php:234
msgid "Morocco"
msgstr ""

#: includes/functions/data-presets.php:235
msgid "Mozambique"
msgstr ""

#: includes/functions/data-presets.php:236
msgid "Myanmar"
msgstr ""

#: includes/functions/data-presets.php:237
msgid "Namibia"
msgstr ""

#: includes/functions/data-presets.php:238
msgid "Nauru"
msgstr ""

#: includes/functions/data-presets.php:239
msgid "Nepal"
msgstr ""

#: includes/functions/data-presets.php:240
msgid "Netherlands"
msgstr ""

#: includes/functions/data-presets.php:241
msgid "New Caledonia"
msgstr ""

#: includes/functions/data-presets.php:242
msgid "New Zealand"
msgstr ""

#: includes/functions/data-presets.php:243
msgid "Nicaragua"
msgstr ""

#: includes/functions/data-presets.php:244
msgid "Niger"
msgstr ""

#: includes/functions/data-presets.php:245
msgid "Nigeria"
msgstr ""

#: includes/functions/data-presets.php:246
msgid "Niue"
msgstr ""

#: includes/functions/data-presets.php:247
msgid "Norfolk Island"
msgstr ""

#: includes/functions/data-presets.php:248
msgid "Northern Mariana Islands"
msgstr ""

#: includes/functions/data-presets.php:249
msgid "Norway"
msgstr ""

#: includes/functions/data-presets.php:250
msgid "Oman"
msgstr ""

#: includes/functions/data-presets.php:251
msgid "Pakistan"
msgstr ""

#: includes/functions/data-presets.php:252
msgid "Palau"
msgstr ""

#: includes/functions/data-presets.php:253
msgid "Palestine (State of)"
msgstr ""

#: includes/functions/data-presets.php:254
msgid "Panama"
msgstr ""

#: includes/functions/data-presets.php:255
msgid "Papua New Guinea"
msgstr ""

#: includes/functions/data-presets.php:256
msgid "Paraguay"
msgstr ""

#: includes/functions/data-presets.php:257
msgid "Peru"
msgstr ""

#: includes/functions/data-presets.php:258
msgid "Philippines"
msgstr ""

#: includes/functions/data-presets.php:259
msgid "Pitcairn"
msgstr ""

#: includes/functions/data-presets.php:260
msgid "Poland"
msgstr ""

#: includes/functions/data-presets.php:261
msgid "Portugal"
msgstr ""

#: includes/functions/data-presets.php:262
msgid "Puerto Rico"
msgstr ""

#: includes/functions/data-presets.php:263
msgid "Qatar"
msgstr ""

#: includes/functions/data-presets.php:264
msgid "Réunion"
msgstr ""

#: includes/functions/data-presets.php:265
msgid "Romania"
msgstr ""

#: includes/functions/data-presets.php:266
msgid "Russian Federation"
msgstr ""

#: includes/functions/data-presets.php:267
msgid "Rwanda"
msgstr ""

#: includes/functions/data-presets.php:268
msgid "Saint Barthélemy"
msgstr ""

#: includes/functions/data-presets.php:269
msgid "Saint Helena, Ascension and Tristan da Cunha"
msgstr ""

#: includes/functions/data-presets.php:270
msgid "Saint Kitts and Nevis"
msgstr ""

#: includes/functions/data-presets.php:271
msgid "Saint Lucia"
msgstr ""

#: includes/functions/data-presets.php:272
msgid "Saint Martin (French part)"
msgstr ""

#: includes/functions/data-presets.php:273
msgid "Saint Pierre and Miquelon"
msgstr ""

#: includes/functions/data-presets.php:274
msgid "Saint Vincent and the Grenadines"
msgstr ""

#: includes/functions/data-presets.php:275
msgid "Samoa"
msgstr ""

#: includes/functions/data-presets.php:276
msgid "San Marino"
msgstr ""

#: includes/functions/data-presets.php:277
msgid "Sao Tome and Principe"
msgstr ""

#: includes/functions/data-presets.php:278
msgid "Saudi Arabia"
msgstr ""

#: includes/functions/data-presets.php:279
msgid "Senegal"
msgstr ""

#: includes/functions/data-presets.php:280
msgid "Serbia"
msgstr ""

#: includes/functions/data-presets.php:281
msgid "Seychelles"
msgstr ""

#: includes/functions/data-presets.php:282
msgid "Sierra Leone"
msgstr ""

#: includes/functions/data-presets.php:283
msgid "Singapore"
msgstr ""

#: includes/functions/data-presets.php:284
msgid "Sint Maarten (Dutch part)"
msgstr ""

#: includes/functions/data-presets.php:285
msgid "Slovakia"
msgstr ""

#: includes/functions/data-presets.php:286
msgid "Slovenia"
msgstr ""

#: includes/functions/data-presets.php:287
msgid "Solomon Islands"
msgstr ""

#: includes/functions/data-presets.php:288
msgid "Somalia"
msgstr ""

#: includes/functions/data-presets.php:289
msgid "South Africa"
msgstr ""

#: includes/functions/data-presets.php:290
msgid "South Georgia and the South Sandwich Islands"
msgstr ""

#: includes/functions/data-presets.php:291
msgid "South Sudan"
msgstr ""

#: includes/functions/data-presets.php:292
msgid "Spain"
msgstr ""

#: includes/functions/data-presets.php:293
msgid "Sri Lanka"
msgstr ""

#: includes/functions/data-presets.php:294
msgid "Sudan"
msgstr ""

#: includes/functions/data-presets.php:295
msgid "Suriname"
msgstr ""

#: includes/functions/data-presets.php:296
msgid "Svalbard and Jan Mayen"
msgstr ""

#: includes/functions/data-presets.php:297
msgid "Eswatini (Kingdom of)"
msgstr ""

#: includes/functions/data-presets.php:298
msgid "Sweden"
msgstr ""

#: includes/functions/data-presets.php:299
msgid "Switzerland"
msgstr ""

#: includes/functions/data-presets.php:300
msgid "Syrian Arab Republic"
msgstr ""

#: includes/functions/data-presets.php:301
msgid "Taiwan, Republic of China"
msgstr ""

#: includes/functions/data-presets.php:302
msgid "Tajikistan"
msgstr ""

#: includes/functions/data-presets.php:303
msgid "Tanzania (United Republic of)"
msgstr ""

#: includes/functions/data-presets.php:304
msgid "Thailand"
msgstr ""

#: includes/functions/data-presets.php:305
msgid "Timor-Leste"
msgstr ""

#: includes/functions/data-presets.php:306
msgid "Togo"
msgstr ""

#: includes/functions/data-presets.php:307
msgid "Tokelau"
msgstr ""

#: includes/functions/data-presets.php:308
msgid "Tonga"
msgstr ""

#: includes/functions/data-presets.php:309
msgid "Trinidad and Tobago"
msgstr ""

#: includes/functions/data-presets.php:310
msgid "Tunisia"
msgstr ""

#: includes/functions/data-presets.php:311
msgid "Türkiye"
msgstr ""

#: includes/functions/data-presets.php:312
msgid "Turkmenistan"
msgstr ""

#: includes/functions/data-presets.php:313
msgid "Turks and Caicos Islands"
msgstr ""

#: includes/functions/data-presets.php:314
msgid "Tuvalu"
msgstr ""

#: includes/functions/data-presets.php:315
msgid "Uganda"
msgstr ""

#: includes/functions/data-presets.php:316
msgid "Ukraine"
msgstr ""

#: includes/functions/data-presets.php:317
msgid "United Arab Emirates"
msgstr ""

#: includes/functions/data-presets.php:318
msgid "United Kingdom of Great Britain and Northern Ireland"
msgstr ""

#: includes/functions/data-presets.php:319
msgid "United States of America"
msgstr ""

#: includes/functions/data-presets.php:320
msgid "United States Minor Outlying Islands"
msgstr ""

#: includes/functions/data-presets.php:321
msgid "Uruguay"
msgstr ""

#: includes/functions/data-presets.php:322
msgid "Uzbekistan"
msgstr ""

#: includes/functions/data-presets.php:323
msgid "Vanuatu"
msgstr ""

#: includes/functions/data-presets.php:324
msgid "Vatican City State"
msgstr ""

#: includes/functions/data-presets.php:325
msgid "Venezuela (Bolivarian Republic of)"
msgstr ""

#: includes/functions/data-presets.php:326
msgid "Vietnam"
msgstr ""

#: includes/functions/data-presets.php:327
msgid "Virgin Islands (British)"
msgstr ""

#: includes/functions/data-presets.php:328
msgid "Virgin Islands (U.S.)"
msgstr ""

#: includes/functions/data-presets.php:329
msgid "Wallis and Futuna"
msgstr ""

#: includes/functions/data-presets.php:330
msgid "Western Sahara"
msgstr ""

#: includes/functions/data-presets.php:331
msgid "Yemen"
msgstr ""

#: includes/functions/data-presets.php:332
msgid "Zambia"
msgstr ""

#: includes/functions/data-presets.php:333
msgid "Zimbabwe"
msgstr ""

#: includes/functions/data-presets.php:349
msgid "January"
msgstr ""

#: includes/functions/data-presets.php:350
msgid "February"
msgstr ""

#: includes/functions/data-presets.php:351
msgid "March"
msgstr ""

#: includes/functions/data-presets.php:352
msgid "April"
msgstr ""

#: includes/functions/data-presets.php:353
msgid "May"
msgstr ""

#: includes/functions/data-presets.php:354
msgid "June"
msgstr ""

#: includes/functions/data-presets.php:355
msgid "July"
msgstr ""

#: includes/functions/data-presets.php:356
msgid "August"
msgstr ""

#: includes/functions/data-presets.php:357
msgid "September"
msgstr ""

#: includes/functions/data-presets.php:358
msgid "October"
msgstr ""

#: includes/functions/data-presets.php:359
msgid "November"
msgstr ""

#: includes/functions/data-presets.php:360
msgid "December"
msgstr ""

#: includes/functions/data-presets.php:376
msgid "Sunday"
msgstr ""

#: includes/functions/data-presets.php:377
msgid "Monday"
msgstr ""

#: includes/functions/data-presets.php:378
msgid "Tuesday"
msgstr ""

#: includes/functions/data-presets.php:379
msgid "Wednesday"
msgstr ""

#: includes/functions/data-presets.php:380
msgid "Thursday"
msgstr ""

#: includes/functions/data-presets.php:381
msgid "Friday"
msgstr ""

#: includes/functions/data-presets.php:382
msgid "Saturday"
msgstr ""

#. translators: %1$s - formatted date, %2$s - formatted time.
#. translators: %1$s - date, %2$s - time when item was created, e.g. "Oct 22, 2022 at 11:11 am".
#. translators: %1$s - date, %2$s - time when item was created, e.g. "Oct 22 at 11:11am".
#: includes/functions/date-time.php:35
#: src/Admin/Payments/Views/Single.php:992
#: src/Admin/Revisions.php:324
#: templates/admin/payments/single/log.php:34
msgid "%1$s at %2$s"
msgstr ""

#: includes/functions/education.php:36
msgid "Install & Activate"
msgstr ""

#: includes/functions/education.php:43
msgid "Plugin installation is disabled for this site."
msgstr ""

#. translators: %d - post ID.
#: includes/functions/form-fields.php:347
msgid "#%d (no title)"
msgstr ""

#. translators: %d - taxonomy term ID.
#: includes/functions/form-fields.php:368
msgid "#%d (no name)"
msgstr ""

#: includes/functions/form-fields.php:443
msgid "Standard Fields"
msgstr ""

#: includes/functions/form-fields.php:447
msgid "Fancy Fields"
msgstr ""

#: includes/functions/form-fields.php:451
msgid "Payment Fields"
msgstr ""

#: includes/functions/payments.php:19
msgid "U.S. Dollar"
msgstr ""

#: includes/functions/payments.php:27
msgid "Pound Sterling"
msgstr ""

#: includes/functions/payments.php:35
msgid "Euro"
msgstr ""

#: includes/functions/payments.php:43
msgid "Australian Dollar"
msgstr ""

#: includes/functions/payments.php:51
msgid "Brazilian Real"
msgstr ""

#: includes/functions/payments.php:59
msgid "Bulgarian Lev"
msgstr ""

#: includes/functions/payments.php:67
msgid "Canadian Dollar"
msgstr ""

#: includes/functions/payments.php:75
msgid "Costa Rican Colón"
msgstr ""

#: includes/functions/payments.php:83
msgid "Central African CFA Franc"
msgstr ""

#: includes/functions/payments.php:91
msgid "Czech Koruna"
msgstr ""

#: includes/functions/payments.php:99
msgid "Danish Krone"
msgstr ""

#: includes/functions/payments.php:107
msgid "Hong Kong Dollar"
msgstr ""

#: includes/functions/payments.php:115
msgid "Hungarian Forint"
msgstr ""

#: includes/functions/payments.php:123
msgid "Indian Rupee"
msgstr ""

#: includes/functions/payments.php:131
msgid "Israeli New Sheqel"
msgstr ""

#: includes/functions/payments.php:139
msgid "Japanese Yen"
msgstr ""

#: includes/functions/payments.php:147
msgid "Malaysian Ringgit"
msgstr ""

#: includes/functions/payments.php:155
msgid "Mexican Peso"
msgstr ""

#: includes/functions/payments.php:163
msgid "Norwegian Krone"
msgstr ""

#: includes/functions/payments.php:171
msgid "New Zealand Dollar"
msgstr ""

#: includes/functions/payments.php:179
msgid "Philippine Peso"
msgstr ""

#: includes/functions/payments.php:187
msgid "Polish Zloty"
msgstr ""

#: includes/functions/payments.php:195
msgid "Romanian Leu"
msgstr ""

#: includes/functions/payments.php:203
msgid "Russian Ruble"
msgstr ""

#: includes/functions/payments.php:211
msgid "Saudi Arabian Riyal"
msgstr ""

#: includes/functions/payments.php:219
msgid "Singapore Dollar"
msgstr ""

#: includes/functions/payments.php:227
msgid "Serbian Dinar"
msgstr ""

#: includes/functions/payments.php:235
msgid "South African Rand"
msgstr ""

#: includes/functions/payments.php:243
msgid "South Korean Won"
msgstr ""

#: includes/functions/payments.php:251
msgid "Sri Lankan Rupee"
msgstr ""

#: includes/functions/payments.php:259
msgid "Swedish Krona"
msgstr ""

#: includes/functions/payments.php:267
msgid "Swiss Franc"
msgstr ""

#: includes/functions/payments.php:275
msgid "Taiwan New Dollar"
msgstr ""

#: includes/functions/payments.php:283
msgid "Thai Baht"
msgstr ""

#: includes/functions/payments.php:291
msgid "Turkish Lira"
msgstr ""

#: includes/functions/payments.php:299
msgid "United Arab Emirates Dirham"
msgstr ""

#. translators: %1$s - payment amount; %2$d - payment quantity.
#: includes/functions/payments.php:778
msgid "%1$s &times; %2$d"
msgstr ""

#: includes/functions/utilities.php:336
#: src/Integrations/AI/Admin/Builder/Forms.php:205
#: templates/admin/payments/reset-filter-notice.php:57
msgid "and"
msgstr ""

#: includes/integrations.php:27
msgid "Select a form to display"
msgstr ""

#: includes/integrations.php:35
msgid "No forms found"
msgstr ""

#: includes/integrations.php:45
msgid "Add your form"
msgstr ""

#: includes/integrations.php:49
#: src/Admin/Payments/Views/Overview/Table.php:105
#: src/Forms/Preview.php:185
#: src/Forms/Preview.php:391
#: src/Integrations/Divi/WPFormsSelector.php:65
#: src/Integrations/Elementor/Widget.php:127
#: src/Integrations/Elementor/Widget.php:155
#: src/Integrations/Gutenberg/FormSelector.php:532
#: templates/emails/summary-body-plain.php:52
#: templates/emails/summary-body.php:158
msgid "Form"
msgstr ""

#: includes/integrations.php:53
msgid "Select a form to add it to your post or page."
msgstr ""

#: includes/integrations.php:58
msgid "Display Form Name"
msgstr ""

#: includes/integrations.php:61
#: includes/integrations.php:76
#: src/Integrations/Gutenberg/FormSelector.php:577
msgid "No"
msgstr ""

#: includes/integrations.php:62
#: includes/integrations.php:77
#: src/Integrations/Gutenberg/FormSelector.php:576
msgid "Yes"
msgstr ""

#: includes/integrations.php:65
msgid "Would you like to display the forms name?"
msgstr ""

#: includes/integrations.php:73
msgid "Display Form Description"
msgstr ""

#: includes/integrations.php:80
msgid "Would you like to display the form description?"
msgstr ""

#: includes/providers/class-base.php:168
#: includes/providers/class-base.php:1227
#: includes/providers/class-base.php:1275
msgid "You do not have permission"
msgstr ""

#. translators: %s - Name field label.
#: includes/providers/class-base.php:492
msgid "%s (Full)"
msgstr ""

#. translators: %s - Name field label.
#: includes/providers/class-base.php:507
msgid "%s (First)"
msgstr ""

#. translators: %s - Name field label.
#: includes/providers/class-base.php:523
msgid "%s (Middle)"
msgstr ""

#. translators: %s - Name field label.
#: includes/providers/class-base.php:539
msgid "%s (Last)"
msgstr ""

#: includes/providers/class-base.php:765
#: templates/integrations/constant-contact-v3/builder/connection.php:32
#: templates/integrations/constant-contact-v3/builder/connection.php:35
msgid "Select Account"
msgstr ""

#: includes/providers/class-base.php:777
#: includes/providers/class-base.php:1408
#: includes/providers/class-constant-contact.php:543
#: src/Integrations/ConstantContact/V3/Settings/FormBuilder.php:186
#: src/Integrations/ConstantContact/V3/Settings/PageIntegrations.php:91
#: src/Providers/Provider/Settings/FormBuilder.php:555
#: src/Providers/Provider/Settings/PageIntegrations.php:256
msgid "Add New Account"
msgstr ""

#: includes/providers/class-base.php:811
#: src/Integrations/ConstantContact/V3/Settings/FormBuilder.php:216
msgid "Select List"
msgstr ""

#: includes/providers/class-base.php:858
msgid "Select Groups"
msgstr ""

#: includes/providers/class-base.php:860
msgid "We also noticed that you have some segments in your list. You can select specific list segments below if needed. This is optional."
msgstr ""

#: includes/providers/class-base.php:924
#: includes/providers/class-base.php:929
msgid "List Fields"
msgstr ""

#: includes/providers/class-base.php:929
msgid "Available Form Fields"
msgstr ""

#: includes/providers/class-base.php:1005
#: src/Integrations/ConstantContact/V3/Settings/FormBuilder.php:272
#: src/Providers/Provider/Settings/FormBuilder.php:111
msgid "Marketing provider connection"
msgstr ""

#. translators: %s - provider type.
#: includes/providers/class-base.php:1152
msgid "Add New %s"
msgstr ""

#: includes/providers/class-base.php:1235
#: includes/providers/class-base.php:1286
msgid "Missing data"
msgstr ""

#: includes/providers/class-base.php:1251
msgid "Connection missing"
msgstr ""

#: includes/providers/class-base.php:1298
msgid "Could not connect to the provider."
msgstr ""

#. translators: %1$s - Connection date.
#: includes/providers/class-base.php:1310
#: includes/providers/class-base.php:1393
#: src/Providers/Provider/Settings/PageIntegrations.php:180
msgid "Connected on: %1$s"
msgstr ""

#: includes/providers/class-base.php:1315
#: includes/providers/class-base.php:1398
#: src/Integrations/Square/Admin/Settings.php:524
#: src/Providers/Provider/Settings/PageIntegrations.php:193
msgid "Disconnect"
msgstr ""

#. translators: %s - provider name.
#: includes/providers/class-base.php:1341
#: src/Providers/Provider/Settings/PageIntegrations.php:294
msgid "Connect to %s"
msgstr ""

#: includes/providers/class-base.php:1359
#: src/Providers/Provider/Settings/PageIntegrations.php:70
msgid "Show Accounts"
msgstr ""

#. translators: %s - provider name.
#: includes/providers/class-base.php:1368
#: lite/templates/education/admin/settings/integrations-item.php:36
#: src/Providers/Provider/Settings/PageIntegrations.php:79
msgid "Integrate %s with WPForms"
msgstr ""

#: includes/providers/class-base.php:1375
#: src/Providers/Provider/Settings/PageIntegrations.php:86
msgid "Connected"
msgstr ""

#: includes/providers/class-base.php:1416
#: src/Providers/Provider/Settings/PageIntegrations.php:264
msgid "Please fill out all of the fields below to add your new provider account."
msgstr ""

#: includes/providers/class-constant-contact.php:546
msgid "Please fill out all of the fields below to register your new Constant Contact account."
msgstr ""

#: includes/providers/class-constant-contact.php:549
#: src/Integrations/ConstantContact/V3/Settings/PageIntegrations.php:101
msgid "Click here for documentation on connecting WPForms with Constant Contact."
msgstr ""

#: includes/providers/class-constant-contact.php:554
#: includes/providers/class-constant-contact.php:720
msgid "Because Constant Contact requires external authentication, you will need to register WPForms with Constant Contact before you can proceed."
msgstr ""

#: includes/providers/class-constant-contact.php:560
#: includes/providers/class-constant-contact.php:726
msgid "Click here to register with Constant Contact"
msgstr ""

#: includes/providers/class-constant-contact.php:569
#: includes/providers/class-constant-contact.php:735
msgid "Authorization Code"
msgstr ""

#: includes/providers/class-constant-contact.php:575
#: includes/providers/class-constant-contact.php:741
msgid "Account Nickname"
msgstr ""

#: includes/providers/class-constant-contact.php:581
msgid "Connect"
msgstr ""

#: includes/providers/class-constant-contact.php:624
#: includes/providers/class-constant-contact.php:819
#: src/Integrations/ConstantContact/V3/Settings/FormBuilder.php:376
msgid "Try Constant Contact for Free"
msgstr ""

#: includes/providers/class-constant-contact.php:632
#: src/Integrations/ConstantContact/V3/Settings/FormBuilder.php:384
msgid "Learn more about the power of email marketing."
msgstr ""

#: includes/providers/class-constant-contact.php:677
msgid "The \"Authorization Code\" is required."
msgstr ""

#: includes/providers/class-constant-contact.php:685
msgid "The \"Account Nickname\" is required."
msgstr ""

#. translators: %1$s - Documentation URL.
#: includes/providers/class-constant-contact.php:703
msgid "If you need help connecting WPForms to Constant Contact, <a href=\"%1$s\" rel=\"noopener noreferrer\" target=\"_blank\">read our documentation</a>."
msgstr ""

#: includes/providers/class-constant-contact.php:810
msgid "Get the most out of the <strong>WPForms</strong> plugin &mdash; use it with an active Constant Contact account."
msgstr ""

#: includes/providers/class-constant-contact.php:822
msgid "Connect your existing account"
msgstr ""

#. translators: %s - WPForms Constant Contact internal URL.
#: includes/providers/class-constant-contact.php:827
msgid "Learn More about the <a href=\"%s\">power of email marketing</a>"
msgstr ""

#: includes/templates/class-blank.php:38
msgid "The blank form allows you to create any type of form using our drag & drop builder."
msgstr ""

#: includes/templates/class-simple-contact-form.php:21
msgid "Simple Contact Form"
msgstr ""

#: includes/templates/class-simple-contact-form.php:28
msgid "Collect the names, emails, and messages from site visitors that need to talk to you."
msgstr ""

#: includes/templates/class-simple-contact-form.php:50
#: src/Emails/Preview.php:411
msgid "Comment or Message"
msgstr ""

#: lite/templates/admin/addons.php:25
msgid "Search Addons"
msgstr ""

#: lite/templates/admin/addons.php:34
msgid "Unknown Addon"
msgstr ""

#. translators: %s - addon title.
#: lite/templates/admin/addons.php:49
msgid "%s logo"
msgstr ""

#: lite/templates/admin/addons.php:71
#: src/Integrations/Gutenberg/FormSelector.php:542
msgid "Learn more"
msgstr ""

#: lite/templates/admin/addons.php:96
msgid "Sorry, we didn't find any addons that match your criteria."
msgstr ""

#: lite/templates/admin/entries/overview/modal.php:17
msgid "Form entries are not stored in WPForms Lite."
msgstr ""

#: lite/templates/admin/entries/overview/modal.php:22
msgid "View and Manage Your Form Entries inside WordPress"
msgstr ""

#: lite/templates/admin/entries/overview/modal.php:25
msgid "Once you upgrade to WPForms Pro, all future form entries will be stored in your WordPress database and displayed on this Entries screen."
msgstr ""

#: lite/templates/admin/entries/overview/modal.php:29
msgid "View Entries in Dashboard"
msgstr ""

#: lite/templates/admin/entries/overview/modal.php:30
msgid "Export Entries in a CSV File"
msgstr ""

#: lite/templates/admin/entries/overview/modal.php:31
msgid "Add Notes / Comments"
msgstr ""

#: lite/templates/admin/entries/overview/modal.php:32
msgid "Save Favorite Entries"
msgstr ""

#: lite/templates/admin/entries/overview/modal.php:35
msgid "Mark Read / Unread"
msgstr ""

#: lite/templates/admin/entries/overview/modal.php:36
msgid "Print Entries"
msgstr ""

#: lite/templates/admin/entries/overview/modal.php:37
msgid "Resend Notifications"
msgstr ""

#: lite/templates/admin/entries/overview/modal.php:38
msgid "See Geolocation Data"
msgstr ""

#. translators: %d - backed up entries count.
#: lite/templates/admin/entries/overview/modal.php:50
msgid "%d entry has been backed up"
msgid_plural "%d entries have been backed up"
msgstr[0] ""
msgstr[1] ""

#. translators: %s - time when Lite Connect was enabled.
#: lite/templates/admin/entries/overview/modal.php:64
msgid "since you enabled Lite Connect on %s"
msgstr ""

#: lite/templates/admin/entries/overview/modal.php:71
msgid "Upgrade to WPForms Pro & Restore Form Entries"
msgstr ""

#: lite/templates/admin/entries/overview/modal.php:77
msgid "Upgrade to WPForms Pro Now"
msgstr ""

#: lite/templates/admin/entries/overview/modal.php:84
msgid "Explore Entries & Learn More"
msgstr ""

#: lite/templates/admin/entries/single/entry.php:25
msgid "Back to All Entries"
msgstr ""

#: lite/templates/builder/context-menu.php:36
msgid "Duplicate Template"
msgstr ""

#: lite/templates/builder/context-menu.php:51
msgid "Duplicate Form"
msgstr ""

#: lite/templates/builder/context-menu.php:64
msgid "Save as Template"
msgstr ""

#: lite/templates/builder/context-menu.php:83
#: src/Admin/AdminBarMenu.php:631
#: src/Admin/Builder/Shortcuts.php:57
#: src/Forms/Preview.php:238
#: src/Integrations/Gutenberg/FormSelector.php:487
msgid "View Entries"
msgstr ""

#: lite/templates/builder/context-menu.php:98
#: src/Admin/AdminBarMenu.php:632
#: src/Forms/Preview.php:257
msgid "View Payments"
msgstr ""

#: lite/templates/builder/context-menu.php:112
#: src/Admin/Builder/Shortcuts.php:75
msgid "Keyboard Shortcuts"
msgstr ""

#: lite/templates/builder/context-menu.php:126
msgid "What's New"
msgstr ""

#: lite/templates/education/admin/did-you-know.php:42
#: lite/templates/education/admin/did-you-know.php:56
#: lite/templates/education/admin/notice-bar.php:35
#: lite/templates/education/builder/did-you-know.php:31
#: lite/templates/education/builder/lite-connect/top-bar.php:26
#: lite/wpforms-lite.php:149
#: src/Integrations/Stripe/Admin/Notices.php:173
msgid "Dismiss this message."
msgstr ""

#: lite/templates/education/admin/lite-connect/challenge-popup-footer.php:19
msgid "One More Thing"
msgstr ""

#: lite/templates/education/admin/lite-connect/challenge-popup-footer.php:23
msgid "WPForms now offers offsite backups for your form entries. If you decide to upgrade to WPForms Pro, you can restore entries collected while you used WPForms Lite."
msgstr ""

#: lite/templates/education/admin/lite-connect/challenge-popup-footer.php:31
#: lite/templates/education/builder/lite-connect/top-bar.php:23
msgid "Form Entry Backups Are Enabled"
msgstr ""

#: lite/templates/education/admin/lite-connect/dashboard-widget-before.php:26
msgid "Restore Entries"
msgstr ""

#. translators: %s - WPForms.com Upgrade page URL.
#: lite/templates/education/admin/notice-bar.php:21
msgid "<strong>You're using WPForms Lite.</strong> To unlock more features consider <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">upgrading to Pro</a> for 50%% off."
msgstr ""

#: lite/templates/education/builder/did-you-know.php:21
#: src/Lite/Admin/Education/Admin/DidYouKnow.php:128
#: src/Lite/Admin/Education/Admin/DidYouKnow.php:134
msgid "Did You Know?"
msgstr ""

#: lite/templates/education/builder/lite-connect/ai-modal.php:16
msgid "Enable AI Features in WPForms"
msgstr ""

#: lite/templates/education/builder/lite-connect/ai-modal.php:18
msgid "Before you can proceed, we need your permission to record what you input in order to generate content with AI. You’ll also get..."
msgstr ""

#: lite/templates/education/builder/lite-connect/ai-modal.php:22
#: lite/templates/education/lite-connect-modal.php:44
msgid "WPForms AI."
msgstr ""

#: lite/templates/education/builder/lite-connect/ai-modal.php:24
#: lite/templates/education/lite-connect-modal.php:46
msgid "WPForms AI"
msgstr ""

#: lite/templates/education/builder/lite-connect/ai-modal.php:26
#: lite/templates/education/lite-connect-modal.php:48
msgid "Build your forms even faster with state-of-the-art generative AI built right into the form builder."
msgstr ""

#: lite/templates/education/builder/lite-connect/ai-modal.php:31
#: lite/templates/education/lite-connect-modal.php:24
msgid "Backup and Restore."
msgstr ""

#: lite/templates/education/builder/lite-connect/ai-modal.php:33
msgid "Form Entry Backup & Restore"
msgstr ""

#: lite/templates/education/builder/lite-connect/ai-modal.php:35
#: lite/templates/education/lite-connect-modal.php:28
msgid "When you upgrade to WPForms Pro, we'll automatically restore all of the entries that you collected in WPForms Lite."
msgstr ""

#: lite/templates/education/builder/lite-connect/ai-modal.php:40
msgid "Security & Protection."
msgstr ""

#: lite/templates/education/builder/lite-connect/ai-modal.php:42
#: lite/templates/education/lite-connect-modal.php:35
msgid "Security & Protection"
msgstr ""

#: lite/templates/education/builder/lite-connect/ai-modal.php:44
#: lite/templates/education/lite-connect-modal.php:37
msgid "Entries are stored securely and privately until you're ready to upgrade. Our team cannot view your forms or entries."
msgstr ""

#: lite/templates/education/builder/lite-connect/ai-modal.php:49
#: lite/templates/education/lite-connect-modal.php:55
msgid "WPForms Newsletter."
msgstr ""

#: lite/templates/education/builder/lite-connect/ai-modal.php:51
#: lite/templates/education/lite-connect-modal.php:57
msgid "WPForms Newsletter"
msgstr ""

#: lite/templates/education/builder/lite-connect/ai-modal.php:53
#: lite/templates/education/lite-connect-modal.php:59
msgid "Ready to grow your website? Get the latest pro tips and updates from the WPForms team."
msgstr ""

#. translators: %s - WPForms Terms of Service link.
#: lite/templates/education/builder/lite-connect/ai-modal.php:62
#: lite/templates/education/lite-connect-modal.php:69
msgid "By enabling Lite Connect you agree to our <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">Terms of Service</a> and to share your information with WPForms."
msgstr ""

#: lite/templates/education/builder/lite-connect/top-bar.php:25
msgid "Easily restore your entries when you upgrade to WPForms Pro."
msgstr ""

#: lite/templates/education/builder/providers-item.php:34
#: src/Admin/Education/Helpers.php:99
#: templates/builder/payment/sidebar.php:28
msgid "Recommended"
msgstr ""

#: lite/templates/education/lite-connect-modal.php:18
msgid "Form Entry Backups"
msgstr ""

#: lite/templates/education/lite-connect-modal.php:20
msgid "If your email notifications aren't delivered, you’ll lose form entries. Turn on free backups now and restore your entries when you upgrade to Pro."
msgstr ""

#: lite/templates/education/lite-connect-modal.php:26
msgid "Backup & Restore"
msgstr ""

#: lite/templates/education/lite-connect-modal.php:33
msgid "Security and Protection."
msgstr ""

#: lite/wpforms-lite.php:140
msgid "Multiple notifications"
msgstr ""

#: lite/wpforms-lite.php:141
msgid "Add New Notification"
msgstr ""

#. translators: %s - link to the WPForms.com doc article.
#: lite/wpforms-lite.php:153
msgid "Notifications are emails sent when a form is submitted. By default, these emails include entry details. For setup and customization options, including a video overview, please <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">see our tutorial</a>."
msgstr ""

#. translators: 1$s, %2$s - links to the WPForms.com doc articles.
#: lite/wpforms-lite.php:168
msgid "After saving these settings, be sure to <a href=\"%1$s\" target=\"_blank\" rel=\"noopener noreferrer\">test a form submission</a>. This lets you see how emails will look, and to ensure that they <a href=\"%2$s\" target=\"_blank\" rel=\"noopener noreferrer\">are delivered successfully</a>."
msgstr ""

#: lite/wpforms-lite.php:190
msgid "Enable Notifications"
msgstr ""

#: lite/wpforms-lite.php:208
msgid "Send To Email Address"
msgstr ""

#: lite/wpforms-lite.php:211
msgid "Enter the email address to receive form entry notifications. For multiple notifications, separate email addresses with a comma."
msgstr ""

#: lite/wpforms-lite.php:228
msgid "CC"
msgstr ""

#: lite/wpforms-lite.php:245
msgid "Email Subject Line"
msgstr ""

#: lite/wpforms-lite.php:264
msgid "From Name"
msgstr ""

#: lite/wpforms-lite.php:297
msgid "From Email"
msgstr ""

#: lite/wpforms-lite.php:330
msgid "Reply-To"
msgstr ""

#. translators: %s - <<EMAIL>>.
#: lite/wpforms-lite.php:334
msgid "Enter the email address or email address with recipient's name in \"First Last %s\" format."
msgstr ""

#: lite/wpforms-lite.php:354
msgid "Email Message"
msgstr ""

#. translators: %s - {all_fields} Smart Tag.
#: lite/wpforms-lite.php:368
msgid "To display all form fields, use the %s Smart Tag."
msgstr ""

#: lite/wpforms-lite.php:492
msgid "Multiple confirmations"
msgstr ""

#: lite/wpforms-lite.php:493
msgid "Add New Confirmation"
msgstr ""

#: lite/wpforms-lite.php:522
msgid "Confirmation Type"
msgstr ""

#: lite/wpforms-lite.php:526
#: src/Logger/ListTable.php:291
#: src/Logger/ListTable.php:481
msgid "Message"
msgstr ""

#: lite/wpforms-lite.php:527
msgid "Show Page"
msgstr ""

#: lite/wpforms-lite.php:528
msgid "Go to URL (Redirect)"
msgstr ""

#: lite/wpforms-lite.php:541
msgid "Confirmation Message"
msgstr ""

#: lite/wpforms-lite.php:562
msgid "Automatically scroll to the confirmation message"
msgstr ""

#: lite/wpforms-lite.php:575
msgid "Confirmation Page"
msgstr ""

#: lite/wpforms-lite.php:593
msgid "Confirmation Redirect URL"
msgstr ""

#: lite/wpforms-lite.php:606
msgid "Open confirmation in new tab"
msgstr ""

#. translators: %s - WPForms.com docs page URL.
#: lite/wpforms-lite.php:662
msgid "You've just turned off notification emails for this form. Since entries are not stored in WPForms Lite, notification emails are recommended for collecting entry details. For setup steps, <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">please see our notification tutorial</a>."
msgstr ""

#: lite/wpforms-lite.php:698
#: templates/admin/notifications.php:27
msgid "Dismiss this message"
msgstr ""

#: lite/wpforms-lite.php:700
msgid "Thanks for being a loyal WPForms Lite user. Upgrade to WPForms Pro to unlock all the awesome features and experience why WPForms is consistently rated the best WordPress form builder."
msgstr ""

#. translators: %s - star icons.
#: lite/wpforms-lite.php:705
msgid "We know that you will truly love WPForms. It has over 13,000+ five star ratings (%s) and is active on over 6 million websites."
msgstr ""

#: lite/wpforms-lite.php:717
msgid "Pro Features:"
msgstr ""

#: lite/wpforms-lite.php:746
msgid "Get WPForms Pro Today and Unlock all the Powerful Features »"
msgstr ""

#: lite/wpforms-lite.php:752
msgid "<strong>Bonus:</strong> WPForms Lite users get <span class=\"green\">50% off regular price</span>, automatically applied at checkout."
msgstr ""

#: lite/wpforms-lite.php:786
#: src/Admin/Forms/BulkActions.php:345
msgid "Security check failed. Please try again."
msgstr ""

#. translators: %s - addon name.
#: src/Admin/Addons/Addons.php:490
#: src/Admin/Education/AddonsItemBase.php:152
msgid "%s addon"
msgstr ""

#: src/Admin/AdminBarMenu.php:217
#: src/Admin/Builder/AntiSpam.php:233
#: src/Admin/Education/Builder/Captcha.php:120
#: src/Admin/Settings/Captcha/Page.php:122
#: src/Admin/Settings/Captcha/Page.php:145
msgid "CAPTCHA"
msgstr ""

#: src/Admin/AdminBarMenu.php:237
msgid "Access Control"
msgstr ""

#: src/Admin/AdminBarMenu.php:293
#: src/Admin/Tools/Views/Import.php:102
#: src/Admin/Tools/Views/Import.php:224
#: src/Admin/Tools/Views/Import.php:276
#: src/Admin/Tools/Views/Importer.php:190
msgid "Import"
msgstr ""

#: src/Admin/AdminBarMenu.php:297
#: src/Admin/Tools/Views/Export.php:58
#: src/Admin/Tools/Views/Export.php:157
msgid "Export"
msgstr ""

#: src/Admin/AdminBarMenu.php:301
#: src/Admin/Tools/Views/System.php:42
msgid "System Info"
msgstr ""

#: src/Admin/AdminBarMenu.php:305
#: src/Admin/Tools/Views/ActionScheduler.php:61
#: src/Admin/Tools/Views/ActionSchedulerList.php:39
msgid "Scheduled Actions"
msgstr ""

#: src/Admin/AdminBarMenu.php:309
#: src/Admin/Tools/Views/Logs.php:52
#: src/Logger/ListTable.php:44
msgid "Logs"
msgstr ""

#: src/Admin/AdminBarMenu.php:313
#: src/Admin/Tools/Views/CodeSnippets.php:113
#: templates/integrations/wpcode/code-snippets.php:53
msgid "Code Snippets"
msgstr ""

#: src/Admin/AdminBarMenu.php:366
#: templates/builder/help.php:122
msgid "View All"
msgstr ""

#: src/Admin/AdminBarMenu.php:370
msgid "Completed Actions"
msgstr ""

#: src/Admin/AdminBarMenu.php:374
msgid "Failed Actions"
msgstr ""

#: src/Admin/AdminBarMenu.php:378
msgid "Pending Actions"
msgstr ""

#: src/Admin/AdminBarMenu.php:382
msgid "Past Due Actions"
msgstr ""

#: src/Admin/AdminBarMenu.php:606
msgid "Help Docs"
msgstr ""

#: src/Admin/AdminBarMenu.php:630
#: src/Forms/Preview.php:222
#: src/Integrations/Gutenberg/FormSelector.php:486
msgid "Edit Form"
msgstr ""

#: src/Admin/AdminBarMenu.php:633
msgid "Survey Results"
msgstr ""

#. translators: %d - form ID.
#: src/Admin/AdminBarMenu.php:647
msgid "Form ID: %d"
msgstr ""

#: src/Admin/Builder/Ajax/PanelLoader.php:64
#: src/Admin/Education/Core.php:105
#: src/Admin/Forms/Ajax/Columns.php:64
#: src/Integrations/ConstantContact/V3/Auth.php:78
#: src/Integrations/ConstantContact/V3/Migration/Migration.php:268
#: src/Providers/Provider/Settings/FormBuilder.php:340
msgid "You do not have permission to perform this action."
msgstr ""

#: src/Admin/Builder/Ajax/PanelLoader.php:109
msgid "Invalid panel."
msgstr ""

#: src/Admin/Builder/Ajax/PanelLoader.php:129
#: src/Admin/Forms/Ajax/Columns.php:96
#: src/Admin/Forms/Ajax/Tags.php:195
msgid "Most likely, your session expired. Please reload the page."
msgstr ""

#: src/Admin/Builder/AntiSpam.php:65
msgid "Enable modern anti-spam protection"
msgstr ""

#: src/Admin/Builder/AntiSpam.php:68
msgid "Turn on invisible modern spam protection."
msgstr ""

#: src/Admin/Builder/AntiSpam.php:76
msgid "Behind-the-scenes spam filtering that's invisible to your visitors."
msgstr ""

#: src/Admin/Builder/AntiSpam.php:77
msgid "Protection"
msgstr ""

#: src/Admin/Builder/AntiSpam.php:87
msgid "Enable anti-spam protection"
msgstr ""

#: src/Admin/Builder/AntiSpam.php:89
msgid "Turn on invisible spam protection."
msgstr ""

#: src/Admin/Builder/AntiSpam.php:100
msgid "Enable anti-spam honeypot"
msgstr ""

#: src/Admin/Builder/AntiSpam.php:138
msgid "Also Available"
msgstr ""

#: src/Admin/Builder/AntiSpam.php:200
msgid "Enable hCaptcha"
msgstr ""

#: src/Admin/Builder/AntiSpam.php:201
msgid "Enable Cloudflare Turnstile"
msgstr ""

#: src/Admin/Builder/AntiSpam.php:203
msgid "Enable Google Checkbox v2 reCAPTCHA"
msgstr ""

#: src/Admin/Builder/AntiSpam.php:204
msgid "Enable Google Invisible v2 reCAPTCHA"
msgstr ""

#: src/Admin/Builder/AntiSpam.php:205
msgid "Enable Google v3 reCAPTCHA"
msgstr ""

#: src/Admin/Builder/AntiSpam.php:224
msgid "Enable third-party CAPTCHAs to prevent form submissions from bots."
msgstr ""

#: src/Admin/Builder/AntiSpam.php:232
msgid "Automated tests that help to prevent bots from submitting your forms."
msgstr ""

#: src/Admin/Builder/AntiSpam.php:257
msgid "Store spam entries in the database"
msgstr ""

#: src/Admin/Builder/AntiSpam.php:277
msgid "Enable minimum time to submit"
msgstr ""

#: src/Admin/Builder/AntiSpam.php:281
msgid "Set a minimum amount of time a user must spend on a form before submitting."
msgstr ""

#: src/Admin/Builder/AntiSpam.php:291
msgid "Minimum time to submit"
msgstr ""

#: src/Admin/Builder/AntiSpam.php:298
msgid "seconds"
msgstr ""

#: src/Admin/Builder/AntiSpam.php:335
msgid "Enable Akismet anti-spam protection"
msgstr ""

#: src/Admin/Builder/AntiSpam.php:349
msgid "Get Started &rarr;"
msgstr ""

#: src/Admin/Builder/AntiSpam.php:358
msgid "Country Filter"
msgstr ""

#: src/Admin/Builder/AntiSpam.php:359
msgid "Stop spam at its source. Allow or deny entries from specific countries."
msgstr ""

#: src/Admin/Builder/AntiSpam.php:367
msgid "Keyword Filter"
msgstr ""

#: src/Admin/Builder/AntiSpam.php:368
msgid "Block form entries that contain specific words or phrases that you define."
msgstr ""

#: src/Admin/Builder/AntiSpam.php:376
#: src/Admin/Education/Fields.php:165
#: src/Forms/Fields/CustomCaptcha/Field.php:51
msgid "Custom Captcha"
msgstr ""

#: src/Admin/Builder/AntiSpam.php:377
msgid "Ask custom questions or require your visitor to answer a random math puzzle."
msgstr ""

#: src/Admin/Builder/AntiSpam.php:379
msgid "Add to Form"
msgstr ""

#: src/Admin/Builder/AntiSpam.php:386
msgid "Add Google's free anti-spam service and choose between visible or invisible CAPTCHAs."
msgstr ""

#: src/Admin/Builder/AntiSpam.php:394
msgid "Turn on free, privacy-oriented spam prevention that displays a visual CAPTCHA."
msgstr ""

#: src/Admin/Builder/AntiSpam.php:402
msgid "Enable free, CAPTCHA-like spam protection that protects data privacy."
msgstr ""

#: src/Admin/Builder/AntiSpam.php:410
msgid "Integrate the powerful spam-fighting service trusted by millions of sites."
msgstr ""

#: src/Admin/Builder/Help.php:149
msgid "Form Creation"
msgstr ""

#: src/Admin/Builder/Help.php:151
msgid "Form Management"
msgstr ""

#: src/Admin/Builder/Help.php:154
msgid "Payment Processing"
msgstr ""

#: src/Admin/Builder/Help.php:155
msgid "Spam Prevention and Security"
msgstr ""

#: src/Admin/Builder/Help.php:156
msgid "Extending Functionality"
msgstr ""

#: src/Admin/Builder/Help.php:157
msgid "Troubleshooting and Support"
msgstr ""

#: src/Admin/Builder/Notifications/Advanced/EmailTemplate.php:109
msgid "Email Template"
msgstr ""

#: src/Admin/Builder/Notifications/Advanced/EmailTemplate.php:119
msgid "Override the default email template for this specific notification."
msgstr ""

#: src/Admin/Builder/Notifications/Advanced/EmailTemplate.php:172
msgid "Default Template"
msgstr ""

#: src/Admin/Builder/Settings/Themes.php:43
#: src/Admin/Builder/Settings/Themes.php:159
msgid "Form Themes"
msgstr ""

#: src/Admin/Builder/Settings/Themes.php:75
msgid "Before You Can Use Form Themes"
msgstr ""

#: src/Admin/Builder/Settings/Themes.php:78
msgid "Upgrade your forms to use our modern markup and unlock form themes and style controls."
msgstr ""

#: src/Admin/Builder/Settings/Themes.php:84
msgid "Enable Modern Markup"
msgstr ""

#: src/Admin/Builder/Settings/Themes.php:107
msgid "Not Using the Block Editor? Let us know!"
msgstr ""

#: src/Admin/Builder/Settings/Themes.php:110
msgid "If we get enough requests for themes in the form builder we may add them."
msgstr ""

#: src/Admin/Builder/Settings/Themes.php:118
msgid "Request Feature"
msgstr ""

#. translators: %s - URL to the documentation.
#: src/Admin/Builder/Settings/Themes.php:142
msgid "Customize the look and feel of your form with premade themes or simple style settings that allow you to use your own colors to match your brand. Themes and style settings are in the Block Editor, where you can see a realtime preview. <br /><a href=\"%s\" target=\"_blank\">Learn more about styling your forms</a>"
msgstr ""

#: src/Admin/Builder/Shortcuts.php:49
msgid "Save Form"
msgstr ""

#: src/Admin/Builder/Shortcuts.php:50
msgid "Preview Form"
msgstr ""

#: src/Admin/Builder/Shortcuts.php:51
msgid "Embed Form"
msgstr ""

#: src/Admin/Builder/Shortcuts.php:52
msgid "Search Fields"
msgstr ""

#: src/Admin/Builder/Shortcuts.php:55
msgid "Open Help"
msgstr ""

#: src/Admin/Builder/Shortcuts.php:56
msgid "Toggle Sidebar"
msgstr ""

#: src/Admin/Builder/Shortcuts.php:58
msgid "Close Builder"
msgstr ""

#: src/Admin/Builder/Shortcuts.php:76
msgid "Handy shortcuts for common actions in the builder."
msgstr ""

#: src/Admin/Builder/Templates.php:182
#: src/Admin/Education/StringsTrait.php:38
msgid "Activating"
msgstr ""

#: src/Admin/Builder/Templates.php:184
#: src/Integrations/Gutenberg/FormSelector.php:590
msgid "Heads Up!"
msgstr ""

#: src/Admin/Builder/Templates.php:185
msgid "Install and activate"
msgstr ""

#: src/Admin/Builder/Templates.php:187
#: src/Admin/Education/StringsTrait.php:24
msgid "Ok"
msgstr ""

#: src/Admin/Builder/Templates.php:188
msgid "Could not install OR activate all the required addons. Please download from wpforms.com and install them manually. Would you like to use the template anyway?"
msgstr ""

#: src/Admin/Builder/Templates.php:189
msgid "Yes, use template"
msgstr ""

#: src/Admin/Builder/Templates.php:190
#: src/Integrations/Gutenberg/FormSelector.php:495
msgid "Yes, Delete"
msgstr ""

#: src/Admin/Builder/Templates.php:191
msgid "Delete Form Template"
msgstr ""

#: src/Admin/Builder/Templates.php:192
msgid "Are you sure you want to delete this form template? This cannot be undone."
msgstr ""

#. translators: %1$s - template name, %2$s - addon name(s).
#: src/Admin/Builder/Templates.php:197
msgid "The %1$s template requires the %2$s. Would you like to install and activate it?"
msgstr ""

#. translators: %1$s - template name, %2$s - addon name(s).
#: src/Admin/Builder/Templates.php:199
msgid "The %1$s template requires the %2$s. Would you like to install and activate all the required addons?"
msgstr ""

#. translators: %1$s - template name, %2$s - addon name(s).
#: src/Admin/Builder/Templates.php:201
msgid "The %1$s template requires the %2$s addon. Would you like to activate it?"
msgstr ""

#. translators: %s - addon name(s).
#: src/Admin/Builder/Templates.php:204
msgid "To use all of the features in this template, you'll need the %s. Contact your site administrator to install it, then try opening this template again."
msgstr ""

#. translators: %s - addon name(s).
#: src/Admin/Builder/Templates.php:206
msgid "To use all of the features in this template, you'll need the %s. Contact your site administrator to install them, then try opening this template again."
msgstr ""

#: src/Admin/Builder/Templates.php:829
#: src/Integrations/Elementor/Widget.php:213
msgid "New form"
msgstr ""

#. translators: %d - templates count.
#: src/Admin/Builder/Templates.php:1033
msgid "Get Access to Our Complete Library of %d+ Form Templates"
msgstr ""

#: src/Admin/Builder/Templates.php:1036
msgid "Save time and reduce effort with our pre-built form templates covering popular use-cases in business operations, customer service, feedback, marketing, registrations, event planning, non-profit, healthcare, and education."
msgstr ""

#. translators: %d - templates count.
#: src/Admin/Builder/Templates.php:1054
msgid "Get Access to Our Library of %d+ Pre-Made Form Templates"
msgstr ""

#: src/Admin/Builder/Templates.php:1057
msgid "Never start from scratch again! While WPForms Lite allows you to create any type of form, you can save even more time with WPForms Pro. Upgrade to access hundreds more form templates and advanced form fields."
msgstr ""

#: src/Admin/Builder/Templates.php:1104
msgid "User Registration Form"
msgstr ""

#: src/Admin/Builder/Templates.php:1110
msgid "Create customized WordPress user registration forms and add them anywhere on your website."
msgstr ""

#: src/Admin/Builder/Templates.php:1113
msgid "User Login Form"
msgstr ""

#: src/Admin/Builder/Templates.php:1119
msgid "Allow your users to easily log in to your site with their username and password."
msgstr ""

#: src/Admin/Builder/Templates.php:1122
msgid "User Password Reset Form"
msgstr ""

#: src/Admin/Builder/Templates.php:1128
msgid "Allow your users to easily reset their password."
msgstr ""

#: src/Admin/Builder/Templates.php:1148
msgid "Blog Post Submission Form"
msgstr ""

#: src/Admin/Builder/Templates.php:1154
msgid "User-submitted content made easy. Allow your users to submit guest blog posts in WordPress. You can add and remove fields as needed."
msgstr ""

#: src/Admin/Builder/Templates.php:1174
msgid "Survey Form"
msgstr ""

#: src/Admin/Builder/Templates.php:1180
msgid "Collect customer feedback, then generate survey reports to determine satisfaction and spot trends."
msgstr ""

#: src/Admin/Builder/Templates.php:1183
msgid "Poll Form"
msgstr ""

#: src/Admin/Builder/Templates.php:1189
msgid "Ask visitors a question and display the results after they provide an answer."
msgstr ""

#: src/Admin/Builder/Templates.php:1192
msgid "NPS Survey Simple Form"
msgstr ""

#: src/Admin/Builder/Templates.php:1198
msgid "Find out if your clients or customers would recommend you to someone else with this basic Net Promoter Score survey template."
msgstr ""

#: src/Admin/Builder/Templates.php:1201
msgid "NPS Survey Enhanced Form"
msgstr ""

#: src/Admin/Builder/Templates.php:1207
msgid "Measure customer loyalty and find out exactly what they are thinking with this enhanced Net Promoter Score survey template."
msgstr ""

#: src/Admin/Challenge.php:191
msgid "Challenge is frozen."
msgstr ""

#: src/Admin/Dashboard/Widget.php:247
msgid "Select timespan"
msgstr ""

#. translators: %d - number of days.
#: src/Admin/Dashboard/Widget.php:271
msgid "Last %d day"
msgid_plural "Last %d days"
msgstr[0] ""
msgstr[1] ""

#: src/Admin/Education/Admin/EditPost.php:173
#: src/Admin/Splash/SplashTrait.php:144
#: src/Integrations/Divi/Divi.php:224
#: templates/admin/empty-states/payments/get-started.php:34
#: templates/integrations/elementor/no-forms.php:26
#: assets/js/integrations/gutenberg/formselector-legacy.es5.js:261
#: assets/js/integrations/gutenberg/formselector-legacy.es5.js:304
#: assets/js/integrations/gutenberg/formselector-legacy.js:238
#: assets/js/integrations/gutenberg/formselector-legacy.js:275
#: assets/js/integrations/gutenberg/modules/common.js:786
#: assets/js/integrations/gutenberg/modules/common.js:1014
#: assets/lite/js/integrations/gutenberg/formselector.es5.js:3122
#: assets/lite/js/integrations/gutenberg/formselector.es5.js:3321
#: assets/pro/js/integrations/gutenberg/formselector.es5.js:3133
#: assets/pro/js/integrations/gutenberg/formselector.es5.js:3332
msgid "Get Started"
msgstr ""

#: src/Admin/Education/Admin/EditPost.php:186
msgid "Easily add your contact form"
msgstr ""

#: src/Admin/Education/Admin/EditPost.php:187
msgid "Oh hey, it looks like you're working on a contact page. Don't forget to embed your contact form. Click the plus icon above and search for WPForms."
msgstr ""

#: src/Admin/Education/Admin/EditPost.php:191
msgid "Embed your form"
msgstr ""

#: src/Admin/Education/Admin/EditPost.php:192
msgid "Then click on the WPForms block to embed your desired contact form."
msgstr ""

#: src/Admin/Education/Admin/EditPost.php:209
msgid "Don't forget to embed your contact form. Simply click the Add Form button below."
msgstr ""

#. translators: %1$s - link to create a new form.
#: src/Admin/Education/Admin/EditPost.php:211
#: src/Admin/Education/Admin/EditPost.php:237
msgid "Did you know that with <a href=\"%1$s\" target=\"_blank\" rel=\"noopener noreferrer\">WPForms</a>, you can create an easy-to-use contact form in a matter of minutes?"
msgstr ""

#: src/Admin/Education/Admin/EditPost.php:235
msgid "You've already created a form, now add it to the page so your customers can get in touch."
msgstr ""

#: src/Admin/Education/Admin/Settings/Geolocation.php:113
#: src/Forms/Fields/Address/Field.php:46
#: src/Forms/Fields/Address/Field.php:55
#: src/Forms/Fields/Address/Field.php:298
#: src/Forms/Fields/Address/Field.php:522
msgid "City"
msgstr ""

#: src/Admin/Education/Admin/Settings/Geolocation.php:114
msgid "Latitude/Longitude"
msgstr ""

#: src/Admin/Education/Admin/Settings/Geolocation.php:115
msgid "Google Places API"
msgstr ""

#: src/Admin/Education/Admin/Settings/Geolocation.php:116
#: src/Forms/Fields/Address/Field.php:59
#: src/Forms/Fields/Address/Field.php:430
#: src/Forms/Fields/Address/Field.php:525
#: src/Integrations/Stripe/Fields/PaymentElementCreditCard.php:225
msgid "Country"
msgstr ""

#: src/Admin/Education/Admin/Settings/Geolocation.php:117
#: src/Admin/Education/Builder/Geolocation.php:108
msgid "Address Autocomplete"
msgstr ""

#: src/Admin/Education/Admin/Settings/Geolocation.php:118
msgid "Mapbox API"
msgstr ""

#: src/Admin/Education/Admin/Settings/Geolocation.php:119
msgid "Postal/Zip Code"
msgstr ""

#: src/Admin/Education/Admin/Settings/Geolocation.php:120
msgid "Embedded Map in Forms"
msgstr ""

#: src/Admin/Education/Admin/Settings/Geolocation.php:126
msgid "Location Info in Entries"
msgstr ""

#: src/Admin/Education/Admin/Settings/Geolocation.php:131
msgid "Address Autocomplete Field"
msgstr ""

#: src/Admin/Education/Admin/Settings/Geolocation.php:136
msgid "Smart Address Field"
msgstr ""

#: src/Admin/Education/Admin/Settings/Geolocation.php:144
msgid "Do you want to learn more about visitors who fill out your online forms? Our geolocation addon allows you to collect and store your website visitors geolocation data along with their form submission. This insight can help you to be better informed and turn more leads into customers. Furthermore, add a smart address field that autocompletes using the Google Places API."
msgstr ""

#: src/Admin/Education/Admin/Settings/Geolocation.php:146
#: src/Lite/Admin/Settings/Access.php:167
msgid "Pro"
msgstr ""

#: src/Admin/Education/Admin/Settings/Geolocation.php:147
msgid "Powerful location-based insights and features…"
msgstr ""

#: src/Admin/Education/Builder/Calculations.php:113
msgid "NEW FEATURE"
msgstr ""

#: src/Admin/Education/Builder/Calculations.php:114
msgid "AI Calculations Are Here!"
msgstr ""

#. translators: %1$s - link to the WPForms.com doc article.
#: src/Admin/Education/Builder/Calculations.php:118
msgid "Easily create advanced calculations with WPForms AI. Head over to the <a href=\"#advanced-tab\">Advanced Tab</a> to get started or read <a href=\"%1$s\" target=\"_blank\" rel=\"noopener noreferrer\">our documentation</a> to learn more."
msgstr ""

#: src/Admin/Education/Builder/Calculations.php:139
#: src/Admin/Education/Builder/Calculations.php:175
#: templates/education/admin/edit-post/classic-notice.php:27
msgid "Dismiss this notice."
msgstr ""

#. translators: %1$s - link to the WPForms.com doc article.
#: src/Admin/Education/Builder/Calculations.php:158
msgid "Easily perform calculations based on user input. Head over to the <a href=\"#advanced-tab\">Advanced Tab</a> to get started or read <a href=\"%1$s\" target=\"_blank\" rel=\"noopener noreferrer\">our documentation</a> to learn more."
msgstr ""

#: src/Admin/Education/Builder/Calculations.php:239
msgid "Calculations"
msgstr ""

#: src/Admin/Education/Builder/Calculations.php:263
#: src/Admin/Education/Builder/Calculations.php:274
msgid "Enable Calculation"
msgstr ""

#: src/Admin/Education/Builder/Captcha.php:61
msgid "No form ID found."
msgstr ""

#: src/Admin/Education/Builder/Captcha.php:68
#: src/Admin/Pages/Analytics.php:530
#: src/Admin/Pages/SMTP.php:466
#: src/Lite/Admin/Education/LiteConnect.php:410
#: src/Logger/Log.php:200
#: src/Providers/Provider/Settings/PageIntegrations.php:323
msgid "You do not have permission."
msgstr ""

#: src/Admin/Education/Builder/Captcha.php:79
#: src/Admin/Forms/Tags.php:259
msgid "Something wrong. Please try again later."
msgstr ""

#: src/Admin/Education/Builder/Captcha.php:124
#: src/Frontend/Amp.php:360
msgid "hCaptcha"
msgstr ""

#: src/Admin/Education/Builder/Captcha.php:128
msgid "Cloudflare Turnstile"
msgstr ""

#: src/Admin/Education/Builder/Captcha.php:132
msgid "Google Checkbox v2 reCAPTCHA"
msgstr ""

#: src/Admin/Education/Builder/Captcha.php:133
msgid "Google Invisible v2 reCAPTCHA"
msgstr ""

#: src/Admin/Education/Builder/Captcha.php:134
msgid "Google v3 reCAPTCHA"
msgstr ""

#: src/Admin/Education/Builder/Captcha.php:154
msgid "Something wrong. Please, try again later."
msgstr ""

#. translators: %1$s - CAPTCHA settings page URL, %2$s - WPForms.com doc URL.
#: src/Admin/Education/Builder/Captcha.php:164
msgid "Please complete the setup in your <a href=\"%1$s\" target=\"_blank\">WPForms Settings</a>, and check out <a href=\"%2$s\" target=\"_blank\" rel=\"noopener noreferrer\">our guide</a> to learn about available CAPTCHA solutions."
msgstr ""

#. translators: %s - CAPTCHA name.
#: src/Admin/Education/Builder/Captcha.php:180
msgid "%s has been enabled for this form. Don't forget to save your form!"
msgstr ""

#. translators: %s - CAPTCHA name.
#: src/Admin/Education/Builder/Captcha.php:185
msgid "Are you sure you want to disable %s for this form?"
msgstr ""

#: src/Admin/Education/Builder/Fields.php:72
#: src/Integrations/AI/Admin/Builder/Forms.php:219
#: templates/education/admin/payments/single-page.php:50
#: templates/education/admin/settings/smtp-notice.php:37
msgid "Dismiss this notice"
msgstr ""

#: src/Admin/Education/Builder/Geolocation.php:111
msgid "We're sorry, Address Autocomplete is part of the Geolocation Addon and not available on your plan. Please upgrade to the PRO plan to unlock all these awesome features."
msgstr ""

#: src/Admin/Education/Builder/Geolocation.php:133
#: src/Admin/Education/Builder/Geolocation.php:144
msgid "Enable Address Autocomplete"
msgstr ""

#: src/Admin/Education/Core.php:98
msgid "Please specify a section."
msgstr ""

#: src/Admin/Education/Fields.php:37
#: src/Forms/Fields/Phone/Field.php:32
#: src/Integrations/ConstantContact/V3/ConstantContact.php:158
msgid "Phone"
msgstr ""

#: src/Admin/Education/Fields.php:45
#: src/Forms/Fields/Address/Field.php:34
#: src/Integrations/ConstantContact/V3/ConstantContact.php:164
msgid "Address"
msgstr ""

#: src/Admin/Education/Fields.php:53
#: src/Forms/Fields/DateTime/Field.php:60
msgid "Date / Time"
msgstr ""

#: src/Admin/Education/Fields.php:61
#: src/Forms/Fields/Url/Field.php:25
msgid "Website / URL"
msgstr ""

#: src/Admin/Education/Fields.php:69
#: src/Forms/Fields/FileUpload/Field.php:70
msgid "File Upload"
msgstr ""

#: src/Admin/Education/Fields.php:77
#: src/Forms/Fields/FileUpload/Field.php:718
#: src/Forms/Fields/Password/Field.php:25
#: src/Forms/Fields/Password/Field.php:247
msgid "Password"
msgstr ""

#: src/Admin/Education/Fields.php:85
msgid "Layout"
msgstr ""

#: src/Admin/Education/Fields.php:93
msgid "Repeater"
msgstr ""

#: src/Admin/Education/Fields.php:109
#: src/Forms/Fields/Divider/Field.php:25
msgid "Section Divider"
msgstr ""

#: src/Admin/Education/Fields.php:117
#: src/Forms/Fields/Richtext/Field.php:25
msgid "Rich Text"
msgstr ""

#: src/Admin/Education/Fields.php:133
#: src/Forms/Fields/Html/Field.php:25
msgid "HTML"
msgstr ""

#: src/Admin/Education/Fields.php:141
#: src/Forms/Fields/EntryPreview/Field.php:25
#: src/Forms/Fields/EntryPreview/Field.php:178
msgid "Entry Preview"
msgstr ""

#: src/Admin/Education/Fields.php:149
#: src/Forms/Fields/Rating/Field.php:35
#: src/Integrations/Gutenberg/FormSelector.php:589
msgid "Rating"
msgstr ""

#: src/Admin/Education/Fields.php:157
#: src/Forms/Fields/Hidden/Field.php:25
msgid "Hidden Field"
msgstr ""

#: src/Admin/Education/Fields.php:166
#: src/Forms/Fields/CustomCaptcha/Field.php:52
msgid "spam, math, maths, question"
msgstr ""

#: src/Admin/Education/Fields.php:175
#: src/Forms/Fields/Addons/Signature/Field.php:25
msgid "Signature"
msgstr ""

#: src/Admin/Education/Fields.php:176
#: src/Forms/Fields/Addons/Signature/Field.php:26
msgid "user, e-signature"
msgstr ""

#: src/Admin/Education/Fields.php:185
#: src/Forms/Fields/Addons/LikertScale/Field.php:25
msgid "Likert Scale"
msgstr ""

#: src/Admin/Education/Fields.php:186
#: src/Forms/Fields/Addons/LikertScale/Field.php:26
msgid "survey, rating scale"
msgstr ""

#: src/Admin/Education/Fields.php:195
#: src/Forms/Fields/Addons/NetPromoterScore/Field.php:25
msgid "Net Promoter Score"
msgstr ""

#: src/Admin/Education/Fields.php:196
#: src/Forms/Fields/Addons/NetPromoterScore/Field.php:26
msgid "survey, nps"
msgstr ""

#: src/Admin/Education/Fields.php:205
#: src/Db/Payments/ValueValidator.php:70
#: src/Forms/Fields/Helpers/RequirementsAlerts.php:175
msgid "PayPal Commerce"
msgstr ""

#: src/Admin/Education/Fields.php:206
#: src/Admin/Education/Fields.php:216
#: src/Admin/Education/Fields.php:226
#: src/Integrations/Square/Fields/Square.php:25
#: src/Integrations/Stripe/Fields/Traits/CreditCard.php:23
msgid "store, ecommerce, credit card, pay, payment, debit card"
msgstr ""

#: src/Admin/Education/Fields.php:215
#: src/Db/Payments/ValueValidator.php:72
#: src/Forms/Fields/Helpers/RequirementsAlerts.php:183
#: src/Integrations/Square/Admin/Settings.php:252
#: src/Integrations/Square/Fields/Square.php:24
msgid "Square"
msgstr ""

#: src/Admin/Education/Fields.php:225
msgid "Authorize.Net"
msgstr ""

#: src/Admin/Education/Fields.php:235
#: src/Admin/Payments/Views/Overview/Table.php:96
#: src/Admin/Payments/Views/Single.php:373
#: src/Admin/Payments/Views/Single.php:764
#: src/Forms/Fields/Addons/Coupon/Field.php:34
msgid "Coupon"
msgstr ""

#: src/Admin/Education/Fields.php:236
#: src/Forms/Fields/Addons/Coupon/Field.php:35
msgid "discount, sale"
msgstr ""

#: src/Admin/Education/Fields.php:296
msgid "captcha, spam, antispam"
msgstr ""

#: src/Admin/Education/Helpers.php:104
msgid "New"
msgstr ""

#: src/Admin/Education/Helpers.php:108
msgid "Featured"
msgstr ""

#: src/Admin/Education/Pointers/Payment.php:125
msgid "Payment and Donation Forms are here!"
msgstr ""

#. translators: %1$s - Payments page URL.
#: src/Admin/Education/Pointers/Payment.php:127
msgid "Now available for you: create forms that accept credit cards, Apple Pay, and Google Pay payments. Visit our new <a href=\"%1$s\" id=\"wpforms-education-pointers-payments\">Payments area</a> to get started."
msgstr ""

#. translators: %s - addon name.
#: src/Admin/Education/StringsTrait.php:31
msgid "The %s is installed but not activated. Would you like to activate it?"
msgstr ""

#: src/Admin/Education/StringsTrait.php:35
#: src/Integrations/AI/Admin/Builder/Forms.php:189
msgid "Yes, Activate"
msgstr ""

#: src/Admin/Education/StringsTrait.php:36
msgid "Addon activated"
msgstr ""

#: src/Admin/Education/StringsTrait.php:37
msgid "Plugin activated"
msgstr ""

#. translators: %s - addon name.
#: src/Admin/Education/StringsTrait.php:41
msgid "The %s is not installed. Would you like to install and activate it?"
msgstr ""

#: src/Admin/Education/StringsTrait.php:45
msgid "Yes, Install and Activate"
msgstr ""

#: src/Admin/Education/StringsTrait.php:46
#: src/Integrations/WPCode/WPCode.php:242
msgid "Installing"
msgstr ""

#: src/Admin/Education/StringsTrait.php:47
msgid "Almost done! Would you like to save and refresh the form builder?"
msgstr ""

#: src/Admin/Education/StringsTrait.php:48
msgid "Yes, save and refresh"
msgstr ""

#: src/Admin/Education/StringsTrait.php:49
msgid "Saving ..."
msgstr ""

#. translators: %s - addon name.
#: src/Admin/Education/StringsTrait.php:65
msgid "The %s is not installed. Please install and activate it to use this feature."
msgstr ""

#. translators: %s - addon name.
#: src/Admin/Education/StringsTrait.php:75
msgid "The %s is not installed. Please contact the site administrator."
msgstr ""

#. translators: %s - addon name.
#: src/Admin/Education/StringsTrait.php:87
msgid "The %s is not activated. Please contact the site administrator."
msgstr ""

#: src/Admin/Education/StringsTrait.php:103
msgid "<strong>Bonus:</strong> WPForms Lite users get <span>50% off</span> regular price, automatically applied at checkout."
msgstr ""

#. translators: %s - level name, either Pro or Elite.
#: src/Admin/Education/StringsTrait.php:143
msgid "is a %s Feature"
msgstr ""

#. translators: %s - level name, either Pro or Elite.
#: src/Admin/Education/StringsTrait.php:149
msgid "are a %s Feature"
msgstr ""

#. translators: %1$s - addon name, %2$s - level name, either Pro or Elite.
#: src/Admin/Education/StringsTrait.php:155
msgid "We're sorry, the %1$s is not available on your plan. Please upgrade to the %2$s plan to unlock all these awesome features."
msgstr ""

#. translators: %1$s - addon name, %2$s - level name, either Pro or Elite.
#: src/Admin/Education/StringsTrait.php:162
msgid "We're sorry, %1$s are not available on your plan. Please upgrade to the %2$s plan to unlock all these awesome features."
msgstr ""

#: src/Admin/Education/StringsTrait.php:170
msgid "Already purchased?"
msgstr ""

#. translators: %s - level name, either Pro or Elite.
#: src/Admin/Education/StringsTrait.php:174
msgid "Upgrade to %s"
msgstr ""

#: src/Admin/FlyoutMenu.php:63
msgid "See Quick Links"
msgstr ""

#: src/Admin/FlyoutMenu.php:119
msgid "Support & Docs"
msgstr ""

#: src/Admin/FlyoutMenu.php:124
msgid "Join Our Community"
msgstr ""

#: src/Admin/FlyoutMenu.php:129
#: src/Admin/Pages/Community.php:116
#: src/Admin/Pages/Community.php:118
msgid "Suggest a Feature"
msgstr ""

#: src/Admin/Forms/Ajax/Columns.php:79
msgid "Cannot save columns order."
msgstr ""

#: src/Admin/Forms/Ajax/Tags.php:226
msgid "No forms selected when trying to add a tag to them."
msgstr ""

#: src/Admin/Forms/BulkActions.php:291
#: src/Admin/Forms/Views.php:694
#: src/Admin/Payments/Views/Overview/Table.php:497
msgid "Restore"
msgstr ""

#: src/Admin/Forms/BulkActions.php:292
#: src/Admin/Forms/Views.php:715
#: src/Admin/Payments/Views/Overview/Table.php:498
msgid "Delete Permanently"
msgstr ""

#: src/Admin/Forms/BulkActions.php:296
#: src/Admin/Payments/Views/Overview/Table.php:503
msgid "Move to Trash"
msgstr ""

#. translators: %1$d - restored forms count.
#: src/Admin/Forms/BulkActions.php:426
msgid "%1$d form was successfully restored."
msgid_plural "%1$d forms were successfully restored."
msgstr[0] ""
msgstr[1] ""

#. translators: %1$d - deleted forms count.
#: src/Admin/Forms/BulkActions.php:431
msgid "%1$d form was successfully permanently deleted."
msgid_plural "%1$d forms were successfully permanently deleted."
msgstr[0] ""
msgstr[1] ""

#. translators: %1$d - duplicated forms count.
#: src/Admin/Forms/BulkActions.php:436
msgid "%1$d form was successfully duplicated."
msgid_plural "%1$d forms were successfully duplicated."
msgstr[0] ""
msgstr[1] ""

#. translators: %1$d - trashed forms count.
#: src/Admin/Forms/BulkActions.php:441
msgid "%1$d form was successfully moved to Trash."
msgid_plural "%1$d forms were successfully moved to Trash."
msgstr[0] ""
msgstr[1] ""

#. translators: %1$d - restored templates count.
#: src/Admin/Forms/BulkActions.php:468
msgid "%1$d template was successfully restored."
msgid_plural "%1$d templates were successfully restored."
msgstr[0] ""
msgstr[1] ""

#. translators: %1$d - deleted templates count.
#: src/Admin/Forms/BulkActions.php:473
msgid "%1$d template was successfully permanently deleted."
msgid_plural "%1$d templates were successfully permanently deleted."
msgstr[0] ""
msgstr[1] ""

#. translators: %1$d - duplicated templates count.
#: src/Admin/Forms/BulkActions.php:478
msgid "%1$d template was successfully duplicated."
msgid_plural "%1$d templates were successfully duplicated."
msgstr[0] ""
msgstr[1] ""

#. translators: %1$d - trashed templates count.
#: src/Admin/Forms/BulkActions.php:483
msgid "%1$d template was successfully moved to Trash."
msgid_plural "%1$d templates were successfully moved to Trash."
msgstr[0] ""
msgstr[1] ""

#: src/Admin/Forms/ListTable.php:156
#: src/Admin/Payments/Views/Overview/Helpers.php:50
#: src/Providers/Provider/Settings/PageIntegrations.php:160
#: templates/admin/payments/single/payment-details.php:48
msgid "N/A"
msgstr ""

#. translators: %1$s - Post created date.
#: src/Admin/Forms/ListTable.php:165
msgid "Created<br/>%1$s"
msgstr ""

#. translators: %1$s - Post modified date.
#: src/Admin/Forms/ListTable.php:173
msgid "Last Modified<br/>%1$s"
msgstr ""

#: src/Admin/Forms/ListTable.php:185
msgid "Entries are securely backed up in the cloud. Upgrade to restore."
msgstr ""

#: src/Admin/Forms/ListTable.php:318
#: src/Admin/Forms/Views.php:601
msgid "View preview"
msgstr ""

#: src/Admin/Forms/ListTable.php:335
#: src/Admin/Forms/Views.php:570
msgid "View entries"
msgstr ""

#: src/Admin/Forms/ListTable.php:352
msgid "Edit This Form"
msgstr ""

#: src/Admin/Forms/ListTable.php:451
msgid "No form templates found."
msgstr ""

#: src/Admin/Forms/ListTable.php:452
#: src/Admin/Tools/Views/Importer.php:162
msgid "No forms found."
msgstr ""

#: src/Admin/Forms/ListTable.php:594
msgid "0 items"
msgstr ""

#: src/Admin/Forms/Page.php:37
msgid "Pagination"
msgstr ""

#: src/Admin/Forms/Page.php:40
msgid "Number of forms per page:"
msgstr ""

#: src/Admin/Forms/Page.php:54
msgid "View"
msgstr ""

#: src/Admin/Forms/Page.php:57
msgid "Show form templates"
msgstr ""

#: src/Admin/Forms/Page.php:263
msgid "Forms Overview"
msgstr ""

#: src/Admin/Forms/Page.php:269
msgid "Add New"
msgstr ""

#: src/Admin/Forms/Page.php:299
msgid "Search Forms"
msgstr ""

#: src/Admin/Forms/Page.php:345
#: src/Forms/Fields/Addons/LikertScale/Field.php:175
msgid "Columns"
msgstr ""

#. translators: %1$d - number of forms found, %2$s - search term.
#: src/Admin/Forms/Search.php:232
msgid "Found <strong>%1$d form</strong> containing <em>\"%2$s\"</em>"
msgid_plural "Found <strong>%1$d forms</strong> containing <em>\"%2$s\"</em>"
msgstr[0] ""
msgstr[1] ""

#: src/Admin/Forms/Table/Facades/Columns.php:63
msgid "ID"
msgstr ""

#: src/Admin/Forms/Table/Facades/Columns.php:73
msgid "Author"
msgstr ""

#: src/Admin/Forms/Table/Facades/Columns.php:76
msgid "Shortcode"
msgstr ""

#: src/Admin/Forms/Table/Facades/Columns.php:79
#: src/Admin/Payments/Views/Overview/Table.php:84
#: src/Forms/Fields/DateTime/Field.php:132
#: src/Forms/Fields/DateTime/Field.php:208
#: src/Forms/Fields/DateTime/Field.php:647
#: src/Forms/Fields/DateTime/Field.php:653
#: src/Logger/ListTable.php:294
#: src/Logger/ListTable.php:486
#: src/SmartTags/SmartTags.php:121
#: templates/admin/payments/single/payment-history.php:30
#: templates/admin/payments/single/payment-history.php:47
msgid "Date"
msgstr ""

#: src/Admin/Forms/Tags.php:181
msgid "No tags to choose from"
msgstr ""

#: src/Admin/Forms/Tags.php:260
#: src/Admin/Forms/Tags.php:504
msgid "All Tags"
msgstr ""

#: src/Admin/Forms/Tags.php:262
msgid "<strong>1 form</strong> selected for Bulk Edit."
msgstr ""

#. translators: %d - number of forms selected for Bulk Edit.
#: src/Admin/Forms/Tags.php:266
msgid "<strong>%d forms</strong> selected for Bulk Edit."
msgstr ""

#: src/Admin/Forms/Tags.php:269
#: src/Admin/Forms/Tags.php:515
msgid "Manage Tags"
msgstr ""

#: src/Admin/Forms/Tags.php:270
msgid "Delete tags that you're no longer using. Deleting a tag will remove it from a form, but will not delete the form itself."
msgstr ""

#: src/Admin/Forms/Tags.php:271
msgid "Delete Tags"
msgstr ""

#: src/Admin/Forms/Tags.php:273
msgid "You have <strong>1 tag</strong> selected for deletion."
msgstr ""

#. translators: %d - number of forms selected for Bulk Edit.
#: src/Admin/Forms/Tags.php:277
msgid "You have <strong>%d tags</strong> selected for deletion."
msgstr ""

#: src/Admin/Forms/Tags.php:281
msgid "There are no tags to delete.<br>Please create at least one by adding it to any form."
msgstr ""

#: src/Admin/Forms/Tags.php:284
msgid "1 tag was successfully deleted."
msgstr ""

#. translators: %d - number of deleted tags.
#: src/Admin/Forms/Tags.php:286
msgid "%d tags were successfully deleted."
msgstr ""

#: src/Admin/Forms/Tags.php:287
msgid "Almost done!"
msgstr ""

#: src/Admin/Forms/Tags.php:288
msgid "In order to update the tags in the forms list, please refresh the page."
msgstr ""

#: src/Admin/Forms/Tags.php:289
msgid "Refresh"
msgstr ""

#. translators: used between list items, there is a space after the comma.
#: src/Admin/Forms/Tags.php:392
msgid ", "
msgstr ""

#: src/Admin/Forms/Tags.php:416
#: src/Admin/Forms/Views.php:553
#: src/Admin/Tools/Views/Importer.php:330
#: templates/builder/field-context-menu.php:24
msgid "Edit"
msgstr ""

#: src/Admin/Forms/Tags.php:453
msgid "Save changes"
msgstr ""

#: src/Admin/Forms/Tags.php:514
#: templates/admin/payments/tablenav-filters.php:35
msgid "Filter"
msgstr ""

#: src/Admin/Forms/Tags.php:590
msgid "Edit Tags"
msgstr ""

#. translators: %s - delay in formatted time.
#: src/Admin/Forms/UserTemplates.php:188
msgid "Form template entries are for testing purposes and will be automatically deleted after %s."
msgstr ""

#. translators: %s - delay in formatted time.
#: src/Admin/Forms/UserTemplates.php:242
msgid "Form template entries are for testing purposes. This entry will be automatically deleted in %s."
msgstr ""

#: src/Admin/Forms/UserTemplates.php:333
#: src/Admin/Traits/FormTemplates.php:455
msgid "Create Form"
msgstr ""

#: src/Admin/Forms/UserTemplates.php:334
msgid "Edit Template"
msgstr ""

#: src/Admin/Forms/UserTemplates.php:367
msgid "You do not have permission to delete this template."
msgstr ""

#: src/Admin/Forms/UserTemplates.php:374
msgid "Template not found."
msgstr ""

#: src/Admin/Forms/UserTemplates.php:381
msgid "Failed to delete the template."
msgstr ""

#: src/Admin/Forms/Views.php:73
#: src/Admin/Payments/Views/Overview/Table.php:1106
msgid "All"
msgstr ""

#: src/Admin/Forms/Views.php:78
#: src/Admin/Forms/Views.php:648
#: src/Admin/Payments/Views/Overview/Table.php:1120
msgid "Trash"
msgstr ""

#: src/Admin/Forms/Views.php:95
msgid "Forms"
msgstr ""

#: src/Admin/Forms/Views.php:103
msgid "Templates"
msgstr ""

#: src/Admin/Forms/Views.php:552
msgid "Edit this template"
msgstr ""

#: src/Admin/Forms/Views.php:552
msgid "Edit this form"
msgstr ""

#: src/Admin/Forms/Views.php:591
msgid "View payments"
msgstr ""

#: src/Admin/Forms/Views.php:622
msgid "Duplicate this template"
msgstr ""

#: src/Admin/Forms/Views.php:622
msgid "Duplicate this form"
msgstr ""

#: src/Admin/Forms/Views.php:624
#: templates/builder/field-context-menu.php:34
msgid "Duplicate"
msgstr ""

#: src/Admin/Forms/Views.php:647
msgid "Move this form template to trash"
msgstr ""

#: src/Admin/Forms/Views.php:647
msgid "Move this form to trash"
msgstr ""

#: src/Admin/Forms/Views.php:693
msgid "Restore this template"
msgstr ""

#: src/Admin/Forms/Views.php:693
msgid "Restore this form"
msgstr ""

#: src/Admin/Forms/Views.php:713
msgid "Delete this template permanently"
msgstr ""

#: src/Admin/Forms/Views.php:713
msgid "Delete this form permanently"
msgstr ""

#. translators: %1$d - number of forms found in the trash, %2$s - search term.
#: src/Admin/Forms/Views.php:743
msgid "Found <strong>%1$d form</strong> in <em>the trash</em> containing <em>\"%2$s\"</em>"
msgid_plural "Found <strong>%1$d forms</strong> in <em>the trash</em> containing <em>\"%2$s\"</em>"
msgstr[0] ""
msgstr[1] ""

#: src/Admin/Forms/Views.php:795
msgid "Empty Trash"
msgstr ""

#: src/Admin/Helpers/Datepicker.php:415
msgid "Today"
msgstr ""

#: src/Admin/Helpers/Datepicker.php:416
msgid "Yesterday"
msgstr ""

#: src/Admin/Helpers/Datepicker.php:417
msgid "Last 7 days"
msgstr ""

#: src/Admin/Helpers/Datepicker.php:418
msgid "Last 30 days"
msgstr ""

#: src/Admin/Helpers/Datepicker.php:419
msgid "Last 90 days"
msgstr ""

#: src/Admin/Helpers/Datepicker.php:420
msgid "Last 1 year"
msgstr ""

#: src/Admin/Helpers/Datepicker.php:421
#: src/Admin/Traits/FormTemplates.php:382
msgid "Custom"
msgstr ""

#: src/Admin/Notifications/EventDriven.php:522
msgid "Welcome to WPForms!"
msgstr ""

#. translators: %s - number of templates.
#: src/Admin/Notifications/EventDriven.php:524
msgid "We’re grateful that you chose WPForms for your website! Now that you’ve installed the plugin, you’re less than 5 minutes away from publishing your first form. To make it easy, we’ve got %s form templates to get you started!"
msgstr ""

#: src/Admin/Notifications/EventDriven.php:530
msgid "Start Building"
msgstr ""

#: src/Admin/Notifications/EventDriven.php:537
msgid "Read the Guide"
msgstr ""

#: src/Admin/Notifications/EventDriven.php:554
msgid "Don’t Miss Your Form Notification Emails!"
msgstr ""

#: src/Admin/Notifications/EventDriven.php:555
msgid "Did you know that many WordPress sites are not properly configured to send emails? With the free WP Mail SMTP plugin, you can easily optimize your site to send emails, avoid the spam folder, and make sure your emails land in the recipient’s inbox every time."
msgstr ""

#: src/Admin/Notifications/EventDriven.php:559
#: src/Admin/Notifications/EventDriven.php:619
#: src/Admin/Notifications/EventDriven.php:623
#: src/Admin/Notifications/EventDriven.php:672
#: src/Admin/Notifications/EventDriven.php:676
#: src/Admin/Notifications/EventDriven.php:739
#: src/Admin/Notifications/EventDriven.php:743
#: src/Admin/Pages/Analytics.php:169
#: src/Admin/Pages/Analytics.php:504
#: src/Admin/Pages/SMTP.php:186
#: src/Integrations/UncannyAutomator/UncannyAutomator.php:116
msgid "Install Now"
msgstr ""

#: src/Admin/Notifications/EventDriven.php:575
msgid "Want to Be a VIP? Join Now!"
msgstr ""

#: src/Admin/Notifications/EventDriven.php:576
msgid "Running a WordPress site can be challenging. But help is just around the corner! Our Facebook group contains tons of tips and help to get your business growing! When you join our VIP Circle, you’ll get instant access to tips, tricks, and answers from a community of loyal WPForms users. Best of all, membership is 100% free!"
msgstr ""

#: src/Admin/Notifications/EventDriven.php:580
msgid "Join Now"
msgstr ""

#: src/Admin/Notifications/EventDriven.php:588
msgid "Want to Know What Your Customers Really Think?"
msgstr ""

#: src/Admin/Notifications/EventDriven.php:589
msgid "Nothing beats real feedback from your customers and visitors. That’s why many small businesses love our awesome Surveys and Polls addon. Instantly unlock full survey reporting right in your WordPress dashboard. And don’t forget: building a survey is easy with our pre-made templates, so you could get started within a few minutes!"
msgstr ""

#: src/Admin/Notifications/EventDriven.php:641
msgid "Get More Leads From Your Forms!"
msgstr ""

#: src/Admin/Notifications/EventDriven.php:642
msgid "Are your forms converting fewer visitors than you hoped? Often, visitors quit forms partway through. That can prevent you from getting all the leads you deserve to capture. With our Form Abandonment addon, you can capture partial entries even if your visitor didn’t hit Submit! From there, it’s easy to follow up with leads and turn them into loyal customers."
msgstr ""

#: src/Admin/Notifications/EventDriven.php:694
msgid "What’s Your Dream WPForms Feature?"
msgstr ""

#: src/Admin/Notifications/EventDriven.php:695
msgid "If you could add just one feature to WPForms, what would it be? We want to know! Our team is busy surveying valued customers like you as we plan the year ahead. We’d love to know which features would take your business to the next level! Do you have a second to share your idea with us?"
msgstr ""

#: src/Admin/Notifications/EventDriven.php:699
msgid "Share Your Idea"
msgstr ""

#: src/Admin/Notifications/EventDriven.php:708
msgid "Congratulations! You Just Got Your 100th Form Entry!"
msgstr ""

#: src/Admin/Notifications/EventDriven.php:709
msgid "You just hit 100 entries&hellip; and this is just the beginning! Now it’s time to dig into the data and figure out what makes your visitors tick. The User Journey addon shows you what your visitors looked at before submitting your form. Now you can easily find which areas of your site are triggering form conversions."
msgstr ""

#: src/Admin/Notifications/Notifications.php:790
msgid "Watch Video"
msgstr ""

#. translators: %s - Lite plugin download URL.
#: src/Admin/Pages/Analytics.php:143
#: src/Admin/Pages/SMTP.php:160
msgid "Could not install the plugin automatically. Please <a href=\"%s\">download</a> it and install it manually."
msgstr ""

#. translators: %s - Lite plugin download URL.
#: src/Admin/Pages/Analytics.php:155
#: src/Admin/Pages/SMTP.php:172
msgid "Could not activate the plugin. Please activate it on the <a href=\"%s\">Plugins page</a>."
msgstr ""

#: src/Admin/Pages/Analytics.php:166
#: src/Admin/Pages/SMTP.php:183
#: src/Integrations/AI/Admin/Builder/Forms.php:193
msgid "Installing..."
msgstr ""

#: src/Admin/Pages/Analytics.php:167
#: src/Admin/Pages/SMTP.php:184
#: src/Integrations/AI/Admin/Builder/Forms.php:194
msgid "Activating..."
msgstr ""

#: src/Admin/Pages/Analytics.php:168
#: src/Admin/Pages/Analytics.php:422
msgid "MonsterInsights Installed & Activated"
msgstr ""

#: src/Admin/Pages/Analytics.php:170
#: src/Admin/Pages/Analytics.php:504
#: src/Admin/Pages/SMTP.php:187
#: src/Integrations/UncannyAutomator/UncannyAutomator.php:116
msgid "Activate Now"
msgstr ""

#: src/Admin/Pages/Analytics.php:171
#: src/Admin/Pages/SMTP.php:188
msgid "Download Now"
msgstr ""

#: src/Admin/Pages/Analytics.php:172
#: src/Admin/Pages/SMTP.php:189
msgid "Go to Plugins page"
msgstr ""

#: src/Admin/Pages/Analytics.php:214
msgid "WPForms ♥ MonsterInsights"
msgstr ""

#: src/Admin/Pages/Analytics.php:215
msgid "The Best Google Analytics Plugin for WordPress"
msgstr ""

#: src/Admin/Pages/Analytics.php:216
msgid "MonsterInsights connects WPForms to Google Analytics, providing a powerful integration with their Forms addon. MonsterInsights is a sister company of WPForms."
msgstr ""

#: src/Admin/Pages/Analytics.php:242
msgid "Analytics screenshot"
msgstr ""

#: src/Admin/Pages/Analytics.php:244
#: src/Admin/Pages/Analytics.php:398
msgid "Track form impressions and conversions."
msgstr ""

#: src/Admin/Pages/Analytics.php:245
msgid "View form conversion rates from WordPress."
msgstr ""

#: src/Admin/Pages/Analytics.php:246
msgid "Complete UTM tracking with form entries."
msgstr ""

#: src/Admin/Pages/Analytics.php:247
msgid "Automatic integration with WPForms."
msgstr ""

#: src/Admin/Pages/Analytics.php:308
#: src/Admin/Pages/SMTP.php:331
msgid "Step 1"
msgstr ""

#: src/Admin/Pages/Analytics.php:342
#: src/Admin/Pages/SMTP.php:365
msgid "Step 2"
msgstr ""

#: src/Admin/Pages/Analytics.php:343
msgid "Setup MonsterInsights"
msgstr ""

#: src/Admin/Pages/Analytics.php:344
msgid "MonsterInsights has an intuitive setup wizard to guide you through the setup process."
msgstr ""

#: src/Admin/Pages/Analytics.php:378
msgid "Step 3"
msgstr ""

#: src/Admin/Pages/Analytics.php:379
msgid "Get Form Conversion Tracking"
msgstr ""

#: src/Admin/Pages/Analytics.php:380
msgid "With the MonsterInsights Form addon you can easily track your form views, entries, conversion rates, and more."
msgstr ""

#: src/Admin/Pages/Analytics.php:397
msgid "Install & Activate MonsterInsights"
msgstr ""

#: src/Admin/Pages/Analytics.php:408
msgid "Install MonsterInsights"
msgstr ""

#: src/Admin/Pages/Analytics.php:416
msgid "MonsterInsights on WordPress.org"
msgstr ""

#: src/Admin/Pages/Analytics.php:422
msgid "Activate MonsterInsights"
msgstr ""

#: src/Admin/Pages/Analytics.php:451
msgid "Run Setup Wizard"
msgstr ""

#: src/Admin/Pages/Analytics.php:457
msgid "Setup Complete"
msgstr ""

#: src/Admin/Pages/Analytics.php:540
#: src/Admin/Pages/SMTP.php:476
msgid "Plugin unavailable."
msgstr ""

#: src/Admin/Pages/Community.php:66
msgid "WPForms VIP Circle Facebook Group"
msgstr ""

#: src/Admin/Pages/Community.php:67
msgid "Powered by the community, for the community. Anything and everything WPForms: Discussions. Questions. Tutorials. Insights and sneak peaks. Also, exclusive giveaways!"
msgstr ""

#: src/Admin/Pages/Community.php:68
msgid "Join WPForms VIP Circle"
msgstr ""

#: src/Admin/Pages/Community.php:76
msgid "WPForms Announcements"
msgstr ""

#: src/Admin/Pages/Community.php:77
msgid "Check out the latest releases from WPForms. Our team is always innovating to bring you powerful features and functionality that are simple to use. Every release is designed with you in mind!"
msgstr ""

#: src/Admin/Pages/Community.php:78
msgid "View WPForms Announcements"
msgstr ""

#: src/Admin/Pages/Community.php:86
msgid "WPForms YouTube Channel"
msgstr ""

#: src/Admin/Pages/Community.php:87
msgid "Take a visual dive into everything WPForms has to offer. From simple contact forms to advanced payment forms and email marketing integrations, our extensive video collection covers it all."
msgstr ""

#: src/Admin/Pages/Community.php:88
msgid "Visit WPForms YouTube Channel"
msgstr ""

#: src/Admin/Pages/Community.php:96
msgid "WPForms Developer Documentation"
msgstr ""

#: src/Admin/Pages/Community.php:97
msgid "Customize and extend WPForms with code. Our comprehensive developer resources include tutorials, snippets, and documentation on core actions, filters, functions, and more."
msgstr ""

#: src/Admin/Pages/Community.php:98
msgid "View WPForms Dev Docs"
msgstr ""

#: src/Admin/Pages/Community.php:106
msgid "WPBeginner Engage Facebook Group"
msgstr ""

#: src/Admin/Pages/Community.php:107
msgid "Hang out with other WordPress experts and like minded website owners such as yourself! Hosted by WPBeginner, the largest free WordPress site for beginners."
msgstr ""

#: src/Admin/Pages/Community.php:108
msgid "Join WPBeginner Engage"
msgstr ""

#: src/Admin/Pages/Community.php:117
msgid "Do you have an idea or suggestion for WPForms? If you have thoughts on features, integrations, addons, or improvements - we want to hear it! We appreciate all feedback and insight from our users."
msgstr ""

#: src/Admin/Pages/SMTP.php:185
#: src/Admin/Pages/SMTP.php:411
msgid "WP Mail SMTP Installed & Activated"
msgstr ""

#: src/Admin/Pages/SMTP.php:194
#: src/Admin/Pages/SMTP.php:445
msgid "Go to SMTP settings"
msgstr ""

#: src/Admin/Pages/SMTP.php:195
#: src/Admin/Pages/SMTP.php:436
msgid "Open Setup Wizard"
msgstr ""

#: src/Admin/Pages/SMTP.php:234
msgid "WPForms ♥ WP Mail SMTP"
msgstr ""

#: src/Admin/Pages/SMTP.php:235
msgid "Making Email Deliverability Easy for WordPress"
msgstr ""

#: src/Admin/Pages/SMTP.php:236
msgid "WP Mail SMTP fixes deliverability problems with your WordPress emails and form notifications. It's built by the same folks behind WPForms."
msgstr ""

#: src/Admin/Pages/SMTP.php:262
msgid "WP Mail SMTP screenshot"
msgstr ""

#: src/Admin/Pages/SMTP.php:264
msgid "Improves email deliverability in WordPress."
msgstr ""

#: src/Admin/Pages/SMTP.php:265
msgid "Used by 2+ million websites."
msgstr ""

#: src/Admin/Pages/SMTP.php:266
msgid "Free mailers: SendLayer, SMTP.com, Brevo, Google Workspace / Gmail, Mailgun, Postmark, SendGrid."
msgstr ""

#: src/Admin/Pages/SMTP.php:267
msgid "Pro mailers: Amazon SES, Microsoft 365 / Outlook.com, Zoho Mail."
msgstr ""

#: src/Admin/Pages/SMTP.php:366
msgid "Set Up WP Mail SMTP"
msgstr ""

#: src/Admin/Pages/SMTP.php:367
msgid "Select and configure your mailer."
msgstr ""

#: src/Admin/Pages/SMTP.php:385
msgid "Install and Activate WP Mail SMTP"
msgstr ""

#: src/Admin/Pages/SMTP.php:386
msgid "Install WP Mail SMTP from the WordPress.org plugin repository."
msgstr ""

#: src/Admin/Pages/SMTP.php:396
msgid "Install WP Mail SMTP"
msgstr ""

#: src/Admin/Pages/SMTP.php:404
msgid "WP Mail SMTP on WordPress.org"
msgstr ""

#: src/Admin/Pages/SMTP.php:411
msgid "Activate WP Mail SMTP"
msgstr ""

#: src/Admin/Pages/SMTP.php:440
msgid "Start Setup"
msgstr ""

#: src/Admin/Pages/Templates.php:118
msgid "Get a Head Start With Our Pre-Made Form Templates"
msgstr ""

#. translators: %1$s - create template doc link; %2$s - Contact us page link.
#: src/Admin/Pages/Templates.php:125
msgid "Choose a template to speed up the process of creating your form. You can also start with a <a href=\"#\" class=\"wpforms-trigger-blank\">blank form</a> or <a href=\"%1$s\" target=\"_blank\" rel=\"noopener noreferrer\">create your own</a>. <br>Have a suggestion for a new template? <a href=\"%2$s\" target=\"_blank\" rel=\"noopener noreferrer\">We’d love to hear it</a>!"
msgstr ""

#: src/Admin/Payments/ScreenOptions.php:93
msgid "Number of payments per page:"
msgstr ""

#: src/Admin/Payments/ScreenOptions.php:119
msgid "Advanced details"
msgstr ""

#: src/Admin/Payments/ScreenOptions.php:120
#: src/Logger/ListTable.php:45
#: src/Logger/Log.php:105
#: templates/admin/payments/single/log.php:19
msgid "Log"
msgstr ""

#: src/Admin/Payments/ScreenOptions.php:124
msgid "Additional information"
msgstr ""

#: src/Admin/Payments/ScreenOptions.php:140
#: src/Forms/Fields/Addons/Coupon/Field.php:166
#: src/Forms/Fields/Addons/Coupon/Field.php:284
#: src/Logger/ListTable.php:462
#: templates/admin/components/datepicker.php:73
msgid "Apply"
msgstr ""

#: src/Admin/Payments/Views/Coupons/Education.php:53
#: src/Forms/Fields/Helpers/RequirementsAlerts.php:171
msgid "Coupons"
msgstr ""

#: src/Admin/Payments/Views/Coupons/Education.php:148
msgid "Custom Coupon Codes"
msgstr ""

#: src/Admin/Payments/Views/Coupons/Education.php:149
msgid "Percentage or Fixed Discounts"
msgstr ""

#: src/Admin/Payments/Views/Coupons/Education.php:150
msgid "Start and End Dates"
msgstr ""

#: src/Admin/Payments/Views/Coupons/Education.php:151
msgid "Maximum Usage Limit"
msgstr ""

#: src/Admin/Payments/Views/Coupons/Education.php:152
msgid "Once Per Email Address Limit"
msgstr ""

#: src/Admin/Payments/Views/Coupons/Education.php:153
msgid "Usage Statistics"
msgstr ""

#: src/Admin/Payments/Views/Coupons/Education.php:159
msgid "Coupons Overview"
msgstr ""

#: src/Admin/Payments/Views/Coupons/Education.php:164
msgid "Coupon Settings"
msgstr ""

#. translators: %1$s - WPForms.com Upgrade page URL.
#: src/Admin/Payments/Views/Coupons/Education.php:171
msgid "With the Coupons addon, you can offer customers discounts using custom coupon codes. Create your own percentage or fixed rate discount, then add the Coupon field to any payment form. When a customer enters your unique code, they’ll receive the specified discount. You can also add limits to restrict when coupons are available and how often they can be used. The Coupons addon requires a license level of Pro or higher.%s"
msgstr ""

#: src/Admin/Payments/Views/Coupons/Education.php:184
msgid "Easy to Use, Yet Powerful"
msgstr ""

#: src/Admin/Payments/Views/Overview/BulkActions.php:89
#: src/Providers/Provider/Settings/PageIntegrations.php:314
#: src/Providers/Provider/Settings/PageIntegrations.php:364
msgid "Your session expired. Please reload the page."
msgstr ""

#. translators: %d - number of deleted payments.
#: src/Admin/Payments/Views/Overview/BulkActions.php:183
msgid "%d payment was successfully permanently deleted."
msgid_plural "%d payments were successfully permanently deleted."
msgstr[0] ""
msgstr[1] ""

#. translators: %d - number of restored payments.
#: src/Admin/Payments/Views/Overview/BulkActions.php:188
msgid "%d payment was successfully restored."
msgid_plural "%d payments were successfully restored."
msgstr[0] ""
msgstr[1] ""

#. translators: %d - number of trashed payments.
#: src/Admin/Payments/Views/Overview/BulkActions.php:193
msgid "%d payment was successfully moved to the Trash."
msgid_plural "%d payments were successfully moved to the Trash."
msgstr[0] ""
msgstr[1] ""

#: src/Admin/Payments/Views/Overview/Chart.php:92
msgid "Payments Summary"
msgstr ""

#: src/Admin/Payments/Views/Overview/Chart.php:147
msgid "Viewing Test Data"
msgstr ""

#: src/Admin/Payments/Views/Overview/Chart.php:168
#: src/Admin/Payments/Views/Overview/Page.php:155
msgid "No payments for selected period"
msgstr ""

#: src/Admin/Payments/Views/Overview/Chart.php:169
msgid "Please select a different period or check back later."
msgstr ""

#: src/Admin/Payments/Views/Overview/Chart.php:216
msgid "Total Payments"
msgstr ""

#: src/Admin/Payments/Views/Overview/Chart.php:222
msgid "Total Sales"
msgstr ""

#: src/Admin/Payments/Views/Overview/Chart.php:235
msgid "Total Refunded"
msgstr ""

#: src/Admin/Payments/Views/Overview/Chart.php:244
msgid "New Subscriptions"
msgstr ""

#: src/Admin/Payments/Views/Overview/Chart.php:261
msgid "Subscription Renewals"
msgstr ""

#: src/Admin/Payments/Views/Overview/Chart.php:278
msgid "Coupons Redeemed"
msgstr ""

#: src/Admin/Payments/Views/Overview/Page.php:75
#: templates/emails/summary-body.php:119
msgid "Overview"
msgstr ""

#: src/Admin/Payments/Views/Overview/Page.php:152
#: src/Providers/Provider/Settings/FormBuilder.php:269
#: templates/admin/payments/single/details.php:79
#: templates/builder/field-context-menu.php:44
#: templates/builder/templates-item.php:78
msgid "Delete"
msgstr ""

#: src/Admin/Payments/Views/Overview/Page.php:156
msgid "No sales for selected period"
msgstr ""

#: src/Admin/Payments/Views/Overview/Page.php:157
msgid "No refunds for selected period"
msgstr ""

#: src/Admin/Payments/Views/Overview/Page.php:158
msgid "No new subscriptions for selected period"
msgstr ""

#: src/Admin/Payments/Views/Overview/Page.php:159
msgid "No subscription renewals for the selected period"
msgstr ""

#: src/Admin/Payments/Views/Overview/Page.php:160
msgid "No coupons applied during the selected period"
msgstr ""

#: src/Admin/Payments/Views/Overview/Page.php:367
msgid "First you need to set up a payment gateway. We've partnered with <strong>Stripe and Square</strong> to bring easy payment forms to everyone.&nbsp;"
msgstr ""

#. translators: %s - WPForms Addons admin page URL.
#: src/Admin/Payments/Views/Overview/Page.php:370
msgid "Other payment gateways such as <strong>PayPal</strong> and <strong>Authorize.Net</strong> can be installed from the <a href=\"%s\">Addons screen</a>."
msgstr ""

#. translators: %s - WPForms.com Upgrade page URL.
#: src/Admin/Payments/Views/Overview/Page.php:381
msgid "If you'd like to use another payment gateway, please consider <a href='%s'>upgrading to WPForms Pro</a>."
msgstr ""

#. translators: WPForms.com docs page URL.
#: src/Admin/Payments/Views/Overview/Page.php:498
msgid "Deleting one or more selected payments may prevent processing of future subscription renewals. Payment filtering may also be affected. <a href=\"%1$s\" rel=\"noopener\" target=\"_blank\">Learn More</a>"
msgstr ""

#: src/Admin/Payments/Views/Overview/Table.php:83
#: src/Logger/Log.php:106
msgid "Payment"
msgstr ""

#: src/Admin/Payments/Views/Overview/Table.php:88
msgid "Gateway"
msgstr ""

#: src/Admin/Payments/Views/Overview/Table.php:99
#: src/Admin/Payments/Views/Single.php:350
#: src/Forms/Fields/PaymentTotal/Field.php:24
#: src/Forms/Fields/PaymentTotal/Field.php:555
#: src/Forms/Fields/PaymentTotal/Field.php:616
#: src/SmartTags/SmartTag/OrderSummary.php:164
#: templates/admin/payments/single/payment-history.php:32
#: templates/admin/payments/single/payment-history.php:53
#: templates/fields/total/summary-preview.php:85
msgid "Total"
msgstr ""

#: src/Admin/Payments/Views/Overview/Table.php:102
#: src/Db/Payments/ValueValidator.php:156
msgid "Subscription"
msgstr ""

#: src/Admin/Payments/Views/Overview/Table.php:106
#: templates/admin/payments/single/payment-history.php:33
#: templates/admin/payments/single/payment-history.php:56
msgid "Status"
msgstr ""

#: src/Admin/Payments/Views/Overview/Table.php:225
msgid "No payments found in the trash."
msgstr ""

#: src/Admin/Payments/Views/Overview/Table.php:231
msgid "No payments found, please try a different search."
msgstr ""

#: src/Admin/Payments/Views/Overview/Table.php:236
msgid "No payments found."
msgstr ""

#: src/Admin/Payments/Views/Overview/Table.php:328
msgid "Search Payments"
msgstr ""

#: src/Admin/Payments/Views/Overview/Table.php:352
msgid "types"
msgstr ""

#: src/Admin/Payments/Views/Overview/Table.php:356
msgid "gateways"
msgstr ""

#: src/Admin/Payments/Views/Overview/Table.php:360
msgid "subscriptions"
msgstr ""

#. translators: %s - plural label.
#: src/Admin/Payments/Views/Overview/Table.php:433
msgid "Multiple %s selected"
msgstr ""

#. translators: %s - plural label.
#: src/Admin/Payments/Views/Overview/Table.php:437
msgid "All %s"
msgstr ""

#: src/Admin/Payments/Views/Overview/Table.php:464
msgid "Select which field to use when searching for payments"
msgstr ""

#: src/Admin/Payments/Views/Overview/Table.php:473
msgid "Select which comparison method to use when searching for payments"
msgstr ""

#: src/Admin/Payments/Views/Overview/Table.php:642
msgid "Payment Title"
msgstr ""

#: src/Admin/Payments/Views/Overview/Table.php:643
#: src/Admin/Payments/Views/Single.php:741
msgid "Transaction ID"
msgstr ""

#: src/Admin/Payments/Views/Overview/Table.php:644
#: src/Integrations/Square/Admin/Builder/Traits/Content.php:172
#: src/Integrations/Stripe/Admin/Builder/Traits/ContentTrait.php:390
#: src/Integrations/Stripe/Admin/Builder/Traits/ContentTrait.php:465
msgid "Customer Email"
msgstr ""

#: src/Admin/Payments/Views/Overview/Table.php:645
#: src/Admin/Payments/Views/Single.php:746
msgid "Subscription ID"
msgstr ""

#: src/Admin/Payments/Views/Overview/Table.php:646
msgid "Last 4 digits of credit card"
msgstr ""

#: src/Admin/Payments/Views/Overview/Table.php:647
msgid "Any payment field"
msgstr ""

#: src/Admin/Payments/Views/Overview/Table.php:700
msgid "equals"
msgstr ""

#. translators: %s - relative time difference, e.g. "5 minutes", "12 days".
#: src/Admin/Payments/Views/Overview/Table.php:826
#: src/Admin/Revisions.php:319
msgid "%s ago"
msgstr ""

#: src/Admin/Payments/Views/Overview/Table.php:928
msgid "% Refunded"
msgstr ""

#: src/Admin/Payments/Views/Overview/Table.php:1005
msgid "Filter entries by coupon"
msgstr ""

#: src/Admin/Payments/Views/Overview/Traits/ResetNotices.php:74
msgid "where"
msgstr ""

#: src/Admin/Payments/Views/Overview/Traits/ResetNotices.php:110
msgid "with the status"
msgstr ""

#: src/Admin/Payments/Views/Overview/Traits/ResetNotices.php:146
msgid "with the coupon"
msgstr ""

#: src/Admin/Payments/Views/Overview/Traits/ResetNotices.php:180
msgid "with the form titled"
msgstr ""

#: src/Admin/Payments/Views/Overview/Traits/ResetNotices.php:209
msgid "with the type"
msgid_plural "with the types"
msgstr[0] ""
msgstr[1] ""

#: src/Admin/Payments/Views/Overview/Traits/ResetNotices.php:238
msgid "with the gateway"
msgid_plural "with the gateways"
msgstr[0] ""
msgstr[1] ""

#: src/Admin/Payments/Views/Overview/Traits/ResetNotices.php:267
msgid "with the subscription status"
msgid_plural "with the subscription statuses"
msgstr[0] ""
msgstr[1] ""

#: src/Admin/Payments/Views/Single.php:150
msgid "Are you sure you want to delete this payment and all its information (details, notes, etc.)?"
msgstr ""

#: src/Admin/Payments/Views/Single.php:151
msgid "Are you sure you want to refund this payment?"
msgstr ""

#: src/Admin/Payments/Views/Single.php:152
msgid "Are you sure you want to cancel this subscription?"
msgstr ""

#: src/Admin/Payments/Views/Single.php:153
msgid "Payment was successfully refunded!"
msgstr ""

#: src/Admin/Payments/Views/Single.php:154
msgid "Subscription was successfully canceled!"
msgstr ""

#: src/Admin/Payments/Views/Single.php:170
msgid "It looks like the provided payment ID is not valid."
msgstr ""

#: src/Admin/Payments/Views/Single.php:180
msgid "It looks like the payment you are trying to access is no longer available."
msgstr ""

#: src/Admin/Payments/Views/Single.php:188
msgid "You can't edit this payment because it's in the trash."
msgstr ""

#: src/Admin/Payments/Views/Single.php:333
msgid "Payment Details"
msgstr ""

#. translators: %s - payment gateway name.
#: src/Admin/Payments/Views/Single.php:337
#: src/Admin/Payments/Views/Single.php:407
msgid "View in %s"
msgstr ""

#: src/Admin/Payments/Views/Single.php:341
msgid "Refund"
msgstr ""

#: src/Admin/Payments/Views/Single.php:365
msgid "Method"
msgstr ""

#: src/Admin/Payments/Views/Single.php:404
msgid "Subscription Details"
msgstr ""

#: src/Admin/Payments/Views/Single.php:420
msgid "Lifetime Total"
msgstr ""

#: src/Admin/Payments/Views/Single.php:428
msgid "Billing Cycle"
msgstr ""

#: src/Admin/Payments/Views/Single.php:435
msgid "Times Billed"
msgstr ""

#: src/Admin/Payments/Views/Single.php:442
msgid "Renewal Date"
msgstr ""

#: src/Admin/Payments/Views/Single.php:476
msgid "Payment History"
msgstr ""

#. translators: %s - credit card expiry date.
#: src/Admin/Payments/Views/Single.php:649
msgid "Expires %s"
msgstr ""

#: src/Admin/Payments/Views/Single.php:751
msgid "Customer ID"
msgstr ""

#: src/Admin/Payments/Views/Single.php:756
msgid "Customer IP Address"
msgstr ""

#: src/Admin/Payments/Views/Single.php:760
msgid "Payment Method"
msgstr ""

#. translators: %d - field ID.
#: src/Admin/Payments/Views/Single.php:940
msgid "Field ID #%d"
msgstr ""

#: src/Admin/Payments/Views/Single.php:948
msgid "Empty"
msgstr ""

#: src/Admin/Revisions.php:386
msgid "You do not have permission to restore revisions for this form."
msgstr ""

#: src/Admin/Revisions.php:394
msgid "Invalid revision. The revision does not belong to this form."
msgstr ""

#: src/Admin/Revisions.php:472
msgid "You’re about to save a form revision. Continuing will make this the current version."
msgstr ""

#: src/Admin/Settings/Captcha.php:155
#: src/Admin/Settings/Captcha/Page.php:274
msgid "A preview of your CAPTCHA is displayed below. Please view to verify the CAPTCHA settings are correct."
msgstr ""

#: src/Admin/Settings/Captcha/HCaptcha.php:58
#: src/Admin/Settings/Captcha/ReCaptcha.php:70
#: src/Admin/Settings/Captcha/Turnstile.php:78
msgid "Site Key"
msgstr ""

#: src/Admin/Settings/Captcha/HCaptcha.php:63
#: src/Admin/Settings/Captcha/ReCaptcha.php:75
#: src/Admin/Settings/Captcha/Turnstile.php:83
msgid "Secret Key"
msgstr ""

#: src/Admin/Settings/Captcha/HCaptcha.php:68
#: src/Admin/Settings/Captcha/ReCaptcha.php:80
#: src/Admin/Settings/Captcha/Turnstile.php:88
msgid "Fail Message"
msgstr ""

#: src/Admin/Settings/Captcha/HCaptcha.php:69
#: src/Admin/Settings/Captcha/ReCaptcha.php:81
#: src/Admin/Settings/Captcha/Turnstile.php:89
msgid "Displays to users who fail the verification process."
msgstr ""

#: src/Admin/Settings/Captcha/HCaptcha.php:71
msgid "hCaptcha verification failed, please try again later."
msgstr ""

#: src/Admin/Settings/Captcha/Page.php:145
msgid "A CAPTCHA is an anti-spam technique which helps to protect your website from spam and abuse while letting real people pass through with ease."
msgstr ""

#. translators: %s - WPForms.com CAPTCHA comparison page URL.
#: src/Admin/Settings/Captcha/Page.php:162
msgid "Not sure which service is right for you? <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">Check out our comparison</a> for more details."
msgstr ""

#: src/Admin/Settings/Captcha/Page.php:186
msgid "No-Conflict Mode"
msgstr ""

#: src/Admin/Settings/Captcha/Page.php:187
msgid "Forcefully remove other CAPTCHA occurrences in order to prevent conflicts. Only enable this option if your site is having compatibility issues or instructed by support."
msgstr ""

#: src/Admin/Settings/Captcha/Page.php:195
msgid "Please save settings to generate a preview of your CAPTCHA here."
msgstr ""

#: src/Admin/Settings/Captcha/Page.php:228
msgid "This CAPTCHA is generated using your site and secret keys. If an error is displayed, please double-check your keys."
msgstr ""

#: src/Admin/Settings/Captcha/ReCaptcha.php:62
msgid "Checkbox reCAPTCHA v2"
msgstr ""

#: src/Admin/Settings/Captcha/ReCaptcha.php:63
msgid "Invisible reCAPTCHA v2"
msgstr ""

#: src/Admin/Settings/Captcha/ReCaptcha.php:64
msgid "reCAPTCHA v3"
msgstr ""

#: src/Admin/Settings/Captcha/ReCaptcha.php:83
#: src/Frontend/Frontend.php:1892
msgid "Google reCAPTCHA verification failed, please try again later."
msgstr ""

#: src/Admin/Settings/Captcha/ReCaptcha.php:87
msgid "Score Threshold"
msgstr ""

#: src/Admin/Settings/Captcha/ReCaptcha.php:88
msgid "reCAPTCHA v3 returns a score (1.0 is very likely a good interaction, 0.0 is very likely a bot). If the score is less than or equal to this threshold, the form submission will be blocked and the message above will be displayed."
msgstr ""

#: src/Admin/Settings/Captcha/ReCaptcha.php:95
msgid "0.4"
msgstr ""

#: src/Admin/Settings/Captcha/Turnstile.php:91
#: src/Frontend/Frontend.php:1893
msgid "Cloudflare Turnstile verification failed, please try again later."
msgstr ""

#: src/Admin/Settings/Captcha/Turnstile.php:99
msgid "Auto"
msgstr ""

#: src/Admin/Settings/Captcha/Turnstile.php:100
#: src/Admin/Settings/Email.php:231
msgid "Light"
msgstr ""

#: src/Admin/Settings/Captcha/Turnstile.php:101
#: src/Admin/Settings/Email.php:232
msgid "Dark"
msgstr ""

#: src/Admin/Settings/Email.php:122
msgid "This color combination may be hard to read. Try increasing the contrast between the body and text colors."
msgstr ""

#: src/Admin/Settings/Email.php:224
msgid "Appearance"
msgstr ""

#: src/Admin/Settings/Email.php:225
msgid "Modern email clients support viewing emails in light and dark modes. You can upload a header image and customize the style for each appearance mode independently to ensure an optimal reading experience."
msgstr ""

#: src/Admin/Settings/Email.php:243
#: src/Admin/Tools/Importers/ContactForm7.php:136
#: src/Admin/Tools/Importers/NinjaForms.php:144
#: src/Admin/Tools/Importers/PirateForms.php:460
msgid "Sending"
msgstr ""

#: src/Admin/Settings/Email.php:250
msgid "Optimize Email Sending"
msgstr ""

#. translators: %1$s - WPForms.com Email settings documentation URL.
#: src/Admin/Settings/Email.php:253
msgid "Send emails asynchronously, which can make processing faster but may delay email delivery by a minute or two. <a href=\"%1$s\" target=\"_blank\" rel=\"noopener noreferrer\" class=\"wpforms-learn-more\">Learn More</a>"
msgstr ""

#: src/Admin/Settings/Email.php:270
msgid "Carbon Copy"
msgstr ""

#: src/Admin/Settings/Email.php:271
msgid "Enable the ability to CC: email addresses in the form notification settings."
msgstr ""

#. translators: %1$s - WPForms.com Email settings legacy template documentation URL.
#: src/Admin/Settings/Email.php:304
msgid "Some style settings are not available when using the Legacy template. <a href=\"%1$s\" target=\"_blank\" rel=\"noopener noreferrer\">Learn More</a>"
msgstr ""

#: src/Admin/Settings/Email.php:351
msgid "Header Image"
msgstr ""

#: src/Admin/Settings/Email.php:352
msgid "Upload or choose a logo to be displayed at the top of email notifications."
msgstr ""

#: src/Admin/Settings/Email.php:374
#: src/Admin/Settings/Email.php:379
#: templates/admin/dashboard/widget/settings.php:43
msgid "Color Scheme"
msgstr ""

#: src/Admin/Settings/Email.php:384
#: src/Admin/Settings/Email.php:388
msgid "Typography"
msgstr ""

#: src/Admin/Settings/Email.php:385
msgid "Choose the style that’s applied to all text in email notifications."
msgstr ""

#: src/Admin/Settings/Email.php:394
msgid "Sans Serif"
msgstr ""

#: src/Admin/Settings/Email.php:395
msgid "Serif"
msgstr ""

#: src/Admin/Settings/Email.php:449
msgid "Upload or choose a logo to be displayed at the top of email notifications. Light mode image will be used if not set."
msgstr ""

#: src/Admin/Settings/Email.php:520
#: src/Integrations/Elementor/WidgetModern.php:155
#: src/Integrations/Elementor/WidgetModern.php:286
#: src/Integrations/Gutenberg/FormSelector.php:550
msgid "Background"
msgstr ""

#: src/Admin/Settings/Email.php:521
msgid "Body"
msgstr ""

#: src/Admin/Settings/Email.php:522
#: src/Forms/Fields/Richtext/Field.php:176
#: src/Forms/Fields/Traits/ContentInput.php:316
#: src/Integrations/Elementor/WidgetModern.php:174
#: src/Integrations/Elementor/WidgetModern.php:295
#: src/Integrations/Gutenberg/FormSelector.php:552
msgid "Text"
msgstr ""

#: src/Admin/Settings/Email.php:523
msgid "Links"
msgstr ""

#. translators: %1$s - Email template preview URL.
#: src/Admin/Settings/Email.php:567
msgid "<a href=\"%1$s\" class=\"wpforms-btn-preview\" target=\"_blank\" rel=\"noopener\">Preview Email Template</a>"
msgstr ""

#: src/Admin/Settings/Email.php:604
msgid "Background Color"
msgstr ""

#: src/Admin/Settings/Email.php:605
msgid "Customize the background color of the email template."
msgstr ""

#: src/Admin/Settings/ModernMarkup.php:76
msgid "Use Modern Markup"
msgstr ""

#. translators: %s - WPForms.com form markup setting URL.
#: src/Admin/Settings/ModernMarkup.php:79
msgid "Check this option to use modern markup, which has increased accessibility and allows you to easily customize your forms in the block editor. <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">Read our form markup documentation</a> to learn more."
msgstr ""

#. translators: %s - WPForms Stripe addon URL.
#: src/Admin/Settings/ModernMarkup.php:125
msgid "<strong>You cannot use modern markup because you’re using the deprecated Credit Card field.</strong> If you’d like to use modern markup, replace your credit card field with a payment gateway like <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">Stripe</a>."
msgstr ""

#: src/Admin/Settings/Payments.php:85
msgid "Currency"
msgstr ""

#: src/Admin/SiteHealth.php:47
msgid "Version"
msgstr ""

#: src/Admin/SiteHealth.php:58
msgid "Lite install date"
msgstr ""

#: src/Admin/SiteHealth.php:65
msgid "Pro install date"
msgstr ""

#: src/Admin/SiteHealth.php:73
msgid "Uploads directory"
msgstr ""

#: src/Admin/SiteHealth.php:74
msgid "Writable"
msgstr ""

#: src/Admin/SiteHealth.php:74
msgid "Not writable"
msgstr ""

#: src/Admin/SiteHealth.php:81
msgid "Not found"
msgstr ""

#: src/Admin/SiteHealth.php:84
msgid "DB tables"
msgstr ""

#: src/Admin/SiteHealth.php:92
msgid "Total forms"
msgstr ""

#: src/Admin/SiteHealth.php:111
msgid "Total submissions (since v1.5.0)"
msgstr ""

#: src/Admin/Splash/SplashScreen.php:267
msgid "See the new features!"
msgstr ""

#: src/Admin/Splash/SplashTrait.php:149
msgid "What’s New in WPForms"
msgstr ""

#: src/Admin/Splash/SplashTrait.php:150
msgid "Since you’ve been gone, we’ve added some great new features to help grow your business and generate more leads. Here are some highlights..."
msgstr ""

#: src/Admin/Splash/SplashTrait.php:153
msgid "Start Building Smarter WordPress Forms"
msgstr ""

#: src/Admin/Splash/SplashTrait.php:154
msgid "Add advanced form fields and conditional logic, plus offer more payment options, manage entries, and connect to your favorite marketing tools – all when you purchase a premium plan."
msgstr ""

#: src/Admin/Splash/SplashTrait.php:156
msgid "Upgrade to Pro Today"
msgstr ""

#: src/Admin/Tools/Importers/Base.php:111
msgid "There was an error while creating a new form."
msgstr ""

#: src/Admin/Tools/Importers/ContactForm7.php:112
msgid "Unknown Form"
msgstr ""

#: src/Admin/Tools/Importers/ContactForm7.php:113
msgid "The form you are trying to import does not exist."
msgstr ""

#: src/Admin/Tools/Importers/ContactForm7.php:141
#: src/Admin/Tools/Importers/NinjaForms.php:149
msgid "Notification 1"
msgstr ""

#: src/Admin/Tools/Importers/ContactForm7.php:170
#: src/Admin/Tools/Importers/NinjaForms.php:178
#: src/Admin/Tools/Importers/PirateForms.php:446
msgid "No form fields found."
msgstr ""

#: src/Admin/Tools/Importers/ContactForm7.php:345
msgid "Acceptance Field"
msgstr ""

#: src/Admin/Tools/Importers/ContactForm7.php:452
msgid "Notification 2"
msgstr ""

#. translators: %1$s - field type, %2$s - field name if available.
#: src/Admin/Tools/Importers/ContactForm7.php:546
msgid "%1$s Field %2$s"
msgstr ""

#: src/Admin/Tools/Importers/NinjaForms.php:255
#: src/Admin/Tools/Importers/PirateForms.php:262
#: src/Admin/Tools/Importers/PirateForms.php:355
msgid "Single Checkbox Field"
msgstr ""

#: src/Admin/Tools/Importers/NinjaForms.php:438
#: src/Admin/Tools/Importers/NinjaForms.php:455
#: templates/emails/summary-body.php:223
msgid "Notification"
msgstr ""

#. translators: %s - field type.
#: src/Admin/Tools/Importers/NinjaForms.php:508
msgid "%s Field"
msgstr ""

#: src/Admin/Tools/Importers/PirateForms.php:100
#: src/Admin/Tools/Importers/PirateForms.php:190
msgid "Default Form"
msgstr ""

#. translators: %s - Action Scheduler website URL.
#: src/Admin/Tools/Views/ActionSchedulerList.php:45
msgid "WPForms is using the <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">Action Scheduler</a> library, which allows it to queue and process bigger tasks in the background without making your site slower for your visitors. Below you can see the list of all tasks and their status. This table can be very useful when debugging certain issues."
msgstr ""

#: src/Admin/Tools/Views/ActionSchedulerList.php:60
msgid "Action Scheduler library is also used by other plugins, like WP Mail SMTP and WooCommerce, so you might see tasks that are not related to our plugin in the table below."
msgstr ""

#. translators: %s - search term.
#: src/Admin/Tools/Views/ActionSchedulerList.php:71
msgid "Search results for <strong>%s</strong>"
msgstr ""

#: src/Admin/Tools/Views/Export.php:146
msgid "Export Forms"
msgstr ""

#: src/Admin/Tools/Views/Export.php:148
msgid "Use form export files to create a backup of your forms or to import forms to another site."
msgstr ""

#: src/Admin/Tools/Views/Export.php:153
msgid "Select Form(s)"
msgstr ""

#: src/Admin/Tools/Views/Export.php:161
msgid "You need to create a form before you can use form export."
msgstr ""

#: src/Admin/Tools/Views/Export.php:177
msgid "Export a Form Template"
msgstr ""

#. translators: %s - WPForms.com docs URL.
#: src/Admin/Tools/Views/Export.php:184
msgid "For more information <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">see our documentation</a>."
msgstr ""

#: src/Admin/Tools/Views/Export.php:196
msgid "The following code can be used to register your custom form template. Copy and paste the following code to your theme's functions.php file or include it within an external file."
msgstr ""

#: src/Admin/Tools/Views/Export.php:203
msgid "Select a form to generate PHP code that can be used to register a custom form template."
msgstr ""

#: src/Admin/Tools/Views/Export.php:211
msgid "Export Template"
msgstr ""

#: src/Admin/Tools/Views/Export.php:215
msgid "You need to create a form before you can generate a template."
msgstr ""

#: src/Admin/Tools/Views/Import.php:156
msgid "Import was successfully finished."
msgstr ""

#. translators: %s - forms list page URL.
#: src/Admin/Tools/Views/Import.php:161
msgid "You can go and <a href=\"%s\">check your forms</a>."
msgstr ""

#. translators: %s - WPForms contact page URL.
#: src/Admin/Tools/Views/Import.php:184
msgid "You can’t import forms because you don’t have unfiltered HTML permissions. Please contact your site administrator or <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">reach out to our support team</a>."
msgstr ""

#: src/Admin/Tools/Views/Import.php:207
msgid "WPForms Import"
msgstr ""

#: src/Admin/Tools/Views/Import.php:208
msgid "Select a WPForms export file."
msgstr ""

#: src/Admin/Tools/Views/Import.php:213
msgid "files selected"
msgstr ""

#: src/Admin/Tools/Views/Import.php:216
msgid "No file chosen"
msgstr ""

#: src/Admin/Tools/Views/Import.php:218
msgid "Choose a File"
msgstr ""

#: src/Admin/Tools/Views/Import.php:241
msgid "Import from Other Form Plugins"
msgstr ""

#: src/Admin/Tools/Views/Import.php:242
msgid "Not happy with other WordPress contact form plugins?"
msgstr ""

#: src/Admin/Tools/Views/Import.php:243
msgid "WPForms makes it easy for you to switch by allowing you import your third-party forms with a single click."
msgstr ""

#: src/Admin/Tools/Views/Import.php:247
msgid "No form importers are currently enabled."
msgstr ""

#: src/Admin/Tools/Views/Import.php:252
msgid "Select previous contact form plugin..."
msgstr ""

#: src/Admin/Tools/Views/Import.php:260
msgid "Not Active"
msgstr ""

#: src/Admin/Tools/Views/Import.php:304
#: src/Admin/Tools/Views/Import.php:354
msgid "Please upload a valid .json form export file."
msgstr ""

#: src/Admin/Tools/Views/Import.php:305
#: src/Admin/Tools/Views/Import.php:323
#: src/Integrations/AI/Admin/Builder/Forms.php:202
#: src/Integrations/Elementor/WidgetModern.php:236
#: src/Lite/Admin/Education/LiteConnect.php:244
msgid "Error"
msgstr ""

#: src/Admin/Tools/Views/Import.php:347
msgid "The unfiltered HTML permissions are required to import form."
msgstr ""

#: src/Admin/Tools/Views/Import.php:358
msgid "There was an error saving your form. Please check your file and try again."
msgstr ""

#: src/Admin/Tools/Views/Importer.php:137
msgid "Form Import"
msgstr ""

#: src/Admin/Tools/Views/Importer.php:153
msgid "Select the forms you would like to import."
msgstr ""

#: src/Admin/Tools/Views/Importer.php:157
msgid "Available Forms"
msgstr ""

#: src/Admin/Tools/Views/Importer.php:176
msgid "Select All"
msgstr ""

#: src/Admin/Tools/Views/Importer.php:181
msgid "Forms to Import"
msgstr ""

#. translators: %s - provider name.
#: src/Admin/Tools/Views/Importer.php:211
msgid "Analyzing <span class=\"form-current\">1</span> of <span class=\"form-total\">0</span> forms from %s."
msgstr ""

#: src/Admin/Tools/Views/Importer.php:224
msgid "One or more of your forms contain fields that are not available in WPForms Lite. To properly import these fields, we recommend upgrading to WPForms Pro."
msgstr ""

#: src/Admin/Tools/Views/Importer.php:225
msgid "You can continue with the import without upgrading, and we will do our best to match the fields. However, some of them will be omitted due to compatibility issues."
msgstr ""

#: src/Admin/Tools/Views/Importer.php:231
msgid "Continue Import without Upgrading"
msgstr ""

#: src/Admin/Tools/Views/Importer.php:234
msgid "Below is the list of form fields that may be impacted:"
msgstr ""

#. translators: %s - provider name.
#: src/Admin/Tools/Views/Importer.php:255
msgid "Importing <span class=\"form-current\">1</span> of <span class=\"form-total\">0</span> forms from %s."
msgstr ""

#: src/Admin/Tools/Views/Importer.php:270
msgid "Congrats, the import process has finished! We have successfully imported <span class=\"forms-completed\"></span> forms. You can review the results below."
msgstr ""

#: src/Admin/Tools/Views/Importer.php:336
msgid "The following fields are available in PRO and were not imported:"
msgstr ""

#: src/Admin/Tools/Views/Importer.php:344
msgid "The following fields are available in PRO and were imported as text fields:"
msgstr ""

#: src/Admin/Tools/Views/Importer.php:352
msgid "The following fields are not supported and were not imported:"
msgstr ""

#: src/Admin/Tools/Views/Importer.php:361
msgid "Upgrade to the PRO plan to import these fields."
msgstr ""

#: src/Admin/Tools/Views/Logs.php:98
msgid "Log Settings"
msgstr ""

#: src/Admin/Tools/Views/Logs.php:99
msgid "Enable and configure the logging functionality while debugging behavior of various parts of the plugin, including form and entry processing."
msgstr ""

#: src/Admin/Tools/Views/Logs.php:103
msgid "Enable Logs"
msgstr ""

#: src/Admin/Tools/Views/Logs.php:114
msgid "Start logging WPForms-related events. This is recommended only while debugging."
msgstr ""

#: src/Admin/Tools/Views/Logs.php:153
msgid "Log Types"
msgstr ""

#: src/Admin/Tools/Views/Logs.php:169
msgid "Select the types of events you want to log. Everything is logged by default."
msgstr ""

#: src/Admin/Tools/Views/Logs.php:187
#: src/Forms/Fields/FileUpload/Field.php:481
msgid "User Roles"
msgstr ""

#: src/Admin/Tools/Views/Logs.php:208
msgid "Select the user roles you want to log. All roles are logged by default."
msgstr ""

#: src/Admin/Tools/Views/Logs.php:227
#: src/Forms/Fields/FileUpload/Field.php:569
msgid "Users"
msgstr ""

#: src/Admin/Tools/Views/Logs.php:249
msgid "Log events for specific users only. All users are logged by default."
msgstr ""

#: src/Admin/Tools/Views/System.php:65
msgid "System Information"
msgstr ""

#: src/Admin/Tools/Views/System.php:71
msgid "Copy System Information"
msgstr ""

#: src/Admin/Tools/Views/System.php:76
msgid "Test SSL Connections"
msgstr ""

#: src/Admin/Tools/Views/System.php:77
msgid "Click the button below to verify your web server can perform SSL connections successfully."
msgstr ""

#: src/Admin/Tools/Views/System.php:79
msgid "Test Connection"
msgstr ""

#: src/Admin/Tools/Views/System.php:92
msgid "Recreate custom tables"
msgstr ""

#: src/Admin/Tools/Views/System.php:93
msgid "Click the button below to recreate WPForms custom database tables."
msgstr ""

#: src/Admin/Tools/Views/System.php:95
msgid "Recreate Tables"
msgstr ""

#: src/Admin/Traits/FormTemplates.php:101
msgid "Search Templates"
msgstr ""

#: src/Admin/Traits/FormTemplates.php:117
msgid "Sorry, we didn't find any templates that match your criteria."
msgstr ""

#: src/Admin/Traits/FormTemplates.php:166
msgid "All Templates"
msgstr ""

#: src/Admin/Traits/FormTemplates.php:170
msgid "Available Templates"
msgstr ""

#: src/Admin/Traits/FormTemplates.php:173
msgid "Favorite Templates"
msgstr ""

#: src/Admin/Traits/FormTemplates.php:174
msgid "New Templates"
msgstr ""

#: src/Admin/Traits/FormTemplates.php:175
msgid "My Templates"
msgstr ""

#: src/Admin/Traits/FormTemplates.php:184
msgid "Custom Templates"
msgstr ""

#: src/Admin/Traits/FormTemplates.php:188
msgid "Addon Templates"
msgstr ""

#. translators: %s - form template name.
#: src/Admin/Traits/FormTemplates.php:365
msgid "%s template"
msgstr ""

#: src/Admin/Traits/FormTemplates.php:375
#: src/Integrations/AI/Admin/Builder/Forms.php:204
msgid "Addon"
msgstr ""

#: src/Admin/Traits/FormTemplates.php:451
msgid "Create Blank Form"
msgstr ""

#: src/Db/Payments/ValueValidator.php:45
msgid "Live"
msgstr ""

#: src/Db/Payments/ValueValidator.php:46
#: templates/admin/payments/single/details.php:70
msgid "Test"
msgstr ""

#: src/Db/Payments/ValueValidator.php:69
#: src/Forms/Fields/Helpers/RequirementsAlerts.php:179
msgid "PayPal Standard"
msgstr ""

#: src/Db/Payments/ValueValidator.php:71
#: src/Integrations/Stripe/Admin/Settings.php:273
msgid "Stripe"
msgstr ""

#: src/Db/Payments/ValueValidator.php:73
msgid "Authorize.net"
msgstr ""

#: src/Db/Payments/ValueValidator.php:103
msgid "Processed"
msgstr ""

#: src/Db/Payments/ValueValidator.php:104
msgid "Completed"
msgstr ""

#: src/Db/Payments/ValueValidator.php:105
msgid "Pending"
msgstr ""

#: src/Db/Payments/ValueValidator.php:106
#: src/Db/Payments/ValueValidator.php:125
msgid "Failed"
msgstr ""

#: src/Db/Payments/ValueValidator.php:107
msgid "Refunded"
msgstr ""

#: src/Db/Payments/ValueValidator.php:108
msgid "Partially Refunded"
msgstr ""

#: src/Db/Payments/ValueValidator.php:123
msgid "Cancelled"
msgstr ""

#: src/Db/Payments/ValueValidator.php:124
msgid "Not Synced"
msgstr ""

#: src/Db/Payments/ValueValidator.php:140
msgid "One-Time"
msgstr ""

#: src/Db/Payments/ValueValidator.php:157
msgid "Renewal"
msgstr ""

#: src/Db/Payments/ValueValidator.php:172
msgid "day"
msgstr ""

#: src/Db/Payments/ValueValidator.php:173
msgid "week"
msgstr ""

#: src/Db/Payments/ValueValidator.php:174
msgid "month"
msgstr ""

#: src/Db/Payments/ValueValidator.php:175
msgid "quarter"
msgstr ""

#: src/Db/Payments/ValueValidator.php:176
msgid "semi-year"
msgstr ""

#: src/Db/Payments/ValueValidator.php:177
msgid "year"
msgstr ""

#: src/Emails/Helpers.php:34
msgid "Legacy"
msgstr ""

#: src/Emails/Mailer.php:317
msgid "New Email Submit"
msgstr ""

#. translators: %1$s - namespaced class name, %2$s - invalid email.
#: src/Emails/Mailer.php:490
msgid "%1$s Invalid email address %2$s."
msgstr ""

#. translators: %s - namespaced class name.
#: src/Emails/Mailer.php:499
msgid "%s Empty subject line."
msgstr ""

#. translators: %s - namespaced class name.
#: src/Emails/Mailer.php:506
msgid "%s Empty message."
msgstr ""

#: src/Emails/Mailer.php:552
msgid "You cannot send emails with WPForms\\Emails\\Mailer until init/admin_init has been reached."
msgstr ""

#. translators: %1$d - field ID.
#: src/Emails/Notifications.php:1116
msgid "Field ID #%1$s"
msgstr ""

#: src/Emails/Notifications.php:1296
#: src/Forms/Fields/EntryPreview/Field.php:265
msgid "Compact"
msgstr ""

#: src/Emails/Notifications.php:1306
msgid "Elegant"
msgstr ""

#: src/Emails/Notifications.php:1311
msgid "Tech"
msgstr ""

#: src/Emails/Notifications.php:1316
msgid "Plain Text"
msgstr ""

#: src/Emails/Summaries.php:118
msgid "Disable Email Summaries weekly delivery."
msgstr ""

#: src/Emails/Summaries.php:121
msgid "View Email Summary Example"
msgstr ""

#: src/Emails/Summaries.php:133
msgid "Disable Email Summaries"
msgstr ""

#: src/Emails/Summaries.php:263
msgid "Weekly WPForms Email Summaries"
msgstr ""

#. translators: %s - site domain.
#: src/Emails/Summaries.php:321
msgid "Your Weekly WPForms Summary for %s"
msgstr ""

#: src/Forms/Akismet.php:318
msgid "Anti-spam verification failed, please try again later."
msgstr ""

#: src/Forms/Fields/Addons/Coupon/Field.php:113
msgid "You haven't selected any coupons that can be used with this form. Please choose at least one coupon."
msgstr ""

#: src/Forms/Fields/Addons/Coupon/Field.php:121
msgid "Allowed Coupons"
msgstr ""

#: src/Forms/Fields/Addons/Coupon/Field.php:122
msgid "Choose coupons that can be used in the field."
msgstr ""

#: src/Forms/Fields/Addons/Coupon/Field.php:154
#: src/Integrations/Gutenberg/FormSelector.php:505
msgid "Button Text"
msgstr ""

#: src/Forms/Fields/Addons/Coupon/Field.php:155
msgid "Change button text."
msgstr ""

#: src/Forms/Fields/Addons/LikertScale/Field.php:38
msgid "Item #1"
msgstr ""

#: src/Forms/Fields/Addons/LikertScale/Field.php:39
msgid "Item #2"
msgstr ""

#: src/Forms/Fields/Addons/LikertScale/Field.php:40
msgid "Item #3"
msgstr ""

#: src/Forms/Fields/Addons/LikertScale/Field.php:43
msgid "Strongly Disagree"
msgstr ""

#: src/Forms/Fields/Addons/LikertScale/Field.php:44
msgid "Disagree"
msgstr ""

#: src/Forms/Fields/Addons/LikertScale/Field.php:45
msgid "Neutral"
msgstr ""

#: src/Forms/Fields/Addons/LikertScale/Field.php:46
msgid "Agree"
msgstr ""

#: src/Forms/Fields/Addons/LikertScale/Field.php:47
msgid "Strongly Agree"
msgstr ""

#: src/Forms/Fields/Addons/LikertScale/Field.php:95
msgid "Rows"
msgstr ""

#: src/Forms/Fields/Addons/LikertScale/Field.php:96
msgid "Add rows to the likert scale."
msgstr ""

#: src/Forms/Fields/Addons/LikertScale/Field.php:113
msgid "Add likert scale row"
msgstr ""

#: src/Forms/Fields/Addons/LikertScale/Field.php:114
msgid "Remove likert scale row"
msgstr ""

#: src/Forms/Fields/Addons/LikertScale/Field.php:140
msgid "Make this a single-row rating scale"
msgstr ""

#: src/Forms/Fields/Addons/LikertScale/Field.php:141
msgid "Check this option to make this a single-row rating scale and remove the row choices."
msgstr ""

#: src/Forms/Fields/Addons/LikertScale/Field.php:160
msgid "Allow multiple responses per row"
msgstr ""

#: src/Forms/Fields/Addons/LikertScale/Field.php:161
msgid "Check this option to allow multiple responses per row (uses checkboxes)."
msgstr ""

#: src/Forms/Fields/Addons/LikertScale/Field.php:176
msgid "Add columns to the likert scale."
msgstr ""

#: src/Forms/Fields/Addons/LikertScale/Field.php:186
msgid "Add likert scale column"
msgstr ""

#: src/Forms/Fields/Addons/LikertScale/Field.php:187
msgid "Remove likert scale column"
msgstr ""

#: src/Forms/Fields/Addons/LikertScale/Field.php:236
msgid "Select the style for the likert scale."
msgstr ""

#: src/Forms/Fields/Addons/NetPromoterScore/Field.php:112
msgid "Select the style for the net promoter score."
msgstr ""

#: src/Forms/Fields/Addons/NetPromoterScore/Field.php:148
msgid "Lowest Score Label"
msgstr ""

#: src/Forms/Fields/Addons/NetPromoterScore/Field.php:158
#: src/Forms/Fields/Addons/NetPromoterScore/Field.php:240
msgid "Not at all Likely"
msgstr ""

#: src/Forms/Fields/Addons/NetPromoterScore/Field.php:178
msgid "Highest Score Label"
msgstr ""

#: src/Forms/Fields/Addons/NetPromoterScore/Field.php:188
#: src/Forms/Fields/Addons/NetPromoterScore/Field.php:241
msgid "Extremely Likely"
msgstr ""

#: src/Forms/Fields/Addons/Signature/Field.php:109
msgid "Ink Color"
msgstr ""

#: src/Forms/Fields/Addons/Signature/Field.php:110
msgid "Select the color for the signature ink."
msgstr ""

#: src/Forms/Fields/Address/Field.php:43
#: src/Forms/Fields/Phone/Field.php:100
msgid "US"
msgstr ""

#: src/Forms/Fields/Address/Field.php:44
#: src/Forms/Fields/Address/Field.php:53
#: src/Forms/Fields/Address/Field.php:207
#: src/Forms/Fields/Address/Field.php:520
msgid "Address Line 1"
msgstr ""

#: src/Forms/Fields/Address/Field.php:45
#: src/Forms/Fields/Address/Field.php:54
#: src/Forms/Fields/Address/Field.php:246
#: src/Forms/Fields/Address/Field.php:521
msgid "Address Line 2"
msgstr ""

#: src/Forms/Fields/Address/Field.php:47
msgid "Zip Code"
msgstr ""

#: src/Forms/Fields/Address/Field.php:48
msgid "State"
msgstr ""

#: src/Forms/Fields/Address/Field.php:52
#: src/Forms/Fields/Phone/Field.php:101
msgid "International"
msgstr ""

#: src/Forms/Fields/Address/Field.php:56
#: src/Forms/Fields/Address/Field.php:524
msgid "Postal Code"
msgstr ""

#: src/Forms/Fields/Address/Field.php:57
#: src/Forms/Fields/Address/Field.php:333
#: src/Forms/Fields/Address/Field.php:523
msgid "State / Province / Region"
msgstr ""

#: src/Forms/Fields/Address/Field.php:118
msgid "Select scheme format for the address field."
msgstr ""

#: src/Forms/Fields/Address/Field.php:133
msgid "Scheme"
msgstr ""

#: src/Forms/Fields/Address/Field.php:256
#: src/Forms/Fields/Address/Field.php:384
#: src/Forms/Fields/Address/Field.php:440
#: src/Integrations/Elementor/Widget.php:237
#: src/Integrations/Elementor/Widget.php:251
msgid "Hide"
msgstr ""

#: src/Forms/Fields/Address/Field.php:257
#: src/Forms/Fields/Address/Field.php:385
#: src/Forms/Fields/Address/Field.php:441
msgid "Turn On if you want to hide this sub field."
msgstr ""

#: src/Forms/Fields/Address/Field.php:374
msgid "ZIP / Postal"
msgstr ""

#. translators: %s - subfield name, e.g., state, country.
#: src/Forms/Fields/Address/Field.php:765
msgid "--- Select %s ---"
msgstr ""

#: src/Forms/Fields/Content/Field.php:28
#: src/Forms/Fields/Richtext/Field.php:26
msgid "image, text, table, list, heading, wysiwyg, visual"
msgstr ""

#: src/Forms/Fields/CreditCard/Field.php:107
msgid "Card Number Placeholder Text"
msgstr ""

#: src/Forms/Fields/CreditCard/Field.php:132
msgid "Security Code Placeholder Text"
msgstr ""

#: src/Forms/Fields/CreditCard/Field.php:157
#: src/Integrations/Square/Fields/Square.php:313
#: src/Integrations/Stripe/Fields/StripeCreditCard.php:120
msgid "Name on Card Placeholder Text"
msgstr ""

#: src/Forms/Fields/CreditCard/Field.php:215
#: src/Integrations/Stripe/Fields/PaymentElementCreditCard.php:222
msgid "Card Number"
msgstr ""

#: src/Forms/Fields/CreditCard/Field.php:220
msgid "Security Code"
msgstr ""

#: src/Forms/Fields/CreditCard/Field.php:227
#: src/Integrations/Square/Fields/Square.php:119
#: src/Integrations/Square/Fields/Square.php:542
#: src/Integrations/Stripe/Fields/StripeCreditCard.php:82
#: src/Integrations/Stripe/Fields/StripeCreditCard.php:166
msgid "Name on Card"
msgstr ""

#: src/Forms/Fields/CreditCard/Field.php:232
#: src/Integrations/Stripe/Fields/PaymentElementCreditCard.php:223
msgid "Expiration"
msgstr ""

#: src/Forms/Fields/CustomCaptcha/Field.php:59
msgid "What is 7+4?"
msgstr ""

#: src/Forms/Fields/CustomCaptcha/Field.php:60
msgid "11"
msgstr ""

#: src/Forms/Fields/CustomCaptcha/Field.php:130
msgid "Select type of captcha to use."
msgstr ""

#: src/Forms/Fields/CustomCaptcha/Field.php:141
msgid "Math"
msgstr ""

#: src/Forms/Fields/CustomCaptcha/Field.php:142
msgid "Question and Answer"
msgstr ""

#: src/Forms/Fields/CustomCaptcha/Field.php:163
msgid "Questions and Answers"
msgstr ""

#: src/Forms/Fields/CustomCaptcha/Field.php:164
msgid "Add questions to ask the user. Questions are randomly selected."
msgstr ""

#: src/Forms/Fields/CustomCaptcha/Field.php:182
msgid "Question"
msgstr ""

#: src/Forms/Fields/CustomCaptcha/Field.php:190
msgid "Answer"
msgstr ""

#: src/Forms/Fields/DateTime/Field.php:120
msgid "Select format for the date field."
msgstr ""

#: src/Forms/Fields/DateTime/Field.php:131
msgid "Date and Time"
msgstr ""

#: src/Forms/Fields/DateTime/Field.php:133
#: src/Forms/Fields/DateTime/Field.php:322
#: src/Forms/Fields/DateTime/Field.php:660
msgid "Time"
msgstr ""

#: src/Forms/Fields/DateTime/Field.php:209
msgid "Advanced date options."
msgstr ""

#: src/Forms/Fields/DateTime/Field.php:223
msgid "Date Picker"
msgstr ""

#: src/Forms/Fields/DateTime/Field.php:228
msgid "Date Dropdown"
msgstr ""

#: src/Forms/Fields/DateTime/Field.php:306
msgid "15 minutes"
msgstr ""

#: src/Forms/Fields/DateTime/Field.php:307
msgid "30 minutes"
msgstr ""

#: src/Forms/Fields/DateTime/Field.php:308
msgid "1 hour"
msgstr ""

#: src/Forms/Fields/DateTime/Field.php:323
msgid "Advanced time options."
msgstr ""

#: src/Forms/Fields/DateTime/Field.php:346
msgid "Interval"
msgstr ""

#: src/Forms/Fields/DateTime/Field.php:432
msgid "Limit Days"
msgstr ""

#: src/Forms/Fields/DateTime/Field.php:433
msgid "Check this option to adjust which days of the week can be selected."
msgstr ""

#: src/Forms/Fields/DateTime/Field.php:450
msgid "Sun"
msgstr ""

#: src/Forms/Fields/DateTime/Field.php:451
msgid "Mon"
msgstr ""

#: src/Forms/Fields/DateTime/Field.php:452
msgid "Tue"
msgstr ""

#: src/Forms/Fields/DateTime/Field.php:453
msgid "Wed"
msgstr ""

#: src/Forms/Fields/DateTime/Field.php:454
msgid "Thu"
msgstr ""

#: src/Forms/Fields/DateTime/Field.php:455
msgid "Fri"
msgstr ""

#: src/Forms/Fields/DateTime/Field.php:456
msgid "Sat"
msgstr ""

#: src/Forms/Fields/DateTime/Field.php:484
msgid "Disable Today's Date"
msgstr ""

#: src/Forms/Fields/DateTime/Field.php:485
msgid "Check this option to prevent today's date from being selected."
msgstr ""

#: src/Forms/Fields/DateTime/Field.php:518
msgid "Limit Hours"
msgstr ""

#: src/Forms/Fields/DateTime/Field.php:519
msgid "Check this option to adjust the range of times that can be selected."
msgstr ""

#: src/Forms/Fields/DateTime/Field.php:749
msgid "Disable Past Dates"
msgstr ""

#: src/Forms/Fields/DateTime/Field.php:750
msgid "Check this option to prevent any previous date from being selected."
msgstr ""

#: src/Forms/Fields/DateTime/Field.php:836
msgid "Start Time"
msgstr ""

#: src/Forms/Fields/DateTime/Field.php:836
msgid "End Time"
msgstr ""

#: src/Forms/Fields/Divider/Field.php:26
msgid "line, hr"
msgstr ""

#: src/Forms/Fields/EntryPreview/Field.php:26
msgid "confirm"
msgstr ""

#: src/Forms/Fields/EntryPreview/Field.php:73
msgid "Entry Preview must be displayed on its own page, without other fields. HTML fields are allowed."
msgstr ""

#: src/Forms/Fields/EntryPreview/Field.php:91
msgid "Display Preview Notice"
msgstr ""

#: src/Forms/Fields/EntryPreview/Field.php:92
msgid "Check this option to show a message above the entry preview."
msgstr ""

#: src/Forms/Fields/EntryPreview/Field.php:110
msgid "Preview Notice"
msgstr ""

#: src/Forms/Fields/EntryPreview/Field.php:111
msgid "Fill in the message to show above the entry preview."
msgstr ""

#: src/Forms/Fields/EntryPreview/Field.php:143
msgid "Choose the entry preview display style."
msgstr ""

#: src/Forms/Fields/EntryPreview/Field.php:198
msgid "Entry preview will be displayed here and will contain all fields found on the previous page."
msgstr ""

#: src/Forms/Fields/EntryPreview/Field.php:231
msgid "Page breaks are required for entry previews to work. If you'd like to remove page breaks, you'll have to first remove the entry preview field."
msgstr ""

#: src/Forms/Fields/EntryPreview/Field.php:233
msgid "You can't hide the previous button because it is required for the entry preview field on this page."
msgstr ""

#: src/Forms/Fields/EntryPreview/Field.php:249
msgid "This is a preview of your submission. It has not been submitted yet!"
msgstr ""

#: src/Forms/Fields/EntryPreview/Field.php:250
msgid "Please take a moment to verify your information. You can also go back to make changes."
msgstr ""

#: src/Forms/Fields/EntryPreview/Field.php:264
#: src/Forms/Fields/Richtext/Field.php:130
msgid "Basic"
msgstr ""

#: src/Forms/Fields/EntryPreview/Field.php:266
msgid "Table"
msgstr ""

#: src/Forms/Fields/EntryPreview/Field.php:267
msgid "Table, Compact"
msgstr ""

#: src/Forms/Fields/FileUpload/Field.php:122
msgid "Allowed File Extensions"
msgstr ""

#: src/Forms/Fields/FileUpload/Field.php:123
msgid "Enter the extensions you would like to allow, comma separated."
msgstr ""

#: src/Forms/Fields/FileUpload/Field.php:127
msgid "See More Details"
msgstr ""

#: src/Forms/Fields/FileUpload/Field.php:158
msgid "Max File Size"
msgstr ""

#. translators: %s - max upload size.
#: src/Forms/Fields/FileUpload/Field.php:160
msgid "Enter the max size of each file, in megabytes, to allow. If left blank, the value defaults to the maximum size the server allows which is %s."
msgstr ""

#: src/Forms/Fields/FileUpload/Field.php:198
msgid "Max File Uploads"
msgstr ""

#: src/Forms/Fields/FileUpload/Field.php:199
msgid "Enter the max number of files to allow. If left blank, the value defaults to 1."
msgstr ""

#: src/Forms/Fields/FileUpload/Field.php:251
msgid "Modern Style supports multiple file uploads, displays a drag-and-drop upload box, and uses AJAX. Classic Style supports single file upload and displays a traditional upload button."
msgstr ""

#: src/Forms/Fields/FileUpload/Field.php:289
msgid "Store Files in WordPress Media Library"
msgstr ""

#: src/Forms/Fields/FileUpload/Field.php:290
msgid "Check this option to store the final uploaded file in the WordPress Media Library"
msgstr ""

#: src/Forms/Fields/FileUpload/Field.php:336
msgid "Enable File Access Restrictions"
msgstr ""

#: src/Forms/Fields/FileUpload/Field.php:337
msgid "Choose who can access the uploaded files."
msgstr ""

#: src/Forms/Fields/FileUpload/Field.php:420
msgid "User Restriction"
msgstr ""

#: src/Forms/Fields/FileUpload/Field.php:433
msgid "Logged-in Users"
msgstr ""

#: src/Forms/Fields/FileUpload/Field.php:482
msgid "Select the user roles that can access the uploaded files."
msgstr ""

#: src/Forms/Fields/FileUpload/Field.php:493
msgid "All users with selected roles will be able to access the uploaded files."
msgstr ""

#: src/Forms/Fields/FileUpload/Field.php:570
msgid "Select the users that can access the uploaded files."
msgstr ""

#: src/Forms/Fields/FileUpload/Field.php:686
msgid "Password Protection"
msgstr ""

#: src/Forms/Fields/FileUpload/Field.php:687
msgid "Check this option to password protect the uploaded files."
msgstr ""

#: src/Forms/Fields/FileUpload/Field.php:719
msgid "Set a password to protect the uploaded files."
msgstr ""

#: src/Forms/Fields/FileUpload/Field.php:805
msgid "Enter Password"
msgstr ""

#: src/Forms/Fields/FileUpload/Field.php:841
#: src/Forms/Fields/Password/Field.php:252
msgid "Confirm Password"
msgstr ""

#: src/Forms/Fields/FileUpload/Field.php:853
msgid "Passwords do not match"
msgstr ""

#: src/Forms/Fields/FileUpload/Field.php:958
#: templates/fields/file-upload/file-upload-backend.php:22
msgid "Click or drag a file to this area to upload."
msgid_plural "Click or drag files to this area to upload."
msgstr[0] ""
msgstr[1] ""

#: src/Forms/Fields/FileUpload/Field.php:959
msgid "Click or drag files to this area to upload."
msgstr ""

#. translators: % - max number of files as a template string (not a number), replaced by a number later.
#: src/Forms/Fields/FileUpload/Field.php:961
msgid "You can upload up to %s files."
msgstr ""

#: src/Forms/Fields/FileUpload/Field.php:964
msgid "Passwords Do Not Match"
msgstr ""

#: src/Forms/Fields/FileUpload/Field.php:965
msgid "Please check the password for the following fields: {fields}"
msgstr ""

#: src/Forms/Fields/FileUpload/Field.php:966
msgid "Passwords Are Empty"
msgstr ""

#: src/Forms/Fields/FileUpload/Field.php:967
msgid "Please enter a password for the following fields: {fields}"
msgstr ""

#: src/Forms/Fields/FileUpload/Field.php:968
msgid "Cannot Enable Restrictions"
msgstr ""

#: src/Forms/Fields/FileUpload/Field.php:969
msgid "This field is attached to Notifications. In order to enable restrictions, please first remove it from File Upload Attachments in Notifications."
msgstr ""

#: src/Forms/Fields/FileUpload/Field.php:970
msgid "Cannot Enable Attachments"
msgstr ""

#: src/Forms/Fields/FileUpload/Field.php:971
msgid "The following fields ({fields}) cannot be attached to notifications because restrictions are enabled for them."
msgstr ""

#: src/Forms/Fields/FileUpload/Field.php:972
msgid "All User Roles already selected"
msgstr ""

#: src/Forms/Fields/FileUpload/Field.php:973
msgid "File Upload Restrictions can’t be enabled because the current version of the Post Submissions addon is incompatible."
msgstr ""

#. translators: %1$s - addons list.
#: src/Forms/Fields/Helpers/RequirementsAlerts.php:59
msgid "The following addons require an update to support product quantities: %1$s"
msgstr ""

#: src/Forms/Fields/Helpers/RequirementsAlerts.php:76
msgid "You're using an older version of the Coupons addon that does not support order summary."
msgstr ""

#. translators: %1$s - addon name.
#: src/Forms/Fields/Helpers/RequirementsAlerts.php:143
msgid "You're using an older version of the %1$s addon that does not support the Repeater field."
msgstr ""

#: src/Forms/Fields/Helpers/RequirementsAlerts.php:187
msgid "Save and Resume"
msgstr ""

#: src/Forms/Fields/Helpers/RequirementsAlerts.php:213
msgid "Update Required"
msgstr ""

#: src/Forms/Fields/Helpers/RequirementsAlerts.php:216
msgid "Update Now"
msgstr ""

#: src/Forms/Fields/Hidden/Field.php:76
msgid "Enter text for the form field label. Never displayed on the front-end."
msgstr ""

#: src/Forms/Fields/Html/Field.php:26
msgid "code"
msgstr ""

#: src/Forms/Fields/Html/Field.php:74
msgid "Enter text for the form field label. It will help identify your HTML blocks inside the form builder, but will not be displayed in the form."
msgstr ""

#: src/Forms/Fields/Html/Field.php:195
msgid "Contents of this field are not displayed in the form builder preview."
msgstr ""

#: src/Forms/Fields/Pagebreak/Field.php:45
msgid "progress bar, multi step, multi part"
msgstr ""

#: src/Forms/Fields/Pagebreak/Field.php:128
msgid "Page Title"
msgstr ""

#: src/Forms/Fields/Pagebreak/Field.php:129
msgid "Enter text for the page title."
msgstr ""

#: src/Forms/Fields/Pagebreak/Field.php:161
msgid "Next Label"
msgstr ""

#: src/Forms/Fields/Pagebreak/Field.php:162
msgid "Enter text for Next page navigation button."
msgstr ""

#: src/Forms/Fields/Pagebreak/Field.php:171
#: src/Forms/Fields/Pagebreak/Field.php:467
msgid "Next"
msgstr ""

#: src/Forms/Fields/Pagebreak/Field.php:197
msgid "Display Previous"
msgstr ""

#: src/Forms/Fields/Pagebreak/Field.php:198
msgid "Toggle displaying the Previous page navigation button."
msgstr ""

#: src/Forms/Fields/Pagebreak/Field.php:218
msgid "Previous Label"
msgstr ""

#: src/Forms/Fields/Pagebreak/Field.php:219
msgid "Enter text for Previous page navigation button."
msgstr ""

#: src/Forms/Fields/Pagebreak/Field.php:271
msgid "Progress Bar"
msgstr ""

#: src/Forms/Fields/Pagebreak/Field.php:272
msgid "Circles"
msgstr ""

#: src/Forms/Fields/Pagebreak/Field.php:273
msgid "Connector"
msgstr ""

#: src/Forms/Fields/Pagebreak/Field.php:290
msgid "Progress Indicator"
msgstr ""

#: src/Forms/Fields/Pagebreak/Field.php:291
msgid "Select theme for Page Indicator which is displayed at the top of the form."
msgstr ""

#: src/Forms/Fields/Pagebreak/Field.php:321
msgid "Page Indicator Color"
msgstr ""

#: src/Forms/Fields/Pagebreak/Field.php:322
msgid "Select the primary color for the Page Indicator theme."
msgstr ""

#: src/Forms/Fields/Pagebreak/Field.php:389
msgid "Page Navigation Alignment"
msgstr ""

#: src/Forms/Fields/Pagebreak/Field.php:390
msgid "Select the alignment for the Next/Previous page navigation buttons"
msgstr ""

#: src/Forms/Fields/Pagebreak/Field.php:401
msgid "Left"
msgstr ""

#: src/Forms/Fields/Pagebreak/Field.php:402
msgid "Right"
msgstr ""

#: src/Forms/Fields/Pagebreak/Field.php:403
msgid "Center"
msgstr ""

#: src/Forms/Fields/Pagebreak/Field.php:404
msgid "Split"
msgstr ""

#: src/Forms/Fields/Pagebreak/Field.php:426
msgid "Disable Scroll Animation"
msgstr ""

#: src/Forms/Fields/Pagebreak/Field.php:427
msgid "By default, a user's view is pulled to the top of each form page. Set to ON to disable this animation."
msgstr ""

#: src/Forms/Fields/Pagebreak/Field.php:471
msgid "First Page / Progress Indicator"
msgstr ""

#: src/Forms/Fields/Password/Field.php:84
msgid "Enable Password Confirmation"
msgstr ""

#: src/Forms/Fields/Password/Field.php:85
msgid "Check this option to ask users to provide their password twice."
msgstr ""

#: src/Forms/Fields/Password/Field.php:103
msgid "Enable Password Strength"
msgstr ""

#: src/Forms/Fields/Password/Field.php:104
msgid "Check this option to set minimum password strength."
msgstr ""

#: src/Forms/Fields/Password/Field.php:119
msgid "Minimum Strength"
msgstr ""

#: src/Forms/Fields/Password/Field.php:120
msgid "Select minimum password strength level."
msgstr ""

#: src/Forms/Fields/Password/Field.php:131
msgid "Weak"
msgstr ""

#: src/Forms/Fields/Password/Field.php:133
msgid "Strong"
msgstr ""

#: src/Forms/Fields/PaymentCheckbox/Field.php:22
msgid "Checkbox Items"
msgstr ""

#: src/Forms/Fields/PaymentCheckbox/Field.php:23
#: src/Forms/Fields/PaymentMultiple/Field.php:23
#: src/Forms/Fields/PaymentSelect/Field.php:41
#: src/Forms/Fields/PaymentSingle/Field.php:57
msgid "product, store, ecommerce, pay, payment"
msgstr ""

#: src/Forms/Fields/PaymentCheckbox/Field.php:30
#: src/Forms/Fields/PaymentMultiple/Field.php:30
#: src/Forms/Fields/PaymentSelect/Field.php:48
msgid "First Item"
msgstr ""

#: src/Forms/Fields/PaymentCheckbox/Field.php:38
#: src/Forms/Fields/PaymentMultiple/Field.php:38
#: src/Forms/Fields/PaymentSelect/Field.php:53
msgid "Second Item"
msgstr ""

#: src/Forms/Fields/PaymentCheckbox/Field.php:46
#: src/Forms/Fields/PaymentMultiple/Field.php:46
#: src/Forms/Fields/PaymentSelect/Field.php:58
msgid "Third Item"
msgstr ""

#: src/Forms/Fields/PaymentCheckbox/Field.php:274
#: src/Forms/Fields/PaymentMultiple/Field.php:262
#: src/Forms/Fields/PaymentSelect/Field.php:251
msgid "Show price after item labels"
msgstr ""

#: src/Forms/Fields/PaymentCheckbox/Field.php:275
#: src/Forms/Fields/PaymentMultiple/Field.php:263
#: src/Forms/Fields/PaymentSelect/Field.php:252
msgid "Check this option to show price of the item after the label."
msgstr ""

#: src/Forms/Fields/PaymentCheckbox/Field.php:492
#: src/Forms/Fields/PaymentMultiple/Field.php:482
msgid "Invalid payment option."
msgstr ""

#: src/Forms/Fields/PaymentMultiple/Field.php:22
msgid "Multiple Items"
msgstr ""

#: src/Forms/Fields/PaymentSelect/Field.php:40
msgid "Dropdown Items"
msgstr ""

#: src/Forms/Fields/PaymentSelect/Field.php:477
msgid "Invalid payment option"
msgstr ""

#: src/Forms/Fields/PaymentSingle/Field.php:56
#: src/Forms/Fields/PaymentSingle/Field.php:352
msgid "Single Item"
msgstr ""

#: src/Forms/Fields/PaymentSingle/Field.php:248
msgid "Price Display"
msgstr ""

#: src/Forms/Fields/PaymentSingle/Field.php:249
msgid "Specify how the price is displayed under the product name."
msgstr ""

#. translators: %s - Single item field price label.
#: src/Forms/Fields/PaymentSingle/Field.php:287
msgid "Price: %s"
msgstr ""

#: src/Forms/Fields/PaymentSingle/Field.php:305
msgid "Enter the price of the item, without a currency symbol."
msgstr ""

#: src/Forms/Fields/PaymentSingle/Field.php:312
msgid "Item Price"
msgstr ""

#: src/Forms/Fields/PaymentSingle/Field.php:350
msgid "Select the item type."
msgstr ""

#: src/Forms/Fields/PaymentSingle/Field.php:353
msgid "User Defined"
msgstr ""

#: src/Forms/Fields/PaymentSingle/Field.php:354
msgid "Hidden"
msgstr ""

#: src/Forms/Fields/PaymentSingle/Field.php:362
msgid "Item Type"
msgstr ""

#: src/Forms/Fields/PaymentSingle/Field.php:410
msgid "Enter the minimum price of the item, without a currency symbol."
msgstr ""

#. translators: %1$s - the default minimum price.
#: src/Forms/Fields/PaymentSingle/Field.php:440
msgid "Requiring a minimum price of at least %1$s helps protect you against card testing by fraudsters."
msgstr ""

#: src/Forms/Fields/PaymentSingle/Field.php:451
msgid "Security Recommendation"
msgstr ""

#. translators: %1$s - Item Price value.
#: src/Forms/Fields/PaymentSingle/Field.php:506
msgid "Price: <span class=\"price\">%1$s</span>"
msgstr ""

#. translators: %1$s - Minimum Price value.
#: src/Forms/Fields/PaymentSingle/Field.php:543
msgid "Minimum Price: <span class=\"min-price\">%1$s</span>"
msgstr ""

#: src/Forms/Fields/PaymentSingle/Field.php:555
msgid "Note: Item type is set to hidden and will not be visible when viewing the form."
msgstr ""

#: src/Forms/Fields/PaymentSingle/Field.php:671
msgid "Amount mismatch"
msgstr ""

#: src/Forms/Fields/PaymentSingle/Field.php:684
msgid "Amount can't be negative"
msgstr ""

#: src/Forms/Fields/PaymentSingle/Field.php:694
msgid "Amount can't be less than the required minimum."
msgstr ""

#. translators: %1$s - Minimum Price value.
#: src/Forms/Fields/PaymentSingle/Field.php:752
msgid "Minimum Price: %1$s"
msgstr ""

#: src/Forms/Fields/PaymentTotal/Field.php:25
msgid "store, ecommerce, pay, payment, sum"
msgstr ""

#: src/Forms/Fields/PaymentTotal/Field.php:430
msgid "Enable Summary"
msgstr ""

#: src/Forms/Fields/PaymentTotal/Field.php:431
msgid "Enable order summary for this field."
msgstr ""

#: src/Forms/Fields/PaymentTotal/Field.php:477
msgid "Example data is shown in the form editor. Actual products and totals will be displayed when you preview or embed your form."
msgstr ""

#: src/Forms/Fields/PaymentTotal/Field.php:512
msgid "Example Product 1"
msgstr ""

#: src/Forms/Fields/PaymentTotal/Field.php:518
msgid "Example Product 2"
msgstr ""

#: src/Forms/Fields/PaymentTotal/Field.php:524
msgid "Example Product 3"
msgstr ""

#: src/Forms/Fields/PaymentTotal/Field.php:809
msgid "Show order summary after confirmation message"
msgstr ""

#: src/Forms/Fields/Phone/Field.php:33
msgid "telephone, mobile, cell"
msgstr ""

#: src/Forms/Fields/Phone/Field.php:88
msgid "Select format for the phone form field"
msgstr ""

#: src/Forms/Fields/Phone/Field.php:99
msgid "Smart"
msgstr ""

#: src/Forms/Fields/Rating/Field.php:36
msgid "review, emoji, star"
msgstr ""

#: src/Forms/Fields/Rating/Field.php:95
msgid "Scale"
msgstr ""

#: src/Forms/Fields/Rating/Field.php:96
msgid "Select rating scale"
msgstr ""

#: src/Forms/Fields/Rating/Field.php:162
msgid "Icon"
msgstr ""

#: src/Forms/Fields/Rating/Field.php:163
msgid "Select icon to display"
msgstr ""

#: src/Forms/Fields/Rating/Field.php:175
msgid "Star"
msgstr ""

#: src/Forms/Fields/Rating/Field.php:176
msgid "Heart"
msgstr ""

#: src/Forms/Fields/Rating/Field.php:177
msgid "Thumb"
msgstr ""

#: src/Forms/Fields/Rating/Field.php:178
msgid "Smiley Face"
msgstr ""

#: src/Forms/Fields/Rating/Field.php:200
msgid "Select the size of the rating icon"
msgstr ""

#: src/Forms/Fields/Rating/Field.php:235
msgid "Select the color for the rating icon"
msgstr ""

#: src/Forms/Fields/Richtext/Field.php:77
msgid "Allow Media Uploads"
msgstr ""

#: src/Forms/Fields/Richtext/Field.php:78
msgid "Check this option to allow uploading and embedding files."
msgstr ""

#: src/Forms/Fields/Richtext/Field.php:91
msgid "Store files in WordPress Media Library"
msgstr ""

#: src/Forms/Fields/Richtext/Field.php:92
msgid "Check this option to store files in the WordPress Media Library."
msgstr ""

#: src/Forms/Fields/Richtext/Field.php:117
msgid "Field Style"
msgstr ""

#: src/Forms/Fields/Richtext/Field.php:175
#: src/Forms/Fields/Traits/ContentInput.php:313
msgid "Visual"
msgstr ""

#: src/Forms/Fields/Traits/ContentInput.php:176
msgid "Uploaded to this form"
msgstr ""

#: src/Forms/Fields/Traits/ContentInput.php:308
msgid "Add Media"
msgstr ""

#: src/Forms/Fields/Traits/ContentInput.php:358
msgid "<h4>Add Text and Images to Your Form With Ease</h4> <p>To get started, replace this text with your own.</p>"
msgstr ""

#: src/Forms/Fields/Traits/ContentInput.php:359
msgid "Expand Editor"
msgstr ""

#: src/Forms/Fields/Traits/ContentInput.php:360
msgid "Collapse Editor"
msgstr ""

#: src/Forms/Fields/Traits/ContentInput.php:361
msgid "Update Preview"
msgstr ""

#: src/Forms/Fields/Traits/NumberField.php:170
msgid "Define the minimum and the maximum values for the field."
msgstr ""

#: src/Forms/Fields/Traits/NumberField.php:249
msgid "Enter a default value for this field."
msgstr ""

#: src/Forms/Fields/Traits/NumberField.php:279
msgid "Increment"
msgstr ""

#: src/Forms/Fields/Traits/NumberField.php:280
msgid "Determines the increment between selectable values on the field."
msgstr ""

#. translators: %1$s - Field name.
#: src/Forms/Fields/Traits/ProField.php:261
msgid "%1$s is a Pro Feature"
msgstr ""

#: src/Forms/Fields/Traits/ProField.php:264
msgid "Incompatible Addon"
msgstr ""

#. translators: %1$s - Field name.
#: src/Forms/Fields/Traits/ProField.php:269
msgid "Upgrade to gain access to the %1$s field and dozens of other powerful features to help you build smarter forms and grow your business."
msgstr ""

#. translators: %1$s - Addon name.
#: src/Forms/Fields/Traits/ProField.php:273
msgid "You have access to the %1$s, but it's not currently installed."
msgstr ""

#. translators: %1$s - Addon name.
#: src/Forms/Fields/Traits/ProField.php:277
msgid "You have access to the %1$s, but it's not currently activated."
msgstr ""

#. translators: %1$s - Addon name.
#: src/Forms/Fields/Traits/ProField.php:281
msgid "The %1$s is not compatible with this version of WPForms and requires an update."
msgstr ""

#: src/Forms/Fields/Traits/ProField.php:289
msgid "Activate Addon"
msgstr ""

#: src/Forms/Fields/Traits/ProField.php:290
msgid "Update Addon"
msgstr ""

#: src/Forms/Fields/Traits/ProField.php:367
msgid "Update required"
msgstr ""

#: src/Forms/Fields/Url/Field.php:26
msgid "uri, link, hyperlink"
msgstr ""

#: src/Forms/Honeypot.php:79
msgid "WPForms honeypot field triggered."
msgstr ""

#: src/Forms/IconChoices.php:489
msgid "Done!"
msgstr ""

#: src/Forms/IconChoices.php:501
msgid "In order to use the Icon Choices feature, an icon library must be downloaded and installed. It's quick and easy, and you'll only have to do this once."
msgstr ""

#: src/Forms/IconChoices.php:502
msgid "Installing Icon Library"
msgstr ""

#: src/Forms/IconChoices.php:503
msgid "This should only take a minute. Please don’t close or reload your browser window."
msgstr ""

#: src/Forms/IconChoices.php:504
msgid "The icon library has been installed successfully. We will now save your form and reload the form builder."
msgstr ""

#. translators: %s - WPForms Support URL.
#: src/Forms/IconChoices.php:507
msgid "There was an error installing the icon library. Please try again later or <a href=\"%s\" target=\"_blank\" rel=\"noreferrer noopener\">contact support</a> if the issue persists."
msgstr ""

#: src/Forms/IconChoices.php:524
msgid "The icon library appears to be missing or damaged. It will now be reinstalled."
msgstr ""

#: src/Forms/IconChoices.php:525
msgid "Icon Picker"
msgstr ""

#: src/Forms/IconChoices.php:526
msgid "Browse or search for the perfect icon."
msgstr ""

#: src/Forms/IconChoices.php:527
msgid "Search 2000+ icons..."
msgstr ""

#: src/Forms/IconChoices.php:528
msgid "Sorry, we didn't find any matching icons."
msgstr ""

#: src/Forms/Locator.php:182
msgid "WPForms Widget"
msgstr ""

#: src/Forms/Locator.php:183
msgid "Text Widget"
msgstr ""

#: src/Forms/Locator.php:184
msgid "Block Widget"
msgstr ""

#: src/Forms/Locator.php:238
#: src/Forms/Locator.php:258
#: src/Forms/Locator.php:262
#: src/Forms/Locator.php:338
msgid "Locations"
msgstr ""

#: src/Forms/Locator.php:239
#: src/Forms/Locator.php:263
msgid "Form locations"
msgstr ""

#: src/Forms/Locator.php:307
#: src/Forms/Locator.php:337
msgid "View form locations"
msgstr ""

#: src/Forms/Locator.php:375
msgid "Form Locations"
msgstr ""

#: src/Forms/Locator.php:459
msgid "Inactive widgets"
msgstr ""

#: src/Forms/Locator.php:480
msgid "Site editor template"
msgstr ""

#: src/Forms/Locator.php:642
msgid "(no title)"
msgstr ""

#. translators: %s - form name.
#: src/Forms/Preview.php:178
msgid "%s Template Preview"
msgstr ""

#: src/Forms/Preview.php:179
msgid "Form Template"
msgstr ""

#. translators: %s - form name.
#: src/Forms/Preview.php:184
#: src/Forms/Preview.php:390
msgid "%s Preview"
msgstr ""

#: src/Forms/Preview.php:222
msgid "Edit Form Template"
msgstr ""

#: src/Forms/Preview.php:264
msgid "Close this window"
msgstr ""

#: src/Forms/Preview.php:274
msgid "This is a preview of the latest saved revision of your form template. If this preview does not match your template, save your changes and then refresh this page. This template preview is not publicly accessible."
msgstr ""

#: src/Forms/Preview.php:275
msgid "This is a preview of the latest saved revision of your form. If this preview does not match your form, save your changes and then refresh this page. This form preview is not publicly accessible."
msgstr ""

#. translators: %s - WPForms doc link.
#: src/Forms/Preview.php:298
msgid "For form testing tips, check out our <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">complete guide!</a>"
msgstr ""

#: src/Forms/Preview.php:339
msgid "You're viewing a preview of a form template."
msgstr ""

#. translators: %s - time period, e.g. 24 hours.
#: src/Forms/Preview.php:347
msgid "Entries are automatically deleted after %s."
msgstr ""

#: src/Forms/Token.php:273
msgid "Antispam token is invalid."
msgstr ""

#: src/Forms/Token.php:285
msgid "Antispam filter did not allow your data to pass through."
msgstr ""

#: src/Forms/Token.php:299
msgid "Please reload the page and try submitting the form again."
msgstr ""

#. translators: placeholders are links.
#: src/Forms/Token.php:322
msgid "Please check out our %1$stroubleshooting guide%2$s for details on resolving this issue."
msgstr ""

#: src/Forms/Token.php:341
msgid "Error updating token. Please try again or contact support if the issue persists."
msgstr ""

#: src/Forms/Token.php:345
msgid "Network error or server is unreachable. Check your connection or try again later."
msgstr ""

#. translators: %s - URL to a non-amp version of a page with the form.
#: src/Frontend/Amp.php:95
msgid "<a href=\"%s\">Go to the full page</a> to view and submit the form."
msgstr ""

#: src/Frontend/Amp.php:360
msgid "Google reCAPTCHA v2"
msgstr ""

#. translators: %1$s - CAPTCHA provider name, %2$s - URL to reCAPTCHA documentation.
#: src/Frontend/Amp.php:365
msgid "%1$s is not supported by AMP and is currently disabled.<br><a href=\"%2$s\" rel=\"noopener noreferrer\" target=\"_blank\">Upgrade to reCAPTCHA v3</a> for full AMP support. <br><em>Please note: this message is only displayed to site administrators.</em>"
msgstr ""

#: src/Frontend/Frontend.php:775
msgid "Please enable JavaScript in your browser to complete this form."
msgstr ""

#: src/Frontend/Frontend.php:1869
msgid "Click to accept this suggestion."
msgstr ""

#. translators: %1$s - characters count, %2$s - characters limit.
#: src/Frontend/Frontend.php:1879
msgid "%1$s of %2$s max characters."
msgstr ""

#. translators: %1$s - words count, %2$s - words limit.
#: src/Frontend/Frontend.php:1887
msgid "%1$s of %2$s max words."
msgstr ""

#: src/Frontend/Frontend.php:1910
msgid "Country list"
msgstr ""

#. translators: %s - URL to the troubleshooting guide.
#: src/Frontend/Frontend.php:2187
msgid "Heads up! WPForms has detected an issue with JavaScript on this page. JavaScript is required for this form to work properly, so this form may not work as expected. See our <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">troubleshooting guide</a> to learn more or contact support."
msgstr ""

#: src/Frontend/Frontend.php:2200
msgid "This message is only displayed to site administrators."
msgstr ""

#: src/Frontend/Modern.php:73
#: src/Frontend/Modern.php:86
#: src/Frontend/Modern.php:99
#: src/Frontend/Modern.php:112
#: src/Frontend/Modern.php:316
msgid "Form error message"
msgstr ""

#: src/Frontend/Modern.php:122
msgid "Recaptcha error message"
msgstr ""

#: src/Frontend/Modern.php:270
#: src/Frontend/Modern.php:317
msgid "Error message"
msgstr ""

#: src/Frontend/Modern.php:318
msgid "Submit button is disabled during form submission."
msgstr ""

#: src/Integrations/AI/Admin/Ajax/Forms.php:92
msgid "Empty prompt."
msgstr ""

#: src/Integrations/AI/Admin/Ajax/Forms.php:123
msgid "Empty field data."
msgstr ""

#: src/Integrations/AI/Admin/Ajax/Forms.php:268
msgid "Empty form data."
msgstr ""

#: src/Integrations/AI/Admin/Ajax/Forms.php:282
msgid "Form database object not found."
msgstr ""

#: src/Integrations/AI/Admin/Ajax/Forms.php:348
msgid "Form could not be created."
msgstr ""

#: src/Integrations/AI/Admin/Ajax/Forms.php:405
msgid "Please specify an element."
msgstr ""

#: src/Integrations/AI/Admin/Ajax/Forms.php:412
msgid "Sorry, you are not allowed to dismiss."
msgstr ""

#: src/Integrations/AI/Admin/Builder/Enqueues.php:132
msgid "Bad response"
msgstr ""

#: src/Integrations/AI/Admin/Builder/Enqueues.php:133
msgid "Clear chat history"
msgstr ""

#: src/Integrations/AI/Admin/Builder/Enqueues.php:134
msgid "Yes, Continue"
msgstr ""

#: src/Integrations/AI/Admin/Builder/Enqueues.php:137
msgid "Clear Chat History"
msgstr ""

#: src/Integrations/AI/Admin/Builder/Enqueues.php:138
msgid "Are you sure you want to clear the AI chat history and start over?"
msgstr ""

#: src/Integrations/AI/Admin/Builder/Enqueues.php:141
msgid "An error occurred."
msgstr ""

#: src/Integrations/AI/Admin/Builder/Enqueues.php:142
#: src/Integrations/AI/Admin/Builder/Forms.php:201
#: src/Integrations/AI/API/Http/Response.php:82
msgid "There appears to be a network error."
msgstr ""

#: src/Integrations/AI/Admin/Builder/Enqueues.php:143
msgid "I'm not sure what to do with that."
msgstr ""

#: src/Integrations/AI/Admin/Builder/Enqueues.php:146
msgid "Prohibited code has been removed."
msgstr ""

#: src/Integrations/AI/Admin/Builder/Enqueues.php:149
msgid "Please try again."
msgstr ""

#: src/Integrations/AI/Admin/Builder/Enqueues.php:150
msgid "Please try a different prompt. You might need to be more descriptive."
msgstr ""

#: src/Integrations/AI/Admin/Builder/Enqueues.php:151
msgid "Only basic styling tags are permitted. All other code deemed unsafe has been removed."
msgstr ""

#: src/Integrations/AI/Admin/Builder/Enqueues.php:155
msgid "Dock to the Left"
msgstr ""

#: src/Integrations/AI/Admin/Builder/Enqueues.php:155
msgid "Dock to the Right"
msgstr ""

#: src/Integrations/AI/Admin/Builder/Enqueues.php:156
msgid "Open in Popup"
msgstr ""

#: src/Integrations/AI/Admin/Builder/Enqueues.php:207
msgid "Describe the choices you would like to create or use one of the examples below to get started."
msgstr ""

#: src/Integrations/AI/Admin/Builder/Enqueues.php:210
msgid "<strong>What do you think of these choices?</strong> If you’re happy with them, you can insert these choices, or make changes by entering additional prompts."
msgstr ""

#: src/Integrations/AI/Admin/Builder/Enqueues.php:215
#: src/Integrations/AI/Admin/Builder/Forms.php:247
msgid "Learn More About WPForms AI"
msgstr ""

#: src/Integrations/AI/Admin/Builder/Enqueues.php:216
msgid "It looks like you have some existing choices in this field. If you generate new choices, your existing choices will be overwritten. You can simply close this window if you’d like to keep your existing choices."
msgstr ""

#: src/Integrations/AI/Admin/Builder/Enqueues.php:217
#: src/Integrations/AI/Admin/Builder/Forms.php:251
msgid "What would you like to create?"
msgstr ""

#: src/Integrations/AI/Admin/Builder/Enqueues.php:218
#: src/Integrations/AI/Admin/Builder/Forms.php:252
msgid "Just a minute..."
msgstr ""

#: src/Integrations/AI/Admin/Builder/Enqueues.php:219
msgid "Insert Choices"
msgstr ""

#: src/Integrations/AI/Admin/Builder/Enqueues.php:222
msgid "An error occurred while generating choices."
msgstr ""

#: src/Integrations/AI/Admin/Builder/Enqueues.php:223
msgid "Sorry, you've reached your daily limit for generating choices."
msgstr ""

#. translators: %s - WPForms contact support link.
#: src/Integrations/AI/Admin/Builder/Enqueues.php:228
msgid "You may only generate choices 50 times per day. If you believe this is an error, <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">please contact WPForms support</a>."
msgstr ""

#: src/Integrations/AI/Admin/Builder/Enqueues.php:241
msgid "Prohibited code has been removed from your choices."
msgstr ""

#: src/Integrations/AI/Admin/Builder/Enqueues.php:246
msgid "american public holidays with dates in brackets"
msgstr ""

#: src/Integrations/AI/Admin/Builder/Enqueues.php:250
msgid "provinces of canada ordered by population"
msgstr ""

#: src/Integrations/AI/Admin/Builder/Enqueues.php:254
msgid "top 5 social networks in europe"
msgstr ""

#: src/Integrations/AI/Admin/Builder/Enqueues.php:258
msgid "top 10 most spoken languages in the world"
msgstr ""

#: src/Integrations/AI/Admin/Builder/Enqueues.php:262
msgid "top 20 most popular tropical travel destinations"
msgstr ""

#: src/Integrations/AI/Admin/Builder/Enqueues.php:266
msgid "30 household item categories for a marketplace"
msgstr ""

#: src/Integrations/AI/Admin/Builder/FieldOption.php:68
msgid "Open AI Modal"
msgstr ""

#: src/Integrations/AI/Admin/Builder/FieldOption.php:125
#: src/Integrations/AI/Admin/Builder/Forms.php:174
#: src/Integrations/AI/Admin/Pages/Templates.php:70
msgid "WPForms AI is not available on local sites."
msgstr ""

#: src/Integrations/AI/Admin/Builder/Forms.php:126
#: src/Integrations/AI/Admin/Builder/Forms.php:169
#: src/Integrations/AI/Admin/Pages/Templates.php:97
msgid "Generate With AI"
msgstr ""

#: src/Integrations/AI/Admin/Builder/Forms.php:170
#: src/Integrations/AI/Admin/Pages/Templates.php:99
msgid "Write simple prompts to create complex forms catered to your specific needs."
msgstr ""

#: src/Integrations/AI/Admin/Builder/Forms.php:171
#: src/Integrations/AI/Admin/Pages/Templates.php:102
msgid "Generate Form"
msgstr ""

#: src/Integrations/AI/Admin/Builder/Forms.php:172
msgid "Continue Generating"
msgstr ""

#: src/Integrations/AI/Admin/Builder/Forms.php:173
#: src/Integrations/AI/Admin/Pages/Templates.php:98
msgid "NEW!"
msgstr ""

#: src/Integrations/AI/Admin/Builder/Forms.php:177
msgid "Back to Templates"
msgstr ""

#: src/Integrations/AI/Admin/Builder/Forms.php:178
msgid "Build Your Form Fast With the Help of AI"
msgstr ""

#: src/Integrations/AI/Admin/Builder/Forms.php:179
msgid "Not sure where to begin? Use our Generative AI tool to get started or take your pick from our wide variety of fields and start building out your form!"
msgstr ""

#: src/Integrations/AI/Admin/Builder/Forms.php:181
msgid "This is just a preview of your form."
msgstr ""

#: src/Integrations/AI/Admin/Builder/Forms.php:182
msgid "Click \"Use This Form\" to start editing."
msgstr ""

#: src/Integrations/AI/Admin/Builder/Forms.php:185
msgid "Before We Proceed"
msgstr ""

#: src/Integrations/AI/Admin/Builder/Forms.php:186
msgid "In order to build the best forms possible, we need to install some addons. Would you like to install the recommended addons?"
msgstr ""

#: src/Integrations/AI/Admin/Builder/Forms.php:187
msgid "In order to build the best forms possible, we need to activate some addons. Would you like to activate the recommended addons?"
msgstr ""

#: src/Integrations/AI/Admin/Builder/Forms.php:188
msgid "Yes, Install"
msgstr ""

#: src/Integrations/AI/Admin/Builder/Forms.php:190
msgid "No, Thanks"
msgstr ""

#: src/Integrations/AI/Admin/Builder/Forms.php:191
msgid "Don't show this again"
msgstr ""

#: src/Integrations/AI/Admin/Builder/Forms.php:192
msgid "Okay"
msgstr ""

#: src/Integrations/AI/Admin/Builder/Forms.php:195
msgid "Addons Installed"
msgstr ""

#: src/Integrations/AI/Admin/Builder/Forms.php:196
msgid "Addons Activated"
msgstr ""

#: src/Integrations/AI/Admin/Builder/Forms.php:197
msgid "You’re all set. We’re going to reload the builder and you can start building your form."
msgstr ""

#: src/Integrations/AI/Admin/Builder/Forms.php:198
msgid "Addons Installation Error"
msgstr ""

#: src/Integrations/AI/Admin/Builder/Forms.php:199
msgid "Addons Activation Error"
msgstr ""

#: src/Integrations/AI/Admin/Builder/Forms.php:200
msgid "Can't install or activate the required addons."
msgstr ""

#: src/Integrations/AI/Admin/Builder/Forms.php:203
msgid "Can't dismiss the modal window."
msgstr ""

#: src/Integrations/AI/Admin/Builder/Forms.php:208
msgid "This Form Would Be Even Better With Fields From"
msgstr ""

#. translators: %1$s - Upgrade to Pro link attributes.
#: src/Integrations/AI/Admin/Builder/Forms.php:210
msgid "<a href=\"#\">Upgrade to Pro</a> and gain access to all fields and create the best possible forms."
msgstr ""

#: src/Integrations/AI/Admin/Builder/Forms.php:222
msgid "You’re about to overwrite your existing form. This will delete all fields and reset external connections. Are you sure you want to continue?"
msgstr ""

#: src/Integrations/AI/Admin/Builder/Forms.php:223
msgid "The challenge will continue once AI form generation is complete"
msgstr ""

#: src/Integrations/AI/Admin/Builder/Forms.php:244
msgid "Generate a Form"
msgstr ""

#: src/Integrations/AI/Admin/Builder/Forms.php:245
msgid "Describe the form you would like to create or use one of the example prompts below to get started."
msgstr ""

#: src/Integrations/AI/Admin/Builder/Forms.php:249
msgid "Go back to this version of the form"
msgstr ""

#: src/Integrations/AI/Admin/Builder/Forms.php:250
msgid "Use This Form"
msgstr ""

#: src/Integrations/AI/Admin/Builder/Forms.php:254
msgid "An error occurred while generating form."
msgstr ""

#: src/Integrations/AI/Admin/Builder/Forms.php:255
msgid "Sorry, you've reached your daily limit for generating forms."
msgstr ""

#: src/Integrations/AI/Admin/Builder/Forms.php:258
msgid "What do you think of the form I created for you? If you’re happy with it, you can use this form. Otherwise, make changes by entering additional prompts."
msgstr ""

#: src/Integrations/AI/Admin/Builder/Forms.php:259
msgid "How’s that? Are you ready to use this form?"
msgstr ""

#: src/Integrations/AI/Admin/Builder/Forms.php:260
msgid "Does this look good? Are you ready to implement this form?"
msgstr ""

#: src/Integrations/AI/Admin/Builder/Forms.php:261
msgid "Is this what you had in mind? Are you satisfied with the results?"
msgstr ""

#: src/Integrations/AI/Admin/Builder/Forms.php:262
msgid "Happy with the form? Ready to move forward?"
msgstr ""

#: src/Integrations/AI/Admin/Builder/Forms.php:263
msgid "Is this form a good fit for your needs? Can we proceed?"
msgstr ""

#: src/Integrations/AI/Admin/Builder/Forms.php:264
msgid "Are you pleased with the outcome? Ready to use this form?"
msgstr ""

#: src/Integrations/AI/Admin/Builder/Forms.php:265
msgid "Does this form meet your expectations? Can we move on to the next step?"
msgstr ""

#: src/Integrations/AI/Admin/Builder/Forms.php:266
msgid "Is this form what you were envisioning? Are you ready to use it?"
msgstr ""

#: src/Integrations/AI/Admin/Builder/Forms.php:267
msgid "Satisfied with the form? Let's use it!"
msgstr ""

#: src/Integrations/AI/Admin/Builder/Forms.php:268
msgid "Does this form align with your goals? Are you ready to implement it?"
msgstr ""

#: src/Integrations/AI/Admin/Builder/Forms.php:269
msgid "Happy with the results? Let's put this form to work!"
msgstr ""

#. translators: %1$s - Reload link class.
#: src/Integrations/AI/Admin/Builder/Forms.php:274
msgid "<a href=\"#\" class=\"%1$s\">Reload this window</a> and try again."
msgstr ""

#. translators: %s - WPForms contact support link.
#: src/Integrations/AI/Admin/Builder/Forms.php:286
msgid "You may only generate forms 50 times per day. If you believe this is an error, <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">please contact WPForms support</a>."
msgstr ""

#: src/Integrations/AI/Admin/Builder/Forms.php:301
msgid "restaurant customer satisfaction survey"
msgstr ""

#: src/Integrations/AI/Admin/Builder/Forms.php:305
msgid "online event registration"
msgstr ""

#: src/Integrations/AI/Admin/Builder/Forms.php:309
msgid "job application for a web designer"
msgstr ""

#: src/Integrations/AI/Admin/Builder/Forms.php:313
msgid "cancelation survey for a subscription"
msgstr ""

#: src/Integrations/AI/Admin/Builder/Forms.php:317
msgid "takeout order for a pizza store"
msgstr ""

#: src/Integrations/AI/Admin/Builder/Forms.php:321
msgid "market vendor application"
msgstr ""

#: src/Integrations/AI/Admin/Settings.php:49
msgid "Hide AI Features"
msgstr ""

#: src/Integrations/AI/Admin/Settings.php:50
msgid "Hide everything related to AI in WPForms."
msgstr ""

#: src/Integrations/AI/Admin/Settings.php:59
msgid "<strong>AI features were hidden by filter or constant.</strong>"
msgstr ""

#: src/Integrations/AI/API/Forms.php:119
msgid "Untitled Form"
msgstr ""

#. translators: %1$s - error code, %2$s - error message.
#: src/Integrations/AI/API/Http/Response.php:105
msgid "API response: %1$s %2$s"
msgstr ""

#: src/Integrations/ConstantContact/V3/Api/Api.php:376
msgid "Cannot refresh the token."
msgstr ""

#: src/Integrations/ConstantContact/V3/Auth.php:62
msgid "Please wait a moment..."
msgstr ""

#: src/Integrations/ConstantContact/V3/Auth.php:63
msgid "There was an error while processing your request. Please try again."
msgstr ""

#: src/Integrations/ConstantContact/V3/Auth.php:108
msgid "Invalid code."
msgstr ""

#: src/Integrations/ConstantContact/V3/Auth.php:135
msgid "Invalid account."
msgstr ""

#: src/Integrations/ConstantContact/V3/Auth.php:150
msgid "This email is already connected."
msgstr ""

#: src/Integrations/ConstantContact/V3/ConstantContact.php:159
msgid "Job Title"
msgstr ""

#: src/Integrations/ConstantContact/V3/ConstantContact.php:160
msgid "Company Name"
msgstr ""

#. translators: %1$s - link to the migration page, %2$s - closing HTML tag.
#: src/Integrations/ConstantContact/V3/Migration/Migration.php:154
msgid "You need to migrate your existing forms to the new version of the Constant Contact addon. Please %1$s click here%2$s to start the migration."
msgstr ""

#: src/Integrations/ConstantContact/V3/Migration/Migration.php:756
msgid "Can't receive v2 lists and finish migration."
msgstr ""

#: src/Integrations/ConstantContact/V3/Settings/FormBuilder.php:203
msgid "Subscribe"
msgstr ""

#: src/Integrations/ConstantContact/V3/Settings/FormBuilder.php:204
msgid "Unsubscribe"
msgstr ""

#: src/Integrations/ConstantContact/V3/Settings/FormBuilder.php:205
msgid "Delete subscriber"
msgstr ""

#: src/Integrations/ConstantContact/V3/Settings/FormBuilder.php:219
msgid "--- Select Mailing List ---"
msgstr ""

#: src/Integrations/ConstantContact/V3/Settings/FormBuilder.php:222
#: src/Providers/Provider/Settings/FormBuilder.php:121
msgid "Custom Fields"
msgstr ""

#: src/Integrations/ConstantContact/V3/Settings/FormBuilder.php:236
msgid "Reason"
msgstr ""

#: src/Integrations/ConstantContact/V3/Settings/PageIntegrations.php:61
msgid "Your Constant Contact account connection has expired. Please reconnect your account."
msgstr ""

#: src/Integrations/ConstantContact/V3/Settings/PageIntegrations.php:73
msgid "Reconnect Account"
msgstr ""

#. translators: %s - forms overview page URL.
#: src/Integrations/DefaultContent/DefaultContent.php:58
msgctxt "Theme starter content"
msgid "Create your <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">contact form</a> with WPForms in minutes."
msgstr ""

#: src/Integrations/Divi/Divi.php:218
#: templates/integrations/elementor/no-forms.php:20
#: assets/js/integrations/gutenberg/modules/common.js:997
#: assets/lite/js/integrations/gutenberg/formselector.es5.js:3313
#: assets/pro/js/integrations/gutenberg/formselector.es5.js:3324
msgid "You can use <b>WPForms</b> to build contact forms, surveys, payment forms, and more with just a few clicks."
msgstr ""

#: src/Integrations/Divi/Divi.php:226
msgid "comprehensive guide"
msgstr ""

#: src/Integrations/Divi/Divi.php:227
msgid "Need some help? Check out our"
msgstr ""

#: src/Integrations/Divi/Divi.php:261
msgid "You do not have permission to preview form."
msgstr ""

#: src/Integrations/Divi/WPFormsSelector.php:59
msgid "Select form"
msgstr ""

#: src/Integrations/Divi/WPFormsSelector.php:73
#: src/Integrations/Gutenberg/FormSelector.php:533
msgid "Show Title"
msgstr ""

#: src/Integrations/Divi/WPFormsSelector.php:83
#: src/Integrations/Gutenberg/FormSelector.php:534
msgid "Show Description"
msgstr ""

#: src/Integrations/Elementor/Elementor.php:134
msgid "Confirm"
msgstr ""

#: src/Integrations/Elementor/Elementor.php:135
#: src/Integrations/Elementor/WidgetModern.php:368
msgid "Reset Style Settings"
msgstr ""

#: src/Integrations/Elementor/Elementor.php:136
msgid "Are you sure you want to reset the style settings for this form? All your current styling will be removed and canʼt be recovered."
msgstr ""

#: src/Integrations/Elementor/Elementor.php:137
#: src/Integrations/Gutenberg/FormSelector.php:579
msgid "There was an error parsing your JSON code. Please check your code and try again."
msgstr ""

#: src/Integrations/Elementor/Widget.php:141
msgid "<b>You haven't created a form yet.</b><br> What are you waiting for?"
msgstr ""

#. translators: %s - WPForms documentation link.
#: src/Integrations/Elementor/Widget.php:169
msgid "Need to make changes? <a href=\"#\">Edit the selected form.</a>"
msgstr ""

#. translators: %s - WPForms documentation link.
#: src/Integrations/Elementor/Widget.php:185
msgid "<b>Heads up!</b> Don't forget to test your form. <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">Check out our complete guide!</a>"
msgstr ""

#: src/Integrations/Elementor/Widget.php:223
msgid "Display Options"
msgstr ""

#: src/Integrations/Elementor/Widget.php:236
#: src/Integrations/Elementor/Widget.php:250
msgid "Show"
msgstr ""

#: src/Integrations/Elementor/Widget.php:383
msgid "Select a form"
msgstr ""

#: src/Integrations/Elementor/WidgetModern.php:113
#: src/Integrations/Gutenberg/FormSelector.php:499
msgid "Field Styles"
msgstr ""

#: src/Integrations/Elementor/WidgetModern.php:125
#: src/Integrations/Gutenberg/FormSelector.php:546
msgid "Form Styles are disabled because Lead Form Mode is turned on."
msgstr ""

#: src/Integrations/Elementor/WidgetModern.php:126
#: src/Integrations/Gutenberg/FormSelector.php:547
msgid "To change the styling for this form, open it in the form builder and edit the options in the Lead Forms settings."
msgstr ""

#: src/Integrations/Elementor/WidgetModern.php:136
#: src/Integrations/Elementor/WidgetModern.php:206
#: src/Integrations/Elementor/WidgetModern.php:267
#: src/Integrations/Gutenberg/FormSelector.php:548
msgid "Size"
msgstr ""

#: src/Integrations/Elementor/WidgetModern.php:146
#: src/Integrations/Elementor/WidgetModern.php:277
msgid "Border Radius (px)"
msgstr ""

#: src/Integrations/Elementor/WidgetModern.php:164
#: src/Integrations/Gutenberg/FormSelector.php:551
#: src/Integrations/Gutenberg/FormSelector.php:568
msgid "Border"
msgstr ""

#: src/Integrations/Elementor/WidgetModern.php:198
#: src/Integrations/Gutenberg/FormSelector.php:503
msgid "Label Styles"
msgstr ""

#: src/Integrations/Elementor/WidgetModern.php:226
#: src/Integrations/Gutenberg/FormSelector.php:571
msgid "Sublabel & Hint"
msgstr ""

#: src/Integrations/Elementor/WidgetModern.php:259
#: src/Integrations/Gutenberg/FormSelector.php:506
msgid "Button Styles"
msgstr ""

#: src/Integrations/Elementor/WidgetModern.php:326
msgid "Additional Classes"
msgstr ""

#: src/Integrations/Elementor/WidgetModern.php:328
msgid "Separate multiple classes with spaces."
msgstr ""

#: src/Integrations/Elementor/WidgetModern.php:346
#: src/Integrations/Gutenberg/FormSelector.php:578
msgid "Copy / Paste Style Settings"
msgstr ""

#: src/Integrations/Elementor/WidgetModern.php:348
#: src/Integrations/Gutenberg/FormSelector.php:580
msgid "If you've copied style settings from another form, you can paste them here to add the same styling to this form. Any current style settings will be overwritten."
msgstr ""

#: src/Integrations/Gutenberg/FormSelector.php:478
msgid "Select and display one of your forms."
msgstr ""

#: src/Integrations/Gutenberg/FormSelector.php:480
msgid "form"
msgstr ""

#: src/Integrations/Gutenberg/FormSelector.php:481
msgid "contact"
msgstr ""

#: src/Integrations/Gutenberg/FormSelector.php:482
msgid "survey"
msgstr ""

#: src/Integrations/Gutenberg/FormSelector.php:484
msgid "Select a Form"
msgstr ""

#: src/Integrations/Gutenberg/FormSelector.php:485
msgid "Form Settings"
msgstr ""

#: src/Integrations/Gutenberg/FormSelector.php:489
msgid "Theme Name"
msgstr ""

#: src/Integrations/Gutenberg/FormSelector.php:490
msgid "Delete Theme"
msgstr ""

#: src/Integrations/Gutenberg/FormSelector.php:491
msgid "Delete Form Theme"
msgstr ""

#. Translators: %1$s: Theme name.
#: src/Integrations/Gutenberg/FormSelector.php:493
msgid "Are you sure you want to delete the %1$s theme?"
msgstr ""

#: src/Integrations/Gutenberg/FormSelector.php:494
msgid "This cannot be undone."
msgstr ""

#: src/Integrations/Gutenberg/FormSelector.php:496
msgid "Copy"
msgstr ""

#: src/Integrations/Gutenberg/FormSelector.php:497
msgid "Custom Theme"
msgstr ""

#: src/Integrations/Gutenberg/FormSelector.php:498
msgid "Noname Theme"
msgstr ""

#: src/Integrations/Gutenberg/FormSelector.php:500
msgid "Field Label"
msgstr ""

#: src/Integrations/Gutenberg/FormSelector.php:501
msgid "Field Sublabel"
msgstr ""

#: src/Integrations/Gutenberg/FormSelector.php:502
msgid "Field Border"
msgstr ""

#: src/Integrations/Gutenberg/FormSelector.php:504
msgid "Button Background"
msgstr ""

#: src/Integrations/Gutenberg/FormSelector.php:507
msgid "Container Styles"
msgstr ""

#: src/Integrations/Gutenberg/FormSelector.php:508
msgid "Background Styles"
msgstr ""

#: src/Integrations/Gutenberg/FormSelector.php:510
msgid "Position"
msgstr ""

#: src/Integrations/Gutenberg/FormSelector.php:511
msgid "Top Left"
msgstr ""

#: src/Integrations/Gutenberg/FormSelector.php:512
msgid "Top Center"
msgstr ""

#: src/Integrations/Gutenberg/FormSelector.php:513
msgid "Top Right"
msgstr ""

#: src/Integrations/Gutenberg/FormSelector.php:514
msgid "Center Left"
msgstr ""

#: src/Integrations/Gutenberg/FormSelector.php:515
msgid "Center Center"
msgstr ""

#: src/Integrations/Gutenberg/FormSelector.php:516
msgid "Center Right"
msgstr ""

#: src/Integrations/Gutenberg/FormSelector.php:517
msgid "Bottom Left"
msgstr ""

#: src/Integrations/Gutenberg/FormSelector.php:518
msgid "Bottom Center"
msgstr ""

#: src/Integrations/Gutenberg/FormSelector.php:519
msgid "Bottom Right"
msgstr ""

#: src/Integrations/Gutenberg/FormSelector.php:520
msgid "Repeat"
msgstr ""

#: src/Integrations/Gutenberg/FormSelector.php:521
msgid "No Repeat"
msgstr ""

#: src/Integrations/Gutenberg/FormSelector.php:522
msgid "Repeat Horizontal"
msgstr ""

#: src/Integrations/Gutenberg/FormSelector.php:523
msgid "Repeat Vertical"
msgstr ""

#: src/Integrations/Gutenberg/FormSelector.php:524
msgid "Tile"
msgstr ""

#: src/Integrations/Gutenberg/FormSelector.php:525
msgid "Cover"
msgstr ""

#: src/Integrations/Gutenberg/FormSelector.php:526
msgid "Dimensions"
msgstr ""

#: src/Integrations/Gutenberg/FormSelector.php:527
msgid "Width"
msgstr ""

#: src/Integrations/Gutenberg/FormSelector.php:528
msgid "Height"
msgstr ""

#: src/Integrations/Gutenberg/FormSelector.php:529
msgid "Also used for other fields like Multiple Choice, Checkboxes, Rating, and NPS Survey."
msgstr ""

#: src/Integrations/Gutenberg/FormSelector.php:531
msgid "Additional CSS Classes"
msgstr ""

#: src/Integrations/Gutenberg/FormSelector.php:536
msgid "Do not forget to test your form."
msgstr ""

#: src/Integrations/Gutenberg/FormSelector.php:538
msgid "Check out our complete guide!"
msgstr ""

#: src/Integrations/Gutenberg/FormSelector.php:539
#: src/Integrations/Gutenberg/FormSelector.php:543
msgid "Want to customize your form styles without editing CSS?"
msgstr ""

#: src/Integrations/Gutenberg/FormSelector.php:540
msgid "Update WordPress to the latest version to use our modern markup and unlock the controls below."
msgstr ""

#: src/Integrations/Gutenberg/FormSelector.php:544
msgid "Enable modern markup in your WPForms settings to unlock the controls below."
msgstr ""

#: src/Integrations/Gutenberg/FormSelector.php:549
msgid "Padding"
msgstr ""

#: src/Integrations/Gutenberg/FormSelector.php:553
msgid "Menu"
msgstr ""

#: src/Integrations/Gutenberg/FormSelector.php:554
msgid "Image"
msgstr ""

#: src/Integrations/Gutenberg/FormSelector.php:555
msgid "Media Library"
msgstr ""

#: src/Integrations/Gutenberg/FormSelector.php:556
msgid "Choose Image"
msgstr ""

#: src/Integrations/Gutenberg/FormSelector.php:557
msgid "Stock Photo"
msgstr ""

#: src/Integrations/Gutenberg/FormSelector.php:558
msgid "Border Radius"
msgstr ""

#: src/Integrations/Gutenberg/FormSelector.php:559
#: src/Integrations/Gutenberg/FormSelector.php:567
msgid "Border Size"
msgstr ""

#: src/Integrations/Gutenberg/FormSelector.php:560
msgid "Border Style"
msgstr ""

#: src/Integrations/Gutenberg/FormSelector.php:562
msgid "Solid"
msgstr ""

#: src/Integrations/Gutenberg/FormSelector.php:563
msgid "Dashed"
msgstr ""

#: src/Integrations/Gutenberg/FormSelector.php:564
msgid "Dotted"
msgstr ""

#: src/Integrations/Gutenberg/FormSelector.php:565
msgid "Double"
msgstr ""

#: src/Integrations/Gutenberg/FormSelector.php:566
msgid "Shadow"
msgstr ""

#: src/Integrations/Gutenberg/FormSelector.php:569
msgid "Colors"
msgstr ""

#: src/Integrations/Gutenberg/FormSelector.php:572
msgid "Error Message"
msgstr ""

#: src/Integrations/Gutenberg/FormSelector.php:581
msgid "Custom CSS"
msgstr ""

#: src/Integrations/Gutenberg/FormSelector.php:582
msgid "Further customize the look of this form without having to edit theme files."
msgstr ""

#. Translators: %1$s: Opening strong tag, %2$s: Closing strong tag.
#: src/Integrations/Gutenberg/FormSelector.php:584
msgid "You can use %1$sWPForms%2$s to build contact forms, surveys, payment forms, and more with just a few clicks."
msgstr ""

#. Translators: %1$s: Opening anchor tag, %2$s: Closing achor tag.
#: src/Integrations/Gutenberg/FormSelector.php:586
msgid "Need some help? Check out our %1$scomprehensive guide.%2$s"
msgstr ""

#: src/Integrations/Gutenberg/FormSelector.php:587
msgid "Other Styles"
msgstr ""

#: src/Integrations/Gutenberg/FormSelector.php:591
msgid "It looks like the form you had selected is in the Trash or has been permanently deleted."
msgstr ""

#: src/Integrations/Gutenberg/FormSelector.php:889
msgid "The form cannot be displayed."
msgstr ""

#: src/Integrations/Gutenberg/RestApi.php:129
msgid "This route is private."
msgstr ""

#: src/Integrations/Gutenberg/RestApi.php:146
msgid "This route is accessible only to administrators."
msgstr ""

#: src/Integrations/Gutenberg/RestApi.php:203
msgid "Can't create themes storage file."
msgstr ""

#: src/Integrations/Gutenberg/RestApi.php:217
msgid "Can't save theme data."
msgstr ""

#. translators: %s - WPForms documentation link.
#: src/Integrations/LiteConnect/Integration.php:366
msgid "Your form entries can’t be backed up because WPForms can’t connect to the backup server. If you’d like to back up your entries, find out how to <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">fix entry backup issues</a>."
msgstr ""

#: src/Integrations/LiteConnect/LiteConnect.php:240
msgid "This is the Lite Connect endpoint page."
msgstr ""

#: src/Integrations/SMTP/Notifications.php:66
#: src/Integrations/SMTP/Notifications.php:90
msgid "Please enter a valid email address. Your notifications won't be sent if the field is not filled in correctly."
msgstr ""

#: src/Integrations/SMTP/Notifications.php:91
#: src/Integrations/SMTP/Notifications.php:125
msgid "Notifications can only use 1 From Email. Please do not enter multiple addresses."
msgstr ""

#: src/Integrations/SMTP/Notifications.php:92
msgid "This smart tag does not point to an Email field in your form."
msgstr ""

#. translators: %1$s - WordPress site domain.
#: src/Integrations/SMTP/Notifications.php:180
msgid "The current 'From Email' address does not match your website domain name (%1$s). This can cause your notification emails to be blocked or marked as spam."
msgstr ""

#. translators: %1$s - WP Mail SMTP install page URL.
#: src/Integrations/SMTP/Notifications.php:193
msgid "We strongly recommend that you install the free <a href=\"%1$s\" target=\"_blank\">WP Mail SMTP</a> plugin! The Setup Wizard makes it easy to fix your emails."
msgstr ""

#. translators: %1$s - WordPress site domain.
#: src/Integrations/SMTP/Notifications.php:209
msgid "Alternately, try using a From Address that matches your website domain (admin@%1$s)."
msgstr ""

#. translators: %1$s - fixing email delivery issues doc URL.
#: src/Integrations/SMTP/Notifications.php:215
msgid "Please check out our <a href=\"%1$s\" target=\"_blank\" rel=\"noopener noreferrer\">doc on fixing email delivery issues</a> for more details."
msgstr ""

#: src/Integrations/Square/AddonCompatibility.php:64
msgid "The WPForms Square addon is out of date. To avoid payment processing issues, please upgrade the Square addon to the latest version."
msgstr ""

#: src/Integrations/Square/Admin/Builder/Enqueues.php:80
msgid "<p>Square account connection is required when using the Square field.</p><p>To proceed, please go to <strong>WPForms Settings » Payments » Square</strong> and press <strong>Connect with Square</strong> button.</p>"
msgstr ""

#: src/Integrations/Square/Admin/Builder/Enqueues.php:88
msgid "<p>Square Payments must be enabled when using the Square field.</p><p>To proceed, please go to <strong>Payments » Square</strong> and check <strong>Enable Square Payments</strong>.</p>"
msgstr ""

#: src/Integrations/Square/Admin/Builder/Enqueues.php:96
msgid "<p>AJAX form submissions are required when using the Square field.</p><p>To proceed, please go to <strong>Settings » General » Advanced</strong> and check <strong>Enable AJAX form submission</strong>.</p>"
msgstr ""

#: src/Integrations/Square/Admin/Builder/Enqueues.php:103
#: src/Integrations/Stripe/Admin/Builder/Enqueues.php:47
msgid "Missing Required Fields"
msgstr ""

#: src/Integrations/Square/Admin/Builder/Enqueues.php:104
msgid "When recurring subscription payments are enabled, the Customer Email and Customer Name are required."
msgstr ""

#: src/Integrations/Square/Admin/Builder/Enqueues.php:106
msgid "Please go to the <a href=\"#\" class=\"wpforms-square-settings-redirect\">Square payment settings</a> and select a Customer Email and Customer Name."
msgstr ""

#: src/Integrations/Square/Admin/Builder/Notifications.php:55
msgid "Enable for Square completed payments"
msgstr ""

#: src/Integrations/Square/Admin/Builder/Notifications.php:109
msgid "The Square Pro addon is required to enable notification for completed payments. Would you like to install and activate it?"
msgstr ""

#: src/Integrations/Square/Admin/Builder/Notifications.php:119
msgid "The Square Pro addon is required to enable notification for completed payments. Would you like to activate it?"
msgstr ""

#: src/Integrations/Square/Admin/Builder/Notifications.php:127
msgid "Notification for Square Completed Payments"
msgstr ""

#: src/Integrations/Square/Admin/Builder/Settings.php:218
#: src/Integrations/Stripe/Admin/Builder/Settings.php:243
msgid "Enable Conditional Logic"
msgstr ""

#: src/Integrations/Square/Admin/Builder/Settings.php:259
msgid "The Square Pro addon is required to enable conditional logic for payments. Would you like to install and activate it?"
msgstr ""

#: src/Integrations/Square/Admin/Builder/Settings.php:269
msgid "The Square Pro addon is required to enable conditional logic for payments. Would you like to activate it?"
msgstr ""

#: src/Integrations/Square/Admin/Builder/Traits/Content.php:77
#: src/Integrations/Stripe/Admin/Builder/Traits/ContentTrait.php:362
msgid "Payment Description"
msgstr ""

#: src/Integrations/Square/Admin/Builder/Traits/Content.php:80
msgid "Enter your payment description. Eg: Donation for the soccer team."
msgstr ""

#: src/Integrations/Square/Admin/Builder/Traits/Content.php:90
msgid "Buyer Email"
msgstr ""

#: src/Integrations/Square/Admin/Builder/Traits/Content.php:94
msgid "--- Select Buyer Email ---"
msgstr ""

#: src/Integrations/Square/Admin/Builder/Traits/Content.php:95
msgid "Select the field that contains the buyer's email address. This field is optional."
msgstr ""

#: src/Integrations/Square/Admin/Builder/Traits/Content.php:105
msgid "Billing Name"
msgstr ""

#: src/Integrations/Square/Admin/Builder/Traits/Content.php:109
msgid "--- Select Billing Name ---"
msgstr ""

#: src/Integrations/Square/Admin/Builder/Traits/Content.php:110
msgid "Select the field that contains the billing's name. This field is optional."
msgstr ""

#: src/Integrations/Square/Admin/Builder/Traits/Content.php:142
#: src/Integrations/Stripe/Admin/Builder/Traits/ContentTrait.php:428
msgid "Enter the subscription name. Eg: Email Newsletter. Subscription period and price are automatically appended. If left empty the form name will be used."
msgstr ""

#: src/Integrations/Square/Admin/Builder/Traits/Content.php:153
msgid "Phase Cadence"
msgstr ""

#: src/Integrations/Square/Admin/Builder/Traits/Content.php:160
#: src/Integrations/Stripe/Admin/Builder/Traits/ContentTrait.php:453
msgid "How often you would like the charge to recur."
msgstr ""

#: src/Integrations/Square/Admin/Builder/Traits/Content.php:179
#: src/Integrations/Stripe/Admin/Builder/Traits/ContentTrait.php:379
#: src/Integrations/Stripe/Admin/Builder/Traits/ContentTrait.php:394
#: src/Integrations/Stripe/Admin/Builder/Traits/ContentTrait.php:472
msgid "--- Select Email ---"
msgstr ""

#: src/Integrations/Square/Admin/Builder/Traits/Content.php:180
#: src/Integrations/Stripe/Admin/Builder/Traits/ContentTrait.php:473
msgid "Select the field that contains the customer's email address. This field is required."
msgstr ""

#: src/Integrations/Square/Admin/Builder/Traits/Content.php:192
#: src/Integrations/Stripe/Admin/Builder/Traits/ContentTrait.php:543
msgid "Customer Name"
msgstr ""

#: src/Integrations/Square/Admin/Builder/Traits/Content.php:199
#: src/Integrations/Stripe/Admin/Builder/Traits/ContentTrait.php:529
msgid "--- Select Name ---"
msgstr ""

#: src/Integrations/Square/Admin/Builder/Traits/Content.php:200
msgid "Select the field that contains the customer's name. This field is required."
msgstr ""

#: src/Integrations/Square/Admin/Builder/Traits/Content.php:221
#: src/Integrations/Stripe/Admin/Builder/Traits/ContentTrait.php:111
msgid "One-Time Payments"
msgstr ""

#: src/Integrations/Square/Admin/Builder/Traits/Content.php:229
#: src/Integrations/Stripe/Admin/Builder/Traits/ContentTrait.php:119
msgid "Enable one-time payments"
msgstr ""

#: src/Integrations/Square/Admin/Builder/Traits/Content.php:233
#: src/Integrations/Stripe/Admin/Builder/Traits/ContentTrait.php:123
msgid "Allow your customers to one-time pay via the form."
msgstr ""

#: src/Integrations/Square/Admin/Builder/Traits/Content.php:255
#: src/Integrations/Stripe/Admin/Builder/Traits/ContentTrait.php:145
msgid "Recurring Payments "
msgstr ""

#: src/Integrations/Square/Admin/Builder/Traits/Content.php:266
#: src/Integrations/Stripe/Admin/Builder/Traits/ContentTrait.php:156
#: src/Integrations/Stripe/Admin/Builder/Traits/ContentTrait.php:333
msgid "Enable recurring subscription payments"
msgstr ""

#: src/Integrations/Square/Admin/Builder/Traits/Content.php:270
#: src/Integrations/Stripe/Admin/Builder/Traits/ContentTrait.php:160
msgid "Allow your customer to pay recurringly via the form."
msgstr ""

#: src/Integrations/Square/Admin/Builder/Traits/Content.php:308
#: src/Integrations/Stripe/Admin/Builder/Traits/ContentTrait.php:199
msgid "Add New Plan"
msgstr ""

#: src/Integrations/Square/Admin/Builder/Traits/Content.php:315
#: src/Integrations/Stripe/Admin/Builder/Traits/ContentTrait.php:226
msgid "Multiple Subscriptions"
msgstr ""

#: src/Integrations/Square/Admin/Builder/Traits/Content.php:335
msgid "Square Pro"
msgstr ""

#: src/Integrations/Square/Admin/Builder/Traits/Content.php:361
#: src/Integrations/Square/Admin/Builder/Traits/Content.php:382
#: src/Integrations/Stripe/Admin/Builder/Traits/ContentTrait.php:579
#: src/Integrations/Stripe/Admin/Builder/Traits/ContentTrait.php:594
msgid "Customer Address"
msgstr ""

#: src/Integrations/Square/Admin/Builder/Traits/Content.php:368
msgid "Select the field that contains the customer's Address. This field is optional."
msgstr ""

#: src/Integrations/Square/Admin/Builder/Traits/Content.php:373
#: src/Integrations/Stripe/Admin/Builder/Traits/ContentTrait.php:563
msgid "--- Select Address ---"
msgstr ""

#: src/Integrations/Square/Admin/Builder/Traits/Content.php:389
#: src/Integrations/Square/Admin/Builder/Traits/Content.php:401
msgid "Billing Address"
msgstr ""

#: src/Integrations/Square/Admin/Builder/Traits/Content.php:391
msgid "Select the field that contains the billing's address. This field is optional."
msgstr ""

#: src/Integrations/Square/Admin/Builder/Traits/Content.php:394
msgid "--- Select Billing Address ---"
msgstr ""

#: src/Integrations/Square/Admin/Builder/Traits/Content.php:420
msgid "Heads up! Square payments can't be enabled yet."
msgstr ""

#. translators: %s - Admin area Payments settings page URL.
#: src/Integrations/Square/Admin/Builder/Traits/Content.php:423
msgid "First, please connect to your Square account on the <a href='%s'>WPForms Settings</a> page."
msgstr ""

#: src/Integrations/Square/Admin/Builder/Traits/Content.php:440
msgid "Square payments can't be processed because there's a problem with the account connection."
msgstr ""

#. translators: %s - the WPForms Payments settings page URL.
#: src/Integrations/Square/Admin/Builder/Traits/Content.php:443
msgid "First, please resolve the connection issue on the <a href='%2$s'>Payment Settings</a> page."
msgstr ""

#: src/Integrations/Square/Admin/Builder/Traits/Content.php:450
#: src/Integrations/Square/Admin/Builder/Traits/Content.php:471
#: src/Integrations/Square/Admin/Settings.php:377
#: src/Integrations/Square/Admin/Settings.php:390
msgid "Sandbox"
msgstr ""

#: src/Integrations/Square/Admin/Builder/Traits/Content.php:450
#: src/Integrations/Square/Admin/Builder/Traits/Content.php:471
#: src/Integrations/Square/Admin/Settings.php:377
#: src/Integrations/Square/Admin/Settings.php:390
msgid "Production"
msgstr ""

#: src/Integrations/Square/Admin/Builder/Traits/Content.php:461
msgid "Heads up! Square account connection is expired."
msgstr ""

#. translators: %s - the WPForms Payments settings page URL.
#: src/Integrations/Square/Admin/Builder/Traits/Content.php:464
msgid "Tokens must be refreshed. First, please refresh them on the <a href='%2$s'>WPForms Settings</a> page."
msgstr ""

#: src/Integrations/Square/Admin/Builder/Traits/Content.php:528
msgid "To use Square, first add the Square payment field to your form."
msgstr ""

#: src/Integrations/Square/Admin/Builder/Traits/Content.php:546
msgid "Connect WPForms to Square."
msgstr ""

#: src/Integrations/Square/Admin/Builder/Traits/Content.php:562
msgid "Learn more about our Square integration."
msgstr ""

#: src/Integrations/Square/Admin/Connect.php:258
#: src/Integrations/Square/Api/WebhooksManager.php:42
msgid "You are not allowed to perform this action"
msgstr ""

#: src/Integrations/Square/Admin/Connect.php:261
msgid "Something went wrong while performing a refresh tokens request"
msgstr ""

#: src/Integrations/Square/Admin/Connect.php:408
msgid "Square Error: We could not connect to Square. No tokens were given."
msgstr ""

#: src/Integrations/Square/Admin/Connect.php:427
msgid "Square Error: We could not save an account connection safely. Please, try again later."
msgstr ""

#: src/Integrations/Square/Admin/Notices.php:80
msgid "There Are Some Problems With Your Square Connection"
msgstr ""

#: src/Integrations/Square/Admin/Notices.php:103
msgid "Square account connection is missing required data. You must reconnect your Square account."
msgstr ""

#: src/Integrations/Square/Admin/Notices.php:107
msgid "Square account connection is invalid. You must reconnect your Square account."
msgstr ""

#: src/Integrations/Square/Admin/Notices.php:111
msgid "Square account connection is expired. Tokens must be refreshed."
msgstr ""

#: src/Integrations/Square/Admin/Notices.php:115
#: src/Integrations/Square/Admin/Settings.php:308
msgid "Business Location is required to process Square payments."
msgstr ""

#. translators: %1$s - Selected currency on the WPForms Settings admin page; %2$s - Currency of a business location.
#: src/Integrations/Square/Admin/Notices.php:123
msgid "The currency you have set (%1$s) does not match the currency of your Square business location (%2$s). Please choose a different business location or update your WPForms currency to %2$s."
msgstr ""

#: src/Integrations/Square/Admin/Notices.php:153
#: src/Integrations/Square/Admin/WebhookSettings.php:207
msgid "Webhooks are enabled, but not yet connected."
msgstr ""

#. translators: %s - general admin settings page URL.
#: src/Integrations/Square/Admin/Notices.php:216
msgid "<strong>Pay-as-you-go Pricing</strong><br>3%% fee per-transaction + Square fees. <a href=\"%s\">Activate your license</a> to remove additional fees and unlock powerful features."
msgstr ""

#. translators: %s - general admin settings page URL.
#: src/Integrations/Square/Admin/Notices.php:232
msgid "<strong>Pay-as-you-go Pricing</strong><br> 3%% fee per-transaction + Square fees. <a href=\"%s\">Renew your license</a> to remove additional fees and unlock powerful features."
msgstr ""

#. translators: %s - WPForms.com Upgrade page URL.
#: src/Integrations/Square/Admin/Notices.php:263
msgid "<strong>Pay-as-you-go Pricing</strong><br> 3%% fee per-transaction + Square fees. <a href=\"%s\" target=\"_blank\">Upgrade to Pro</a> to remove additional fees and unlock powerful features."
msgstr ""

#: src/Integrations/Square/Admin/Payments/SingleActionsHandler.php:102
#: src/Integrations/Stripe/Admin/Payments/SingleActionsHandler.php:122
msgid "Refund failed."
msgstr ""

#: src/Integrations/Square/Admin/Payments/SingleActionsHandler.php:111
#: src/Integrations/Stripe/Admin/Payments/SingleActionsHandler.php:139
msgid "Refund successful."
msgstr ""

#: src/Integrations/Square/Admin/Payments/SingleActionsHandler.php:114
#: src/Integrations/Stripe/Admin/Payments/SingleActionsHandler.php:142
msgid "Saving refund in the database failed."
msgstr ""

#: src/Integrations/Square/Admin/Payments/SingleActionsHandler.php:131
#: src/Integrations/Stripe/Admin/Payments/SingleActionsHandler.php:175
msgid "Subscription cancellation failed."
msgstr ""

#: src/Integrations/Square/Admin/Payments/SingleActionsHandler.php:135
#: src/Integrations/Stripe/Admin/Payments/SingleActionsHandler.php:179
msgid "Subscription cancelled."
msgstr ""

#: src/Integrations/Square/Admin/Payments/SingleActionsHandler.php:138
#: src/Integrations/Stripe/Admin/Payments/SingleActionsHandler.php:182
msgid "Updating subscription in the database failed."
msgstr ""

#: src/Integrations/Square/Admin/Payments/SingleActionsHandler.php:160
#: src/Integrations/Stripe/Admin/Payments/SingleActionsHandler.php:109
msgid "Payment not found in the database."
msgstr ""

#: src/Integrations/Square/Admin/Settings.php:114
msgid "<p>Switching sandbox/production modes requires Square account reconnection.</p><p>Press the <em>\"Connect with Square\"</em> button after saving the settings to reconnect.</p>"
msgstr ""

#: src/Integrations/Square/Admin/Settings.php:123
msgid "Something went wrong while performing a refresh tokens request."
msgstr ""

#: src/Integrations/Square/Admin/Settings.php:124
#: src/Integrations/Square/Admin/Settings.php:140
msgid "Personal Access Token"
msgstr ""

#. translators: %s - the Square developer dashboard URL.
#: src/Integrations/Square/Admin/Settings.php:127
msgid "<p>To receive events, create a webhook route by providing your Personal Access Token, which you can find after registering an app on the <a href=\"%1$s\" target=\"_blank\">Square Developer Dashboard</a>. You can also set it up manually in the Advanced section.</p><p>See <a href=\"%2$s\" target=\"_blank\">our documentation</a> for details.</p>"
msgstr ""

#: src/Integrations/Square/Admin/Settings.php:141
msgid "Personal Access Token is required to proceed."
msgstr ""

#: src/Integrations/Square/Admin/Settings.php:177
#: src/Integrations/Stripe/Admin/Settings.php:196
msgid "Connection Status"
msgstr ""

#: src/Integrations/Square/Admin/Settings.php:190
msgid "Business Location"
msgstr ""

#: src/Integrations/Square/Admin/Settings.php:191
msgid "Only active locations that support credit card processing in Square can be chosen."
msgstr ""

#: src/Integrations/Square/Admin/Settings.php:209
#: src/Integrations/Stripe/Admin/Settings.php:202
msgid "Test Mode"
msgstr ""

#. translators: %s - WPForms.com URL for Square payment with more details.
#: src/Integrations/Square/Admin/Settings.php:212
msgid "Prevent Square from processing live transactions. <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\" class=\"wpforms-learn-more\">Learn More</a>"
msgstr ""

#. translators: %s - WPForms.com Square documentation article URL.
#: src/Integrations/Square/Admin/Settings.php:255
msgid "Easily collect credit card payments with Square. For getting started and more information, see our <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">Square documentation</a>."
msgstr ""

#. translators: %s - Square mode.
#: src/Integrations/Square/Admin/Settings.php:372
msgid "Connected to Square in <strong>%s</strong> mode."
msgstr ""

#. translators: %1$s - Square connected account name; %2$s - Square mode.
#: src/Integrations/Square/Admin/Settings.php:383
msgid "Connected to Square as <em>%1$s</em> in <strong>%2$s</strong> mode."
msgstr ""

#. translators: %s - WPForms.com Square documentation article URL.
#: src/Integrations/Square/Admin/Settings.php:409
msgid "Securely connect to Square with just a few clicks to begin accepting payments! <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\" class=\"wpforms-learn-more\">Learn More</a>"
msgstr ""

#: src/Integrations/Square/Admin/Settings.php:435
msgid "Your connection is missing required data. You must reconnect your Square account."
msgstr ""

#: src/Integrations/Square/Admin/Settings.php:450
msgid "It appears your connection may be invalid. You must refresh tokens or reconnect your account."
msgstr ""

#: src/Integrations/Square/Admin/Settings.php:466
msgid "Your connection is expired. You must refresh tokens or reconnect your account."
msgstr ""

#: src/Integrations/Square/Admin/Settings.php:482
msgid "WPForms currency and Business Location currency are not matched."
msgstr ""

#: src/Integrations/Square/Admin/Settings.php:501
msgid "Connect Square account"
msgstr ""

#: src/Integrations/Square/Admin/Settings.php:502
msgid "Connect with Square"
msgstr ""

#: src/Integrations/Square/Admin/Settings.php:523
msgid "Disconnect Square account"
msgstr ""

#: src/Integrations/Square/Admin/Settings.php:545
msgid "Refresh connection tokens"
msgstr ""

#: src/Integrations/Square/Admin/Settings.php:546
msgid "Refresh tokens"
msgstr ""

#: src/Integrations/Square/Admin/Settings.php:575
msgid "No locations were found"
msgstr ""

#: src/Integrations/Square/Admin/WebhookSettings.php:55
#: src/Integrations/Stripe/Admin/WebhookSettings.php:47
msgid "Enable Webhooks"
msgstr ""

#. translators: %s - WPForms.com URL for Square webhooks documentation.
#: src/Integrations/Square/Admin/WebhookSettings.php:61
msgid "Square uses webhooks to notify WPForms when an event has occurred in your Square account. Please see <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">our documentation on Square webhooks</a> for full details."
msgstr ""

#: src/Integrations/Square/Admin/WebhookSettings.php:100
#: src/Integrations/Stripe/Admin/WebhookSettings.php:77
msgid "Webhooks Method"
msgstr ""

#: src/Integrations/Square/Admin/WebhookSettings.php:104
#: src/Integrations/Stripe/Admin/WebhookSettings.php:81
msgid "REST API (recommended)"
msgstr ""

#: src/Integrations/Square/Admin/WebhookSettings.php:105
#: src/Integrations/Stripe/Admin/WebhookSettings.php:82
msgid "PHP listener"
msgstr ""

#. translators: %s - WPForms.com URL for Square webhooks documentation.
#: src/Integrations/Square/Admin/WebhookSettings.php:109
msgid "Choose the method of communication between Square and WPForms. If REST API support is disabled for WordPress, use PHP listener. <a href=\"%s\" rel=\"nofollow noopener\" target=\"_blank\">Learn more</a>."
msgstr ""

#: src/Integrations/Square/Admin/WebhookSettings.php:133
#: src/Integrations/Stripe/Admin/WebhookSettings.php:102
msgid "Webhooks Endpoint"
msgstr ""

#. translators: %s - Square Dashboard Webhooks Settings URL.
#: src/Integrations/Square/Admin/WebhookSettings.php:139
msgid "Ensure an endpoint with the above URL is present in the <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">Square webhook settings</a>."
msgstr ""

#: src/Integrations/Square/Admin/WebhookSettings.php:155
#: src/Integrations/Stripe/Admin/WebhookSettings.php:123
msgid "Webhooks Test ID"
msgstr ""

#: src/Integrations/Square/Admin/WebhookSettings.php:163
msgid "Webhooks Test Signature Key"
msgstr ""

#: src/Integrations/Square/Admin/WebhookSettings.php:171
#: src/Integrations/Stripe/Admin/WebhookSettings.php:139
msgid "Webhooks Live ID"
msgstr ""

#: src/Integrations/Square/Admin/WebhookSettings.php:179
msgid "Webhooks Live Signature Key"
msgstr ""

#: src/Integrations/Square/Admin/WebhookSettings.php:223
msgid "Make sure that Webhooks Endpoint is updated inside the Square app after Webhooks Method switch."
msgstr ""

#: src/Integrations/Square/Admin/WebhookSettings.php:239
msgid "Live Mode Endpoint Subscription ID"
msgstr ""

#: src/Integrations/Square/Admin/WebhookSettings.php:240
msgid "Test Mode Endpoint Subscription ID"
msgstr ""

#. translators: %1$s - Live Mode Endpoint ID or Test Mode Endpoint ID. %2$s - Square Dashboard Webhooks Settings URL.
#: src/Integrations/Square/Admin/WebhookSettings.php:245
msgid "Retrieve your %1$s from your <a href=\"%2$s\" target=\"_blank\" rel=\"noopener noreferrer\">Square webhook settings</a>. Select the endpoint, then click Copy button."
msgstr ""

#: src/Integrations/Square/Admin/WebhookSettings.php:271
msgid "Live Mode Signature Key"
msgstr ""

#: src/Integrations/Square/Admin/WebhookSettings.php:272
msgid "Test Mode Signature Key"
msgstr ""

#. translators: %1$s - Live Mode Signing Secret or Test Mode Signing Secret. %2$s - Square Dashboard Webhooks Settings URL.
#: src/Integrations/Square/Admin/WebhookSettings.php:277
msgid "Retrieve your %1$s from your <a href=\"%2$s\" target=\"_blank\" rel=\"noopener noreferrer\">Square webhook settings</a>. Select the endpoint, then click Reveal."
msgstr ""

#: src/Integrations/Square/Admin/WebhookSettings.php:408
msgid "Webhooks are connected and active."
msgstr ""

#: src/Integrations/Square/Admin/WebhookSettings.php:414
msgid "Press here to see the further instructions."
msgstr ""

#: src/Integrations/Square/Admin/WebhookSettings.php:415
msgid "Connect Webhooks"
msgstr ""

#. translators: %s - WPForms.com URL for Square webhooks documentation.
#: src/Integrations/Square/Admin/WebhookSettings.php:422
msgid "To start using webhooks, please register a webhook route inside our application. You can do this by pressing the button above or setting the credentials manually. Please see <a href=\"%1$s\" target=\"_blank\">our documentation on Square webhooks</a> for full details."
msgstr ""

#: src/Integrations/Square/Api/Api.php:140
msgid "Square account connection is missing."
msgstr ""

#: src/Integrations/Square/Api/Api.php:146
msgid "Square account connection is invalid."
msgstr ""

#: src/Integrations/Square/Api/Api.php:152
msgid "The currency associated with the payment is not valid for the provided business location."
msgstr ""

#: src/Integrations/Square/Api/Api.php:164
msgid "Square payment stopped, missing card tokens."
msgstr ""

#: src/Integrations/Square/Api/Api.php:178
msgid "Missing location ID."
msgstr ""

#: src/Integrations/Square/Api/Api.php:182
msgid "Missing currency."
msgstr ""

#: src/Integrations/Square/Api/Api.php:186
msgid "Missing amount."
msgstr ""

#: src/Integrations/Square/Api/Api.php:200
msgid "Missing order/payment items."
msgstr ""

#: src/Integrations/Square/Api/Api.php:632
#: src/Integrations/Square/Api/Api.php:641
msgid "Square fail: order was not created."
msgstr ""

#: src/Integrations/Square/Api/Api.php:688
#: src/Integrations/Square/Api/Api.php:699
msgid "Square fail: payment was not processed."
msgstr ""

#: src/Integrations/Square/Api/Api.php:1102
msgid "Missing subscription plan name."
msgstr ""

#: src/Integrations/Square/Api/Api.php:1106
msgid "Missing subscription plan variation name."
msgstr ""

#: src/Integrations/Square/Api/Api.php:1110
msgid "Missing subscription cadence."
msgstr ""

#: src/Integrations/Square/Api/Api.php:1114
msgid "Missing customer name."
msgstr ""

#: src/Integrations/Square/Api/Api.php:1118
msgid "Missing customer email."
msgstr ""

#: src/Integrations/Square/Api/Api.php:1365
#: src/Integrations/Square/Api/Api.php:1374
msgid "Square fail: customer was not created."
msgstr ""

#: src/Integrations/Square/Api/Api.php:1420
#: src/Integrations/Square/Api/Api.php:1429
msgid "Square fail: customer card was not created."
msgstr ""

#: src/Integrations/Square/Api/Api.php:1511
#: src/Integrations/Square/Api/Api.php:1520
msgid "Square fail: subscription plan was not created."
msgstr ""

#: src/Integrations/Square/Api/Api.php:1541
#: src/Integrations/Square/Api/Api.php:1550
msgid "Square fail: subscription plan variation was not created."
msgstr ""

#: src/Integrations/Square/Api/Api.php:1571
#: src/Integrations/Square/Api/Api.php:1580
msgid "Square fail: something went wrong during subscription process."
msgstr ""

#: src/Integrations/Square/Api/Api.php:1628
#: src/Integrations/Square/Api/Api.php:1637
msgid "Square fail: something went wrong during subscription update."
msgstr ""

#: src/Integrations/Square/Api/WebhookRoute.php:156
msgid "It seems to be request to Square PHP Listener method handler but the site is not configured to use it."
msgstr ""

#. translators: %s - transaction id.
#: src/Integrations/Square/Api/Webhooks/RefundUpdated.php:55
msgid "Payment for transaction %s was not updated."
msgstr ""

#: src/Integrations/Square/Api/WebhooksManager.php:48
msgid "Personal access token is required."
msgstr ""

#: src/Integrations/Square/Api/WebhooksManager.php:60
msgid "Webhook created successfully!"
msgstr ""

#: src/Integrations/Square/Api/WebhooksManager.php:63
msgid "Failed to create webhook."
msgstr ""

#: src/Integrations/Square/CurlCompatibility.php:56
msgid "The WPForms Square payments require cURL to be enabled to work correctly."
msgstr ""

#: src/Integrations/Square/Fields/Square.php:99
#: src/Integrations/Square/Fields/Square.php:535
#: src/Integrations/Stripe/Fields/StripeCreditCard.php:61
#: src/Integrations/Stripe/Fields/StripeCreditCard.php:160
msgid "Card"
msgstr ""

#: src/Integrations/Square/Fields/Square.php:381
#: src/Integrations/Stripe/Fields/Traits/CreditCard.php:337
msgid "This page is insecure. Credit Card field should be used for testing purposes only."
msgstr ""

#: src/Integrations/Square/Fields/Square.php:389
msgid "Credit Card field is disabled, Square account connection is missing."
msgstr ""

#: src/Integrations/Square/Fields/Square.php:397
msgid "Credit Card field is disabled, Square account connection is invalid. Please, contact to the site administrator."
msgstr ""

#: src/Integrations/Square/Fields/Square.php:405
msgid "Credit Card field is disabled, Square payments are not enabled in the form settings."
msgstr ""

#: src/Integrations/Square/Fields/Square.php:413
msgid "Heads up! Square account connection is expired. Tokens must be refreshed."
msgstr ""

#: src/Integrations/Square/Fields/Square.php:528
#: src/Integrations/Stripe/Fields/StripeCreditCard.php:142
#: src/Integrations/Stripe/Fields/StripeCreditCard.php:247
msgid "Card number"
msgstr ""

#: src/Integrations/Square/Fields/Square.php:531
msgid "MM/YY"
msgstr ""

#: src/Integrations/Square/Fields/Square.php:532
msgid "CVV"
msgstr ""

#: src/Integrations/Square/Frontend.php:142
msgid "Square.js failed to load properly."
msgstr ""

#: src/Integrations/Square/Frontend.php:143
msgid "An unexpected Square SDK error has occurred."
msgstr ""

#: src/Integrations/Square/Frontend.php:144
msgid "Client ID and/or Location ID is incorrect."
msgstr ""

#: src/Integrations/Square/Frontend.php:145
#: src/Integrations/Square/Integrations/BlockEditor.php:141
msgid "Initializing Card failed."
msgstr ""

#: src/Integrations/Square/Frontend.php:146
msgid "Tokenization of the payment card failed."
msgstr ""

#: src/Integrations/Square/Frontend.php:147
msgid "Tokenization failed with status:"
msgstr ""

#: src/Integrations/Square/Frontend.php:148
msgid "The verification was not successful. An issue occurred while verifying the buyer."
msgstr ""

#: src/Integrations/Square/Frontend.php:149
#: src/Integrations/Stripe/Frontend.php:172
msgid "Please fill out payment details to continue."
msgstr ""

#: src/Integrations/Square/Helpers.php:423
#: src/Integrations/Stripe/Admin/Builder/Traits/ContentTrait.php:446
#: src/Integrations/Stripe/Api/Common.php:361
msgid "Daily"
msgstr ""

#: src/Integrations/Square/Helpers.php:428
#: src/Integrations/Stripe/Admin/Builder/Traits/ContentTrait.php:447
#: src/Integrations/Stripe/Api/Common.php:367
msgid "Weekly"
msgstr ""

#: src/Integrations/Square/Helpers.php:433
#: src/Integrations/Stripe/Admin/Builder/Traits/ContentTrait.php:448
#: src/Integrations/Stripe/Api/Common.php:373
msgid "Monthly"
msgstr ""

#: src/Integrations/Square/Helpers.php:438
#: src/Integrations/Stripe/Admin/Builder/Traits/ContentTrait.php:449
#: src/Integrations/Stripe/Api/Common.php:379
msgid "Quarterly"
msgstr ""

#: src/Integrations/Square/Helpers.php:443
#: src/Integrations/Stripe/Admin/Builder/Traits/ContentTrait.php:450
#: src/Integrations/Stripe/Api/Common.php:385
msgid "Semi-Yearly"
msgstr ""

#: src/Integrations/Square/Helpers.php:448
#: src/Integrations/Stripe/Admin/Builder/Traits/ContentTrait.php:451
#: src/Integrations/Stripe/Api/Common.php:391
msgid "Yearly"
msgstr ""

#: src/Integrations/Square/Process.php:259
msgid "Square payment stopped, account connection is missing."
msgstr ""

#: src/Integrations/Square/Process.php:270
msgid "Square payment stopped, amount is smaller than the allowed minimum amount for a payment."
msgstr ""

#: src/Integrations/Square/Process.php:287
msgid "Square payment stopped, missing payment fields."
msgstr ""

#: src/Integrations/Square/Process.php:436
msgid "Square Subscription payment stopped validation error."
msgstr ""

#: src/Integrations/Square/Process.php:526
msgid "Square subscription payment stopped, missing form settings."
msgstr ""

#: src/Integrations/Square/Process.php:531
msgid "Square subscription payment stopped, customer email not found."
msgstr ""

#: src/Integrations/Square/Process.php:536
msgid "Square subscription payment stopped, customer name not found."
msgstr ""

#. translators: %d - Form ID.
#: src/Integrations/Square/Process.php:710
msgid "Form #%d"
msgstr ""

#: src/Integrations/Square/Process.php:857
msgid "Square subscription payment stopped"
msgstr ""

#: src/Integrations/Square/Process.php:859
msgid "Square payment stopped"
msgstr ""

#. translators: %s - WPForms.com URL for Square webhooks documentation.
#: src/Integrations/Square/WebhooksHealthCheck.php:204
msgid "Looks like you have a problem with your webhooks configuration. Please check and confirm that you've configured the WPForms webhooks in your Square account. This notice will disappear automatically when a new Square request comes in. See our <a href=\"%1$s\" rel=\"nofollow noopener\" target=\"_blank\">documentation</a> for more information."
msgstr ""

#: src/Integrations/Stripe/Admin/Builder/Enqueues.php:48
msgid "When recurring subscription payments are enabled, the Customer Email is required."
msgstr ""

#: src/Integrations/Stripe/Admin/Builder/Enqueues.php:50
msgid "Please go to the <a href=\"#\" class=\"wpforms-stripe-settings-redirect\">Stripe payment settings</a> and select a Customer Email."
msgstr ""

#: src/Integrations/Stripe/Admin/Builder/Notifications.php:55
msgid "Enable for Stripe completed payments"
msgstr ""

#: src/Integrations/Stripe/Admin/Builder/Notifications.php:109
msgid "The Stripe Pro addon is required to enable notification for completed payments. Would you like to install and activate it?"
msgstr ""

#: src/Integrations/Stripe/Admin/Builder/Notifications.php:119
msgid "The Stripe Pro addon is required to enable notification for completed payments. Would you like to activate it?"
msgstr ""

#: src/Integrations/Stripe/Admin/Builder/Notifications.php:127
msgid "Notification for Stripe Completed Payments"
msgstr ""

#: src/Integrations/Stripe/Admin/Builder/Settings.php:283
msgid "The Stripe Pro addon is required to enable conditional logic for payments. Would you like to install and activate it?"
msgstr ""

#: src/Integrations/Stripe/Admin/Builder/Settings.php:293
msgid "The Stripe Pro addon is required to enable conditional logic for payments. Would you like to activate it?"
msgstr ""

#: src/Integrations/Stripe/Admin/Builder/Traits/ContentTrait.php:216
msgid "Stripe Pro"
msgstr ""

#: src/Integrations/Stripe/Admin/Builder/Traits/ContentTrait.php:251
msgid "Heads up! Stripe payments can't be enabled yet."
msgstr ""

#. translators: %1$s - admin area Payments settings page URL.
#: src/Integrations/Stripe/Admin/Builder/Traits/ContentTrait.php:255
msgid "First, please connect to your Stripe account on the <a href=\"%1$s\" class=\"secondary-text\">WPForms Settings</a> page."
msgstr ""

#: src/Integrations/Stripe/Admin/Builder/Traits/ContentTrait.php:287
msgid "To use Stripe, first add the Stripe payment field to your form."
msgstr ""

#: src/Integrations/Stripe/Admin/Builder/Traits/ContentTrait.php:307
msgid "Enable Stripe payments"
msgstr ""

#: src/Integrations/Stripe/Admin/Builder/Traits/ContentTrait.php:326
msgid "Subscriptions"
msgstr ""

#: src/Integrations/Stripe/Admin/Builder/Traits/ContentTrait.php:365
msgid "Enter your payment description. Eg: Donation for the soccer team. Only used for standard one-time payments."
msgstr ""

#: src/Integrations/Stripe/Admin/Builder/Traits/ContentTrait.php:375
msgid "Stripe Payment Receipt"
msgstr ""

#: src/Integrations/Stripe/Admin/Builder/Traits/ContentTrait.php:380
msgid "If you would like to have Stripe send a receipt after payment, select the email field to use. This is optional but recommended. Only used for standard one-time payments."
msgstr ""

#: src/Integrations/Stripe/Admin/Builder/Traits/ContentTrait.php:395
msgid "Select the field that contains the customer's email address. This is optional but recommended."
msgstr ""

#: src/Integrations/Stripe/Admin/Builder/Traits/ContentTrait.php:439
msgid "Recurring Period"
msgstr ""

#: src/Integrations/Stripe/Admin/Builder/Traits/ContentTrait.php:495
msgid "Connect WPForms to Stripe."
msgstr ""

#: src/Integrations/Stripe/Admin/Builder/Traits/ContentTrait.php:511
msgid "Learn more about our Stripe integration."
msgstr ""

#: src/Integrations/Stripe/Admin/Builder/Traits/ContentTrait.php:530
msgid "Select the field that contains the customer's name. This is optional but recommended."
msgstr ""

#: src/Integrations/Stripe/Admin/Builder/Traits/ContentTrait.php:586
msgid "Select the field that contains the customer's address. This is optional but required for some regions."
msgstr ""

#: src/Integrations/Stripe/Admin/Builder/Traits/ContentTrait.php:604
#: src/Integrations/Stripe/Admin/Builder/Traits/ContentTrait.php:614
msgid "Shipping Address"
msgstr ""

#: src/Integrations/Stripe/Admin/Builder/Traits/ContentTrait.php:606
msgid "Select the field that contains the shipping address. This is optional but required for some regions."
msgstr ""

#. translators: %s - general admin settings page URL.
#: src/Integrations/Stripe/Admin/Notices.php:75
msgid "<strong>Pay-as-you-go Pricing</strong><br>3%% fee per-transaction + Stripe fees. <a href=\"%s\">Activate your license</a> to remove additional fees and unlock powerful features."
msgstr ""

#. translators: %s - general admin settings page URL.
#: src/Integrations/Stripe/Admin/Notices.php:91
msgid "<strong>Pay-as-you-go Pricing</strong><br> 3%% fee per-transaction + Stripe fees. <a href=\"%s\">Renew your license</a> to remove additional fees and unlock powerful features."
msgstr ""

#. translators: %s - WPForms.com Upgrade page URL.
#: src/Integrations/Stripe/Admin/Notices.php:122
msgid "<strong>Pay-as-you-go Pricing</strong><br> 3%% fee per-transaction + Stripe fees. <a href=\"%s\" target=\"_blank\">Upgrade to Pro</a> to remove additional fees and unlock powerful features."
msgstr ""

#: src/Integrations/Stripe/Admin/Notices.php:153
msgid "A new and improved Stripe interface is available with new Stripe Pro addon."
msgstr ""

#: src/Integrations/Stripe/Admin/Notices.php:155
msgid "A new and improved Stripe interface is available when you create new forms."
msgstr ""

#: src/Integrations/Stripe/Admin/Notices.php:167
msgid "What's new?"
msgstr ""

#: src/Integrations/Stripe/Admin/Payments/SingleActionsHandler.php:95
msgid "Missing payment ID."
msgstr ""

#: src/Integrations/Stripe/Admin/Payments/SingleActionsHandler.php:155
msgid "Payment ID not provided."
msgstr ""

#: src/Integrations/Stripe/Admin/Payments/SingleActionsHandler.php:169
msgid "Subscription not found in the database."
msgstr ""

#. translators: %s - Payments settings page URL.
#: src/Integrations/Stripe/Admin/Payments/SingleActionsHandler.php:201
msgid "The used Stripe payment collection type doesn't support this action.<br><br> Please <a href='%s'>update your payment collection type</a> to continue processing payments successfully."
msgstr ""

#: src/Integrations/Stripe/Admin/Settings.php:78
msgid "Stripe is not connected for your current payment mode. Please press the \"Connect with Stripe\" button to complete this setup."
msgstr ""

#. translators: %1$s - Selected currency on the WPForms Settings admin page.
#: src/Integrations/Stripe/Admin/Settings.php:114
msgid "<strong>Payments Cannot Be Processed</strong><br>The currency you have set (%1$s) is not supported by Stripe. Please choose a different currency."
msgstr ""

#: src/Integrations/Stripe/Admin/Settings.php:151
msgid "<p>Switching test/live modes requires Stripe account reconnection.</p><p>Press the <em>\"Connect with Stripe\"</em> button after saving the settings to reconnect.</p>"
msgstr ""

#. translators: %s - WPForms.com URL for Stripe payments with more details.
#: src/Integrations/Stripe/Admin/Settings.php:207
msgid "Prevent Stripe from processing live transactions. Please see <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">our documentation on Stripe test payments</a> for full details."
msgstr ""

#: src/Integrations/Stripe/Admin/Settings.php:230
msgid "Credit Card Field Mode"
msgstr ""

#: src/Integrations/Stripe/Admin/Settings.php:235
msgid "Card Element"
msgstr ""

#: src/Integrations/Stripe/Admin/Settings.php:236
msgid "Payment Element"
msgstr ""

#. translators: %s - WPForms.com Stripe documentation article URL.
#: src/Integrations/Stripe/Admin/Settings.php:277
msgid "Easily collect credit card payments with Stripe. For getting started and more information, see our <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">Stripe documentation</a>."
msgstr ""

#. translators: %1$s - Stripe account name connected, %2$s - Stripe mode connected (live or test).
#: src/Integrations/Stripe/Admin/Settings.php:342
msgid "Connected to Stripe as <em>%1$s</em> in <strong>%2$s Mode</strong>."
msgstr ""

#. translators: %s - Stripe connect URL.
#: src/Integrations/Stripe/Admin/Settings.php:356
msgid "<a href=\"%s\">Switch Accounts</a>"
msgstr ""

#. translators: %s - WPForms.com Stripe documentation article URL.
#: src/Integrations/Stripe/Admin/Settings.php:383
msgid "Securely connect to Stripe with just a few clicks to begin accepting payments! <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\" class=\"wpforms-learn-more\">Learn More</a>"
msgstr ""

#: src/Integrations/Stripe/Admin/Settings.php:399
msgid "Connect with Stripe"
msgstr ""

#. translators: %s - WPForms.com Stripe documentation article URL.
#: src/Integrations/Stripe/Admin/Settings.php:415
msgid "Please see <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">our documentation on Stripe Credit Card field modes for full details</a>."
msgstr ""

#. translators: %s - WPForms.com URL for Stripe webhooks documentation.
#: src/Integrations/Stripe/Admin/WebhookSettings.php:53
msgid "Stripe uses webhooks to notify WPForms when an event has occurred in your Stripe account. Please see <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">our documentation on Stripe webhooks</a> for full details."
msgstr ""

#. translators: %s - WPForms.com URL for Stripe webhooks documentation.
#: src/Integrations/Stripe/Admin/WebhookSettings.php:86
msgid "Choose the method of communication between Stripe and WPForms. If REST API support is disabled for WordPress, use PHP listener. <a href=\"%s\" rel=\"nofollow noopener\" target=\"_blank\">Learn more</a>."
msgstr ""

#. translators: %s - Stripe Webhooks Settings url.
#: src/Integrations/Stripe/Admin/WebhookSettings.php:107
msgid "Ensure an endpoint with the above URL is present in the <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">Stripe webhook settings</a>."
msgstr ""

#: src/Integrations/Stripe/Admin/WebhookSettings.php:131
msgid "Webhooks Test Secret"
msgstr ""

#: src/Integrations/Stripe/Admin/WebhookSettings.php:147
msgid "Webhooks Live Secret"
msgstr ""

#: src/Integrations/Stripe/Admin/WebhookSettings.php:168
msgid "Live Mode Endpoint ID"
msgstr ""

#: src/Integrations/Stripe/Admin/WebhookSettings.php:169
msgid "Test Mode Endpoint ID"
msgstr ""

#. translators: %1$s - Live Mode Endpoint ID or Test Mode Endpoint ID. %2$s - WPForms.com Stripe documentation article URL.
#: src/Integrations/Stripe/Admin/WebhookSettings.php:174
msgid "Retrieve your %1$s from your <a href=\"%2$s\" target=\"_blank\" rel=\"noopener noreferrer\">Stripe webhook settings</a>. Select the endpoint, then click Copy button."
msgstr ""

#: src/Integrations/Stripe/Admin/WebhookSettings.php:200
msgid "Live Mode Signing Secret"
msgstr ""

#: src/Integrations/Stripe/Admin/WebhookSettings.php:201
msgid "Test Mode Signing Secret"
msgstr ""

#. translators: %1$s - Live Mode Signing Secret or Test Mode Signing Secret. %2$s - WPForms.com Stripe documentation article URL.
#: src/Integrations/Stripe/Admin/WebhookSettings.php:206
msgid "Retrieve your %1$s from your <a href=\"%2$s\" target=\"_blank\" rel=\"noopener noreferrer\">Stripe webhook settings</a>. Select the endpoint, then click Reveal."
msgstr ""

#: src/Integrations/Stripe/Api/Common.php:304
msgid "Too many requests made to the API too quickly."
msgstr ""

#: src/Integrations/Stripe/Api/Common.php:305
msgid "Invalid parameters were supplied to Stripe API."
msgstr ""

#: src/Integrations/Stripe/Api/Common.php:306
msgid "Authentication with Stripe API failed."
msgstr ""

#: src/Integrations/Stripe/Api/Common.php:307
msgid "Network communication with Stripe failed."
msgstr ""

#: src/Integrations/Stripe/Api/Common.php:308
msgid "Unable to process Stripe payment."
msgstr ""

#: src/Integrations/Stripe/Api/Common.php:309
msgid "Unable to process payment."
msgstr ""

#: src/Integrations/Stripe/Api/PaymentIntents.php:242
msgid "Stripe payment stopped, missing both PaymentMethod and PaymentIntent ids."
msgstr ""

#: src/Integrations/Stripe/Api/PaymentIntents.php:441
msgid "Stripe payment stopped, missing PaymentMethod id."
msgstr ""

#: src/Integrations/Stripe/Api/PaymentIntents.php:470
msgid "Stripe payment stopped. Invalid PaymentIntent status."
msgstr ""

#: src/Integrations/Stripe/Api/PaymentIntents.php:507
msgid "Stripe payment was not confirmed. Please check your Stripe dashboard."
msgstr ""

#: src/Integrations/Stripe/Api/PaymentIntents.php:549
msgid "Stripe subscription stopped, missing PaymentMethod id."
msgstr ""

#: src/Integrations/Stripe/Api/PaymentIntents.php:595
msgid "Stripe subscription stopped. invalid PaymentIntent status."
msgstr ""

#: src/Integrations/Stripe/Api/PaymentIntents.php:632
msgid "Stripe subscription was not confirmed. Please check your Stripe dashboard."
msgstr ""

#: src/Integrations/Stripe/Api/WebhookRoute.php:152
msgid "It seems to be request to Stripe PHP Listener method handler but the site is not configured to use it."
msgstr ""

#. translators: %1$s - Stripe.com URL for domains registration documentation.
#: src/Integrations/Stripe/DomainHealthCheck.php:140
msgid "Heads up! It looks like there's a problem with your domain verification, and Stripe Apple Pay may stop working. If this notice does not disappear in a day, <a href=\"%1$s\" rel=\"nofollow noopener\" target=\"_blank\">please register it manually.</a>"
msgstr ""

#: src/Integrations/Stripe/Fields/PaymentElementCreditCard.php:60
msgid "Link Email"
msgstr ""

#: src/Integrations/Stripe/Fields/PaymentElementCreditCard.php:61
msgid "Select an Email field to autofill your customers’ payment information using Link."
msgstr ""

#: src/Integrations/Stripe/Fields/PaymentElementCreditCard.php:76
#: src/Integrations/Stripe/Fields/PaymentElementCreditCard.php:134
msgid "Stripe Credit Card Email"
msgstr ""

#: src/Integrations/Stripe/Fields/PaymentElementCreditCard.php:96
msgid "Sublabel Position"
msgstr ""

#: src/Integrations/Stripe/Fields/PaymentElementCreditCard.php:108
msgid "Above"
msgstr ""

#: src/Integrations/Stripe/Fields/PaymentElementCreditCard.php:109
msgid "Floating"
msgstr ""

#: src/Integrations/Stripe/Fields/PaymentElementCreditCard.php:224
msgid "CVC"
msgstr ""

#: src/Integrations/Stripe/Fields/Traits/CreditCard.php:22
msgid "Stripe Credit Card"
msgstr ""

#: src/Integrations/Stripe/Fields/Traits/CreditCard.php:210
msgid "<p>AJAX form submissions are required when using the Stripe Credit Card field.</p><p>To proceed, please go to <strong>Settings » General » Advanced</strong> and check <strong>Enable AJAX form submission</strong>.</p>"
msgstr ""

#: src/Integrations/Stripe/Fields/Traits/CreditCard.php:218
msgid "<p>Stripe account connection is required when using the Stripe Credit Card field.</p><p>To proceed, please go to <strong>WPForms Settings » Payments » Stripe</strong> and press <strong>Connect with Stripe</strong> button.</p>"
msgstr ""

#: src/Integrations/Stripe/Fields/Traits/CreditCard.php:226
msgid "<p>Stripe Payments must be enabled when using the Stripe Credit Card field.</p><p>To proceed, please go to <strong>Payments » Stripe</strong> and check <strong>Enable Stripe payments</strong>.</p>"
msgstr ""

#: src/Integrations/Stripe/Fields/Traits/CreditCard.php:343
msgid "Credit Card field is disabled, Stripe keys are missing."
msgstr ""

#: src/Integrations/Stripe/Fields/Traits/CreditCard.php:351
msgid "Credit Card field is disabled, Stripe payments are not enabled in the form settings."
msgstr ""

#: src/Integrations/Stripe/Frontend.php:173
msgid "Payment Element failed to load. Stripe API responded with the message:"
msgstr ""

#: src/Integrations/Stripe/Process.php:208
msgid "Unable to process payment, please try again later."
msgstr ""

#: src/Integrations/Stripe/Process.php:574
msgid "Stripe payment stopped, missing keys."
msgstr ""

#: src/Integrations/Stripe/Process.php:580
msgid "Stripe payment stopped, missing payment fields."
msgstr ""

#: src/Integrations/Stripe/Process.php:585
msgid "Stripe payment stopped, invalid/empty amount."
msgstr ""

#: src/Integrations/Stripe/Process.php:589
msgid "Stripe payment stopped, amount less than minimum charge required."
msgstr ""

#: src/Integrations/Stripe/Process.php:775
msgid "Stripe Subscription payment stopped validation error."
msgstr ""

#. translators: %s - error message.
#: src/Integrations/Stripe/Process.php:907
msgid "Payment Error: %s"
msgstr ""

#: src/Integrations/Stripe/Process.php:914
msgid "Stripe subscription payment stopped by error"
msgstr ""

#: src/Integrations/Stripe/Process.php:916
msgid "Stripe payment stopped by error"
msgstr ""

#: src/Integrations/Stripe/Process.php:1033
msgid "Stripe subscription payment stopped, missing form settings."
msgstr ""

#: src/Integrations/Stripe/Process.php:1038
msgid "Stripe subscription payment stopped, customer email not found."
msgstr ""

#: src/Integrations/Stripe/Process.php:1151
msgid "Secondary form submission was declined."
msgstr ""

#: src/Integrations/Stripe/Process.php:1169
msgid "Irregular activity detected. Your submission has been declined and payment refunded."
msgstr ""

#: src/Integrations/Stripe/StripeAddonCompatibility.php:87
msgid "The WPForms Stripe addon is out of date. To avoid payment processing issues, please upgrade the Stripe addon to the latest version."
msgstr ""

#. translators: %s - WPForms.com URL for Stripe webhooks documentation.
#: src/Integrations/Stripe/WebhooksHealthCheck.php:227
msgid "Heads up! Looks like you have a problem with your webhooks configuration. Please check and confirm that you've configured the WPForms webhooks in your Stripe account. This notice will disappear automatically when a new Stripe request comes in. See our <a href=\"%1$s\" rel=\"nofollow noopener\" target=\"_blank\">documentation</a> for more information."
msgstr ""

#: src/Integrations/UncannyAutomator/UncannyAutomator.php:69
msgid "Uncanny Automator"
msgstr ""

#. translators: %s - plugin name.
#: src/Integrations/UncannyAutomator/UncannyAutomator.php:150
msgid "%s plugin"
msgstr ""

#: src/Integrations/UncannyAutomator/UncannyAutomator.php:165
msgid "Put Your WordPress Site on Autopilot"
msgstr ""

#: src/Integrations/UncannyAutomator/UncannyAutomator.php:166
msgid "Build powerful automations that control what happens on form submission. Connect your forms to Google Sheets, Zoom, social media, membership plugins, elearning platforms, and more with Uncanny Automator."
msgstr ""

#: src/Integrations/UsageTracking/UsageTracking.php:109
msgid "Allow Usage Tracking"
msgstr ""

#: src/Integrations/UsageTracking/UsageTracking.php:110
msgid "By allowing us to track usage data, we can better help you, as we will know which WordPress configurations, themes, and plugins we should test."
msgstr ""

#: src/Integrations/WooCommerce/Notifications.php:133
#: templates/education/admin/settings/smtp-notice.php:15
msgid "Make Sure Important Emails Reach Your Customers"
msgstr ""

#: src/Integrations/WooCommerce/Notifications.php:137
msgid "Solve common email deliverability issues for good."
msgstr ""

#: src/Integrations/WooCommerce/Notifications.php:141
msgid "Get WP Mail SMTP"
msgstr ""

#: src/Integrations/WooCommerce/Notifications.php:147
msgid "Close the notification"
msgstr ""

#. translators: %s - URL WP Mail SMTP settings.
#: src/Integrations/WPMailSMTP/Notifications.php:188
msgid "This setting is disabled because you have the \"Force From Name\" setting enabled in the <a href=\"%s\" target=\"_blank\">WP Mail SMTP</a> plugin."
msgstr ""

#. translators: %s - URL WP Mail SMTP settings.
#: src/Integrations/WPMailSMTP/Notifications.php:217
msgid "This setting is disabled because you have the \"Force From Email\" setting enabled in the <a href=\"%s\" target=\"_blank\">WP Mail SMTP</a> plugin."
msgstr ""

#: src/Lite/Admin/Connect.php:79
msgid "You are not allowed to install plugins."
msgstr ""

#: src/Lite/Admin/Connect.php:87
msgid "There must be a non-developer Lite version installed to upgrade."
msgstr ""

#: src/Lite/Admin/Connect.php:94
msgid "Please enter your license key to connect."
msgstr ""

#: src/Lite/Admin/Connect.php:99
msgid "Only the Lite version can be upgraded."
msgstr ""

#: src/Lite/Admin/Connect.php:115
msgid "WPForms Pro is installed but not activated."
msgstr ""

#: src/Lite/Admin/Connect.php:166
msgid "There was an error while installing an upgrade. Please download the plugin from wpforms.com and install it manually."
msgstr ""

#: src/Lite/Admin/Connect.php:225
msgid "There was an error while installing an upgrade. Please check file system permissions and try again. Also, you can download the plugin from wpforms.com and install it manually."
msgstr ""

#: src/Lite/Admin/Connect.php:251
msgid "No key provided."
msgstr ""

#: src/Lite/Admin/Connect.php:282
msgid "Pro version installed but needs to be activated on the Plugins page inside your WordPress admin."
msgstr ""

#: src/Lite/Admin/ConnectSkin.php:32
msgid "There was an error installing WPForms Pro. Please try again."
msgstr ""

#: src/Lite/Admin/DashboardWidget.php:174
#: src/Lite/Admin/DashboardWidget.php:405
msgid "Show More"
msgstr ""

#: src/Lite/Admin/DashboardWidget.php:175
msgid "Show Less"
msgstr ""

#: src/Lite/Admin/DashboardWidget.php:267
msgid "Create Your First Form to Start Collecting Leads"
msgstr ""

#: src/Lite/Admin/DashboardWidget.php:268
#: templates/admin/empty-states/no-forms.php:18
msgid "You can use WPForms to build contact forms, surveys, payment forms, and more with just a few clicks."
msgstr ""

#: src/Lite/Admin/DashboardWidget.php:272
#: templates/admin/empty-states/no-forms.php:25
msgid "Create Your Form"
msgstr ""

#: src/Lite/Admin/DashboardWidget.php:318
msgid "View all Form Entries inside the WordPress Dashboard"
msgstr ""

#: src/Lite/Admin/DashboardWidget.php:319
msgid "Form entries reports are not available."
msgstr ""

#: src/Lite/Admin/DashboardWidget.php:320
msgid "Form entries are not stored in Lite."
msgstr ""

#: src/Lite/Admin/DashboardWidget.php:321
msgid "Upgrade to Pro and get access to the reports."
msgstr ""

#: src/Lite/Admin/DashboardWidget.php:335
msgid "Total Entries by Form"
msgstr ""

#: src/Lite/Admin/DashboardWidget.php:376
msgid "No entries were submitted yet."
msgstr ""

#: src/Lite/Admin/DashboardWidget.php:404
msgid "Show all forms"
msgstr ""

#: src/Lite/Admin/DashboardWidget.php:435
msgid "Recommended Plugin:"
msgstr ""

#: src/Lite/Admin/DashboardWidget.php:440
msgid "Install"
msgstr ""

#. translators: %s - WPForms version.
#: src/Lite/Admin/DashboardWidget.php:463
msgid "Welcome to <strong>WPForms %s</strong>"
msgstr ""

#: src/Lite/Admin/Education/Admin/DidYouKnow.php:69
msgid "Entries are not stored in WPForms Lite"
msgstr ""

#: src/Lite/Admin/Education/Admin/DidYouKnow.php:70
msgid "Entries are available through email notifications. If you enable Entry Backups, you can restore them once you upgrade to WPForms Pro."
msgstr ""

#: src/Lite/Admin/Education/Admin/DidYouKnow.php:71
#: src/Lite/Admin/Education/LiteConnect.php:226
#: src/Lite/Integrations/LiteConnect/LiteConnect.php:112
msgid "Enable Entry Backups"
msgstr ""

#: src/Lite/Admin/Education/Admin/DidYouKnow.php:77
msgid "Entries Backups Are Enabled"
msgstr ""

#: src/Lite/Admin/Education/Admin/DidYouKnow.php:78
msgid "Restore Form Entries"
msgstr ""

#. translators: %d - backed up entries count.
#: src/Lite/Admin/Education/Admin/DidYouKnow.php:159
#: src/Lite/Admin/Education/LiteConnect.php:265
msgid "%d entry backed up"
msgid_plural "%d entries backed up"
msgstr[0] ""
msgstr[1] ""

#. translators: %1$s - time when Lite Connect was enabled.
#: src/Lite/Admin/Education/Admin/DidYouKnow.php:173
msgid "since %1$s"
msgstr ""

#: src/Lite/Admin/Education/Builder/Confirmations.php:66
msgid "Show entry preview after confirmation"
msgstr ""

#: src/Lite/Admin/Education/Builder/Confirmations.php:75
msgid "Show Entry Preview"
msgstr ""

#: src/Lite/Admin/Education/Builder/DidYouKnow.php:61
msgid "You can have multiple notifications with conditional logic."
msgstr ""

#: src/Lite/Admin/Education/Builder/DidYouKnow.php:74
msgid "You can have multiple confirmations with conditional logic."
msgstr ""

#: src/Lite/Admin/Education/Builder/Fields.php:83
msgid "Smart Logic"
msgstr ""

#. translators: %s - WPForms.com announcement page URL.
#: src/Lite/Admin/Education/Builder/Fields.php:147
msgid "They will not be present in the published form. <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">Upgrade now</a> to unlock these features."
msgstr ""

#: src/Lite/Admin/Education/Builder/Fields.php:162
msgid "Your Form Contains Pro Fields"
msgstr ""

#: src/Lite/Admin/Education/Builder/Notifications.php:77
msgid "Enable File Upload Attachments"
msgstr ""

#: src/Lite/Admin/Education/Builder/Notifications.php:85
msgid "File Upload Attachments"
msgstr ""

#: src/Lite/Admin/Education/Builder/Notifications.php:102
msgid "Enable Entry CSV Attachment"
msgstr ""

#: src/Lite/Admin/Education/Builder/Notifications.php:110
msgid "Entry CSV Attachment"
msgstr ""

#: src/Lite/Admin/Education/LiteConnect.php:227
msgid "No Thanks"
msgstr ""

#: src/Lite/Admin/Education/LiteConnect.php:230
msgid "Enable AI Features"
msgstr ""

#: src/Lite/Admin/Education/LiteConnect.php:231
msgid "AI Features Enabled"
msgstr ""

#: src/Lite/Admin/Education/LiteConnect.php:234
msgid "Are you sure?"
msgstr ""

#: src/Lite/Admin/Education/LiteConnect.php:235
msgid "If you disable Lite Connect, you will no longer be able to restore your entries when you upgrade to WPForms Pro."
msgstr ""

#: src/Lite/Admin/Education/LiteConnect.php:236
msgid "Disable Entry Backups"
msgstr ""

#: src/Lite/Admin/Education/LiteConnect.php:240
msgid "Entry Backups Enabled"
msgstr ""

#: src/Lite/Admin/Education/LiteConnect.php:241
msgid "Awesome! If you decide to upgrade to WPForms Pro, you can restore your entries and will have instant access to reports."
msgstr ""

#: src/Lite/Admin/Education/LiteConnect.php:242
msgid "Entry Backups Disabled"
msgstr ""

#: src/Lite/Admin/Education/LiteConnect.php:243
msgid "Form Entry Backups were successfully disabled."
msgstr ""

#: src/Lite/Admin/Education/LiteConnect.php:245
msgid "Unfortunately, the error occurs while updating Form Entry Backups setting. Please try again later."
msgstr ""

#. translators: %s - time when Lite Connect was enabled.
#: src/Lite/Admin/Education/LiteConnect.php:278
msgid "since %s"
msgstr ""

#: src/Lite/Admin/Education/LiteConnect.php:303
msgid "Enable Form Entry Backups"
msgstr ""

#: src/Lite/Admin/Education/LiteConnect.php:347
#: src/Lite/Admin/Education/LiteConnect.php:383
msgid "Enable Form Entry Backups for Free"
msgstr ""

#: src/Lite/Admin/Pages/Addons.php:92
msgid "Upgrade to Unlock WPForms Addons"
msgstr ""

#: src/Lite/Admin/Pages/Addons.php:93
msgid "Access powerful marketing and payment integrations, advanced form fields, and more when you purchase our Plus, Pro, or Elite plans."
msgstr ""

#: src/Lite/Admin/Settings/Access.php:86
msgid "Access"
msgstr ""

#: src/Lite/Admin/Settings/Access.php:129
#: src/Lite/Admin/Settings/Access.php:254
msgid "Create Forms"
msgstr ""

#: src/Lite/Admin/Settings/Access.php:130
#: src/Lite/Admin/Settings/Access.php:261
msgid "Delete Forms"
msgstr ""

#: src/Lite/Admin/Settings/Access.php:131
#: src/Lite/Admin/Settings/Access.php:267
msgid "Edit Forms Entries"
msgstr ""

#: src/Lite/Admin/Settings/Access.php:132
#: src/Lite/Admin/Settings/Access.php:255
msgid "Edit Forms"
msgstr ""

#: src/Lite/Admin/Settings/Access.php:133
#: src/Lite/Admin/Settings/Access.php:262
msgid "Delete Others Forms"
msgstr ""

#: src/Lite/Admin/Settings/Access.php:134
#: src/Lite/Admin/Settings/Access.php:268
msgid "Edit Others Forms Entries"
msgstr ""

#: src/Lite/Admin/Settings/Access.php:135
#: src/Lite/Admin/Settings/Access.php:256
msgid "Edit Others Forms"
msgstr ""

#: src/Lite/Admin/Settings/Access.php:136
#: src/Lite/Admin/Settings/Access.php:263
msgid "View Forms Entries"
msgstr ""

#: src/Lite/Admin/Settings/Access.php:137
#: src/Lite/Admin/Settings/Access.php:269
msgid "Delete Forms Entries"
msgstr ""

#: src/Lite/Admin/Settings/Access.php:138
#: src/Lite/Admin/Settings/Access.php:257
msgid "View Forms"
msgstr ""

#: src/Lite/Admin/Settings/Access.php:139
#: src/Lite/Admin/Settings/Access.php:264
msgid "View Others Forms Entries"
msgstr ""

#: src/Lite/Admin/Settings/Access.php:140
#: src/Lite/Admin/Settings/Access.php:270
msgid "Delete Others Forms Entries"
msgstr ""

#: src/Lite/Admin/Settings/Access.php:141
#: src/Lite/Admin/Settings/Access.php:258
msgid "View Others Forms"
msgstr ""

#: src/Lite/Admin/Settings/Access.php:147
#: src/Lite/Admin/Settings/Access.php:215
#: src/Lite/Admin/Settings/Access.php:216
msgid "Simple Built-in Controls"
msgstr ""

#: src/Lite/Admin/Settings/Access.php:152
#: src/Lite/Admin/Settings/Access.php:225
#: src/Lite/Admin/Settings/Access.php:226
msgid "Members Integration"
msgstr ""

#: src/Lite/Admin/Settings/Access.php:157
#: src/Lite/Admin/Settings/Access.php:235
#: src/Lite/Admin/Settings/Access.php:236
msgid "User Role Editor Integration"
msgstr ""

#: src/Lite/Admin/Settings/Access.php:162
#: src/Lite/Admin/Settings/Access.php:184
msgid "Access Controls"
msgstr ""

#: src/Lite/Admin/Settings/Access.php:165
msgid "Access controls allows you to manage and customize access to WPForms functionality. You can easily grant or restrict access using the simple built-in controls, or use our official integrations with Members and User Role Editor plugins."
msgstr ""

#: src/Lite/Admin/Settings/Access.php:168
#: src/Lite/Admin/Settings/Access.php:274
msgid "Custom access to the following capabilities…"
msgstr ""

#: src/Lite/Admin/Settings/Access.php:186
msgid "Access controls allows you to manage and customize access to WPForms functionality."
msgstr ""

#: src/Lite/Admin/Settings/Access.php:187
msgid "You can easily grant or restrict access using the simple built-in controls, or use our official integrations with Members and User Role Editor plugins."
msgstr ""

#: src/Lite/Emails/Summaries.php:82
msgid "Calculate WPForms Lite Weekly Entries Count"
msgstr ""

#: src/Lite/Integrations/LiteConnect/LiteConnect.php:111
msgid "Lite Connect"
msgstr ""

#. translators: %s - upgrade to WPForms Pro landing page URL.
#: src/Lite/Integrations/LiteConnect/LiteConnect.php:119
msgid "<strong>Your form entries are not being stored locally, but are backed up remotely.</strong> If you <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\" class=\"wpforms-upgrade-modal\">upgrade to WPForms PRO</a>, you can restore your entries and they’ll be available in the WordPress dashboard."
msgstr ""

#. translators: %s - upgrade to WPForms Pro landing page URL.
#: src/Lite/Integrations/LiteConnect/LiteConnect.php:134
msgid "<strong>Your form entries are not being stored in WordPress, and your entry backups are not active.</strong> If there's a problem with deliverability, you'll lose form entries. We recommend that you enable Entry Backups, especially if you're considering <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\" class=\"wpforms-upgrade-modal\">upgrading to WPForms PRO</a>."
msgstr ""

#: src/Logger/ListTable.php:290
#: src/Logger/ListTable.php:477
msgid "Log Title"
msgstr ""

#: src/Logger/ListTable.php:292
#: src/Logger/ListTable.php:500
#: src/SmartTags/SmartTags.php:116
msgid "Form ID"
msgstr ""

#: src/Logger/ListTable.php:293
#: src/Logger/ListTable.php:490
msgid "Types"
msgstr ""

#: src/Logger/ListTable.php:307
msgid "View Logs"
msgstr ""

#. translators: %s - search query.
#: src/Logger/ListTable.php:312
msgid "Search results for \"%s\""
msgstr ""

#: src/Logger/ListTable.php:321
msgid "Search Logs"
msgstr ""

#: src/Logger/ListTable.php:376
msgid "Delete All Logs"
msgstr ""

#: src/Logger/ListTable.php:421
msgid "No logs found."
msgstr ""

#: src/Logger/ListTable.php:453
msgid "All Logs"
msgstr ""

#: src/Logger/ListTable.php:496
msgid "Log ID"
msgstr ""

#: src/Logger/ListTable.php:512
msgid "Entry ID"
msgstr ""

#: src/Logger/ListTable.php:524
#: src/SmartTags/SmartTags.php:124
msgid "User ID"
msgstr ""

#: src/Logger/Log.php:104
msgid "Errors"
msgstr ""

#: src/Logger/Log.php:107
msgid "Providers"
msgstr ""

#: src/Logger/Log.php:108
msgid "Security"
msgstr ""

#: src/Logger/Log.php:109
msgid "Spam"
msgstr ""

#: src/Logger/Log.php:110
msgid "Translation"
msgstr ""

#: src/Logger/Log.php:206
msgid "Record ID not found"
msgstr ""

#: src/Logger/Log.php:212
msgid "No such record."
msgstr ""

#: src/Migrations/Upgrade177.php:29
msgid "Please fill out all blanks."
msgstr ""

#: src/Providers/Provider/Settings/FormBuilder.php:125
msgid "Custom Field Name"
msgstr ""

#: src/Providers/Provider/Settings/FormBuilder.php:126
msgid "Form Field Value"
msgstr ""

#: src/Providers/Provider/Settings/FormBuilder.php:186
#: src/Providers/Provider/Settings/FormBuilder.php:241
msgid "Field Name"
msgstr ""

#: src/Providers/Provider/Settings/FormBuilder.php:210
#: src/Providers/Provider/Settings/FormBuilder.php:263
msgid "Add Another"
msgstr ""

#: src/Providers/Provider/Settings/FormBuilder.php:216
msgid "Remove"
msgstr ""

#: src/Providers/Provider/Settings/FormBuilder.php:279
msgid "Map custom fields (or properties) to form fields values."
msgstr ""

#: src/Providers/Provider/Settings/FormBuilder.php:346
#: templates/integrations/constant-contact-v3/builder/error.php:10
msgid "Something went wrong while performing an AJAX request."
msgstr ""

#. translators: %s - provider name.
#: src/Providers/Provider/Settings/FormBuilder.php:497
msgid "Get the most out of WPForms &mdash; use it with an active %s account."
msgstr ""

#: src/Providers/Provider/Settings/FormBuilder.php:549
msgid "Add New Connection"
msgstr ""

#. translators: %1$s - Documentation URL.
#: src/Providers/Provider/Settings/FormBuilder.php:754
msgid "Something went wrong and we can’t connect to the provider. Please check your <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">connection settings</a>."
msgstr ""

#: src/Providers/Provider/Settings/PageIntegrations.php:175
msgid "No Label"
msgstr ""

#: src/Providers/Provider/Settings/PageIntegrations.php:331
msgid "Missing data."
msgstr ""

#: src/Providers/Provider/Settings/PageIntegrations.php:347
msgid "Connection missing."
msgstr ""

#: src/Providers/Provider/Settings/PageIntegrations.php:373
msgid "You do not have permissions."
msgstr ""

#: src/Providers/Provider/Settings/PageIntegrations.php:381
msgid "Missing required data in payload."
msgstr ""

#. translators: translators: %1$s - requirements message.
#: src/Requirements/Requirements.php:948
msgid "It requires %1$s."
msgstr ""

#. translators: %s - required PHP version.
#: src/Requirements/Requirements.php:954
msgid "<a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">Learn more</a>"
msgstr ""

#: src/Requirements/Requirements.php:986
msgid "WPForms plugin"
msgstr ""

#. translators: translators: %1$s - WPForms addon name.
#: src/Requirements/Requirements.php:990
msgid "%1$s addon"
msgstr ""

#. translators: translators: %1$s - WPForms plugin or addon name, %2$d - requirements message.
#: src/Requirements/Requirements.php:997
msgid "The %1$s requires %2$s."
msgstr ""

#. translators: %s - required PHP version.
#: src/Requirements/Requirements.php:1004
msgid "<a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">Read more</a> for additional information."
msgstr ""

#. translators: %s - PHP extension name(s).
#: src/Requirements/Requirements.php:1088
msgid "%s PHP extension"
msgid_plural "%s PHP extensions"
msgstr[0] ""
msgstr[1] ""

#. translators: %s - license name(s).
#: src/Requirements/Requirements.php:1159
msgid "%s license"
msgstr ""

#. translators: %1$s - What is being checked (PHP, WPForms, etc.), %2$s - required version. This is used as the completion of the sentence "The {addon name} addon requires {here goes this string}".
#: src/Requirements/Requirements.php:1268
msgid "%1$s %2$s or above"
msgstr ""

#. translators: %1$s - What is being checked (PHP, WPForms, etc.), %2$s - required version. This is used as the completion of the sentence "The {addon name} addon requires {here goes this string}".
#: src/Requirements/Requirements.php:1270
msgid "%1$s %2$s or below"
msgstr ""

#. translators: %1$s - What is being checked (PHP, WPForms, etc.), %2$s - required version. This is used as the completion of the sentence "The {addon name} addon requires {here goes this string}".
#: src/Requirements/Requirements.php:1273
msgid "a newer version of %1$s than %2$s"
msgstr ""

#. translators: %1$s - What is being checked (PHP, WPForms, etc.), %2$s - required version. This is used as the completion of the sentence "The {addon name} addon requires {here goes this string}".
#: src/Requirements/Requirements.php:1275
msgid "an older version of %1$s than %2$s"
msgstr ""

#: src/SmartTags/SmartTag/OrderSummary.php:295
msgid "Subtotal"
msgstr ""

#. translators: %s - Coupon value.
#: src/SmartTags/SmartTag/OrderSummary.php:302
msgid "Coupon (%s)"
msgstr ""

#: src/SmartTags/SmartTags.php:112
msgid "Site Administrator Email"
msgstr ""

#: src/SmartTags/SmartTags.php:113
msgid "Field ID"
msgstr ""

#: src/SmartTags/SmartTags.php:114
msgid "Field HTML ID"
msgstr ""

#: src/SmartTags/SmartTags.php:115
msgid "Field Value"
msgstr ""

#: src/SmartTags/SmartTags.php:118
msgid "Embedded Post/Page Title"
msgstr ""

#: src/SmartTags/SmartTags.php:119
msgid "Embedded Post/Page URL"
msgstr ""

#: src/SmartTags/SmartTags.php:120
msgid "Embedded Post/Page ID"
msgstr ""

#: src/SmartTags/SmartTags.php:122
msgid "Query String Variable"
msgstr ""

#: src/SmartTags/SmartTags.php:123
msgid "User IP Address"
msgstr ""

#: src/SmartTags/SmartTags.php:125
msgid "User Display Name"
msgstr ""

#: src/SmartTags/SmartTags.php:126
msgid "User Full Name"
msgstr ""

#: src/SmartTags/SmartTags.php:127
msgid "User First Name"
msgstr ""

#: src/SmartTags/SmartTags.php:128
msgid "User Last Name"
msgstr ""

#: src/SmartTags/SmartTags.php:129
msgid "User Email"
msgstr ""

#: src/SmartTags/SmartTags.php:130
msgid "User Meta"
msgstr ""

#: src/SmartTags/SmartTags.php:131
msgid "Author ID"
msgstr ""

#: src/SmartTags/SmartTags.php:132
msgid "Author Name"
msgstr ""

#: src/SmartTags/SmartTags.php:133
msgid "Author Email"
msgstr ""

#: src/SmartTags/SmartTags.php:134
msgid "Referrer URL"
msgstr ""

#: src/SmartTags/SmartTags.php:135
msgid "Login URL"
msgstr ""

#: src/SmartTags/SmartTags.php:136
msgid "Logout URL"
msgstr ""

#: src/SmartTags/SmartTags.php:137
msgid "Register URL"
msgstr ""

#: src/SmartTags/SmartTags.php:138
msgid "Lost Password URL"
msgstr ""

#: src/SmartTags/SmartTags.php:139
msgid "Unique Value"
msgstr ""

#: src/SmartTags/SmartTags.php:140
msgid "Site Name"
msgstr ""

#: src/SmartTags/SmartTags.php:141
#: templates/fields/total/summary-preview.php:77
msgid "Order Summary"
msgstr ""

#: src/SmartTags/SmartTags.php:172
msgid "Apply changes"
msgstr ""

#: src/SmartTags/SmartTags.php:173
msgid "Delete smart tag"
msgstr ""

#: src/SmartTags/SmartTags.php:174
msgid "edit"
msgstr ""

#: src/SmartTags/SmartTags.php:175
msgid "argument"
msgstr ""

#: src/SmartTags/SmartTags.php:176
msgid "Unknown Field"
msgstr ""

#. translators: %1$s - field ID, %2$s - field label.
#: src/SmartTags/SmartTags.php:179
msgid "Field %1$s"
msgstr ""

#. translators: %1$s - field ID, %2$s - field label.
#: src/SmartTags/SmartTags.php:181
msgid "Field value %1$s"
msgstr ""

#. translators: %1$s - field ID, %2$s - field label.
#: src/SmartTags/SmartTags.php:183
msgid "Field HTML %1$s"
msgstr ""

#. translators: %1$s - Query String Variable.
#: src/SmartTags/SmartTags.php:185
msgid "Query String Variable: %1$s"
msgstr ""

#. translators: %1$s - User meta key.
#: src/SmartTags/SmartTags.php:187
msgid "User Meta: %1$s"
msgstr ""

#. translators: %1$s - Date format.
#: src/SmartTags/SmartTags.php:189
msgid "Date: %1$s"
msgstr ""

#. translators: %1$s - Date format.
#: src/SmartTags/SmartTags.php:191
msgid "Entry Date: %1$s"
msgstr ""

#. translators: %1$s - link to the WPForms.com doc article.
#: src/SmartTags/SmartTags.php:205
msgid "Easily add dynamic information from various sources with <a href=\"%1$s\" target=\"_blank\" rel=\"noopener noreferrer\">Smart Tags</a>."
msgstr ""

#: templates/admin/challenge/builder.php:16
msgid "Give your form a name so you can easily identify it."
msgstr ""

#: templates/admin/challenge/builder.php:17
#: templates/admin/challenge/builder.php:27
#: templates/admin/challenge/builder.php:33
#: templates/admin/challenge/embed.php:44
#: templates/admin/form-embed-wizard/tooltip.php:41
msgid "Done"
msgstr ""

#: templates/admin/challenge/builder.php:22
msgid "Build your form from scratch or use one of our pre-made templates."
msgstr ""

#: templates/admin/challenge/builder.php:26
msgid "You can add additional fields to your form, if you need them."
msgstr ""

#: templates/admin/challenge/builder.php:31
msgid "Check Notification Settings"
msgstr ""

#: templates/admin/challenge/builder.php:32
msgid "The default notification settings might be sufficient, but double&#8209;check to be sure."
msgstr ""

#: templates/admin/challenge/builder.php:40
msgid "Welcome to the Form Builder"
msgstr ""

#: templates/admin/challenge/builder.php:41
msgid "Our form builder is a full-screen, distraction-free experience where you manage your forms. The following steps will walk you through essential areas."
msgstr ""

#: templates/admin/challenge/builder.php:42
#: templates/admin/form-embed-wizard/popup.php:41
msgid "Let’s Go!"
msgstr ""

#: templates/admin/challenge/embed.php:19
#: templates/admin/form-embed-wizard/tooltip.php:17
msgid "Add a Block"
msgstr ""

#. translators: %s - link to the WPForms documentation page.
#: templates/admin/challenge/embed.php:25
#: templates/admin/form-embed-wizard/tooltip.php:22
msgid "Click the plus button, search for WPForms, click the block to<br>embed it. <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">Learn More</a>"
msgstr ""

#: templates/admin/challenge/embed.php:41
#: templates/admin/challenge/modal.php:42
#: templates/admin/form-embed-wizard/popup.php:18
#: templates/admin/form-embed-wizard/tooltip.php:38
msgid "Embed in a Page"
msgstr ""

#: templates/admin/challenge/embed.php:42
#: templates/admin/form-embed-wizard/tooltip.php:39
msgid "Click the “Add Form” button, select your form, then add the embed code."
msgstr ""

#: templates/admin/challenge/embed.php:53
msgid "Congrats, You Did It!"
msgstr ""

#. translators: %1$s - number of minutes in HTML container, %2$s - singular or plural form of 'minute', %3$s - number of seconds in HTML container, %4$s - singular or plural form of 'second', %5$s - 5 rating star symbols HTML.
#: templates/admin/challenge/embed.php:61
msgid "You completed the WPForms Challenge in <b>%1$s %2$s %3$s %4$s</b>. Share your success story with other WPForms users and help us spread the word <b>by giving WPForms a 5-star rating (%5$s) on WordPress.org</b>. Thanks for your support and we look forward to bringing you more awesome features."
msgstr ""

#: templates/admin/challenge/embed.php:63
#: templates/admin/challenge/embed.php:101
#: templates/admin/challenge/modal.php:31
msgid "minute"
msgid_plural "minutes"
msgstr[0] ""
msgstr[1] ""

#: templates/admin/challenge/embed.php:65
msgid "second"
msgid_plural "seconds"
msgstr[0] ""
msgstr[1] ""

#: templates/admin/challenge/embed.php:81
msgid "Rate WPForms on WordPress.org"
msgstr ""

#: templates/admin/challenge/embed.php:93
msgid "Do you need more help?"
msgstr ""

#. translators: %1$d - number of minutes, %2$s - singular or plural form of 'minute'.
#: templates/admin/challenge/embed.php:99
msgid "We're sorry that it took longer than %1$d %2$s to publish your form. Our goal is to create the most beginner-friendly WordPress form plugin.<br>How can we help you to be successful? Please send us your feedback. Our support team is standing by to help."
msgstr ""

#: templates/admin/challenge/embed.php:110
msgid "Yes, I give WPForms permission to contact me for any follow up questions."
msgstr ""

#: templates/admin/challenge/embed.php:113
msgid "Submit Feedback"
msgstr ""

#: templates/admin/challenge/modal.php:21
msgid "Toggle list"
msgstr ""

#: templates/admin/challenge/modal.php:22
msgid "Skip challenge"
msgstr ""

#: templates/admin/challenge/modal.php:23
msgid "Cancel challenge"
msgstr ""

#. translators: %1$d - number of minutes, %2$s - singular or plural form of 'minute'.
#: templates/admin/challenge/modal.php:29
msgid "Complete the <b>WPForms Challenge</b> and get up and running within %1$d&nbsp;%2$s."
msgstr ""

#: templates/admin/challenge/modal.php:40
msgid "Add Fields to Your Form"
msgstr ""

#: templates/admin/challenge/modal.php:41
msgid "Check Notifications"
msgstr ""

#: templates/admin/challenge/modal.php:43
msgid "Challenge Complete"
msgstr ""

#: templates/admin/challenge/modal.php:54
msgid "WPForms Challenge"
msgstr ""

#. translators: %s - minutes in 2:00 format.
#: templates/admin/challenge/modal.php:59
msgid "%s remaining"
msgstr ""

#: templates/admin/challenge/modal.php:70
msgid "Start Challenge"
msgstr ""

#: templates/admin/challenge/modal.php:73
msgid "Pause"
msgstr ""

#: templates/admin/challenge/modal.php:75
msgid "End Challenge"
msgstr ""

#: templates/admin/challenge/welcome.php:15
msgid "Take the WPForms Challenge"
msgstr ""

#: templates/admin/challenge/welcome.php:16
msgid "Create your first form with our guided setup wizard in less than 5 minutes to experience the WPForms difference."
msgstr ""

#: templates/admin/challenge/welcome.php:19
msgid "Start the WPForms Challenge"
msgstr ""

#: templates/admin/components/datepicker.php:58
msgid "Datepicker options"
msgstr ""

#: templates/admin/dashboard/widget/settings.php:27
msgid "Graph Style"
msgstr ""

#: templates/admin/dashboard/widget/settings.php:31
msgid "Bar"
msgstr ""

#: templates/admin/dashboard/widget/settings.php:35
msgid "Line"
msgstr ""

#: templates/admin/dashboard/widget/settings.php:51
msgid "WordPress"
msgstr ""

#: templates/admin/empty-states/no-forms.php:15
#: templates/admin/empty-states/no-user-templates.php:15
#: templates/admin/empty-states/payments/get-started.php:27
#: templates/admin/empty-states/payments/no-payments.php:16
#: templates/admin/payments/single/no-payment.php:17
#: templates/builder/fullscreen/abort-message.php:18
#: templates/emails/summary-body-plain.php:23
#: templates/emails/summary-body.php:36
msgid "Hi there!"
msgstr ""

#: templates/admin/empty-states/no-forms.php:17
msgid "It looks like you haven’t created any forms yet."
msgstr ""

#. translators: %s - URL to the documentation article.
#: templates/admin/empty-states/no-forms.php:32
msgid "Need some help? Check out our <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">comprehensive guide</a>."
msgstr ""

#: templates/admin/empty-states/no-user-templates.php:18
msgid "Did you know you can save your forms as reusable templates?"
msgstr ""

#: templates/admin/empty-states/no-user-templates.php:19
msgid "Save your custom forms to the templates library for quick and easy use."
msgstr ""

#. translators: %s - URL to the documentation article.
#: templates/admin/empty-states/no-user-templates.php:27
msgid "Need some help? Check out our <a href=\"%s\" rel=\"noopener noreferrer\" target=\"_blank\">documentation</a>."
msgstr ""

#: templates/admin/empty-states/payments/get-started.php:28
msgid "Ready to start collecting payments from your customers?"
msgstr ""

#. translators: %s - URL to the comprehensive guide.
#: templates/admin/empty-states/payments/get-started.php:42
#: templates/admin/empty-states/payments/no-payments.php:31
msgid "Need some help? Check out our <a href=\"%s\" rel=\"noopener noreferrer\" target=\"_blank\">comprehensive guide.</a>"
msgstr ""

#: templates/admin/empty-states/payments/no-payments.php:17
msgid "It looks like you haven't received any payments yet."
msgstr ""

#: templates/admin/empty-states/payments/no-payments.php:18
msgid "Your payment gateway has been configured and you're ready to go."
msgstr ""

#: templates/admin/empty-states/payments/no-payments.php:23
msgid "Go To All Forms"
msgstr ""

#: templates/admin/form-embed-wizard/popup.php:20
msgid "We can help embed your form with just a few clicks!"
msgstr ""

#: templates/admin/form-embed-wizard/popup.php:23
msgid "Would you like to embed your form in an existing page, or create a new one?"
msgstr ""

#: templates/admin/form-embed-wizard/popup.php:29
msgid "Select the page you would like to embed your form in."
msgstr ""

#: templates/admin/form-embed-wizard/popup.php:32
msgid "What would you like to call the new page?"
msgstr ""

#: templates/admin/form-embed-wizard/popup.php:35
msgid "Select Existing Page"
msgstr ""

#: templates/admin/form-embed-wizard/popup.php:36
msgid "Create New Page"
msgstr ""

#: templates/admin/form-embed-wizard/popup.php:40
msgid "Name Your Page"
msgstr ""

#. translators: %1$s - video tutorial toggle CSS classes, %2$s - shortcode toggle CSS classes.
#: templates/admin/form-embed-wizard/popup.php:58
msgid "You can also <a href=\"#\" class=\"%1$s\">embed your form manually</a> or <a href=\"#\" class=\"%2$s\">use a shortcode</a>"
msgstr ""

#. translators: %1$s - video tutorial toggle CSS classes, %2$s - shortcode toggle CSS classes.
#: templates/admin/form-embed-wizard/popup.php:69
msgid "You can embed your form using the <a href=\"#\" class=\"%1$s\">WPForms block</a> or <a href=\"#\" class=\"%2$s\">a shortcode</a>."
msgstr ""

#: templates/admin/form-embed-wizard/popup.php:82
msgid "Copy embed code to clipboard"
msgstr ""

#: templates/admin/form-embed-wizard/popup.php:89
msgid "Go back"
msgstr ""

#: templates/admin/forms/bulk-edit-tags.php:34
msgid "Update"
msgstr ""

#: templates/admin/forms/search-reset.php:25
msgid "Clear search and return to All Forms"
msgstr ""

#: templates/admin/notifications.php:36
msgid "Previous message"
msgstr ""

#: templates/admin/notifications.php:40
msgid "Next message"
msgstr ""

#: templates/admin/pages/constant-contact.php:15
msgid "Constant Contact"
msgstr ""

#: templates/admin/pages/constant-contact.php:17
msgid "Grow Your Website with WPForms + Email Marketing"
msgstr ""

#: templates/admin/pages/constant-contact.php:18
msgid "Wondering if email marketing is really worth your time?"
msgstr ""

#: templates/admin/pages/constant-contact.php:22
msgid "Email is hands-down the most effective way to nurture leads and turn them into customers, with a return on investment (ROI) of <strong>$44 back for every $1 spent</strong> according to DMA."
msgstr ""

#: templates/admin/pages/constant-contact.php:27
msgid "Here are 3 big reasons why every smart business in the world has an email list:"
msgstr ""

#: templates/admin/pages/constant-contact.php:41
msgid "<strong>Email is still #1</strong> - At least 91% of consumers check their email on a daily basis. You get direct access to your subscribers, without having to play by social media's rules and algorithms."
msgstr ""

#: templates/admin/pages/constant-contact.php:49
msgid "<strong>You own your email list</strong> - Unlike with social media, your list is your property and no one can revoke your access to it."
msgstr ""

#: templates/admin/pages/constant-contact.php:57
msgid "<strong>Email converts</strong> - People who buy products marketed through email spend 138% more than those who don't receive email offers."
msgstr ""

#: templates/admin/pages/constant-contact.php:63
msgid "That's why it's crucial to start collecting email addresses and building your list as soon as possible."
msgstr ""

#. translators: %s - WPBeginners.com Guide to Email Lists URL.
#: templates/admin/pages/constant-contact.php:68
msgid "For more details, see this guide on <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">why building your email list is so important</a>."
msgstr ""

#: templates/admin/pages/constant-contact.php:82
msgid "You've Already Started - Here's the Next Step (It's Easy)"
msgstr ""

#: templates/admin/pages/constant-contact.php:83
msgid "Here are the 3 things you need to build an email list:"
msgstr ""

#: templates/admin/pages/constant-contact.php:85
msgid "A Website or Blog"
msgstr ""

#: templates/admin/pages/constant-contact.php:86
msgid "High-Converting Form Builder"
msgstr ""

#: templates/admin/pages/constant-contact.php:87
msgid "The Best Email Marketing Service"
msgstr ""

#: templates/admin/pages/constant-contact.php:89
msgid "With a powerful email marketing service like Constant Contact, you can instantly send out mass notifications and beautifully designed newsletters to engage your subscribers."
msgstr ""

#: templates/admin/pages/constant-contact.php:92
msgid "Get Started with Constant Contact for Free"
msgstr ""

#: templates/admin/pages/constant-contact.php:95
msgid "WPForms plugin makes it fast and easy to capture all kinds of visitor information right from your WordPress site - even if you don't have a Constant Contact account."
msgstr ""

#: templates/admin/pages/constant-contact.php:96
msgid "But when you combine WPForms with Constant Contact, you can nurture your contacts and engage with them even after they leave your website. When you use Constant Contact + WPForms together, you can:"
msgstr ""

#: templates/admin/pages/constant-contact.php:98
msgid "Seamlessly add new contacts to your email list"
msgstr ""

#: templates/admin/pages/constant-contact.php:99
msgid "Create and send professional email newsletters"
msgstr ""

#: templates/admin/pages/constant-contact.php:100
msgid "Get expert marketing and support"
msgstr ""

#: templates/admin/pages/constant-contact.php:104
msgid "Try Constant Contact Today"
msgstr ""

#: templates/admin/pages/constant-contact.php:108
msgid "WPForms Makes List Building Easy"
msgstr ""

#: templates/admin/pages/constant-contact.php:109
msgid "When creating WPForms, our goal was to make a WordPress forms plugin that's both EASY and POWERFUL."
msgstr ""

#: templates/admin/pages/constant-contact.php:110
msgid "We made the form creation process extremely intuitive, so you can create a form to start capturing emails within 5 minutes or less."
msgstr ""

#: templates/admin/pages/constant-contact.php:111
msgid "Here's how it works."
msgstr ""

#: templates/admin/pages/constant-contact.php:116
msgid "Select from our pre-built templates, or create a form from scratch."
msgstr ""

#: templates/admin/pages/constant-contact.php:117
msgid "Drag and drop any field you want onto your signup form."
msgstr ""

#: templates/admin/pages/constant-contact.php:118
msgid "Connect your Constant Contact email list."
msgstr ""

#: templates/admin/pages/constant-contact.php:119
msgid "Add your new form to any post, page, or sidebar."
msgstr ""

#: templates/admin/pages/constant-contact.php:136
msgid "It doesn't matter what kind of business you run, what kind of website you have, or what industry you are in - you need to start building your email list today."
msgstr ""

#: templates/admin/pages/constant-contact.php:137
msgid "With Constant Contact + WPForms, growing your list is easy."
msgstr ""

#: templates/admin/payments/mode-toggle.php:18
msgid "Toggle between live and test data"
msgstr ""

#: templates/admin/payments/mode-toggle.php:20
msgid "Test Data"
msgstr ""

#: templates/admin/payments/reports.php:20
msgid "Payments report indicators"
msgstr ""

#: templates/admin/payments/reports.php:22
msgid "List of data points available for filtering. Click a data point for a detailed report."
msgstr ""

#: templates/admin/payments/reports.php:45
msgid "Comparison to previous period"
msgstr ""

#. translators: %d - number of payments found.
#: templates/admin/payments/reset-filter-notice.php:23
msgid "Found <strong>%d payment</strong>"
msgid_plural "Found <strong>%d payments</strong>"
msgstr[0] ""
msgstr[1] ""

#: templates/admin/payments/reset-filter-notice.php:52
msgid "Reset search"
msgstr ""

#: templates/admin/payments/single/advanced-details.php:19
msgid "Advanced Details"
msgstr ""

#: templates/admin/payments/single/details.php:25
msgid "Details"
msgstr ""

#: templates/admin/payments/single/details.php:35
msgid "Submitted:"
msgstr ""

#: templates/admin/payments/single/details.php:43
msgid "Gateway:"
msgstr ""

#: templates/admin/payments/single/details.php:50
msgid "Form:"
msgstr ""

#: templates/admin/payments/single/details.php:69
msgid "Payment Mode:"
msgstr ""

#: templates/admin/payments/single/entry-details.php:24
msgid "Entry Summary"
msgstr ""

#: templates/admin/payments/single/entry-details.php:79
msgid "View Entry"
msgstr ""

#: templates/admin/payments/single/heading-navigation.php:25
#: templates/admin/payments/single/no-payment.php:24
msgid "Back to All Payments"
msgstr ""

#. translators: %1$d - current number of payment, %2$d - total number of payments.
#: templates/admin/payments/single/heading-navigation.php:32
msgid "Payment %1$d of %2$d"
msgstr ""

#: templates/admin/payments/single/heading-navigation.php:41
msgid "Previous payment"
msgstr ""

#: templates/admin/payments/single/heading-navigation.php:48
msgid "Current payment"
msgstr ""

#: templates/admin/payments/single/heading-navigation.php:53
msgid "Next payment"
msgstr ""

#: templates/admin/payments/single/log.php:26
msgid "No Logs"
msgstr ""

#: templates/admin/payments/single/payment-details.php:62
msgid "Status:"
msgstr ""

#: templates/admin/payments/single/payment-history.php:26
msgid "Subscription Renewal History Table"
msgstr ""

#: templates/admin/payments/single/payment-history.php:29
#: templates/admin/payments/single/payment-history.php:42
msgid "Payment ID"
msgstr ""

#: templates/admin/settings/email-heading.php:15
msgid "Customize your email template and sending preferences."
msgstr ""

#: templates/admin/settings/hcaptcha-description.php:12
msgid "hCaptcha is a free and privacy-oriented spam prevention service. Within your forms, hCaptcha will display a checkbox asking users to prove they're human (much like Google's v2 Checkbox reCAPTCHA). This is a simple step for legitimate site visitors, but is extremely effective at blocking spam."
msgstr ""

#. translators: %s - WPForms.com Setup hCaptcha URL.
#: templates/admin/settings/hcaptcha-description.php:17
msgid "For more details on how hCaptcha works, as well as a step by step setup guide, please check out our <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">documentation</a>."
msgstr ""

#: templates/admin/settings/recaptcha-description.php:12
msgid "reCAPTCHA is a free anti-spam service from Google which helps to protect your website from spam and abuse while letting real people pass through with ease."
msgstr ""

#: templates/admin/settings/recaptcha-description.php:13
msgid "Google offers 3 versions of reCAPTCHA (all supported within WPForms):"
msgstr ""

#: templates/admin/settings/recaptcha-description.php:18
msgid "<strong>v2 Checkbox reCAPTCHA</strong>: Prompts users to check a box to prove they're human."
msgstr ""

#: templates/admin/settings/recaptcha-description.php:26
msgid "<strong>v2 Invisible reCAPTCHA</strong>: Uses advanced technology to detect real users without requiring any input."
msgstr ""

#: templates/admin/settings/recaptcha-description.php:34
msgid "<strong>v3 reCAPTCHA</strong>: Uses a behind-the-scenes scoring system to detect abusive traffic, and lets you decide the minimum passing score. Recommended for advanced use only (or if using Google AMP)."
msgstr ""

#: templates/admin/settings/recaptcha-description.php:40
msgid "Sites already using one type of reCAPTCHA will need to create new site keys before switching to a different option."
msgstr ""

#. translators: %s - WPForms.com Setup reCAPTCHA URL.
#: templates/admin/settings/recaptcha-description.php:45
msgid "<a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">Read our walk through</a> to learn more and for step-by-step directions."
msgstr ""

#: templates/admin/settings/turnstile-description.php:12
msgid "Cloudflare Turnstile is a free, CAPTCHA-like service for preventing form spam while protecting data privacy. It offers a user-friendly experience by confirming visitors are real humans without requiring them to solve puzzles or math questions."
msgstr ""

#. translators: %s - WPForms.com Setup Cloudflare Turnstile URL.
#: templates/admin/settings/turnstile-description.php:17
msgid "For more details on how Turnstile works, as well as a step by step setup guide, please check out our <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">documentation</a>."
msgstr ""

#: templates/admin/splash/section.php:31
msgid "New Feature"
msgstr ""

#: templates/builder/field-context-menu.php:55
msgid "Mark as Optional"
msgstr ""

#: templates/builder/field-context-menu.php:56
msgid "Mark as Required"
msgstr ""

#: templates/builder/field-context-menu.php:65
msgid "Show Label"
msgstr ""

#: templates/builder/field-context-menu.php:120
msgid "Edit Smart Logic"
msgstr ""

#: templates/builder/fullscreen/abort-message.php:27
#: templates/builder/fullscreen/ie-notice.php:40
#: templates/builder/fullscreen/mobile-notice.php:22
msgid "Back to All Forms"
msgstr ""

#: templates/builder/fullscreen/ie-notice.php:16
msgid "You are using an outdated browser!"
msgstr ""

#. translators: %1$s - link to the update Internet Explorer page, %2$s - link to the browse happy page.
#: templates/builder/fullscreen/ie-notice.php:22
msgid "The Internet Explorer browser no more supported.<br>Our form builder is optimized for modern browsers.<br>Please <a href=\"%1$s\" target=\"_blank\" rel=\"nofollow noopener\">install Microsoft Edge</a> or learn<br>how to <a href=\"%2$s\" target=\"_blank\" rel=\"nofollow noopener\">browse happy</a>."
msgstr ""

#: templates/builder/fullscreen/mobile-notice.php:17
msgid "Our form builder is optimized for desktop computers."
msgstr ""

#: templates/builder/fullscreen/mobile-notice.php:18
msgid "We recommend that you edit your forms on a bigger screen. If you'd like to proceed, please understand that some functionality might not behave as expected."
msgstr ""

#: templates/builder/help.php:42
msgid "Ask a question or search the docs..."
msgstr ""

#: templates/builder/help.php:43
msgid "Clear"
msgstr ""

#: templates/builder/help.php:51
msgid "No docs found"
msgstr ""

#: templates/builder/help.php:62
msgid "View Documentation"
msgstr ""

#: templates/builder/help.php:63
msgid "Browse documentation, reference material, and tutorials for WPForms."
msgstr ""

#: templates/builder/help.php:68
msgid "View All Documentation"
msgstr ""

#: templates/builder/help.php:74
msgid "Get Support"
msgstr ""

#: templates/builder/help.php:77
msgid "Submit a ticket and our world class support team will be in touch soon."
msgstr ""

#: templates/builder/help.php:82
msgid "Submit a Support Ticket"
msgstr ""

#: templates/builder/help.php:86
msgid "Upgrade to WPForms Pro to access our world class customer support."
msgstr ""

#: templates/builder/help.php:132
msgid "Unfortunately the error occurred while downloading help data."
msgstr ""

#. translators: %1$s - Opening anchor tag, %2$s - Closing anchor tag.
#: templates/builder/notifications/email-template-link.php:20
msgid "Select a template to use for this notification or %1$sview templates%2$s."
msgstr ""

#: templates/builder/notifications/email-template-modal.php:21
msgid "Choose a Template"
msgstr ""

#: templates/builder/notifications/email-template-modal.php:24
msgid "Browse through our collection of email notification templates."
msgstr ""

#: templates/builder/revisions/list.php:30
msgid "Current Version"
msgstr ""

#. translators: %s - form revision author name.
#: templates/builder/revisions/list.php:38
#: templates/builder/revisions/list.php:68
msgid "by %s"
msgstr ""

#: templates/builder/revisions/list.php:39
#: templates/builder/revisions/list.php:69
msgid "Unknown user"
msgstr ""

#: templates/builder/revisions/notice-disabled.php:20
msgid "Form Revisions Are Disabled"
msgstr ""

#: templates/builder/revisions/notice-disabled.php:21
msgid "It appears that revisions are disabled on your WordPress installation. You can enable revisions for WPForms while leaving posts revisions disabled."
msgstr ""

#: templates/builder/revisions/notice-disabled.php:24
#: templates/builder/revisions/notice-limited.php:33
msgid "Learn How"
msgstr ""

#: templates/builder/revisions/notice-limited.php:22
msgid "Form Revisions Are Limited"
msgstr ""

#. translators: %d - maximum number of revisions to keep.
#: templates/builder/revisions/notice-limited.php:26
msgid "Revisions are enabled, but they’re limited to %d. You can increase this by making a simple change to your WordPress configuration."
msgstr ""

#: templates/builder/templates-item.php:83
msgid "Remove from Favorites"
msgstr ""

#: templates/builder/templates-item.php:84
msgid "Mark as Favorite"
msgstr ""

#: templates/builder/templates-item.php:115
msgid "View Demo"
msgstr ""

#: templates/education/admin/edit-post/notice.php:17
msgid "Oh hey, it looks like you're working on a Contact page."
msgstr ""

#: templates/education/admin/payments/single-page.php:17
msgid "Get More Out of Payments"
msgstr ""

#: templates/education/admin/payments/single-page.php:22
msgid "Unlock conditional logic, coupons, lower Stripe and Square fees, and more."
msgstr ""

#: templates/education/admin/payments/single-page.php:24
msgid "Unlock conditional logic, coupons, lower Stripe fee, and more."
msgstr ""

#. translators: %s - WPForms.com Upgrade page URL.
#: templates/education/admin/payments/single-page.php:29
msgid "<a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">Upgrade to Pro!</a>"
msgstr ""

#. translators: %1$s - link to WPForms SMTP page.
#: templates/education/admin/settings/smtp-notice.php:20
msgid "Solve common email deliverability issues for good. <a href=\"%1$s\" target=\"%2$s\" rel=\"noopener noreferrer\">Get WP Mail SMTP!</a>"
msgstr ""

#. translators: %1$s - site URL; %2$s - site title.
#: templates/emails/classic-footer.php:26
#: templates/emails/compact-footer.php:26
msgid "Sent from <a href=\"%1$s\">%2$s</a>"
msgstr ""

#: templates/emails/summary-body-plain.php:25
#: templates/emails/summary-body.php:37
msgid "Let’s see how your forms performed in the past week."
msgstr ""

#: templates/emails/summary-body-plain.php:28
msgid "Below is the total number of submissions for each form, however actual entries are not stored in WPForms Lite."
msgstr ""

#: templates/emails/summary-body-plain.php:29
msgid "To view future entries inside your WordPress dashboard, and get more detailed reports, consider upgrading to Pro:"
msgstr ""

#. translators: %1$d - number of entries.
#: templates/emails/summary-body-plain.php:38
#: templates/emails/summary-body.php:126
msgid "%1$d Total"
msgstr ""

#: templates/emails/summary-body-plain.php:44
#: templates/emails/summary-body.php:132
msgid "Entry This Week"
msgid_plural "Entries This Week"
msgstr[0] ""
msgstr[1] ""

#: templates/emails/summary-body-plain.php:67
#: templates/emails/summary-body.php:201
msgid "It appears you do not have any form entries yet."
msgstr ""

#: templates/emails/summary-body.php:40
msgid "Below is the total number of submissions for each form. However, form entries are not stored by WPForms Lite."
msgstr ""

#: templates/emails/summary-body.php:45
msgid "We’ve got you covered!"
msgstr ""

#. translators: %1$s - WPForms.com Upgrade page URL.
#: templates/emails/summary-body.php:49
msgid "Your entries are being backed up securely in the cloud. When you’re ready to manage your entries inside WordPress, just <a href=\"%1$s\" target=\"_blank\" rel=\"noopener noreferrer\">upgrade to Pro</a> and we’ll automatically import them in seconds!"
msgstr ""

#: templates/emails/summary-body.php:67
msgid "Check out what else you’ll get with your Pro license."
msgstr ""

#: templates/emails/summary-body.php:73
msgid "Note: Entry backups are not enabled."
msgstr ""

#: templates/emails/summary-body.php:74
msgid "We recommend that you enable entry backups to guard against lost entries."
msgstr ""

#. translators: %1$s - WPForms.com Documentation page URL.
#: templates/emails/summary-body.php:80
msgid "Backups are completely free, 100%% secure, and you can turn them on in a few clicks! <a href=\"%1$s\" target=\"_blank\" rel=\"noopener noreferrer\">Enable entry backups now.</a>"
msgstr ""

#. translators: %1$s - WPForms.com Upgrade page URL.
#: templates/emails/summary-body.php:97
msgid "When you’re ready to manage your entries inside WordPress, <a href=\"%1$s\" target=\"_blank\" rel=\"noopener noreferrer\">upgrade to Pro</a> to import your entries."
msgstr ""

#. translators: %s - link to the site.
#: templates/emails/summary-footer-plain.php:18
msgid "This email was auto-generated and sent from %s."
msgstr ""

#. translators: %s - link to the documentation.
#: templates/emails/summary-footer-plain.php:23
msgid "Learn how to disable: %s."
msgstr ""

#. translators: %1$s - site URL, %2$s - link to the documentation.
#: templates/emails/summary-footer.php:23
msgid "This email was auto-generated and sent from %1$s. Learn <a href=\"%2$s\">how to disable</a>"
msgstr ""

#: templates/fields/total/summary-preview.php:80
msgid "Item"
msgstr ""

#: templates/fields/total/summary-preview.php:82
msgid "Quantity"
msgstr ""

#: templates/fields/total/summary-preview.php:83
msgid "Qty"
msgstr ""

#: templates/fields/total/summary-preview.php:90
msgid "There are no products selected."
msgstr ""

#: templates/integrations/constant-contact-v3/builder/connection.php:47
msgid "Action To Perform"
msgstr ""

#: templates/integrations/constant-contact-v3/builder/connection.php:55
msgid "--- Select Action ---"
msgstr ""

#. translators: %s - URL to the documentation article.
#: templates/integrations/elementor/no-forms.php:32
msgid "Need some help? Check out our <a href=\"%s\" class=\"wpforms-comprehensive-link\" target=\"_blank\" rel=\"noopener noreferrer\">comprehensive guide</a>."
msgstr ""

#: templates/integrations/wpcode/code-snippets.php:20
msgid "Please Install WPCode to Use the WPForms Snippet Library"
msgstr ""

#: templates/integrations/wpcode/code-snippets.php:21
msgid "Install + Activate WPCode"
msgstr ""

#: templates/integrations/wpcode/code-snippets.php:24
msgid "Please Update WPCode to Use the WPForms Snippet Library"
msgstr ""

#: templates/integrations/wpcode/code-snippets.php:25
msgid "Update + Activate WPCode"
msgstr ""

#: templates/integrations/wpcode/code-snippets.php:29
msgid "Please Activate WPCode to Use the WPForms Snippet Library"
msgstr ""

#: templates/integrations/wpcode/code-snippets.php:30
msgid "Activate WPCode"
msgstr ""

#: templates/integrations/wpcode/code-snippets.php:39
msgid "Using WPCode, you can install WPForms code snippets with 1 click right from this page or the WPCode Library in the WordPress admin."
msgstr ""

#: templates/integrations/wpcode/code-snippets.php:45
msgid "Learn more about WPCode"
msgstr ""

#. translators: %s - WPCode library website URL.
#: templates/integrations/wpcode/code-snippets.php:58
msgid "Using WPCode, you can install WPForms code snippets with 1 click directly from this page or the <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">WPCode library</a>."
msgstr ""

#: templates/integrations/wpcode/code-snippets.php:75
msgid "Search Snippets"
msgstr ""

#: templates/integrations/wpcode/code-snippets.php:84
msgid "Edit Snippet"
msgstr ""

#: templates/integrations/wpcode/code-snippets.php:84
msgid "Install Snippet"
msgstr ""

#: templates/integrations/wpcode/code-snippets.php:87
msgid "Installed"
msgstr ""

#: templates/integrations/wpcode/code-snippets.php:104
msgid "Sorry, we didn't find any snippets that match your criteria."
msgstr ""

#. translators: %s - Path to installed plugins.
#: wpforms.php:200
msgid "Your site already has WPForms Pro activated. If you want to switch to WPForms Lite, please first go to %s and deactivate WPForms. Then, you can activate WPForms Lite."
msgstr ""

#: wpforms.php:201
msgid "Network Admin → Plugins → Installed Plugins"
msgstr ""

#: wpforms.php:201
msgid "Plugins → Installed Plugins"
msgstr ""

#. translators: %s - WPBeginner URL for recommended WordPress hosting.
#: wpforms.php:251
msgid "Your site is running an <strong>insecure version</strong> of PHP that is no longer supported. Please contact your web hosting provider to update your PHP version or switch to a <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">recommended WordPress hosting company</a>."
msgstr ""

#. translators: %s - WPForms.com URL for documentation with more details.
#: wpforms.php:269
msgid "<strong>Note:</strong> The WPForms plugin is disabled on your site until you fix the issue. <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">Read more for additional information.</a>"
msgstr ""

#. translators: %s - WordPress version.
#: wpforms.php:319
msgid "The WPForms plugin is disabled because it requires WordPress %s or later."
msgstr ""

#: assets/js/integrations/gutenberg/formselector-legacy.es5.js:298
#: assets/js/integrations/gutenberg/formselector-legacy.js:265
#: assets/js/integrations/gutenberg/modules/common.js:776
#: assets/lite/js/integrations/gutenberg/formselector.es5.js:3116
#: assets/pro/js/integrations/gutenberg/formselector.es5.js:3127
msgid "You haven’t created a form, yet!"
msgstr ""

#: assets/js/integrations/gutenberg/formselector-legacy.es5.js:298
#: assets/js/integrations/gutenberg/formselector-legacy.js:266
#: assets/js/integrations/gutenberg/modules/common.js:777
#: assets/lite/js/integrations/gutenberg/formselector.es5.js:3116
#: assets/pro/js/integrations/gutenberg/formselector.es5.js:3127
msgid "What are you waiting for?"
msgstr ""

#: assets/js/integrations/gutenberg/modules/common.js:1019
#: assets/lite/js/integrations/gutenberg/formselector.es5.js:3323
#: assets/pro/js/integrations/gutenberg/formselector.es5.js:3334
msgid "Need some help? Check out our <a>comprehensive guide.</a>"
msgstr ""
