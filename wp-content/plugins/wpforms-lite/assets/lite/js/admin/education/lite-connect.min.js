var WPFormsEducation=window.WPFormsEducation||{};WPFormsEducation.liteConnect=window.WPFormsEducation.liteConnect||function(i,n,l){const c={init(){l(c.ready),l(n).on("load",function(){"function"==typeof l.ready.then?l.ready.then(c.load):c.load()})},ready(){},load(){c.events(),c.initLiteConnectToggle(),c.maybeRevealBuilderTopBar()},events(){c.enableLiteConnectToggleClick(),c.enableLiteConnectButtonClick(),c.dismissBuilderTopBarClick(),c.autoSaveToggleChange(),c.enableLiteConnectAIButtonClick()},initLiteConnectToggle(){l(".wpforms-toggle-control.wpforms-setting-lite-connect-auto-save-toggle input").prop("disabled",!1)},enableLiteConnectToggleClick(){l(i).on("mousedown touchstart","#wpforms-setting-row-lite-connect-enabled label, .wpforms-setting-lite-connect-auto-save-toggle label",function(e){"ontouchstart"in i.documentElement||e.preventDefault();const t=l(this).closest("#wpforms-setting-row-lite-connect-enabled, .wpforms-setting-lite-connect-auto-save-toggle").find("#wpforms-setting-lite-connect-enabled");t.prop("disabled")||(e=t.is(":checked"),c.openSettingsLiteConnectModal(e,function(){t.trigger("click").prop("disabled",!0)}))})},enableLiteConnectButtonClick(){l(i).on("click",".wpforms-dyk-lite-connect .button-primary",function(e){e.preventDefault();e=l(this);e.hasClass("wpforms-is-enabled")?n.open(e.attr("href")):c.openSettingsLiteConnectModal(!1,c.enableLiteConnectButtonModalConfirm)})},enableLiteConnectAIButtonClick(){l(i).on("click",".enable-lite-connect-modal",c.handleLiteConnectModalClick)},finalizeLiteConnectSetup(){return l.get(wpforms_education_lite_connect.ajax_url,{action:"wpforms_lite_connect_finalize",nonce:wpforms_education_lite_connect.nonce})},handleLiteConnectModalClick(e){e.preventDefault(),c.openAILiteConnectEnableModal(function(){c.saveSettingAjaxPost(!0,l(),function(){c.switchSettingView(!0,l("#wpforms-builder-lite-connect-top-bar .wpforms-toggle-control")),c.finalizeLiteConnectSetup().done(()=>{c.removeLiteConnectModalOnAIButtons()})})})},removeLiteConnectModalOnAIButtons(){l(".enable-lite-connect-modal.wpforms-prevent-default").each(function(){var e=l(this);e.removeClass("enable-lite-connect-modal wpforms-prevent-default"),e.hasClass("wpforms-template-generate")&&e.trigger("click"),l("#wpforms-builder-lite-connect-top-bar").length&&c.toggleBuilderTopBar(!1)})},enableLiteConnectButtonModalConfirm(){const e=l(".wpforms-dyk-lite-connect .button-primary");c.saveSettingAjaxPost(!0,e,function(){c.switchSettingView(!0,e)})},openSettingsLiteConnectModal(e,t){e?c.openSettingsLiteConnectDisableModal(t):c.openSettingsLiteConnectEnableModal(t)},openSettingsLiteConnectEnableModal(e){e={content:wp.template("wpforms-settings-lite-connect-modal-content")(),confirm:{text:wpforms_education_lite_connect.enable_modal.confirm,callback:e}};c.enableModal(e)},openAILiteConnectEnableModal(e){e={content:wp.template("wpforms-builder-ai-lite-connect-modal-content")(),confirm:{text:wpforms_education_lite_connect.enable_ai.confirm,callback:e},theme:"modern, ai-modal"};wpforms_education_lite_connect.update_result.enabled_title=wpforms_education_lite_connect.enable_ai.enabled_title,c.enableModal(e)},enableModal(t){l.alert({title:!1,content:t.content,icon:!1,type:"orange",boxWidth:550,theme:t.theme||"modern",useBootstrap:!1,scrollToPreviousElement:!1,buttons:{confirm:{text:t.confirm.text,btnClass:"btn-confirm",keys:["enter"],action(){var e;"function"==typeof t.confirm.callback&&t.confirm.callback(),"function"==typeof(e=n.WPFormsChallenge?WPFormsChallenge.embed&&WPFormsChallenge.embed.completeChallenge:e)&&e()}},cancel:{text:wpforms_education_lite_connect.enable_modal.cancel,action(){l(".wpforms-challenge-popup-container").removeClass("wpforms-invisible")}}},onOpenBefore(){l("body").addClass("wpforms-setting-lite-connect-modal"),l(".wpforms-challenge-popup-container").addClass("wpforms-invisible")},onDestroy(){l("body").removeClass("wpforms-setting-lite-connect-modal")}})},openSettingsLiteConnectDisableModal(e){l.alert({title:wpforms_education_lite_connect.disable_modal.title,content:wpforms_education_lite_connect.disable_modal.content,icon:"fa fa-exclamation-circle",type:"red",boxWidth:"400px",theme:"modern",useBootstrap:!1,animateFromElement:!1,scrollToPreviousElement:!1,buttons:{cancel:{text:wpforms_education_lite_connect.disable_modal.cancel,keys:["enter"],btnClass:"btn-confirm"},confirm:{text:wpforms_education_lite_connect.disable_modal.confirm,action(){"function"==typeof e&&e()}}}})},saveSettingAjaxPost(t,e,n){const o=(e=e||l()).find("input");l.post(wpforms_education_lite_connect.ajax_url,{action:"wpforms_update_lite_connect_enabled_setting",value:t?1:0,nonce:wpforms_education_lite_connect.nonce}).done(function(e){e.success?(c.updateResultModal(t?"enabled":"disabled"),"function"==typeof n&&n()):(o.prop("checked",!t),c.updateResultModal("error"))}).fail(function(){o.prop("checked",!t),c.updateResultModal("error")}).always(function(){o.prop("disabled",!1)})},autoSaveToggleChange(){l(i).on("change",".wpforms-toggle-control.wpforms-setting-lite-connect-auto-save-toggle input",function(){const e=l(this),t=e.closest(".wpforms-toggle-control"),n=e.is(":checked");c.saveSettingAjaxPost(n,t,function(){c.switchSettingView(n,t),c.removeLiteConnectModalOnAIButtons(),c.finalizeLiteConnectSetup()})})},switchSettingView(e,t){var t=t.closest(".wpforms-education-lite-connect-wrapper"),n=t.find(".wpforms-education-lite-connect-setting"),t=t.find(".wpforms-education-lite-connect-enabled-info");n.toggleClass("wpforms-hidden",e),t.toggleClass("wpforms-hidden",!e)},updateResultModal(e){wpforms_education_lite_connect.update_result[e]&&l.alert({title:wpforms_education_lite_connect.update_result[e+"_title"],content:wpforms_education_lite_connect.update_result[e],icon:"fa fa-check-circle",type:"error"===e?"red":"green",theme:"modern",boxWidth:"400px",useBootstrap:!1,animation:"scale",closeAnimation:"scale",animateFromElement:!1,scrollToPreviousElement:!1,buttons:{confirm:{text:wpforms_education_lite_connect.update_result.close,btnClass:"btn-confirm",keys:["enter"]}}})},maybeRevealBuilderTopBar(){n.wpforms_builder&&"1"!==wpforms_education_lite_connect.is_enabled&&0!==l("#wpforms-builder-lite-connect-top-bar").length&&setTimeout(function(){c.toggleBuilderTopBar(!0)},3e3)},toggleBuilderTopBar(e){var t="--wpforms-admin-bar-height",n=i.documentElement,o=parseInt(getComputedStyle(n).getPropertyValue(t),10);n.setAttribute("style",t+": "+(o+=e?45:-45)+"px!important;")},dismissBuilderTopBarClick(){l(i).on("click","#wpforms-builder-lite-connect-top-bar .wpforms-dismiss-button",function(){c.toggleBuilderTopBar(!1)})}};return c}(document,window,jQuery),WPFormsEducation.liteConnect.init();