var WPFormsEducation=window.WPFormsEducation||{};WPFormsEducation.liteCore=window.WPFormsEducation.liteCore||function(o,l,c){const a={init(){c(a.ready)},ready(){a.events()},events(){a.openModalButtonClick()},openModalButtonClick(){c(o).on("click",".education-modal:not(.wpforms-add-fields-button)",a.openModalButtonHandler).on("mousedown",".education-modal.wpforms-add-fields-button",a.openModalButtonHandler)},openModalButtonHandler(e){var t=c(this);if(!t.data("action")||!["activate","install"].includes(t.data("action"))){e.preventDefault(),e.stopImmediatePropagation();let o=t.data("name");t.hasClass("wpforms-add-fields-button")&&(o=t.text(),o+=o.indexOf(wpforms_builder.field)<0?" "+wpforms_builder.field:"");e=WPFormsEducation.core.getUTMContentValue(t);a.upgradeModal(o,e,t.data("license"),t.data("video"),t.data("plural"))}},upgradeModal(e,t,a,n,d){if(void 0!==a&&0!==a.length||(a="pro"),!(c.inArray(a,["pro","elite"])<0)){var i=wpforms_education.upgrade[a].message.replace(/%name%/g,e);const r=Boolean(n);d=d?wpforms_education.upgrade[a].title_plural:wpforms_education.upgrade[a].title;let o=WPFormsEducation.core.getUpgradeModalWidth(r);const s=c.alert({backgroundDismiss:!0,title:e+" "+d,icon:"fa fa-lock",content:i,boxWidth:o,theme:"modern,wpforms-education",closeIcon:!0,onOpenBefore(){r&&this.$el.addClass("has-video");var o=r?'<iframe src="'+n+'" class="feature-video" allowfullscreen="" width="475" height="267"></iframe>':"";this.$btnc.after('<div class="discount-note">'+wpforms_education.upgrade_bonus+"</div>"),this.$btnc.after(wpforms_education.upgrade[a].doc.replace(/%25name%25/g,e)),this.$btnc.after(o),this.$body.find(".jconfirm-content").addClass("lite-upgrade")},buttons:{confirm:{text:wpforms_education.upgrade[a].button,btnClass:"btn-confirm",keys:["enter"],action:()=>{l.open(WPFormsEducation.core.getUpgradeURL(t,a),"_blank"),WPFormsEducation.core.upgradeModalThankYou(a)}}}});c(l).on("resize",function(){o=WPFormsEducation.core.getUpgradeModalWidth(r),s.isOpen()&&s.setBoxWidth(o)})}}};return a}(document,window,jQuery),WPFormsEducation.liteCore.init();