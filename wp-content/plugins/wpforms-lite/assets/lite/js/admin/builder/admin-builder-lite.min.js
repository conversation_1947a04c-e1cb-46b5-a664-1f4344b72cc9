var WPFormsBuilderLite=window.WPFormsBuilderLite||function(i,e,o){const t={init(){o(t.ready)},ready(){t.bindUIActions()},bindUIActions(){o(i).on("change","#wpforms-panel-field-settings-notification_enable",function(){t.formBuilderNotificationAlert(o(this).is(":checked"))})},formBuilderNotificationAlert(i){!1===i&&o.alert({title:wpforms_builder.heads_up,content:wpforms_builder_lite.disable_notifications,icon:"fa fa-exclamation-circle",type:"orange",buttons:{confirm:{text:wpforms_builder.ok,btnClass:"btn-confirm",keys:["enter"]}}})},initCouponsChoicesJS(){"function"==typeof e.Choices&&o(".wpforms-field-option-row-allowed_coupons select:not(.choices__input)").each(function(){var i=o(this),e=new Choices(i.get(0),{shouldSort:!1,removeItemButton:!0,renderChoicesLimit:5,callbackOnInit(){wpf.showMoreButtonForChoices(this.containerOuter.element)}});i.data("choicesjs",e)})}};return t}(document,window,jQuery);WPFormsBuilderLite.init();