!function n(o,a,l){function s(r,e){if(!a[r]){if(!o[r]){var t="function"==typeof require&&require;if(!e&&t)return t(r,!0);if(i)return i(r,!0);throw new Error("Cannot find module '"+r+"'")}e=a[r]={exports:{}};o[r][0].call(e.exports,function(e){var t=o[r][1][e];return s(t||e)},e,e.exports,n,o,a,l)}return a[r].exports}for(var i="function"==typeof require&&require,e=0;e<l.length;e++)s(l[e]);return s}({1:[function(e,t,r){var n,o,a,t=t.exports={};function l(){}t.nextTick=(o="undefined"!=typeof window&&window.setImmediate,a="undefined"!=typeof window&&window.postMessage&&window.addEventListener,o?function(e){return window.setImmediate(e)}:a?(n=[],window.addEventListener("message",function(e){var t=e.source;t!==window&&null!==t||"process-tick"!==e.data||(e.stopPropagation(),0<n.length&&n.shift()())},!0),function(e){n.push(e),window.postMessage("process-tick","*")}):function(e){setTimeout(e,0)}),t.title="browser",t.browser=!0,t.env={},t.argv=[],t.on=l,t.addListener=l,t.once=l,t.off=l,t.removeListener=l,t.removeAllListeners=l,t.emit=l,t.binding=function(e){throw new Error("process.binding is not supported")},t.cwd=function(){return"/"},t.chdir=function(e){throw new Error("process.chdir is not supported")}},{}],2:[function(e,t,r){"use strict";var i=Object.getOwnPropertySymbols,c=Object.prototype.hasOwnProperty,u=Object.prototype.propertyIsEnumerable;t.exports=function(){try{if(Object.assign){var e=new String("abc");if(e[5]="de","5"!==Object.getOwnPropertyNames(e)[0]){for(var t={},r=0;r<10;r++)t["_"+String.fromCharCode(r)]=r;var n,o=Object.getOwnPropertyNames(t).map(function(e){return t[e]});if("0123456789"===o.join(""))return n={},"abcdefghijklmnopqrst".split("").forEach(function(e){n[e]=e}),"abcdefghijklmnopqrst"===Object.keys(Object.assign({},n)).join("")?1:void 0}}}catch(e){}}()?Object.assign:function(e,t){for(var r,n=function(e){if(null==e)throw new TypeError("Object.assign cannot be called with null or undefined");return Object(e)}(e),o=1;o<arguments.length;o++){for(var a in r=Object(arguments[o]))c.call(r,a)&&(n[a]=r[a]);if(i)for(var l=i(r),s=0;s<l.length;s++)u.call(r,l[s])&&(n[l[s]]=r[l[s]])}return n}},{}],3:[function(t,r,e){!function(i){"use strict";var c,u,d,f=function(){};function e(e,t,r,n,o){if("production"!==i.env.NODE_ENV)for(var a in e)if(d(e,a)){var l,s;try{if("function"!=typeof e[a])throw(s=Error((n||"React class")+": "+r+" type `"+a+"` is invalid; it must be a function, usually from the `prop-types` package, but received `"+typeof e[a]+"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.")).name="Invariant Violation",s;l=e[a](t,a,n,r,null,c)}catch(e){l=e}!l||l instanceof Error||f((n||"React class")+": type specification of "+r+" `"+a+"` is invalid; the type checker function must return `null` or an `Error` but returned a "+typeof l+". You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument)."),l instanceof Error&&!(l.message in u)&&(u[l.message]=!0,a=o?o():"",f("Failed "+r+" type: "+l.message+(null!=a?a:"")))}}"production"!==i.env.NODE_ENV&&(c=t("./lib/ReactPropTypesSecret"),u={},d=t("./lib/has"),f=function(e){e="Warning: "+e;"undefined"!=typeof console&&console.error(e);try{throw new Error(e)}catch(e){}}),e.resetWarningCache=function(){"production"!==i.env.NODE_ENV&&(u={})},r.exports=e}.call(this,t("hmr7eR"))},{"./lib/ReactPropTypesSecret":7,"./lib/has":8,hmr7eR:1}],4:[function(e,t,r){"use strict";var l=e("./lib/ReactPropTypesSecret");function n(){}function o(){}o.resetWarningCache=n,t.exports=function(){function e(e,t,r,n,o,a){if(a!==l)throw(a=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types")).name="Invariant Violation",a}function t(){return e}var r={array:e.isRequired=e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:o,resetWarningCache:n};return r.PropTypes=r}},{"./lib/ReactPropTypesSecret":7}],5:[function(e,t,r){!function(b){"use strict";var c=e("react-is"),y=e("object-assign"),h=e("./lib/ReactPropTypesSecret"),w=e("./lib/has"),n=e("./checkPropTypes"),v=function(){};function o(){return null}"production"!==b.env.NODE_ENV&&(v=function(e){e="Warning: "+e;"undefined"!=typeof console&&console.error(e);try{throw new Error(e)}catch(e){}}),t.exports=function(a,u){var l="function"==typeof Symbol&&Symbol.iterator,s="@@iterator";var d="<<anonymous>>",e={array:t("array"),bigint:t("bigint"),bool:t("boolean"),func:t("function"),number:t("number"),object:t("object"),string:t("string"),symbol:t("symbol"),any:r(o),arrayOf:function(i){return r(function(e,t,r,n,o){if("function"!=typeof i)return new f("Property `"+o+"` of component `"+r+"` has invalid PropType notation inside arrayOf.");var a=e[t];if(!Array.isArray(a))return new f("Invalid "+n+" `"+o+"` of type `"+p(a)+"` supplied to `"+r+"`, expected an array.");for(var l=0;l<a.length;l++){var s=i(a,l,r,n,o+"["+l+"]",h);if(s instanceof Error)return s}return null})},element:r(function(e,t,r,n,o){return e=e[t],a(e)?null:new f("Invalid "+n+" `"+o+"` of type `"+p(e)+"` supplied to `"+r+"`, expected a single ReactElement.")}),elementType:r(function(e,t,r,n,o){return e=e[t],c.isValidElementType(e)?null:new f("Invalid "+n+" `"+o+"` of type `"+p(e)+"` supplied to `"+r+"`, expected a single ReactElement type.")}),instanceOf:function(l){return r(function(e,t,r,n,o){var a;return e[t]instanceof l?null:(a=l.name||d,new f("Invalid "+n+" `"+o+"` of type `"+((n=e[t]).constructor&&n.constructor.name?n.constructor.name:d)+"` supplied to `"+r+"`, expected instance of `"+a+"`."))})},node:r(function(e,t,r,n,o){return i(e[t])?null:new f("Invalid "+n+" `"+o+"` supplied to `"+r+"`, expected a ReactNode.")}),objectOf:function(i){return r(function(e,t,r,n,o){if("function"!=typeof i)return new f("Property `"+o+"` of component `"+r+"` has invalid PropType notation inside objectOf.");var a,l=e[t];if("object"!==(e=p(l)))return new f("Invalid "+n+" `"+o+"` of type `"+e+"` supplied to `"+r+"`, expected an object.");for(a in l)if(w(l,a)){var s=i(l,a,r,n,o+"."+a,h);if(s instanceof Error)return s}return null})},oneOf:function(s){if(Array.isArray(s))return r(function(e,t,r,n,o){for(var a=e[t],l=0;l<s.length;l++)if(function(e,t){return e===t?0!==e||1/e==1/t:e!=e&&t!=t}(a,s[l]))return null;e=JSON.stringify(s,function(e,t){return"symbol"===g(t)?String(t):t});return new f("Invalid "+n+" `"+o+"` of value `"+String(a)+"` supplied to `"+r+"`, expected one of "+e+".")});"production"!==b.env.NODE_ENV&&v(1<arguments.length?"Invalid arguments supplied to oneOf, expected an array, got "+arguments.length+" arguments. A common mistake is to write oneOf(x, y, z) instead of oneOf([x, y, z]).":"Invalid argument supplied to oneOf, expected an array.");return o},oneOfType:function(i){if(!Array.isArray(i))return"production"!==b.env.NODE_ENV&&v("Invalid argument supplied to oneOfType, expected an instance of array."),o;for(var e=0;e<i.length;e++){var t=i[e];if("function"!=typeof t)return v("Invalid argument supplied to oneOfType. Expected an array of check functions, but received "+function(e){var t=g(e);switch(t){case"array":case"object":return"an "+t;case"boolean":case"date":case"regexp":return"a "+t;default:return t}}(t)+" at index "+e+"."),o}return r(function(e,t,r,n,o){for(var a=[],l=0;l<i.length;l++){var s=(0,i[l])(e,t,r,n,o,h);if(null==s)return null;s.data&&w(s.data,"expectedType")&&a.push(s.data.expectedType)}return new f("Invalid "+n+" `"+o+"` supplied to `"+r+"`"+(0<a.length?", expected one of type ["+a.join(", ")+"]":"")+".")})},shape:function(i){return r(function(e,t,r,n,o){var a,l=e[t];if("object"!==(e=p(l)))return new f("Invalid "+n+" `"+o+"` of type `"+e+"` supplied to `"+r+"`, expected `object`.");for(a in i){var s=i[a];if("function"!=typeof s)return m(r,n,o,a,g(s));s=s(l,a,r,n,o+"."+a,h);if(s)return s}return null})},exact:function(c){return r(function(e,t,r,n,o){var a,l=e[t],s=p(l);if("object"!==s)return new f("Invalid "+n+" `"+o+"` of type `"+s+"` supplied to `"+r+"`, expected `object`.");for(a in y({},e[t],c)){var i=c[a];if(w(c,a)&&"function"!=typeof i)return m(r,n,o,a,g(i));if(!i)return new f("Invalid "+n+" `"+o+"` key `"+a+"` supplied to `"+r+"`.\nBad object: "+JSON.stringify(e[t],null,"  ")+"\nValid keys: "+JSON.stringify(Object.keys(c),null,"  "));i=i(l,a,r,n,o+"."+a,h);if(i)return i}return null})}};function f(e,t){this.message=e,this.data=t&&"object"==typeof t?t:{},this.stack=""}function r(s){var i,c;function e(e,t,r,n,o,a,l){if(n=n||d,a=a||r,l!==h){if(u)throw(l=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use `PropTypes.checkPropTypes()` to call them. Read more at http://fb.me/use-check-prop-types")).name="Invariant Violation",l;"production"!==b.env.NODE_ENV&&"undefined"!=typeof console&&!i[l=n+":"+r]&&c<3&&(v("You are manually calling a React.PropTypes validation function for the `"+a+"` prop on `"+n+"`. This is deprecated and will throw in the standalone `prop-types` package. You may be seeing this warning due to a third-party PropTypes library. See https://fb.me/react-warning-dont-call-proptypes for details."),i[l]=!0,c++)}return null==t[r]?e?null===t[r]?new f("The "+o+" `"+a+"` is marked as required in `"+n+"`, but its value is `null`."):new f("The "+o+" `"+a+"` is marked as required in `"+n+"`, but its value is `undefined`."):null:s(t,r,n,o,a)}"production"!==b.env.NODE_ENV&&(i={},c=0);var t=e.bind(null,!1);return t.isRequired=e.bind(null,!0),t}function t(l){return r(function(e,t,r,n,o,a){return p(e=e[t])!==l?new f("Invalid "+n+" `"+o+"` of type `"+g(e)+"` supplied to `"+r+"`, expected `"+l+"`.",{expectedType:l}):null})}function m(e,t,r,n,o){return new f((e||"React class")+": "+t+" type `"+r+"."+n+"` is invalid; it must be a function, usually from the `prop-types` package, but received `"+o+"`.")}function i(e){switch(typeof e){case"number":case"string":case"undefined":return!0;case"boolean":return!e;case"object":if(Array.isArray(e))return e.every(i);if(null!==e&&!a(e)){var t=function(e){if("function"==typeof(e=e&&(l&&e[l]||e[s])))return e}(e);if(!t)return!1;var r,n=t.call(e);if(t!==e.entries){for(;!(r=n.next()).done;)if(!i(r.value))return!1}else for(;!(r=n.next()).done;){var o=r.value;if(o&&!i(o[1]))return!1}}return!0;default:return!1}}function p(e){var t=typeof e;return Array.isArray(e)?"array":e instanceof RegExp?"object":(e=e,"symbol"===t||e&&("Symbol"===e["@@toStringTag"]||"function"==typeof Symbol&&e instanceof Symbol)?"symbol":t)}function g(e){if(null==e)return""+e;var t=p(e);if("object"===t){if(e instanceof Date)return"date";if(e instanceof RegExp)return"regexp"}return t}return f.prototype=Error.prototype,e.checkPropTypes=n,e.resetWarningCache=n.resetWarningCache,e.PropTypes=e}}.call(this,e("hmr7eR"))},{"./checkPropTypes":3,"./lib/ReactPropTypesSecret":7,"./lib/has":8,hmr7eR:1,"object-assign":2,"react-is":11}],6:[function(t,r,e){!function(e){"production"!==e.env.NODE_ENV?(e=t("react-is"),r.exports=t("./factoryWithTypeCheckers")(e.isElement,!0)):r.exports=t("./factoryWithThrowingShims")()}.call(this,t("hmr7eR"))},{"./factoryWithThrowingShims":4,"./factoryWithTypeCheckers":5,hmr7eR:1,"react-is":11}],7:[function(e,t,r){"use strict";t.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},{}],8:[function(e,t,r){t.exports=Function.call.bind(Object.prototype.hasOwnProperty)},{}],9:[function(e,t,O){!function(e){"use strict";function t(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case o:var r=e.type;switch(r){case d:case f:case l:case i:case s:case p:return r;default:var n=r&&r.$$typeof;switch(n){case u:case m:case b:case g:case c:return n;default:return t}}case a:return t}}}function r(e){return t(e)===f}var o,a,l,s,i,c,u,d,f,m,p,n,g,b,y,h,w,v,k,C,S,_,E,P,R,x,T,B,j,I;"production"!==e.env.NODE_ENV&&(e="function"==typeof Symbol&&Symbol.for,o=e?Symbol.for("react.element"):60103,a=e?Symbol.for("react.portal"):60106,l=e?Symbol.for("react.fragment"):60107,s=e?Symbol.for("react.strict_mode"):60108,i=e?Symbol.for("react.profiler"):60114,c=e?Symbol.for("react.provider"):60109,u=e?Symbol.for("react.context"):60110,d=e?Symbol.for("react.async_mode"):60111,f=e?Symbol.for("react.concurrent_mode"):60111,m=e?Symbol.for("react.forward_ref"):60112,p=e?Symbol.for("react.suspense"):60113,n=e?Symbol.for("react.suspense_list"):60120,g=e?Symbol.for("react.memo"):60115,b=e?Symbol.for("react.lazy"):60116,y=e?Symbol.for("react.block"):60121,h=e?Symbol.for("react.fundamental"):60117,w=e?Symbol.for("react.responder"):60118,v=e?Symbol.for("react.scope"):60119,e=f,k=u,C=c,S=o,_=m,E=l,P=b,R=g,x=a,T=i,B=s,j=p,I=!1,O.AsyncMode=d,O.ConcurrentMode=e,O.ContextConsumer=k,O.ContextProvider=C,O.Element=S,O.ForwardRef=_,O.Fragment=E,O.Lazy=P,O.Memo=R,O.Portal=x,O.Profiler=T,O.StrictMode=B,O.Suspense=j,O.isAsyncMode=function(e){return I||(I=!0,console.warn("The ReactIs.isAsyncMode() alias has been deprecated, and will be removed in React 17+. Update your code to use ReactIs.isConcurrentMode() instead. It has the exact same API.")),r(e)||t(e)===d},O.isConcurrentMode=r,O.isContextConsumer=function(e){return t(e)===u},O.isContextProvider=function(e){return t(e)===c},O.isElement=function(e){return"object"==typeof e&&null!==e&&e.$$typeof===o},O.isForwardRef=function(e){return t(e)===m},O.isFragment=function(e){return t(e)===l},O.isLazy=function(e){return t(e)===b},O.isMemo=function(e){return t(e)===g},O.isPortal=function(e){return t(e)===a},O.isProfiler=function(e){return t(e)===i},O.isStrictMode=function(e){return t(e)===s},O.isSuspense=function(e){return t(e)===p},O.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===l||e===f||e===i||e===s||e===p||e===n||"object"==typeof e&&null!==e&&(e.$$typeof===b||e.$$typeof===g||e.$$typeof===c||e.$$typeof===u||e.$$typeof===m||e.$$typeof===h||e.$$typeof===w||e.$$typeof===v||e.$$typeof===y)},O.typeOf=t)}.call(this,e("hmr7eR"))},{hmr7eR:1}],10:[function(e,t,r){"use strict";var n="function"==typeof Symbol&&Symbol.for,o=n?Symbol.for("react.element"):60103,a=n?Symbol.for("react.portal"):60106,l=n?Symbol.for("react.fragment"):60107,s=n?Symbol.for("react.strict_mode"):60108,i=n?Symbol.for("react.profiler"):60114,c=n?Symbol.for("react.provider"):60109,u=n?Symbol.for("react.context"):60110,d=n?Symbol.for("react.async_mode"):60111,f=n?Symbol.for("react.concurrent_mode"):60111,m=n?Symbol.for("react.forward_ref"):60112,p=n?Symbol.for("react.suspense"):60113,g=n?Symbol.for("react.suspense_list"):60120,b=n?Symbol.for("react.memo"):60115,y=n?Symbol.for("react.lazy"):60116,h=n?Symbol.for("react.block"):60121,w=n?Symbol.for("react.fundamental"):60117,v=n?Symbol.for("react.responder"):60118,k=n?Symbol.for("react.scope"):60119;function C(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case o:switch(e=e.type){case d:case f:case l:case i:case s:case p:return e;default:switch(e=e&&e.$$typeof){case u:case m:case y:case b:case c:return e;default:return t}}case a:return t}}}function S(e){return C(e)===f}r.AsyncMode=d,r.ConcurrentMode=f,r.ContextConsumer=u,r.ContextProvider=c,r.Element=o,r.ForwardRef=m,r.Fragment=l,r.Lazy=y,r.Memo=b,r.Portal=a,r.Profiler=i,r.StrictMode=s,r.Suspense=p,r.isAsyncMode=function(e){return S(e)||C(e)===d},r.isConcurrentMode=S,r.isContextConsumer=function(e){return C(e)===u},r.isContextProvider=function(e){return C(e)===c},r.isElement=function(e){return"object"==typeof e&&null!==e&&e.$$typeof===o},r.isForwardRef=function(e){return C(e)===m},r.isFragment=function(e){return C(e)===l},r.isLazy=function(e){return C(e)===y},r.isMemo=function(e){return C(e)===b},r.isPortal=function(e){return C(e)===a},r.isProfiler=function(e){return C(e)===i},r.isStrictMode=function(e){return C(e)===s},r.isSuspense=function(e){return C(e)===p},r.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===l||e===f||e===i||e===s||e===p||e===g||"object"==typeof e&&null!==e&&(e.$$typeof===y||e.$$typeof===b||e.$$typeof===c||e.$$typeof===u||e.$$typeof===m||e.$$typeof===w||e.$$typeof===v||e.$$typeof===k||e.$$typeof===h)},r.typeOf=C},{}],11:[function(t,r,e){!function(e){"use strict";"production"===e.env.NODE_ENV?r.exports=t("./cjs/react-is.production.min.js"):r.exports=t("./cjs/react-is.development.js")}.call(this,t("hmr7eR"))},{"./cjs/react-is.development.js":9,"./cjs/react-is.production.min.js":10,hmr7eR:1}],12:[function(e,t,r){"use strict";var n=d(e("../../../js/integrations/gutenberg/modules/education.js")),o=d(e("../../../js/integrations/gutenberg/modules/common.js")),a=d(e("../../../js/integrations/gutenberg/modules/themes-panel.js")),l=d(e("../../../js/integrations/gutenberg/modules/container-styles.js")),s=d(e("../../../js/integrations/gutenberg/modules/background-styles.js")),i=d(e("../../../js/integrations/gutenberg/modules/button-styles.js")),c=d(e("../../../js/integrations/gutenberg/modules/advanced-settings.js")),u=d(e("../../../js/integrations/gutenberg/modules/field-styles.js"));function d(e){return e&&e.__esModule?e:{default:e}}function f(e){return(f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function m(t,e){var r,n=Object.keys(t);return Object.getOwnPropertySymbols&&(r=Object.getOwnPropertySymbols(t),e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)),n}function p(n){for(var e=1;e<arguments.length;e++){var o=null!=arguments[e]?arguments[e]:{};e%2?m(Object(o),!0).forEach(function(e){var t,r;t=n,r=o[e=e],(e=function(e){e=function(e,t){if("object"!=f(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0===r)return("string"===t?String:Number)(e);r=r.call(e,t||"default");if("object"!=f(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}(e,"string");return"symbol"==f(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(o)):m(Object(o)).forEach(function(e){Object.defineProperty(n,e,Object.getOwnPropertyDescriptor(o,e))})}return n}var g,e=window.WPForms||{};e.FormSelector=e.FormSelector||(g={common:{},panels:{},init:function(){g.education=n.default,g.common=o.default,g.panels.themes=a.default,g.panels.container=l.default,g.panels.background=s.default,g.panels.button=i.default,g.panels.advanced=c.default,g.panels.field=u.default;var e={panels:g.panels,getThemesPanel:g.panels.themes.getThemesPanel,getFieldStyles:g.panels.field.getFieldStyles,getContainerStyles:g.panels.container.getContainerStyles,getBackgroundStyles:g.panels.background.getBackgroundStyles,getButtonStyles:g.panels.button.getButtonStyles,getCommonAttributes:g.getCommonAttributes,setStylesHandlers:g.getStyleHandlers(),education:g.education};g.panels.advanced.init(g.common),g.common.init(e)},getCommonAttributes:function(){return p(p(p(p({},g.panels.field.getBlockAttributes()),g.panels.container.getBlockAttributes()),g.panels.background.getBlockAttributes()),g.panels.button.getBlockAttributes())},getStyleHandlers:function(){return{"background-image":g.panels.background.setContainerBackgroundImage,"background-position":g.panels.background.setContainerBackgroundPosition,"background-repeat":g.panels.background.setContainerBackgroundRepeat,"background-width":g.panels.background.setContainerBackgroundWidth,"background-height":g.panels.background.setContainerBackgroundHeight,"background-color":g.panels.background.setBackgroundColor,"background-url":g.panels.background.setBackgroundUrl}}}),e.FormSelector.init()},{"../../../js/integrations/gutenberg/modules/advanced-settings.js":13,"../../../js/integrations/gutenberg/modules/background-styles.js":15,"../../../js/integrations/gutenberg/modules/button-styles.js":16,"../../../js/integrations/gutenberg/modules/common.js":17,"../../../js/integrations/gutenberg/modules/container-styles.js":18,"../../../js/integrations/gutenberg/modules/education.js":19,"../../../js/integrations/gutenberg/modules/field-styles.js":20,"../../../js/integrations/gutenberg/modules/themes-panel.js":21}],13:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n,o,a,l,s,i,c;r.default=(n=jQuery,o=wp.hooks.addFilter,r=wp.compose.createHigherOrderComponent,a=wp.element.Fragment,l=(wp.blockEditor||wp.editor).InspectorAdvancedControls,s=wp.components.TextareaControl,i=wpforms_gutenberg_form_selector.strings,c={init:function(e){c.common=e,c.hooks(),c.events()},hooks:function(){o("editor.BlockEdit","editorskit/custom-advanced-control",c.withAdvancedControls)},events:function(){n(document).on("focus click","textarea",c.copyPasteFocus)},copyPasteFocus:function(){var e=n(this);e.siblings("label").text()===i.copy_paste_settings&&e.select()},getFields:function(e){var t,r;return"wpforms/form-selector"===(null==e?void 0:e.name)&&null!=e&&null!=(t=e.attributes)&&t.formId?(r=c.common.getSettingsFieldsHandlers(e),React.createElement(l,null,React.createElement("div",{className:c.common.getPanelClass(e)+" advanced"},React.createElement(s,{className:"wpforms-gutenberg-form-selector-custom-css",label:i.custom_css,rows:"5",spellCheck:"false",value:e.attributes.customCss,onChange:function(e){return r.attrChange("customCss",e)}}),React.createElement("div",{className:"wpforms-gutenberg-form-selector-legend",dangerouslySetInnerHTML:{__html:i.custom_css_notice}}),React.createElement(s,{className:"wpforms-gutenberg-form-selector-copy-paste-settings",label:i.copy_paste_settings,rows:"4",spellCheck:"false",value:e.attributes.copyPasteJsonValue,onChange:function(e){return r.pasteSettings(e)}}),React.createElement("div",{className:"wpforms-gutenberg-form-selector-legend",dangerouslySetInnerHTML:{__html:i.copy_paste_notice}})))):null},withAdvancedControls:r(function(t){return function(e){return React.createElement(a,null,React.createElement(t,e),c.getFields(e))}},"withAdvancedControls")})},{}],14:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;e=(e=e("prop-types"))&&e.__esModule?e:{default:e};function n(e){var t=e.attributes,r=e.onRemoveBackground,n=e.onPreviewClicked,e=wp.components.Button,o=wpforms_gutenberg_form_selector.strings;return React.createElement("div",{className:"wpforms-gutenberg-form-selector-background-preview"},React.createElement("style",null,"\n\t\t\t\t\t.wpforms-gutenberg-form-selector-background-preview-image {\n\t\t\t\t\t\t--wpforms-background-url: ".concat(t.backgroundUrl,";\n\t\t\t\t\t}\n\t\t\t\t")),React.createElement("input",{className:"wpforms-gutenberg-form-selector-background-preview-image",onClick:n,tabIndex:0,type:"button",onKeyDown:function(e){"Enter"!==e.key&&" "!==e.key||n()}}),React.createElement(e,{isSecondary:!0,className:"is-destructive",onClick:r},o.remove_image))}n.propTypes={attributes:e.default.object.isRequired,onRemoveBackground:e.default.func.isRequired,onPreviewClicked:e.default.func.isRequired},r.default=n},{"prop-types":6}],15:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var f=(e=e("./background-preview.js"))&&e.__esModule?e:{default:e};var m,p,g,b,y,h,w,v,k,n,C;r.default=(m=(wp.blockEditor||wp.editor).PanelColorSettings,e=wp.components,p=e.SelectControl,g=e.PanelBody,b=e.Flex,y=e.FlexBlock,h=e.__experimentalUnitControl,w=e.TextControl,v=e.Button,e=wpforms_gutenberg_form_selector,k=e.strings,n=e.defaults,C={getBlockAttributes:function(){return{backgroundImage:{type:"string",default:n.backgroundImage},backgroundPosition:{type:"string",default:n.backgroundPosition},backgroundRepeat:{type:"string",default:n.backgroundRepeat},backgroundSizeMode:{type:"string",default:n.backgroundSizeMode},backgroundSize:{type:"string",default:n.backgroundSize},backgroundWidth:{type:"string",default:n.backgroundWidth},backgroundHeight:{type:"string",default:n.backgroundHeight},backgroundColor:{type:"string",default:n.backgroundColor},backgroundUrl:{type:"string",default:n.backgroundUrl}}},getBackgroundStyles:function(t,r,n,e,o){var a=o.isNotDisabled,l=o.isProEnabled,s=o.showBackgroundPreview,i=o.setShowBackgroundPreview,c=o.lastBgImage,u=o.setLastBgImage,o=a?0:-1,d=n.getPanelClass(t)+(a?"":" wpforms-gutenberg-panel-disabled");return React.createElement(g,{className:d,title:k.background_styles},React.createElement("div",{className:"wpforms-gutenberg-form-selector-panel-body",onClick:function(e){if(!a){if(e.stopPropagation(),!l)return n.education.showProModal("background",k.background_styles);n.education.showLicenseModal("background",k.background_styles,"background-styles")}},onKeyDown:function(e){if(!a){if(e.stopPropagation(),!l)return n.education.showProModal("background",k.background_styles);n.education.showLicenseModal("background",k.background_styles,"background-styles")}}},React.createElement(b,{gap:4,align:"flex-start",className:"wpforms-gutenberg-form-selector-flex",justify:"space-between"},React.createElement(y,null,React.createElement(p,{label:k.image,tabIndex:o,value:t.attributes.backgroundImage,options:[{label:k.none,value:"none"},{label:k.media_library,value:"library"},{label:k.stock_photo,value:"stock"}],onChange:function(e){return C.setContainerBackgroundImageWrapper(t,r,e,c,u)}})),React.createElement(y,null,("none"!==t.attributes.backgroundImage||!a)&&React.createElement(p,{label:k.position,value:t.attributes.backgroundPosition,tabIndex:o,options:[{label:k.top_left,value:"top left"},{label:k.top_center,value:"top center"},{label:k.top_right,value:"top right"},{label:k.center_left,value:"center left"},{label:k.center_center,value:"center center"},{label:k.center_right,value:"center right"},{label:k.bottom_left,value:"bottom left"},{label:k.bottom_center,value:"bottom center"},{label:k.bottom_right,value:"bottom right"}],disabled:"none"===t.attributes.backgroundImage&&a,onChange:function(e){return r.styleAttrChange("backgroundPosition",e)}}))),("none"!==t.attributes.backgroundImage||!a)&&React.createElement(b,{gap:4,align:"flex-start",className:"wpforms-gutenberg-form-selector-flex",justify:"space-between"},React.createElement(y,null,React.createElement(p,{label:k.repeat,tabIndex:o,value:t.attributes.backgroundRepeat,options:[{label:k.no_repeat,value:"no-repeat"},{label:k.tile,value:"repeat"},{label:k.repeat_x,value:"repeat-x"},{label:k.repeat_y,value:"repeat-y"}],disabled:"none"===t.attributes.backgroundImage&&a,onChange:function(e){return r.styleAttrChange("backgroundRepeat",e)}})),React.createElement(y,null,React.createElement(p,{label:k.size,tabIndex:o,value:t.attributes.backgroundSizeMode,options:[{label:k.dimensions,value:"dimensions"},{label:k.cover,value:"cover"}],disabled:"none"===t.attributes.backgroundImage&&a,onChange:function(e){return C.handleSizeFromDimensions(t,r,e)}}))),("dimensions"===t.attributes.backgroundSizeMode&&"none"!==t.attributes.backgroundImage||!a)&&React.createElement(b,{gap:4,align:"flex-start",className:"wpforms-gutenberg-form-selector-flex",justify:"space-between"},React.createElement(y,null,React.createElement(h,{label:k.width,tabIndex:o,value:t.attributes.backgroundWidth,isUnitSelectTabbable:a,onChange:function(e){return C.handleSizeFromWidth(t,r,e)}})),React.createElement(y,null,React.createElement(h,{label:k.height,tabIndex:o,value:t.attributes.backgroundHeight,isUnitSelectTabbable:a,onChange:function(e){return C.handleSizeFromHeight(t,r,e)}}))),(!s||"url()"===t.attributes.backgroundUrl)&&("library"===t.attributes.backgroundImage&&React.createElement(b,{gap:4,align:"flex-start",className:"wpforms-gutenberg-form-selector-flex",justify:"space-between"},React.createElement(y,null,React.createElement(v,{isSecondary:!0,tabIndex:o,className:"wpforms-gutenberg-form-selector-media-library-button",onClick:C.openMediaLibrary.bind(null,t,r,i)},k.choose_image)))||"stock"===t.attributes.backgroundImage&&React.createElement(b,{gap:4,align:"flex-start",className:"wpforms-gutenberg-form-selector-flex",justify:"space-between"},React.createElement(y,null,React.createElement(v,{isSecondary:!0,tabIndex:o,className:"wpforms-gutenberg-form-selector-media-library-button",onClick:null==e?void 0:e.openModal.bind(null,t,r,"bg-styles",i)},k.choose_image)))),(s&&"none"!==t.attributes.backgroundImage||"url()"!==t.attributes.backgroundUrl)&&React.createElement(b,{gap:4,align:"flex-start",className:"wpforms-gutenberg-form-selector-flex",justify:"space-between"},React.createElement(y,null,React.createElement("div",null,React.createElement(f.default,{attributes:t.attributes,onRemoveBackground:function(){C.onRemoveBackground(i,r,u)},onPreviewClicked:function(){return"library"===t.attributes.backgroundImage?C.openMediaLibrary(t,r,i):null==e?void 0:e.openModal(t,r,"bg-styles",i)}})),React.createElement(w,{label:k.image_url,tabIndex:o,value:"none"!==t.attributes.backgroundImage&&t.attributes.backgroundUrl,className:"wpforms-gutenberg-form-selector-image-url",onChange:function(e){return r.styleAttrChange("backgroundUrl",e)},onLoad:function(e){return"none"!==t.attributes.backgroundImage&&r.styleAttrChange("backgroundUrl",e)}}))),React.createElement(b,{gap:4,align:"flex-start",className:"wpforms-gutenberg-form-selector-flex",justify:"space-between"},React.createElement(y,null,React.createElement("div",{className:"wpforms-gutenberg-form-selector-control-label"},k.colors),React.createElement(m,{__experimentalIsRenderedInSidebar:!0,enableAlpha:!0,showTitle:!1,tabIndex:o,className:"wpforms-gutenberg-form-selector-color-panel",colorSettings:[{value:t.attributes.backgroundColor,onChange:function(e){a&&r.styleAttrChange("backgroundColor",e)},label:k.background}]})))))},openMediaLibrary:function(r,n,o){var a=wp.media({title:k.select_background_image,multiple:!1,library:{type:"image"},button:{text:k.select_image}});a.on("select",function(){var e=a.state().get("selection").first().toJSON(),t={};e.url&&(e="url(".concat(e.url,")"),t.backgroundUrl=e,r.setAttributes(t),n.styleAttrChange("backgroundUrl",e),o(!0))}),a.open()},setContainerBackgroundImage:function(e,t){return"none"===t&&e.style.setProperty("--wpforms-background-url","url()"),!0},setContainerBackgroundImageWrapper:function(e,t,r,n,o){"none"===r?(o(e.attributes.backgroundUrl),e.attributes.backgroundUrl="url()",t.styleAttrChange("backgroundUrl","url()")):n&&(e.attributes.backgroundUrl=n,t.styleAttrChange("backgroundUrl",n)),t.styleAttrChange("backgroundImage",r)},setContainerBackgroundPosition:function(e,t){return e.style.setProperty("--wpforms-background-position",t),!0},setContainerBackgroundRepeat:function(e,t){return e.style.setProperty("--wpforms-background-repeat",t),!0},handleSizeFromDimensions:function(e,t,r){"cover"===r?(e.attributes.backgroundSize="cover",t.styleAttrChange("backgroundWidth",e.attributes.backgroundWidth),t.styleAttrChange("backgroundHeight",e.attributes.backgroundHeight),t.styleAttrChange("backgroundSizeMode","cover"),t.styleAttrChange("backgroundSize","cover")):(e.attributes.backgroundSize="dimensions",t.styleAttrChange("backgroundSizeMode","dimensions"),t.styleAttrChange("backgroundSize",e.attributes.backgroundWidth+" "+e.attributes.backgroundHeight))},handleSizeFromWidth:function(e,t,r){e.attributes.backgroundSize=r+" "+e.attributes.backgroundHeight,e.attributes.backgroundWidth=r,t.styleAttrChange("backgroundSize",r+" "+e.attributes.backgroundHeight),t.styleAttrChange("backgroundWidth",r)},handleSizeFromHeight:function(e,t,r){e.attributes.backgroundSize=e.attributes.backgroundWidth+" "+r,e.attributes.backgroundHeight=r,t.styleAttrChange("backgroundSize",e.attributes.backgroundWidth+" "+r),t.styleAttrChange("backgroundHeight",r)},setContainerBackgroundWidth:function(e,t){return e.style.setProperty("--wpforms-background-width",t),!0},setContainerBackgroundHeight:function(e,t){return e.style.setProperty("--wpforms-background-height",t),!0},setBackgroundUrl:function(e,t){return e.style.setProperty("--wpforms-background-url",t),!0},setBackgroundColor:function(e,t){return e.style.setProperty("--wpforms-background-color",t),!0},_showBackgroundPreview:function(e){return"none"!==e.attributes.backgroundImage&&e.attributes.backgroundUrl&&"url()"!==e.attributes.backgroundUrl},onRemoveBackground:function(e,t,r){e(!1),t.styleAttrChange("backgroundUrl","url()"),r("")}})},{"./background-preview.js":14}],16:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var o,a,l,s,i,c,u,n;r.default=(o=(wp.blockEditor||wp.editor).PanelColorSettings,r=wp.components,a=r.SelectControl,l=r.PanelBody,s=r.Flex,i=r.FlexBlock,c=r.__experimentalUnitControl,r=wpforms_gutenberg_form_selector,u=r.strings,n=r.defaults,{getBlockAttributes:function(){return{buttonSize:{type:"string",default:n.buttonSize},buttonBorderStyle:{type:"string",default:n.buttonBorderStyle},buttonBorderSize:{type:"string",default:n.buttonBorderSize},buttonBorderRadius:{type:"string",default:n.buttonBorderRadius},buttonBackgroundColor:{type:"string",default:n.buttonBackgroundColor},buttonTextColor:{type:"string",default:n.buttonTextColor},buttonBorderColor:{type:"string",default:n.buttonBorderColor}}},getButtonStyles:function(e,t,r,n){return React.createElement(l,{className:n.getPanelClass(e),title:u.button_styles},React.createElement(s,{gap:4,align:"flex-start",className:"wpforms-gutenberg-form-selector-flex",justify:"space-between"},React.createElement(i,null,React.createElement(a,{label:u.size,value:e.attributes.buttonSize,options:r,onChange:function(e){return t.styleAttrChange("buttonSize",e)}})),React.createElement(i,null,React.createElement(a,{label:u.border,value:e.attributes.buttonBorderStyle,options:[{label:u.none,value:"none"},{label:u.solid,value:"solid"},{label:u.dashed,value:"dashed"},{label:u.dotted,value:"dotted"}],onChange:function(e){return t.styleAttrChange("buttonBorderStyle",e)}}))),React.createElement(s,{gap:4,align:"flex-start",className:"wpforms-gutenberg-form-selector-flex",justify:"space-between"},React.createElement(i,null,React.createElement(c,{label:u.border_size,value:"none"===e.attributes.buttonBorderStyle?"":e.attributes.buttonBorderSize,min:0,disabled:"none"===e.attributes.buttonBorderStyle,onChange:function(e){return t.styleAttrChange("buttonBorderSize",e)},isUnitSelectTabbable:!0})),React.createElement(i,null,React.createElement(c,{onChange:function(e){return t.styleAttrChange("buttonBorderRadius",e)},label:u.border_radius,min:0,isUnitSelectTabbable:!0,value:e.attributes.buttonBorderRadius}))),React.createElement("div",{className:"wpforms-gutenberg-form-selector-color-picker"},React.createElement("div",{className:"wpforms-gutenberg-form-selector-control-label"},u.colors),React.createElement(o,{__experimentalIsRenderedInSidebar:!0,enableAlpha:!0,showTitle:!1,className:n.getColorPanelClass(e.attributes.buttonBorderStyle),colorSettings:[{value:e.attributes.buttonBackgroundColor,onChange:function(e){return t.styleAttrChange("buttonBackgroundColor",e)},label:u.background},{value:e.attributes.buttonBorderColor,onChange:function(e){return t.styleAttrChange("buttonBorderColor",e)},label:u.border},{value:e.attributes.buttonTextColor,onChange:function(e){return t.styleAttrChange("buttonTextColor",e)},label:u.text}]}),React.createElement("div",{className:"wpforms-gutenberg-form-selector-legend wpforms-button-color-notice"},u.button_color_notice)))}})},{}],17:[function(M,L,e){"use strict";function x(e){return(x="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function t(t,e){var r,n=Object.keys(t);return Object.getOwnPropertySymbols&&(r=Object.getOwnPropertySymbols(t),e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)),n}function n(n){for(var e=1;e<arguments.length;e++){var o=null!=arguments[e]?arguments[e]:{};e%2?t(Object(o),!0).forEach(function(e){var t,r;t=n,r=o[e=e],(e=function(e){e=function(e,t){if("object"!=x(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0===r)return("string"===t?String:Number)(e);r=r.call(e,t||"default");if("object"!=x(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}(e,"string");return"symbol"==x(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(o)):t(Object(o)).forEach(function(e){Object.defineProperty(n,e,Object.getOwnPropertyDescriptor(o,e))})}return n}function d(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,a,l,s=[],i=!0,c=!1;try{if(a=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;i=!1}else for(;!(i=(n=a.call(r)).done)&&(s.push(n.value),s.length!==t);i=!0);}catch(e){c=!0,o=e}finally{try{if(!i&&null!=r.return&&(l=r.return(),Object(l)!==l))return}finally{if(c)throw o}}return s}}(e,t)||function(e,t){{var r;if(e)return"string"==typeof e?z(e,t):"Map"===(r="Object"===(r={}.toString.call(e).slice(8,-1))&&e.constructor?e.constructor.name:r)||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?z(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function z(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function T(){T=function(){return l};var i,l={},e=Object.prototype,c=e.hasOwnProperty,u=Object.defineProperty||function(e,t,r){e[t]=r.value},t="function"==typeof Symbol?Symbol:{},n=t.iterator||"@@iterator",r=t.asyncIterator||"@@asyncIterator",o=t.toStringTag||"@@toStringTag";function a(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{a({},"")}catch(i){a=function(e,t,r){return e[t]=r}}function s(e,t,r,n){var o,a,l,s,t=t&&t.prototype instanceof y?t:y,t=Object.create(t.prototype),n=new P(n||[]);return u(t,"_invoke",{value:(o=e,a=r,l=n,s=f,function(e,t){if(s===p)throw Error("Generator is already running");if(s===g){if("throw"===e)throw t;return{value:i,done:!0}}for(l.method=e,l.arg=t;;){var r=l.delegate;if(r){r=function e(t,r){var n=r.method,o=t.iterator[n];if(o===i)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=i,e(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),b;n=d(o,t.iterator,r.arg);if("throw"===n.type)return r.method="throw",r.arg=n.arg,r.delegate=null,b;o=n.arg;return o?o.done?(r[t.resultName]=o.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=i),r.delegate=null,b):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,b)}(r,l);if(r){if(r===b)continue;return r}}if("next"===l.method)l.sent=l._sent=l.arg;else if("throw"===l.method){if(s===f)throw s=g,l.arg;l.dispatchException(l.arg)}else"return"===l.method&&l.abrupt("return",l.arg);s=p;r=d(o,a,l);if("normal"===r.type){if(s=l.done?g:m,r.arg===b)continue;return{value:r.arg,done:l.done}}"throw"===r.type&&(s=g,l.method="throw",l.arg=r.arg)}})}),t}function d(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}l.wrap=s;var f="suspendedStart",m="suspendedYield",p="executing",g="completed",b={};function y(){}function h(){}function w(){}var t={},v=(a(t,n,function(){return this}),Object.getPrototypeOf),v=v&&v(v(R([]))),k=(v&&v!==e&&c.call(v,n)&&(t=v),w.prototype=y.prototype=Object.create(t));function C(e){["next","throw","return"].forEach(function(t){a(e,t,function(e){return this._invoke(t,e)})})}function S(l,s){var t;u(this,"_invoke",{value:function(r,n){function e(){return new s(function(e,t){!function t(e,r,n,o){var a,e=d(l[e],l,r);if("throw"!==e.type)return(r=(a=e.arg).value)&&"object"==x(r)&&c.call(r,"__await")?s.resolve(r.__await).then(function(e){t("next",e,n,o)},function(e){t("throw",e,n,o)}):s.resolve(r).then(function(e){a.value=e,n(a)},function(e){return t("throw",e,n,o)});o(e.arg)}(r,n,e,t)})}return t=t?t.then(e,e):e()}})}function _(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function E(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function P(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(_,this),this.reset(!0)}function R(t){if(t||""===t){var r,e=t[n];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length))return r=-1,(e=function e(){for(;++r<t.length;)if(c.call(t,r))return e.value=t[r],e.done=!1,e;return e.value=i,e.done=!0,e}).next=e}throw new TypeError(x(t)+" is not iterable")}return u(k,"constructor",{value:h.prototype=w,configurable:!0}),u(w,"constructor",{value:h,configurable:!0}),h.displayName=a(w,o,"GeneratorFunction"),l.isGeneratorFunction=function(e){e="function"==typeof e&&e.constructor;return!!e&&(e===h||"GeneratorFunction"===(e.displayName||e.name))},l.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,w):(e.__proto__=w,a(e,o,"GeneratorFunction")),e.prototype=Object.create(k),e},l.awrap=function(e){return{__await:e}},C(S.prototype),a(S.prototype,r,function(){return this}),l.AsyncIterator=S,l.async=function(e,t,r,n,o){void 0===o&&(o=Promise);var a=new S(s(e,t,r,n),o);return l.isGeneratorFunction(t)?a:a.next().then(function(e){return e.done?e.value:a.next()})},C(k),a(k,o,"Generator"),a(k,n,function(){return this}),a(k,"toString",function(){return"[object Generator]"}),l.keys=function(e){var t,r=Object(e),n=[];for(t in r)n.push(t);return n.reverse(),function e(){for(;n.length;){var t=n.pop();if(t in r)return e.value=t,e.done=!1,e}return e.done=!0,e}},l.values=R,P.prototype={constructor:P,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=i,this.done=!1,this.delegate=null,this.method="next",this.arg=i,this.tryEntries.forEach(E),!e)for(var t in this)"t"===t.charAt(0)&&c.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=i)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(r){if(this.done)throw r;var n=this;function e(e,t){return a.type="throw",a.arg=r,n.next=e,t&&(n.method="next",n.arg=i),!!t}for(var t=this.tryEntries.length-1;0<=t;--t){var o=this.tryEntries[t],a=o.completion;if("root"===o.tryLoc)return e("end");if(o.tryLoc<=this.prev){var l=c.call(o,"catchLoc"),s=c.call(o,"finallyLoc");if(l&&s){if(this.prev<o.catchLoc)return e(o.catchLoc,!0);if(this.prev<o.finallyLoc)return e(o.finallyLoc)}else if(l){if(this.prev<o.catchLoc)return e(o.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return e(o.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;0<=r;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&c.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var o=n;break}}var a=(o=o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc?null:o)?o.completion:{};return a.type=e,a.arg=t,o?(this.method="next",this.next=o.finallyLoc,b):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),b},finish:function(e){for(var t=this.tryEntries.length-1;0<=t;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),E(r),b}},catch:function(e){for(var t=this.tryEntries.length-1;0<=t;--t){var r,n,o=this.tryEntries[t];if(o.tryLoc===e)return"throw"===(r=o.completion).type&&(n=r.arg,E(o)),n}throw Error("illegal catch attempt")},delegateYield:function(e,t,r){return this.delegate={iterator:R(e),resultName:t,nextLoc:r},"next"===this.method&&(this.arg=i),b}},l}function U(e,t,r,n,o,a,l){try{var s=e[a](l),i=s.value}catch(e){return r(e)}s.done?t(i):Promise.resolve(i).then(n,o)}Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var l,s,i,D,$,r,o,a,V,c,u,W,f,m,p,H,g,b,q,y,J,h,w,v,k,G,C,S,E,P,R,B,j,I,O,A,N,F;e.default=(l=document,s=window,i=jQuery,e=wp.serverSideRender,$=void 0===e?wp.components.ServerSideRender:e,e=wp.element,r=e.createElement,o=e.Fragment,a=e.createInterpolateElement,V=wp.blocks.registerBlockType,e=wp.blockEditor||wp.editor,c=e.InspectorControls,u=e.PanelColorSettings,W=e.useBlockProps,e=wp.components,f=e.SelectControl,m=e.ToggleControl,p=e.PanelBody,H=e.Placeholder,g=wp.i18n.__,e=wp.element,b=e.useState,q=e.useEffect,e=wpforms_gutenberg_form_selector,y=e.strings,J=e.defaults,h=e.sizes,w=e.urls,v=e.isPro,k=e.isLicenseActive,G=e.isAdmin,C=J,s.WPFormsEducation,S=wpforms_gutenberg_form_selector.forms,E={},P=!0,B=!(R={}),j={},I={clientId:{type:"string",default:""},formId:{type:"string",default:C.formId},displayTitle:{type:"boolean",default:C.displayTitle},displayDesc:{type:"boolean",default:C.displayDesc},preview:{type:"boolean"},theme:{type:"string",default:C.theme},themeName:{type:"string",default:C.themeName},labelSize:{type:"string",default:C.labelSize},labelColor:{type:"string",default:C.labelColor},labelSublabelColor:{type:"string",default:C.labelSublabelColor},labelErrorColor:{type:"string",default:C.labelErrorColor},pageBreakColor:{type:"string",default:C.pageBreakColor},customCss:{type:"string",default:C.customCss},copyPasteJsonValue:{type:"string",default:C.copyPasteJsonValue}},N=A=!(O={}),F={panels:{},init:function(e){j.$window=i(s),F.panels=e.panels,F.education=e.education,F.initDefaults(e),F.registerBlock(e),F.initJConfirm(),i(F.ready)},ready:function(){F.events()},events:function(){j.$window.on("wpformsFormSelectorEdit",_.debounce(F.blockEdit,250)).on("wpformsFormSelectorFormLoaded",F.formLoaded)},initJConfirm:function(){jconfirm.defaults={closeIcon:!1,backgroundDismiss:!1,escapeKey:!0,animationBounce:1,useBootstrap:!1,theme:"modern",boxWidth:"400px",animateFromElement:!1}},getForms:function(){return s=T().mark(function e(){return T().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(B)return e.abrupt("return");e.next=2;break;case 2:return B=!0,e.prev=3,e.next=6,wp.apiFetch({path:wpforms_gutenberg_form_selector.route_namespace+"forms/",method:"GET",cache:"no-cache"});case 6:S=e.sent,e.next=12;break;case 9:e.prev=9,e.t0=e.catch(3),console.error(e.t0);case 12:return e.prev=12,B=!1,e.finish(12);case 15:case"end":return e.stop()}},e,null,[[3,9,12,15]])}),function(){var e=this,l=arguments;return new Promise(function(t,r){var n=s.apply(e,l);function o(e){U(n,t,r,o,a,"next",e)}function a(e){U(n,t,r,o,a,"throw",e)}o(void 0)})}();var s},openBuilderPopup:function(e){i.isEmptyObject(R)&&(r=i("#wpwrap"),t=i('iframe[name="editor-canvas"]'),t=Boolean(t.length)?t.contents().find("#wpforms-gutenberg-popup"):i("#wpforms-gutenberg-popup"),r.after(t),R=r.siblings("#wpforms-gutenberg-popup"));var t=wpforms_gutenberg_form_selector.get_started_url,r=R.find("iframe");F.builderCloseButtonEvent(e),r.attr("src",t),R.fadeIn()},builderCloseButtonEvent:function(o){R.off("wpformsBuilderInPopupClose").on("wpformsBuilderInPopupClose",function(e,t,r,n){"saved"===t&&r&&(t=wp.blocks.createBlock("wpforms/form-selector",{formId:r.toString()}),S=[{ID:r,post_title:n}],wp.data.dispatch("core/block-editor").removeBlock(o),wp.data.dispatch("core/block-editor").insertBlocks(t))})},registerBlock:function(u){V("wpforms/form-selector",{title:y.title,description:y.description,icon:F.getIcon(),keywords:y.form_keywords,category:"widgets",attributes:F.getBlockAttributes(),supports:{customClassName:F.hasForms()},example:{attributes:{preview:!0}},edit:function(e){var t=e.attributes,r=F.getFormOptions(),n=F.getSettingsFieldsHandlers(e),o=d(b(v&&k),1)[0],a=d(b(v),1)[0],l=d(b(u.panels.background._showBackgroundPreview(e)),2),s=l[0],i=l[1],l=d(b(""),2),c=l[0],l=l[1],o={isNotDisabled:o,isProEnabled:a,showBackgroundPreview:s,setShowBackgroundPreview:i,lastBgImage:c,setLastBgImage:l},a=(q(function(){t.formId&&i("none"!==e.attributes.backgroundImage&&e.attributes.backgroundUrl&&"url()"!==e.attributes.backgroundUrl)},[N,e.attributes.backgroundImage,e.attributes.backgroundUrl]),W()),s=(t.clientId&&F.isClientIdAttrUnique(e)||e.setAttributes({clientId:e.clientId}),[F.jsxParts.getMainSettings(t,n,r)]);return F.hasForms()?(c=F.getSizeOptions(),t&&t.formId&&!1===F.isFormAvailable(t.formId)?s.push(F.jsxParts.getBlockPlaceholder(e.attributes,n,r)):t.formId?(F.maybeSubscribeToBlockEvents(e,n,u),s.push(F.jsxParts.getStyleSettings(e,n,c,u,o),F.jsxParts.getBlockFormContent(e)),A||(n.updateCopyPasteContent(),A=!0),j.$window.trigger("wpformsFormSelectorEdit",[e])):t.preview?s.push(F.jsxParts.getBlockPreview()):s.push(F.jsxParts.getBlockPlaceholder(e.attributes,n,r))):s.push(F.jsxParts.getEmptyFormsPreview(e)),React.createElement("div",a,s)},save:function(){return null}})},initDefaults:function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};I=n(n({},I),e.getCommonAttributes()),O=e.setStylesHandlers,["formId","copyPasteJsonValue"].forEach(function(e){return delete C[e]})},hasForms:function(){return 0<S.length},isFormAvailable:function(t){return void 0!==S.find(function(e){return e.ID===Number(t)})},setTriggerServerRender:function(e){P=Boolean(e)},maybeSubscribeToBlockEvents:function(e,t,r){var n=e.clientId;j.$window.off("wpformsFormSelectorDeleteTheme."+n).off("wpformsFormSelectorUpdateTheme."+n).off("wpformsFormSelectorSetTheme."+n),j.$window.on("wpformsFormSelectorDeleteTheme."+n,F.subscriberDeleteTheme(e,r)).on("wpformsFormSelectorUpdateTheme."+n,F.subscriberUpdateTheme(e,r)).on("wpformsFormSelectorSetTheme."+n,F.subscriberSetTheme(e,r))},subscriberDeleteTheme:function(n,o){return function(e,t,r){n.clientId!==r.clientId&&(null==n||null==(r=n.attributes)?void 0:r.theme)===t&&null!=o&&null!=(r=o.panels)&&r.themes&&o.panels.themes.setBlockTheme(n,"default")}},subscriberUpdateTheme:function(o,a){return function(e,t,r,n){o.clientId!==n.clientId&&(null==o||null==(n=o.attributes)?void 0:n.theme)===t&&null!=a&&null!=(n=a.panels)&&n.themes&&a.panels.themes.setBlockTheme(o,t)}},subscriberSetTheme:function(o,a){return function(e,t,r,n){o.clientId!==n.clientId&&null!=a&&null!=(n=a.panels)&&n.themes&&F.onSetTheme(o)}},jsxParts:{getMainSettings:function(e,t,r){return F.hasForms()?React.createElement(c,{key:"wpforms-gutenberg-form-selector-inspector-main-settings"},React.createElement(p,{className:"wpforms-gutenberg-panel wpforms-gutenberg-panel-form-settings",title:y.form_settings},React.createElement(f,{label:y.form_selected,value:e.formId,options:r,onChange:function(e){return t.attrChange("formId",e)}}),e.formId?React.createElement(React.Fragment,null,React.createElement("p",{className:"wpforms-gutenberg-form-selector-actions"},React.createElement("a",{href:w.form_url.replace("{ID}",e.formId),rel:"noreferrer",target:"_blank"},y.form_edit),v&&k&&React.createElement(React.Fragment,null,"  |  ",React.createElement("a",{href:w.entries_url.replace("{ID}",e.formId),rel:"noreferrer",target:"_blank"},y.form_entries))),React.createElement(m,{label:y.show_title,checked:e.displayTitle,onChange:function(e){return t.attrChange("displayTitle",e)}}),React.createElement(m,{label:y.show_description,checked:e.displayDesc,onChange:function(e){return t.attrChange("displayDesc",e)}})):null,React.createElement("p",{className:"wpforms-gutenberg-panel-notice"},React.createElement("strong",null,y.panel_notice_head),y.panel_notice_text,React.createElement("a",{href:y.panel_notice_link,rel:"noreferrer",target:"_blank"},y.panel_notice_link_text)))):F.jsxParts.printEmptyFormsNotice(e.clientId)},printEmptyFormsNotice:function(e){return React.createElement(c,{key:"wpforms-gutenberg-form-selector-inspector-main-settings"},React.createElement(p,{className:"wpforms-gutenberg-panel",title:y.form_settings},React.createElement("p",{className:"wpforms-gutenberg-panel-notice wpforms-warning wpforms-empty-form-notice",style:{display:"block"}},React.createElement("strong",null,g("You haven’t created a form, yet!","wpforms-lite")),g("What are you waiting for?","wpforms-lite")),React.createElement("button",{type:"button",className:"get-started-button components-button is-secondary",onClick:function(){F.openBuilderPopup(e)}},g("Get Started","wpforms-lite"))))},getLabelStyles:function(e,t,r){return React.createElement(p,{className:F.getPanelClass(e),title:y.label_styles},React.createElement(f,{label:y.size,value:e.attributes.labelSize,className:"wpforms-gutenberg-form-selector-fix-bottom-margin",options:r,onChange:function(e){return t.styleAttrChange("labelSize",e)}}),React.createElement("div",{className:"wpforms-gutenberg-form-selector-color-picker"},React.createElement("div",{className:"wpforms-gutenberg-form-selector-control-label"},y.colors),React.createElement(u,{__experimentalIsRenderedInSidebar:!0,enableAlpha:!0,showTitle:!1,className:"wpforms-gutenberg-form-selector-color-panel",colorSettings:[{value:e.attributes.labelColor,onChange:function(e){return t.styleAttrChange("labelColor",e)},label:y.label},{value:e.attributes.labelSublabelColor,onChange:function(e){return t.styleAttrChange("labelSublabelColor",e)},label:y.sublabel_hints.replace("&amp;","&")},{value:e.attributes.labelErrorColor,onChange:function(e){return t.styleAttrChange("labelErrorColor",e)},label:y.error_message}]})))},getPageIndicatorStyles:function(e,t){var r,n=F.hasPageBreak(S,e.attributes.formId),o=F.hasRating(S,e.attributes.formId);return n||o?(r="",n&&o?r="".concat(y.page_break," / ").concat(y.rating):n?r=y.page_break:o&&(r=y.rating),React.createElement(p,{className:F.getPanelClass(e),title:y.other_styles},React.createElement("div",{className:"wpforms-gutenberg-form-selector-color-picker"},React.createElement("div",{className:"wpforms-gutenberg-form-selector-control-label"},y.colors),React.createElement(u,{__experimentalIsRenderedInSidebar:!0,enableAlpha:!0,showTitle:!1,className:"wpforms-gutenberg-form-selector-color-panel",colorSettings:[{value:e.attributes.pageBreakColor,onChange:function(e){return t.styleAttrChange("pageBreakColor",e)},label:r}]})))):null},getStyleSettings:function(e,t,r,n,o){return React.createElement(c,{key:"wpforms-gutenberg-form-selector-style-settings"},n.getThemesPanel(e,F,n.stockPhotos),n.getFieldStyles(e,t,r,F),F.jsxParts.getLabelStyles(e,t,r),n.getButtonStyles(e,t,r,F),n.getContainerStyles(e,t,F,o),n.getBackgroundStyles(e,t,F,n.stockPhotos,o),F.jsxParts.getPageIndicatorStyles(e,t))},getBlockFormContent:function(e){var t,r;return P?React.createElement($,{key:"wpforms-gutenberg-form-selector-server-side-renderer",block:"wpforms/form-selector",attributes:e.attributes}):(t=e.clientId,null!=(r=F.getBlockContainer(e))&&r.innerHTML?(E[t]=E[t]||{},E[t].blockHTML=r.innerHTML,E[t].loadedFormId=e.attributes.formId,React.createElement(o,{key:"wpforms-gutenberg-form-selector-fragment-form-html"},React.createElement("div",{dangerouslySetInnerHTML:{__html:E[t].blockHTML}}))):(P=!0,F.jsxParts.getBlockFormContent(e)))},getBlockPreview:function(){return React.createElement(o,{key:"wpforms-gutenberg-form-selector-fragment-block-preview"},React.createElement("img",{src:wpforms_gutenberg_form_selector.block_preview_url,style:{width:"100%"},alt:""}))},getEmptyFormsPreview:function(e){var t=e.clientId;return React.createElement(o,{key:"wpforms-gutenberg-form-selector-fragment-block-empty"},React.createElement("div",{className:"wpforms-no-form-preview"},React.createElement("img",{src:wpforms_gutenberg_form_selector.block_empty_url,alt:""}),React.createElement("p",null,a(g("You can use <b>WPForms</b> to build contact forms, surveys, payment forms, and more with just a few clicks.","wpforms-lite"),{b:React.createElement("strong",null)})),React.createElement("button",{type:"button",className:"get-started-button components-button is-primary",onClick:function(){F.openBuilderPopup(t)}},g("Get Started","wpforms-lite")),React.createElement("p",{className:"empty-desc"},a(g("Need some help? Check out our <a>comprehensive guide.</a>","wpforms-lite"),{a:React.createElement("a",{href:wpforms_gutenberg_form_selector.wpforms_guide,target:"_blank",rel:"noopener noreferrer"})})),React.createElement("div",{id:"wpforms-gutenberg-popup",className:"wpforms-builder-popup"},React.createElement("iframe",{src:"about:blank",width:"100%",height:"100%",id:"wpforms-builder-iframe",title:"WPForms Builder Popup"}))))},getBlockPlaceholder:function(e,t,r){var n=e.formId&&!F.isFormAvailable(e.formId);return React.createElement(H,{key:"wpforms-gutenberg-form-selector-wrap",className:"wpforms-gutenberg-form-selector-wrap"},React.createElement("img",{src:wpforms_gutenberg_form_selector.logo_url,alt:""}),n&&React.createElement("p",{style:{textAlign:"center",marginTop:"0"}},y.form_not_available_message),React.createElement(f,{key:"wpforms-gutenberg-form-selector-select-control",value:e.formId,options:r,onChange:function(e){return t.attrChange("formId",e)}}))}},hasPageBreak:function(e,t){var e=e.find(function(e){return parseInt(e.ID,10)===parseInt(t,10)});return!!e.post_content&&(e=null==(e=JSON.parse(e.post_content))?void 0:e.fields,Object.values(e).some(function(e){return"pagebreak"===e.type}))},hasRating:function(e,t){var e=e.find(function(e){return parseInt(e.ID,10)===parseInt(t,10)});return!!(e.post_content&&v&&k)&&(e=null==(e=JSON.parse(e.post_content))?void 0:e.fields,Object.values(e).some(function(e){return"rating"===e.type}))},getPanelClass:function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"",e="wpforms-gutenberg-panel wpforms-block-settings-"+e.clientId;return F.isFullStylingEnabled()||(e+=" disabled_panel"),G||"themes"===t||(e+=" wpforms-gutenberg-panel-restricted"),e},getColorPanelClass:function(e){var t="wpforms-gutenberg-form-selector-color-panel";return"none"===e&&(t+=" wpforms-gutenberg-form-selector-border-color-disabled"),t},isFullStylingEnabled:function(){return wpforms_gutenberg_form_selector.is_modern_markup&&wpforms_gutenberg_form_selector.is_full_styling},isLeadFormsEnabled:function(e){return!!e&&i(e.querySelector(".wpforms-container")).hasClass("wpforms-lead-forms-container")},getBlockContainer:function(e){var t,e="#block-".concat(e.clientId," > div"),r=l.querySelector(e);return r=r?r:null==(t=l.querySelector('iframe[name="editor-canvas"]'))?void 0:t.contentWindow.document.querySelector(e)},getFormBlock:function(e){var t=l.querySelector('iframe[name="editor-canvas"]');return(null==t?void 0:t.contentWindow.document.querySelector("#wpforms-".concat(e)))||i("#wpforms-".concat(e))},updatePreviewCSSVarValue:function(e,t,r,n){if(r&&e){var o=e.replace(/[A-Z]/g,function(e){return"-".concat(e.toLowerCase())});if("function"==typeof O[o])O[o](r,t);else switch(o){case"field-size":case"label-size":case"button-size":case"container-shadow-size":for(var a in h[o][t])r.style.setProperty("--wpforms-".concat(o,"-").concat(a),h[o][t][a]);break;case"field-border-style":"none"===t?F.toggleFieldBorderNoneCSSVarValue(r,!0):(F.toggleFieldBorderNoneCSSVarValue(r,!1),r.style.setProperty("--wpforms-".concat(o),t));break;case"button-background-color":F.maybeUpdateAccentColor(n.attributes.buttonBorderColor,t,r),t=F.maybeSetButtonAltBackgroundColor(t,n.attributes.buttonBorderColor,r),F.maybeSetButtonAltTextColor(n.attributes.buttonTextColor,t,n.attributes.buttonBorderColor,r),r.style.setProperty("--wpforms-".concat(o),t);break;case"button-border-color":F.maybeUpdateAccentColor(t,n.attributes.buttonBackgroundColor,r),F.maybeSetButtonAltTextColor(n.attributes.buttonTextColor,n.attributes.buttonBackgroundColor,t,r),r.style.setProperty("--wpforms-".concat(o),t);break;case"button-text-color":F.maybeSetButtonAltTextColor(t,n.attributes.buttonBackgroundColor,n.attributes.buttonBorderColor,r),r.style.setProperty("--wpforms-".concat(o),t);break;default:r.style.setProperty("--wpforms-".concat(o),t),r.style.setProperty("--wpforms-".concat(o,"-spare"),t)}}},toggleFieldBorderNoneCSSVarValue:function(e,t){e=e.querySelector("form");t?(e.style.setProperty("--wpforms-field-border-style","solid"),e.style.setProperty("--wpforms-field-border-size","1px"),e.style.setProperty("--wpforms-field-border-color","transparent")):(e.style.setProperty("--wpforms-field-border-style",null),e.style.setProperty("--wpforms-field-border-size",null),e.style.setProperty("--wpforms-field-border-color",null))},maybeSetButtonAltBackgroundColor:function(e,t,r){return r.querySelector("form").style.setProperty("--wpforms-button-background-color-alt",e),WPFormsUtils.cssColorsUtils.isTransparentColor(e)?WPFormsUtils.cssColorsUtils.isTransparentColor(t)?C.buttonBackgroundColor:t:e},maybeSetButtonAltTextColor:function(e,t,r,n){var o=n.querySelector("form"),a=null;e=e.toLowerCase(),(WPFormsUtils.cssColorsUtils.isTransparentColor(e)||e===t||WPFormsUtils.cssColorsUtils.isTransparentColor(t)&&e===r)&&(a=WPFormsUtils.cssColorsUtils.getContrastColor(t)),n.style.setProperty("--wpforms-button-text-color-alt",e),o.style.setProperty("--wpforms-button-text-color-alt",a)},maybeUpdateAccentColor:function(e,t,r){var n=r.querySelector("form");e=WPFormsUtils.cssColorsUtils.isTransparentColor(e)?C.buttonBackgroundColor:e,WPFormsUtils.cssColorsUtils.isTransparentColor(t)?(n.style.setProperty("--wpforms-button-background-color-alt","rgba( 0, 0, 0, 0 )"),n.style.setProperty("--wpforms-button-background-color",e)):(r.style.setProperty("--wpforms-button-background-color-alt",t),n.style.setProperty("--wpforms-button-background-color-alt",null),n.style.setProperty("--wpforms-button-background-color",null))},getSettingsFieldsHandlers:function(a){return{styleAttrChange:function(e,t){var r=F.getBlockContainer(a),n=r.querySelector("#wpforms-".concat(a.attributes.formId)),o={};e.includes("Color")&&(t=null!=t?t:"rgba( 0, 0, 0, 0 )"),F.updatePreviewCSSVarValue(e,t,n,a),o[e]=t,F.setBlockRuntimeStateVar(a.clientId,"prevAttributesState",a.attributes),a.setAttributes(o),P=!1,this.updateCopyPasteContent(),F.panels.themes.updateCustomThemeAttribute(e,t,a),this.maybeToggleDropdown(a,e),j.$window.trigger("wpformsFormSelectorStyleAttrChange",[r,a,e,t])},maybeToggleDropdown:function(e,t){var r=this,n=e.attributes.formId,e=l.querySelector("#wpforms-form-".concat(n," .choices__list.choices__list--dropdown")),o=l.querySelector("#wpforms-form-".concat(n," .wpforms-field-select-style-classic select"));"fieldMenuColor"===t?(e?(e.classList.add("is-active"),e.parentElement.classList.add("is-open")):this.showClassicMenu(o),clearTimeout(D),D=setTimeout(function(){var e=l.querySelector("#wpforms-form-".concat(n," .choices__list.choices__list--dropdown"));e?(e.classList.remove("is-active"),e.parentElement.classList.remove("is-open")):r.hideClassicMenu(l.querySelector("#wpforms-form-".concat(n," .wpforms-field-select-style-classic select")))},5e3)):e?e.classList.remove("is-active"):this.hideClassicMenu(o)},showClassicMenu:function(e){e&&(e.size=2,e.style.cssText="padding-top: 40px; padding-inline-end: 0; padding-inline-start: 0; position: relative;",e.querySelectorAll("option").forEach(function(e){e.style.cssText="border-left: 1px solid #8c8f94; border-right: 1px solid #8c8f94; padding: 0 10px; z-index: 999999; position: relative;"}),e.querySelector("option:last-child").style.cssText="border-bottom-left-radius: 4px; border-bottom-right-radius: 4px; padding: 0 10px; border-left: 1px solid #8c8f94; border-right: 1px solid #8c8f94; border-bottom: 1px solid #8c8f94; z-index: 999999; position: relative;")},hideClassicMenu:function(e){e&&(e.size=0,e.style.cssText="padding-top: 0; padding-inline-end: 24px; padding-inline-start: 12px; position: relative;",e.querySelectorAll("option").forEach(function(e){e.style.cssText="border: none;"}))},attrChange:function(e,t){var r={};r[e]=t,F.setBlockRuntimeStateVar(a.clientId,"prevAttributesState",a.attributes),a.setAttributes(r),P=!0,this.updateCopyPasteContent()},updateCopyPasteContent:function(){var e,t={},r=wp.data.select("core/block-editor").getBlockAttributes(a.clientId);for(e in C)t[e]=r[e];a.setAttributes({copyPasteJsonValue:JSON.stringify(t)})},pasteSettings:function(e){e=e.trim();var t,r=F.parseValidateJson(e);r?(r.copyPasteJsonValue=e,t=F.panels.themes.maybeCreateCustomThemeFromAttributes(r),F.setBlockRuntimeStateVar(a.clientId,"prevAttributesState",a.attributes),a.setAttributes(r),F.panels.themes.setBlockTheme(a,t),P=!1):(e&&wp.data.dispatch("core/notices").createErrorNotice(y.copy_paste_error,{id:"wpforms-json-parse-error"}),this.updateCopyPasteContent())}}},parseValidateJson:function(e){if("string"!=typeof e)return!1;var t;try{t=JSON.parse(e.trim())}catch(e){t=!1}return t},getIcon:function(){return r("svg",{width:20,height:20,viewBox:"0 0 612 612",className:"dashicon"},r("path",{fill:"currentColor",d:"M544,0H68C30.445,0,0,30.445,0,68v476c0,37.556,30.445,68,68,68h476c37.556,0,68-30.444,68-68V68 C612,30.445,581.556,0,544,0z M464.44,68L387.6,120.02L323.34,68H464.44z M288.66,68l-64.26,52.02L147.56,68H288.66z M544,544H68 V68h22.1l136,92.14l79.9-64.6l79.56,64.6l136-92.14H544V544z M114.24,263.16h95.88v-48.28h-95.88V263.16z M114.24,360.4h95.88 v-48.62h-95.88V360.4z M242.76,360.4h255v-48.62h-255V360.4L242.76,360.4z M242.76,263.16h255v-48.28h-255V263.16L242.76,263.16z M368.22,457.3h129.54V408H368.22V457.3z"}))},getWPFormsBlocks:function(){return wp.data.select("core/block-editor").getBlocks().filter(function(e){return"wpforms/form-selector"===e.name})},isClientIdAttrUnique:function(e){var t,r=F.getWPFormsBlocks();for(t in r)if(r[t].clientId!==e.clientId&&r[t].attributes.clientId===e.attributes.clientId)return!1;return!0},getBlockAttributes:function(){return I},getBlockRuntimeStateVar:function(e,t){return null==(e=E[e])?void 0:e[t]},setBlockRuntimeStateVar:function(e,t,r){return!(!e||!t||(E[e]=E[e]||{},"object"!==x(E[e][t]=r)||Array.isArray(r)||null===r||(E[e][t]=n({},r)),0))},getFormOptions:function(){var e=S.map(function(e){return{value:e.ID,label:e.post_title}});return e.unshift({value:"",label:y.form_select}),e},getSizeOptions:function(){return[{label:y.small,value:"small"},{label:y.medium,value:"medium"},{label:y.large,value:"large"}]},blockEdit:function(e,t){t=F.getBlockContainer(t);null!=t&&t.dataset&&F.initLeadFormSettings(t)},initLeadFormSettings:function(e){var t;F.isFullStylingEnabled()&&null!=e&&null!=(t=e.dataset)&&t.block&&(t=e.dataset.block,t=i(".wpforms-block-settings-".concat(t)),F.isLeadFormsEnabled(e)?(t.addClass("disabled_panel").find(".wpforms-gutenberg-panel-notice.wpforms-lead-form-notice").css("display","block"),t.find(".wpforms-gutenberg-panel-notice.wpforms-use-modern-notice").css("display","none")):(t.removeClass("disabled_panel").removeClass("wpforms-lead-forms-enabled").find(".wpforms-gutenberg-panel-notice.wpforms-lead-form-notice").css("display","none"),t.find(".wpforms-gutenberg-panel-notice.wpforms-use-modern-notice").css("display",null)))},formLoaded:function(e){F.initLeadFormSettings(e.detail.block),F.updateAccentColors(e.detail),F.loadChoicesJS(e.detail),F.initRichTextField(e.detail.formId),F.initRepeaterField(e.detail.formId),i(e.detail.block).off("click").on("click",F.blockClick)},blockClick:function(e){F.initLeadFormSettings(e.currentTarget)},updateAccentColors:function(e){var t;wpforms_gutenberg_form_selector.is_modern_markup&&null!=(t=s.WPForms)&&t.FrontendModern&&null!=e&&e.block&&(t=i(e.block.querySelector("#wpforms-".concat(e.formId))),(e=s.WPForms.FrontendModern).updateGBBlockPageIndicatorColor(t),e.updateGBBlockIconChoicesColor(t),e.updateGBBlockRatingColor(t))},loadChoicesJS:function(e){"function"==typeof s.Choices&&i(e.block.querySelector("#wpforms-".concat(e.formId))).find(".choicesjs-select").each(function(e,t){var r=i(t);if("active"!==r.data("choice")){var n=s.wpforms_choicesjs_config||{},o=r.data("search-enabled"),a=r.closest(".wpforms-field");n.searchEnabled=void 0===o||o,n.callbackOnInit=function(){var e=i(this.passedElement.element),t=i(this.input.element),r=e.data("size-class");r&&i(this.containerOuter.element).addClass(r),e.prop("multiple")&&(t.data("placeholder",t.attr("placeholder")),this.getValue(!0).length)&&t.hide(),this.disable(),a.find(".is-disabled").removeClass("is-disabled")};try{t instanceof parent.HTMLSelectElement||Object.setPrototypeOf(t,parent.HTMLSelectElement.prototype),r.data("choicesjs",new parent.Choices(t,n))}catch(e){}}})},initRichTextField:function(e){e=F.getFormBlock(e);e&&i(e).find(".wp-editor-wrap").removeClass("html-active").addClass("tmce-active")},initRepeaterField:function(e){var t=F.getFormBlock(e);t&&(i(t).find(".wpforms-field-repeater > .wpforms-field-repeater-display-rows .wpforms-field-repeater-display-rows-buttons").each(function(){var e,t=i(this),r=t.siblings(".wpforms-layout-column").find(".wpforms-field").find(".wpforms-field-label");r.length&&(r=r.first(),e=(null==(e=s.getComputedStyle(r.get(0)))?void 0:e.getPropertyValue("--wpforms-field-size-input-spacing"))||0,r=(r.outerHeight()||0)+parseInt(e,10)+10,t.css({top:r}))}),i('.wpforms-form[data-formid="'.concat(e,'"]')).each(function(){var e=i(this).find(".wpforms-field-repeater");e.find(".wpforms-field-repeater-display-rows-buttons").addClass("wpforms-init"),e.find(".wpforms-field-repeater-display-rows:last .wpforms-field-description").addClass("wpforms-init")}))},onSetTheme:function(e){N="url()"!==e.attributes.backgroundImage}})},{}],18:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n,s,i,c,u,d,f,m,o,a;r.default=(n=jQuery,s=(wp.blockEditor||wp.editor).PanelColorSettings,r=wp.components,i=r.SelectControl,c=r.PanelBody,u=r.Flex,d=r.FlexBlock,f=r.__experimentalUnitControl,r=wpforms_gutenberg_form_selector,m=r.strings,o=r.defaults,a={init:function(){n(a.ready)},ready:function(){a.events()},events:function(){},getBlockAttributes:function(){return{containerPadding:{type:"string",default:o.containerPadding},containerBorderStyle:{type:"string",default:o.containerBorderStyle},containerBorderWidth:{type:"string",default:o.containerBorderWidth},containerBorderColor:{type:"string",default:o.containerBorderColor},containerBorderRadius:{type:"string",default:o.containerBorderRadius},containerShadowSize:{type:"string",default:o.containerShadowSize}}},getContainerStyles:function(e,t,r,n){var o=r.getPanelClass(e),a=n.isNotDisabled,l=n.isProEnabled;return a||(o+=" wpforms-gutenberg-panel-disabled"),React.createElement(c,{className:o,title:m.container_styles},React.createElement("div",{className:"wpforms-gutenberg-form-selector-panel-body",onClick:function(e){if(!a){if(e.stopPropagation(),!l)return r.education.showProModal("container",m.container_styles);r.education.showLicenseModal("container",m.container_styles,"container-styles")}},onKeyDown:function(e){if(!a){if(e.stopPropagation(),!l)return r.education.showProModal("container",m.container_styles);r.education.showLicenseModal("container",m.container_styles,"container-styles")}}},React.createElement(u,{gap:4,align:"flex-start",className:"wpforms-gutenberg-form-selector-flex",justify:"space-between"},React.createElement(d,null,React.createElement(f,{label:m.padding,tabIndex:a?0:-1,value:e.attributes.containerPadding,min:0,isUnitSelectTabbable:a,onChange:function(e){return t.styleAttrChange("containerPadding",e)}})),React.createElement(d,null,React.createElement(i,{label:m.border_style,tabIndex:a?0:-1,value:e.attributes.containerBorderStyle,options:[{label:m.none,value:"none"},{label:m.solid,value:"solid"},{label:m.dotted,value:"dotted"},{label:m.dashed,value:"dashed"},{label:m.double,value:"double"}],onChange:function(e){return t.styleAttrChange("containerBorderStyle",e)}}))),React.createElement(u,{gap:4,align:"flex-start",className:"wpforms-gutenberg-form-selector-flex",justify:"space-between"},React.createElement(d,null,React.createElement(f,{label:m.border_width,tabIndex:a?0:-1,value:"none"===e.attributes.containerBorderStyle?"":e.attributes.containerBorderWidth,min:0,disabled:"none"===e.attributes.containerBorderStyle,isUnitSelectTabbable:a,onChange:function(e){return t.styleAttrChange("containerBorderWidth",e)}})),React.createElement(d,null,React.createElement(f,{label:m.border_radius,tabIndex:a?0:-1,value:e.attributes.containerBorderRadius,min:0,isUnitSelectTabbable:a,onChange:function(e){return t.styleAttrChange("containerBorderRadius",e)}}))),React.createElement(u,{gap:4,align:"flex-start",className:"wpforms-gutenberg-form-selector-flex",justify:"space-between"},React.createElement(d,null,React.createElement(i,{label:m.shadow_size,tabIndex:a?0:-1,value:e.attributes.containerShadowSize,options:[{label:m.none,value:"none"},{label:m.small,value:"small"},{label:m.medium,value:"medium"},{label:m.large,value:"large"}],onChange:function(e){return t.styleAttrChange("containerShadowSize",e)}}))),React.createElement(u,{gap:4,align:"flex-start",className:"wpforms-gutenberg-form-selector-flex",justify:"space-between"},React.createElement(d,null,React.createElement("div",{className:"wpforms-gutenberg-form-selector-control-label"},m.colors),React.createElement(s,{__experimentalIsRenderedInSidebar:!0,enableAlpha:!0,showTitle:!1,tabIndex:a?0:-1,className:"none"===e.attributes.containerBorderStyle?"wpforms-gutenberg-form-selector-color-panel wpforms-gutenberg-form-selector-color-panel-disabled":"wpforms-gutenberg-form-selector-color-panel",colorSettings:[{value:e.attributes.containerBorderColor,onChange:function(e){a&&t.styleAttrChange("containerBorderColor",e)},label:m.border_color}]})))))}})},{}],19:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var a;r.default=(a=jQuery,{showProModal:function(e,t){var r="pro",n=wpforms_education.upgrade.pro.message_plural.replace(/%name%/g,t),o={container:"Upgrade to Pro - Container Styles",background:"Upgrade to Pro - Background Styles",themes:"Upgrade to Pro - Themes"};a.alert({backgroundDismiss:!0,title:t+" "+wpforms_education.upgrade.pro.title_plural,icon:"fa fa-lock",content:n,boxWidth:"550px",theme:"modern,wpforms-education",closeIcon:!0,onOpenBefore:function(){this.$btnc.after('<div class="discount-note">'+wpforms_education.upgrade_bonus+"</div>"),this.$btnc.after(wpforms_education.upgrade.pro.doc.replace(/%25name%25/g,"AP - "+t)),this.$body.find(".jconfirm-content").addClass("lite-upgrade")},buttons:{confirm:{text:wpforms_education.upgrade.pro.button,btnClass:"btn-confirm",keys:["enter"],action:function(){window.open(WPFormsEducation.core.getUpgradeURL(o[e],r),"_blank"),WPFormsEducation.core.upgradeModalThankYou(r)}}}})},showLicenseModal:function(e,t,r){WPFormsEducation.proCore.licenseModal(e,t,r)}})},{}],20:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var o,a,l,s,i,c,u,n;r.default=(o=(wp.blockEditor||wp.editor).PanelColorSettings,r=wp.components,a=r.SelectControl,l=r.PanelBody,s=r.Flex,i=r.FlexBlock,c=r.__experimentalUnitControl,r=wpforms_gutenberg_form_selector,u=r.strings,n=r.defaults,{getBlockAttributes:function(){return{fieldSize:{type:"string",default:n.fieldSize},fieldBorderStyle:{type:"string",default:n.fieldBorderStyle},fieldBorderSize:{type:"string",default:n.fieldBorderSize},fieldBorderRadius:{type:"string",default:n.fieldBorderRadius},fieldBackgroundColor:{type:"string",default:n.fieldBackgroundColor},fieldBorderColor:{type:"string",default:n.fieldBorderColor},fieldTextColor:{type:"string",default:n.fieldTextColor},fieldMenuColor:{type:"string",default:n.fieldMenuColor}}},getFieldStyles:function(e,t,r,n){return React.createElement(l,{className:n.getPanelClass(e),title:u.field_styles},React.createElement(s,{gap:4,align:"flex-start",className:"wpforms-gutenberg-form-selector-flex",justify:"space-between"},React.createElement(i,null,React.createElement(a,{label:u.size,value:e.attributes.fieldSize,options:r,onChange:function(e){return t.styleAttrChange("fieldSize",e)}})),React.createElement(i,null,React.createElement(a,{label:u.border,value:e.attributes.fieldBorderStyle,options:[{label:u.none,value:"none"},{label:u.solid,value:"solid"},{label:u.dashed,value:"dashed"},{label:u.dotted,value:"dotted"}],onChange:function(e){return t.styleAttrChange("fieldBorderStyle",e)}}))),React.createElement(s,{gap:4,align:"flex-start",className:"wpforms-gutenberg-form-selector-flex",justify:"space-between"},React.createElement(i,null,React.createElement(c,{label:u.border_size,value:"none"===e.attributes.fieldBorderStyle?"":e.attributes.fieldBorderSize,min:0,disabled:"none"===e.attributes.fieldBorderStyle,onChange:function(e){return t.styleAttrChange("fieldBorderSize",e)},isUnitSelectTabbable:!0})),React.createElement(i,null,React.createElement(c,{label:u.border_radius,value:e.attributes.fieldBorderRadius,min:0,isUnitSelectTabbable:!0,onChange:function(e){return t.styleAttrChange("fieldBorderRadius",e)}}))),React.createElement("div",{className:"wpforms-gutenberg-form-selector-color-picker"},React.createElement("div",{className:"wpforms-gutenberg-form-selector-control-label"},u.colors),React.createElement(o,{__experimentalIsRenderedInSidebar:!0,enableAlpha:!0,showTitle:!1,className:n.getColorPanelClass(e.attributes.fieldBorderStyle),colorSettings:[{value:e.attributes.fieldBackgroundColor,onChange:function(e){return t.styleAttrChange("fieldBackgroundColor",e)},label:u.background},{value:e.attributes.fieldBorderColor,onChange:function(e){return t.styleAttrChange("fieldBorderColor",e)},label:u.border},{value:e.attributes.fieldTextColor,onChange:function(e){return t.styleAttrChange("fieldTextColor",e)},label:u.text},{value:e.attributes.fieldMenuColor,onChange:function(e){return t.styleAttrChange("fieldMenuColor",e)},label:u.menu}]})))}})},{}],21:[function(e,t,r){"use strict";function n(e){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function o(t,e){var r,n=Object.keys(t);return Object.getOwnPropertySymbols&&(r=Object.getOwnPropertySymbols(t),e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)),n}function u(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?o(Object(r),!0).forEach(function(e){a(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function a(e,t,r){return(t=function(e){e=function(e,t){if("object"!=n(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0===r)return("string"===t?String:Number)(e);r=r.call(e,t||"default");if("object"!=n(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}(e,"string");return"symbol"==n(e)?e:e+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var l,s,i,c,d,f,m,p,g,b,y,h,w,v,k,C,S,E,P;r.default=(document,l=window,s=jQuery,r=wp.components,i=r.PanelBody,c=r.ColorIndicator,d=r.TextControl,f=r.Button,r=wp.components,m=r.__experimentalRadio,p=r.__experimentalRadioGroup,r=wpforms_gutenberg_form_selector,g=r.isAdmin,b=r.isPro,y=r.isLicenseActive,h=r.strings,w=r.route_namespace,k={},C={wpforms:v=null,custom:null},S=null,E={},(P={init:function(){E.$window=s(l),P.fetchThemesData(),s(P.ready)},ready:function(){P.events()},events:function(){wp.data.subscribe(function(){var e,t,r,n,o;g&&(t=null==(t=wp.data.select("core/editor"))?void 0:t.isSavingPost(),r=null==(r=wp.data.select("core/editor"))?void 0:r.isAutosavingPost(),n=null==(n=wp.data.select("core/edit-widgets"))?void 0:n.isSavingWidgetAreas(),o=(null==(o=null==(o=wp.data.select("core/editor"))?void 0:o.getCurrentPost())||null==(e=o.type)?void 0:e.includes("wp_template"))||(null==o||null==(e=o.type)?void 0:e.includes("wp_block")),t||n||o)&&!r&&(o?_.debounce(P.saveCustomThemes,500)():P.saveCustomThemes())})},getAllThemes:function(){return u(u({},C.custom||{}),C.wpforms||{})},getTheme:function(e){return P.getAllThemes()[e]||null},getEnabledThemes:function(){if(!S){var n=P.getAllThemes();if(b&&y)return n;S=Object.keys(n).reduce(function(e,t){var r;return null!=(r=n[t].settings)&&r.fieldSize&&!n[t].disabled&&(e[t]=n[t]),e},{})}return S},updateEnabledThemes:function(e,t){S=S&&u(u({},S),{},a({},e,t))},isDisabledTheme:function(e){var t;return!(null!=(t=P.getEnabledThemes())&&t[e])},isWPFormsTheme:function(e){return Boolean(null==(e=C.wpforms[e])?void 0:e.settings)},fetchThemesData:function(){if(!k.isFetchingThemes&&!C.wpforms){k.isFetchingThemes=!0;try{wp.apiFetch({path:w+"themes/",method:"GET",cache:"no-cache"}).then(function(e){C.wpforms=e.wpforms||{},C.custom=e.custom||{}}).catch(function(e){console.error(null==e?void 0:e.message)}).finally(function(){k.isFetchingThemes=!1})}catch(e){console.error(e)}}},saveCustomThemes:function(){if(!k.isSavingThemes&&C.custom){k.isSavingThemes=!0;try{wp.apiFetch({path:w+"themes/custom/",method:"POST",data:{customThemes:C.custom}}).then(function(e){null!=e&&e.result||console.log(null==e?void 0:e.error)}).catch(function(e){console.error(null==e?void 0:e.message)}).finally(function(){k.isSavingThemes=!1})}catch(e){console.error(e)}}},getCurrentStyleAttributes:function(e){var t,r,n=Object.keys(null==(t=C.wpforms.default)?void 0:t.settings),o={};for(r in n){var a=n[r];o[a]=null!=(a=e.attributes[a])?a:""}return o},maybeCreateCustomTheme:function(e){var t,r=P.getCurrentStyleAttributes(e),n=!!C.wpforms[e.attributes.theme],o=!!C.custom[e.attributes.theme],a=!1;return!(n&&JSON.stringify(null==(t=C.wpforms[e.attributes.theme])?void 0:t.settings)===JSON.stringify(r)||(t=v.getBlockRuntimeStateVar(e.clientId,"prevAttributesState"),"default"!==e.attributes.theme||""!==e.attributes.themeName||t||(a=!0),!n&&o&&!a||P.createCustomTheme(e,r,a),0))},createCustomTheme:function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:null,r=2<arguments.length&&void 0!==arguments[2]&&arguments[2],n=0,o=e.attributes.theme,a=(P.getTheme(e.attributes.theme)||C.wpforms.default).name;for(C.custom=C.custom||{},r&&(o="custom",a=h.theme_custom);o=o+"-copy-"+ ++n,C.custom[o]&&n<1e4;);return a+=" ("+(n<2?h.theme_copy:h.theme_copy+" "+n)+")",a=r&&n<2?h.theme_custom:a,C.custom[o]={name:a,settings:t||P.getCurrentStyleAttributes(e)},P.updateEnabledThemes(o,C.custom[o]),e.setAttributes({theme:o,themeName:a}),!0},maybeCreateCustomThemeFromAttributes:function(e){var t,r=e.theme,n=P.getTheme(e.theme),o=Object.keys(e),a=Boolean(null==n?void 0:n.settings);if(a)for(var l in o){l=o[l];if(!n.settings[l]||n.settings[l]!==e[l]){a=!1;break}}if(!a){var s,i=Object.keys(C.wpforms.default.settings),c={};for(s in i){var u=i[s];c[u]=null!=(u=e[u])?u:""}C.custom[r]={name:null!=(t=e.themeName)?t:h.theme_custom,settings:c},P.updateEnabledThemes(r,C.custom[r])}return r},updateCustomThemeAttribute:function(e,t,r){var n=r.attributes.theme;C.wpforms[n]||"themeName"!==e&&!C.wpforms.default.settings[e]||C.custom[n]&&("themeName"===e?C.custom[n].name=t:(C.custom[n].settings=C.custom[n].settings||C.wpforms.default.settings,C.custom[n].settings[e]=t),E.$window.trigger("wpformsFormSelectorUpdateTheme",[n,C.custom[n],r]))},getThemesPanel:function(e,t,r){var n,o,a,l,s;return v=t,k.stockPhotos=r,C.wpforms?(n=P.getEventHandlers(e),r=g&&t.isFullStylingEnabled()&&P.maybeCreateCustomTheme(e),o=t.isFullStylingEnabled()?e.attributes.theme:"classic",l="block"==(a=(t=t.isLeadFormsEnabled(t.getBlockContainer(e)))?"block":"none")?{display:"none"}:{},s=v.getPanelClass(e,"themes"),s=(s+=t?" wpforms-lead-forms-enabled":"")+(P.isMac()?" wpforms-is-mac":""),React.createElement(i,{className:s,title:h.themes},React.createElement("p",{className:"wpforms-gutenberg-panel-notice wpforms-warning wpforms-use-modern-notice",style:l},React.createElement("strong",null,h.use_modern_notice_head),h.use_modern_notice_text," ",React.createElement("a",{href:h.use_modern_notice_link,rel:"noreferrer",target:"_blank"},h.learn_more)),React.createElement("p",{className:"wpforms-gutenberg-panel-notice wpforms-warning wpforms-lead-form-notice",style:{display:a}},React.createElement("strong",null,h.lead_forms_panel_notice_head),h.lead_forms_panel_notice_text),React.createElement(p,{className:"wpforms-gutenberg-form-selector-themes-radio-group",label:h.themes,checked:o,defaultChecked:e.attributes.theme,onChange:function(e){return n.selectTheme(e)}},P.getThemesItemsJSX(e)),r&&React.createElement(React.Fragment,null,React.createElement(d,{className:"wpforms-gutenberg-form-selector-themes-theme-name",label:h.theme_name,value:e.attributes.themeName,onChange:function(e){return n.changeThemeName(e)}}),React.createElement(f,{isSecondary:!0,className:"wpforms-gutenberg-form-selector-themes-delete",onClick:n.deleteTheme,buttonSettings:""},h.theme_delete)))):(P.fetchThemesData(),React.createElement(React.Fragment,null))},getThemesItemsJSX:function(e){var t=P.getAllThemes();if(!t)return[];var r,n,o,a=[],l=Object.keys(t);for(o in P.isWPFormsTheme(e.attributes.theme)||(n=e.attributes.theme,a.push(P.getThemesItemJSX(e.attributes.theme,P.getTheme(e.attributes.theme)))),l){var s=l[o];n&&n===s||((r=u(u({},t.default),t[s]||{})).settings=u(u({},t.default.settings),r.settings||{}),a.push(P.getThemesItemJSX(s,r)))}return a},getThemesItemJSX:function(e,t){var r,n;return t?(r=0<(null==(r=t.name)?void 0:r.length)?t.name:h.theme_noname,n="wpforms-gutenberg-form-selector-themes-radio",n+=P.isDisabledTheme(e)?" wpforms-gutenberg-form-selector-themes-radio-disabled":" wpforms-gutenberg-form-selector-themes-radio-enabled",React.createElement(m,{value:e,title:r},React.createElement("div",{className:n},React.createElement("div",{className:"wpforms-gutenberg-form-selector-themes-radio-title"},r)),React.createElement("div",{className:"wpforms-gutenberg-form-selector-themes-indicators"},React.createElement(c,{colorValue:t.settings.buttonBackgroundColor,title:h.button_background,"data-index":"0"}),React.createElement(c,{colorValue:t.settings.buttonTextColor,title:h.button_text,"data-index":"1"}),React.createElement(c,{colorValue:t.settings.labelColor,title:h.field_label,"data-index":"2"}),React.createElement(c,{colorValue:t.settings.labelSublabelColor,title:h.field_sublabel,"data-index":"3"}),React.createElement(c,{colorValue:t.settings.fieldBorderColor,title:h.field_border,"data-index":"4"})))):null},setBlockTheme:function(e,t){if(P.maybeDisplayUpgradeModal(t))return!1;var r=P.getTheme(t);if(null==r||!r.settings)return!1;var n,o=Object.keys(r.settings),a=v.getBlockContainer(e),l=a.querySelector("#wpforms-".concat(e.attributes.formId)),s=u(u({},e),{},{attributes:u(u({},e.attributes),r.settings)});for(n in o){var i=o[n];r.settings[i]="0"===r.settings[i]?"0px":r.settings[i],v.updatePreviewCSSVarValue(i,r.settings[i],l,s)}var c=u({theme:t,themeName:r.name},r.settings);return e.setAttributes&&e.setAttributes(c),E.$window.trigger("wpformsFormSelectorSetTheme",[a,t,e]),!0},maybeDisplayUpgradeModal:function(e){return!(!P.isDisabledTheme(e)||(b?y||(v.education.showLicenseModal("themes",h.themes,"select-theme"),0):(v.education.showProModal("themes",h.themes),0)))},getEventHandlers:function(r){var n=v.getSettingsFieldsHandlers(r),t={selectTheme:function(e){var t;P.setBlockTheme(r,e)&&(null!=k&&null!=(t=k.stockPhotos)&&t.onSelectTheme(e,r,P,n),t=v.getBlockContainer(r),v.setTriggerServerRender(!1),n.updateCopyPasteContent(),E.$window.trigger("wpformsFormSelectorSelectTheme",[t,r,e]))},changeThemeName:function(e){v.setTriggerServerRender(!1),r.setAttributes({themeName:e}),P.updateCustomThemeAttribute("themeName",e,r)},deleteTheme:function(){var e=r.attributes.theme;delete C.custom[e],P.deleteThemeModal(r,e,t)}};return t},deleteThemeModal:function(e,t,r){var n=h.theme_delete_confirm.replace("%1$s","<b>".concat(e.attributes.themeName,"</b>")),n='<p class="wpforms-theme-delete-text">'.concat(n," ").concat(h.theme_delete_cant_undone,"</p>");s.confirm({title:h.theme_delete_title,content:n,icon:"wpforms-exclamation-circle",type:"red",buttons:{confirm:{text:h.theme_delete_yes,btnClass:"btn-confirm",keys:["enter"],action:function(){r.selectTheme("default"),E.$window.trigger("wpformsFormSelectorDeleteTheme",[t,e])}},cancel:{text:h.cancel,keys:["esc"]}}})},isMac:function(){return navigator.userAgent.includes("Macintosh")}}).init(),P)},{}]},{},[12]);