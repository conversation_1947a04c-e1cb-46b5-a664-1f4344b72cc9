@media screen and (max-width: 1023px) {
  #wpforms-notice-bar {
    display: none !important;
  }
}

#wpforms-notice-bar {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #dcdcde;
  border-top: 3px solid #e27730;
  color: #646970;
  text-align: center;
  position: relative;
  padding: 7px;
  margin-bottom: -4px;
  opacity: 1;
  transition: all .3s ease-in-out;
  max-height: 100px;
  overflow: hidden;
  z-index: 999;
}

#wpforms-notice-bar.out {
  opacity: .5;
  max-height: 0;
}

#wpforms-notice-bar a {
  color: #e27730;
}

#wpforms-notice-bar a:hover {
  color: #cd6622;
}

#wpforms-notice-bar .wpforms-dismiss-button {
  position: absolute;
  top: 0;
  right: 0;
  border: none;
  padding: 5px;
  margin-top: 1px;
  background: 0 0;
  color: #72777c;
  cursor: pointer;
  margin-right: 10px;
}

#wpforms-notice-bar .wpforms-dismiss-button:before {
  background: 0 0;
  color: #72777c;
  content: "\f335";
  display: block;
  font: normal 20px/20px dashicons;
  speak: none;
  height: 20px;
  text-align: center;
  width: 20px;
  -webkit-font-smoothing: antialiased;
}

#wpforms-notice-bar .wpforms-dismiss-button:hover:before {
  color: #3c434a;
}

#screen-meta-links .screen-meta-toggle {
  position: absolute;
  right: 20px;
  top: auto;
}

body.wpforms-setting-lite-connect-modal {
  overflow: hidden;
  height: 100vh;
}

body.wpforms-setting-lite-connect-modal .jconfirm .jconfirm-bg {
  animation: 0.3s ease-out 0s 1 normal wpforms-jconfirm-bg-fade-in;
}

body.wpforms-setting-lite-connect-modal .jconfirm .jconfirm-cell {
  overflow-y: auto;
  max-height: 100vh;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
}

body.wpforms-setting-lite-connect-modal .jconfirm .jconfirm-holder {
  animation: 0.3s ease-out 0s 1 normal wpforms-jconfirm-scale;
}

body.wpforms-setting-lite-connect-modal .jconfirm .jconfirm-box-container .jconfirm-box {
  width: 100%;
  border-top: none;
  border-radius: 6px;
  box-shadow: 0 5px 60px rgba(0, 0, 0, 0.25);
  padding-top: 30px;
}

body.wpforms-setting-lite-connect-modal .jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-content-pane {
  margin-bottom: 20px;
  height: auto !important;
  min-height: fit-content;
  max-height: fit-content !important;
}

body.wpforms-setting-lite-connect-modal .jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-content {
  margin-bottom: 0;
}

body.wpforms-setting-lite-connect-modal .jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-content .wpforms-settings-lite-connect-modal-content {
  font-style: normal;
  font-weight: normal;
  color: #777777;
}

body.wpforms-setting-lite-connect-modal .jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-content .wpforms-settings-lite-connect-modal-content img.wpforms-mascot {
  height: 50px;
}

body.wpforms-setting-lite-connect-modal .jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-content .wpforms-settings-lite-connect-modal-content h2,
body.wpforms-setting-lite-connect-modal .jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-content .wpforms-settings-lite-connect-modal-content h4 {
  font-weight: 500;
  font-size: 24px;
  line-height: 22px;
  color: #444444;
}

body.wpforms-setting-lite-connect-modal .jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-content .wpforms-settings-lite-connect-modal-content h2 {
  margin: 10px 0;
}

body.wpforms-setting-lite-connect-modal .jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-content .wpforms-settings-lite-connect-modal-content h4 {
  line-height: 24px;
  font-size: 16px;
}

body.wpforms-setting-lite-connect-modal .jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-content .wpforms-settings-lite-connect-modal-content p {
  font-size: 15px;
  line-height: 20px;
}

body.wpforms-setting-lite-connect-modal .jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-content .wpforms-settings-lite-connect-modal-content .wpforms-features {
  margin: 25px 0 15px 0;
  padding: 10px 28px 10px 30px;
  background: #f8f8f8;
  border-radius: 4px;
  text-align: left;
}

body.wpforms-setting-lite-connect-modal .jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-content .wpforms-settings-lite-connect-modal-content .wpforms-features section {
  display: flex;
  justify-content: space-between;
  padding: 15px 0;
  border-bottom: 1px solid #e4e4e4;
  gap: 15px;
}

body.wpforms-setting-lite-connect-modal .jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-content .wpforms-settings-lite-connect-modal-content .wpforms-features section:last-child {
  border-bottom: none;
}

body.wpforms-setting-lite-connect-modal .jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-content .wpforms-settings-lite-connect-modal-content .wpforms-features section img {
  max-height: 24px;
  max-width: 24px;
}

body.wpforms-setting-lite-connect-modal .jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-content .wpforms-settings-lite-connect-modal-content .wpforms-features section:nth-child(2) img {
  margin-left: -4px;
  max-width: 30px;
  width: 30px;
}

body.wpforms-setting-lite-connect-modal .jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-content .wpforms-settings-lite-connect-modal-content .wpforms-features section:nth-child(3) img {
  margin-left: -1px;
}

body.wpforms-setting-lite-connect-modal .jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-content .wpforms-settings-lite-connect-modal-content .wpforms-features section aside h4 {
  margin: 0;
}

body.wpforms-setting-lite-connect-modal .jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-content .wpforms-settings-lite-connect-modal-content .wpforms-features section aside p {
  font-size: 14px;
  line-height: 20px;
}

body.wpforms-setting-lite-connect-modal .jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-content .wpforms-settings-lite-connect-modal-content footer {
  font-size: 13px;
  line-height: 20px;
  max-width: 370px;
  margin: 0 auto;
}

body.wpforms-setting-lite-connect-modal .jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-content .wpforms-settings-lite-connect-modal-content footer a {
  color: inherit;
}

body.wpforms-setting-lite-connect-modal .jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-buttons {
  margin-top: -10px;
  padding-bottom: 0;
}

body.wpforms-setting-lite-connect-modal .jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-buttons button {
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 42px;
  border-radius: 4px;
  padding: 0 17px;
  color: #777777;
  border-color: #f8f8f8;
  background: #f8f8f8;
  text-transform: capitalize;
  min-width: 83px;
  margin: 10px;
}

body.wpforms-setting-lite-connect-modal .jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-buttons button:hover {
  background: #eeeeee;
  border-color: #eeeeee;
}

body.wpforms-setting-lite-connect-modal .jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-buttons button.btn-default {
  margin-left: 20px;
  font-weight: 400;
}

body.wpforms-setting-lite-connect-modal .jconfirm .jconfirm-box-container .jconfirm-box.jconfirm-type-orange .jconfirm-buttons button.btn-confirm {
  color: #ffffff;
  background: #e27730;
  border-color: #e27730;
}

body.wpforms-setting-lite-connect-modal .jconfirm .jconfirm-box-container .jconfirm-box.jconfirm-type-orange .jconfirm-buttons button.btn-confirm:hover {
  background: #cd6622;
  border-color: #cd6622;
}

body.wpforms-setting-lite-connect-modal .jconfirm.jconfirm-ai-modal .jconfirm-box-container .jconfirm-box {
  padding: 30px;
}

body.wpforms-setting-lite-connect-modal .jconfirm.jconfirm-ai-modal .jconfirm-box-container .jconfirm-box .jconfirm-content-pane {
  margin-bottom: 25px;
}

body.wpforms-setting-lite-connect-modal .jconfirm.jconfirm-ai-modal .jconfirm-box-container .jconfirm-box .jconfirm-content .wpforms-settings-lite-connect-modal-content .wpforms-features {
  padding: 25px 30px;
  margin-bottom: 25px;
}

body.wpforms-setting-lite-connect-modal .jconfirm.jconfirm-ai-modal .jconfirm-box-container .jconfirm-box .jconfirm-content .wpforms-settings-lite-connect-modal-content .wpforms-features section {
  justify-content: revert;
}

body.wpforms-setting-lite-connect-modal .jconfirm.jconfirm-ai-modal .jconfirm-box-container .jconfirm-box .jconfirm-content .wpforms-settings-lite-connect-modal-content .wpforms-features section:first-child {
  padding-top: 0;
}

body.wpforms-setting-lite-connect-modal .jconfirm.jconfirm-ai-modal .jconfirm-box-container .jconfirm-box .jconfirm-content .wpforms-settings-lite-connect-modal-content .wpforms-features section:last-child {
  padding-bottom: 0;
}

body.wpforms-setting-lite-connect-modal .jconfirm.jconfirm-ai-modal .jconfirm-box-container .jconfirm-box .jconfirm-content .wpforms-settings-lite-connect-modal-content .wpforms-features section img {
  width: 100%;
}

body.wpforms-setting-lite-connect-modal .jconfirm.jconfirm-ai-modal .jconfirm-box-container .jconfirm-box .jconfirm-content .wpforms-settings-lite-connect-modal-content h2 {
  margin: 15px 0 10px 0;
}

body.wpforms-setting-lite-connect-modal .jconfirm.jconfirm-ai-modal .jconfirm-box-container .jconfirm-buttons {
  display: flex;
  gap: 20px;
  margin-top: 0;
}

body.wpforms-setting-lite-connect-modal .jconfirm.jconfirm-ai-modal .jconfirm-box-container .jconfirm-buttons button {
  margin: 0;
}

body.wpforms-setting-lite-connect-modal .jconfirm.jconfirm-ai-modal .jconfirm-box-container .jconfirm-buttons button.btn-default {
  margin-left: 0;
}

@keyframes wpforms-jconfirm-scale {
  0% {
    transform: scale(0.5);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes wpforms-jconfirm-bg-fade-in {
  0% {
    opacity: 0;
  }
  100% {
    opacity: .6;
  }
}

.wpforms-education-lite-connect-wrapper .wpforms-education-lite-connect-setting,
.wpforms-education-lite-connect-wrapper .wpforms-education-lite-connect-enabled-info {
  transition: opacity 0.25s ease-out;
}

.wpforms-education-lite-connect-wrapper .wpforms-education-lite-connect-setting.wpforms-hidden,
.wpforms-education-lite-connect-wrapper .wpforms-education-lite-connect-enabled-info.wpforms-hidden {
  display: none;
}

@media screen and (max-width: 782px) {
  .wpforms-toggle-control.wpforms-setting-lite-connect-auto-save-toggle .wpforms-toggle-control-label,
  .wpforms-toggle-control.wpforms-setting-lite-connect-auto-save-toggle .wpforms-toggle-control-icon {
    pointer-events: none;
  }
}

.wpforms-toggle-control.wpforms-setting-lite-connect-auto-save-toggle .wpforms-toggle-control-label {
  font-size: 14px;
  font-weight: 400;
  padding-right: 0;
}

.wpforms-admin-settings-form .wpforms-toggle-control.wpforms-setting-lite-connect-auto-save-toggle .wpforms-toggle-control-icon {
  background-color: #d63638;
}

.wpforms-admin-settings-form .wpforms-toggle-control.wpforms-setting-lite-connect-auto-save-toggle input[type=checkbox]:focus + label.wpforms-toggle-control-icon {
  background-color: #d63638;
  box-shadow: 0 0 0 1px #ffffff, 0 0 0 2px #d63638;
}

.wpforms-admin-settings-form .wpforms-toggle-control.wpforms-setting-lite-connect-auto-save-toggle input[type=checkbox]:hover + label.wpforms-toggle-control-icon {
  background-color: #b32d2e;
}

.wpforms-admin-settings-form .wpforms-toggle-control.wpforms-setting-lite-connect-auto-save-toggle input[type=checkbox]:checked:focus + label.wpforms-toggle-control-icon {
  background-color: #2271b1;
  box-shadow: 0 0 0 1px #ffffff, 0 0 0 2px #2271b1;
}

.wpforms-admin-settings-form .wpforms-toggle-control.wpforms-setting-lite-connect-auto-save-toggle input[type=checkbox]:checked:hover + label.wpforms-toggle-control-icon {
  background-color: #215d8f;
}

#wpforms-dash-widget-lite-connect-block {
  margin: 0;
  padding: 10px;
  background: #fafafa;
  border-bottom: 1px solid #f0f0f1;
}

#wpforms-dash-widget-lite-connect-block .wpforms-toggle-control-status {
  color: #444444;
}

#wpforms-dash-widget-lite-connect-block .wpforms-education-lite-connect-enabled-info {
  display: flex;
  justify-content: space-between;
  text-align: left;
  font-size: 13px;
  line-height: 14px;
  color: #a7aaad;
}

#wpforms-dash-widget-lite-connect-block .wpforms-education-lite-connect-enabled-info img {
  margin-right: 5px;
  width: 14px;
  height: 14px;
}

#wpforms-dash-widget-lite-connect-block .wpforms-education-lite-connect-enabled-info span {
  vertical-align: top;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-right: 5px;
}

#wpforms-dash-widget-lite-connect-block .wpforms-education-lite-connect-enabled-info a {
  margin-left: auto;
  white-space: nowrap;
}

#wpforms-builder-lite-connect-top-bar {
  background: #f8f8f8;
  height: 44px;
  padding: 0;
  position: fixed;
  top: calc(var(--wpforms-admin-bar-height) - 45px);
  width: 100%;
  z-index: 55;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: top 0.25s ease-out;
}

#wpforms-builder-lite-connect-top-bar .wpforms-toggle-control {
  white-space: nowrap;
  margin-inline-end: 10px;
  line-height: 17px;
  height: 17px;
}

#wpforms-builder-lite-connect-top-bar .wpforms-toggle-control input:not(:checked) + .wpforms-toggle-control-icon {
  background-color: #d63638;
}

#wpforms-builder-lite-connect-top-bar .wpforms-toggle-control:hover input:not(:checked) + .wpforms-toggle-control-icon {
  background-color: #b32d2e;
}

#wpforms-builder-lite-connect-top-bar .wpforms-toggle-control-label {
  font-weight: 500;
  font-size: 14px;
  line-height: 17px;
  color: #444444;
  vertical-align: 3px;
}

#wpforms-builder-lite-connect-top-bar p {
  font-weight: normal;
  font-size: 14px;
  line-height: 17px;
  color: #777777;
  margin: 0;
  padding-inline-start: 10px;
  border-inline-start: 1px solid #bbbbbb;
}

#wpforms-builder-lite-connect-top-bar .wpforms-education-lite-connect-enabled-info {
  font-weight: 500;
  margin: 0 10px 0 0;
}

#wpforms-builder-lite-connect-top-bar .wpforms-education-lite-connect-enabled-info img {
  margin: 0 6px 0 0;
  vertical-align: bottom;
}

#wpforms-builder-lite-connect-top-bar .wpforms-hidden-element {
  margin-right: auto;
  visibility: hidden;
  width: 0;
}

#wpforms-builder-lite-connect-top-bar .wpforms-dismiss-button {
  margin-inline-end: 22px;
  margin-inline-start: auto;
  color: #a6a6a6;
}

#wpforms-builder-lite-connect-top-bar .wpforms-dismiss-button:hover {
  color: #777777;
}

#wpforms-challenge-popup-lite-connect {
  font-style: normal;
  font-weight: normal;
  font-size: 14px;
  line-height: 17px;
  color: #444444;
}

#wpforms-challenge-popup-lite-connect h3 img {
  width: 24px;
  height: 24px;
}

#wpforms-challenge-popup-lite-connect hr {
  margin: 20px 0;
}

#wpforms-challenge-popup-lite-connect .wpforms-toggle-control .wpforms-toggle-control-label {
  font-weight: normal;
  font-size: 14px;
  line-height: 17px;
  color: #444444;
}

#wpforms-challenge-popup-lite-connect .wpforms-education-lite-connect-enabled-info img {
  margin: 0 6px 0 0;
  width: 17px;
  height: 17px;
  vertical-align: bottom;
}

.wpforms-admin-settings-access {
  font-size: 14px;
  color: #444444;
}

.wpforms-admin-settings-access .wpforms-setting-row {
  padding: 30px 0;
}

.wpforms-admin-settings-access .wpforms-setting-field {
  margin: 0;
  max-width: none;
}

.wpforms-admin-settings-access .heading h4 {
  font-weight: 600;
  margin-bottom: 10px;
  color: #1d2327;
  display: flex;
  align-items: center;
  gap: 10px;
}

.wpforms-admin-settings-access .heading p {
  margin: 0;
  line-height: 20px;
  letter-spacing: 0;
  color: #2c3338;
}

.wpforms-admin-settings-access .screenshots {
  padding-bottom: 55px;
}

.wpforms-admin-settings-access .screenshots > * {
  vertical-align: middle;
}

.wpforms-admin-settings-access .screenshots .cont {
  display: inline-block;
  position: relative;
  width: 240px;
  padding: 5px;
  background-color: #ffffff;
  -webkit-box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.05);
  -moz-box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.05);
  box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.05);
  border-radius: 3px;
  margin-right: 40px;
}

.wpforms-admin-settings-access .screenshots .cont img {
  max-width: 100%;
  display: block;
}

.wpforms-admin-settings-access .screenshots .cont .hover {
  position: absolute;
  opacity: 0;
  height: 100%;
  width: 100%;
  top: 0;
  left: 0;
  border: 5px solid #ffffff;
  background-color: rgba(68, 68, 68, 0.15);
  background-image: url("../../../assets/images/zoom.svg");
  background-repeat: no-repeat;
  background-position: center;
  background-size: 50px;
  transition: all 0.3s;
}

.wpforms-admin-settings-access .screenshots .cont .hover:focus {
  box-shadow: none;
}

.wpforms-admin-settings-access .screenshots .cont:hover .hover {
  opacity: 1;
  transition: all 0.3s;
}

.wpforms-admin-settings-access .screenshots .cont span {
  position: absolute;
  bottom: -25px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 14px;
  color: #646970;
  white-space: nowrap;
}

.wpforms-admin-settings-access .caps {
  color: #2c3338;
}

.wpforms-admin-settings-access .caps p {
  margin: 0;
}

.wpforms-admin-settings-access .caps ul {
  display: inline-block;
  width: 240px;
  margin: 20px 40px 0 0;
  vertical-align: top;
}

.wpforms-admin-settings-access .caps ul li:last-child {
  margin-bottom: 0;
}

.wpforms-admin-settings-access .caps li {
  line-height: 14px;
  margin: 0 0 22px 0;
  padding: 0 0 0 30px;
  background-image: url("../../../assets/images/check-solid.svg");
  background-position: left center;
  background-repeat: no-repeat;
  background-size: 16px 12px;
}

.wpforms-admin-settings-access .upgrade {
  border-bottom: none;
}

@media (max-width: 917px) {
  #wpforms-settings-access .screenshots .cont {
    margin-bottom: 40px;
  }
  #wpforms-settings-access .screenshots .cont:last-child {
    margin-bottom: 0;
  }
  #wpforms-settings-access .caps ul {
    margin-bottom: 20px;
  }
  #wpforms-settings-access .caps ul:last-child {
    margin-bottom: 0;
  }
}
