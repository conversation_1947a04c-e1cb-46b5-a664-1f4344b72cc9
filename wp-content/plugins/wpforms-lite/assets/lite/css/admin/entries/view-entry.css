.wpforms_page_wpforms-entries {
  overflow-x: hidden;
}

#wpforms-entries-single .page-title {
  padding-top: 14px;
  padding-bottom: 14px;
}

#wpforms-entries-single .page-title .page-title-action svg {
  fill: #50575e;
}

#wpforms-entries-single .page-title .page-title-action:hover svg, #wpforms-entries-single .page-title .page-title-action:focus svg {
  fill: #2c3338;
}

#wpforms-entries-single.wpforms-entries-single-sample .postbox-header h2 {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #32373c;
  font-style: normal;
  font-size: 16px;
  font-weight: 500;
  line-height: 20px;
  padding: 10px 12px;
}

#wpforms-entries-single.wpforms-entries-single-sample #wpforms-entry-fields .inside p {
  padding: 0;
  margin: 0;
}

#wpforms-entries-single.wpforms-entries-single-sample #wpforms-entry-fields .inside .wpforms-entry-field-name {
  font-weight: 600;
  background: #f6f6f6;
  padding: 8px 12px;
}

#wpforms-entries-single.wpforms-entries-single-sample #wpforms-entry-fields .inside .wpforms-field-email .wpforms-entry-field-value {
  color: #056aab;
  text-decoration: underline;
  cursor: not-allowed;
}

#wpforms-entries-single.wpforms-entries-single-sample #wpforms-entry-fields .inside .wpforms-field-file-upload .file-name {
  color: #056aab;
  text-decoration: underline;
  cursor: not-allowed;
}

#wpforms-entries-single.wpforms-entries-single-sample #wpforms-entry-fields .inside .wpforms-entry-field-value {
  padding: 8px 12px;
  border-radius: 4px;
}

#wpforms-entries-single.wpforms-entries-single-sample #wpforms-entry-notes {
  color: #2c3338;
}

#wpforms-entries-single.wpforms-entries-single-sample #wpforms-entry-notes .wpforms-entry-sample-notes-new {
  padding: 10px;
}

#wpforms-entries-single.wpforms-entries-single-sample #wpforms-entry-notes .wpforms-entry-notes-single {
  padding: 10px;
}

#wpforms-entries-single.wpforms-entries-single-sample #wpforms-entry-notes .wpforms-entry-notes-single.odd {
  background-color: #f6f6f6;
}

#wpforms-entries-single.wpforms-entries-single-sample #wpforms-entry-notes .wpforms-entry-notes-single .note-user {
  color: #056aab;
  text-decoration: underline;
}

#wpforms-entries-single.wpforms-entries-single-sample #wpforms-entry-notes .wpforms-entry-notes-byline {
  color: #888;
  margin-bottom: 10px;
}

#wpforms-entries-single.wpforms-entries-single-sample #wpforms-entry-notes .wpforms-entry-notes-byline .sample-note-delete {
  color: #a00;
  text-decoration: none;
}

#wpforms-entries-single.wpforms-entries-single-sample #wpforms-entry-user-journey .inside {
  padding: 0;
  margin: 0;
}

#wpforms-entries-single.wpforms-entries-single-sample #wpforms-entry-user-journey table {
  width: 100%;
  border-spacing: 0;
}

#wpforms-entries-single.wpforms-entries-single-sample #wpforms-entry-user-journey table tr td, #wpforms-entries-single.wpforms-entries-single-sample #wpforms-entry-user-journey table tr th {
  vertical-align: top;
  border-bottom: 1px solid #eee;
  padding: 10px;
  text-align: start;
  color: #2c3338;
}

#wpforms-entries-single.wpforms-entries-single-sample #wpforms-entry-user-journey table tr td:first-of-type, #wpforms-entries-single.wpforms-entries-single-sample #wpforms-entry-user-journey table tr th:first-of-type {
  padding-left: 12px;
}

#wpforms-entries-single.wpforms-entries-single-sample #wpforms-entry-user-journey table tr td:last-of-type, #wpforms-entries-single.wpforms-entries-single-sample #wpforms-entry-user-journey table tr th:last-of-type {
  padding-right: 12px;
}

#wpforms-entries-single.wpforms-entries-single-sample #wpforms-entry-user-journey table tr td.date, #wpforms-entries-single.wpforms-entries-single-sample #wpforms-entry-user-journey table tr th.date {
  background: #f5f5f5;
  font-weight: 600;
}

#wpforms-entries-single.wpforms-entries-single-sample #wpforms-entry-user-journey table tr td.time, #wpforms-entries-single.wpforms-entries-single-sample #wpforms-entry-user-journey table tr th.time {
  width: 65px;
}

#wpforms-entries-single.wpforms-entries-single-sample #wpforms-entry-user-journey table tr td .fa-circle, #wpforms-entries-single.wpforms-entries-single-sample #wpforms-entry-user-journey table tr th .fa-circle {
  font-size: 4px;
  vertical-align: middle;
  margin: 0 4px;
  color: #ccc;
}

#wpforms-entries-single.wpforms-entries-single-sample #wpforms-entry-user-journey table tr td.title-area .path, #wpforms-entries-single.wpforms-entries-single-sample #wpforms-entry-user-journey table tr th.title-area .path {
  font-weight: 400;
  color: #a6a6a6;
}

#wpforms-entries-single.wpforms-entries-single-sample #wpforms-entry-user-journey table tr td.title-area a, #wpforms-entries-single.wpforms-entries-single-sample #wpforms-entry-user-journey table tr th.title-area a {
  text-decoration: none;
}

#wpforms-entries-single.wpforms-entries-single-sample #wpforms-entry-user-journey table tr td.title-area .go, #wpforms-entries-single.wpforms-entries-single-sample #wpforms-entry-user-journey table tr th.title-area .go {
  color: #a6a6a6;
  margin: 0 4px;
  font-size: 12px;
}

#wpforms-entries-single.wpforms-entries-single-sample #wpforms-entry-user-journey table tr td .fa-check, #wpforms-entries-single.wpforms-entries-single-sample #wpforms-entry-user-journey table tr th .fa-check {
  color: #009933;
}

#wpforms-entries-single.wpforms-entries-single-sample #wpforms-entry-user-journey table tr:last-child td {
  border-bottom: 0;
}

#wpforms-entries-single.wpforms-entries-single-sample #wpforms-entry-details .inside, #wpforms-entries-single.wpforms-entries-single-sample #wpforms-entry-actions .inside {
  margin: 0;
  padding: 0;
}

#wpforms-entries-single.wpforms-entries-single-sample .wpforms-entry-details-meta, #wpforms-entries-single.wpforms-entries-single-sample .wpforms-entry-actions-meta {
  padding: 5px 0;
}

#wpforms-entries-single.wpforms-entries-single-sample .wpforms-entry-details-meta p, #wpforms-entries-single.wpforms-entries-single-sample .wpforms-entry-actions-meta p {
  color: #32373c;
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  margin: 0;
  padding: 6px 12px 6.5px 42px;
  position: relative;
}

#wpforms-entries-single.wpforms-entries-single-sample .wpforms-entry-details-meta p .dashicons, #wpforms-entries-single.wpforms-entries-single-sample .wpforms-entry-actions-meta p .dashicons {
  color: #8c8f94;
  font-size: 22px;
  height: 22px;
  inset-inline-start: 12px;
  position: absolute;
  text-decoration: none;
  top: 5px;
  width: 22px;
}

#wpforms-entries-single.wpforms-entries-single-sample .wpforms-entry-details-meta p strong, #wpforms-entries-single.wpforms-entries-single-sample .wpforms-entry-actions-meta p strong {
  font-weight: 600;
}

#wpforms-entries-single.wpforms-entries-single-sample #wpforms-entry-details .trash-sample {
  color: #a00;
  text-decoration: none;
  padding: 1px 2px;
}

#wpforms-entries-single.wpforms-entries-single-sample .wpforms-entry-actions-meta a {
  text-decoration: none;
}

#wpforms-entries-single.wpforms-entries-single-sample .wpforms-entry-actions-meta p {
  color: #056aab;
}

#wpforms-entries-single.wpforms-entries-single-sample .wpforms-entry-actions-meta p > span {
  cursor: not-allowed;
}

#wpforms-entries-single.wpforms-entries-single-sample #wpforms-entry-geolocation ul {
  margin: 0;
}

#wpforms-entries-single.wpforms-entries-single-sample #wpforms-entry-geolocation ul li {
  border-bottom: 1px solid #eee;
  margin: 0;
  overflow: auto;
  color: #2c3338;
}

#wpforms-entries-single.wpforms-entries-single-sample #wpforms-entry-geolocation ul li:last-child {
  border-bottom: 0;
}

#wpforms-entries-single.wpforms-entries-single-sample #wpforms-entry-geolocation ul li .wpforms-geolocation-meta {
  width: 65px;
  float: left;
  padding: 10px 0 10px 12px;
}

#wpforms-entries-single.wpforms-entries-single-sample #wpforms-entry-geolocation ul li .wpforms-geolocation-value {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 10px 12px 10px 20px;
}

#wpforms-entries-single.wpforms-entries-single-sample #wpforms-entry-geolocation ul li .wpforms-geolocation-value .wpforms-geolocation-flag {
  width: 16px;
  height: 11px;
}

#wpforms-entries-single.wpforms-entries-single-sample .wpforms-entries-settings-container .button {
  background: #ffffff;
  border-color: #8c8f94;
  color: #50575e;
  padding: 5px;
  height: auto;
  width: 34px;
  min-height: 32px;
  box-shadow: none;
  line-height: 17px;
  margin-bottom: 0;
  pointer-events: inherit;
}

#wpforms-entries-single.wpforms-entries-single-sample .wpforms-entries-settings-container .dashicons {
  font-size: 19px;
  line-height: 17px;
  height: 17px;
  width: 17px;
}

#wpforms-entries-single.wpforms-entries-single-sample .wpforms-entries-settings-container .wpforms-entries-settings-menu {
  background: #ffffff;
  border-radius: 6px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
  display: none;
  width: 240px;
  position: absolute;
  z-index: 30;
  right: 10px;
  top: 55px;
}

#wpforms-entries-single.wpforms-entries-single-sample .wpforms-entries-settings-container .wpforms-entries-settings-menu .wpforms-entries-settings-menu-wrap .wpforms-settings-title {
  display: block;
  width: 100%;
  font-size: 11px;
  line-height: 13px;
  text-transform: uppercase;
  padding: 14px 15px;
  border-top: 1px solid #dcdcde;
  border-bottom: 1px solid #dcdcde;
  margin: 7.5px 0;
  font-weight: 600;
  color: #50575e;
}

#wpforms-entries-single.wpforms-entries-single-sample .wpforms-entries-settings-container .wpforms-entries-settings-menu .wpforms-entries-settings-menu-wrap .wpforms-settings-title:first-child {
  border-top: 0;
  margin-top: 0;
}

#wpforms-entries-single.wpforms-entries-single-sample .wpforms-entries-settings-container .wpforms-entries-settings-menu .wpforms-entries-settings-menu-wrap .wpforms-toggle-control {
  padding: 8px 14px;
  font-weight: 400;
  opacity: .5;
  pointer-events: none;
}

#wpforms-entries-single.wpforms-entries-single-sample .wpforms-sample-notification {
  display: flex;
  padding: 20px;
  flex-direction: column;
  align-items: flex-start;
  gap: 10px;
  border-radius: 6px;
  background: #fff;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.07);
}

#wpforms-entries-single.wpforms-entries-single-sample .wpforms-sample-notification h2 {
  color: #1d2327;
  font-size: 20px;
  font-weight: 500;
  line-height: 18px;
  margin: 0;
}

#wpforms-entries-single.wpforms-entries-single-sample .wpforms-sample-notification .notice-buttons {
  margin: 0;
}

#wpforms-entries-single.wpforms-entries-single-sample .wpforms-sample-notification .notice-buttons .wpforms-btn {
  box-sizing: border-box;
}

#wpforms-entries-single.wpforms-entries-single-sample .wpforms-sample-notification .wpforms-sample-notification-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 5px;
}

#wpforms-entries-single.wpforms-entries-single-sample .wpforms-sample-notification .wpforms-sample-notification-content p {
  color: #2c3338;
  font-size: 15px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  opacity: 0.8;
  margin: 0;
}

#wpforms-entries-single.wpforms-entries-single-sample .wpforms-sample-notification .wpforms-sample-notification-content p a {
  font-size: 15px;
  font-style: normal;
  font-weight: 500;
  line-height: 22px;
  text-decoration-line: underline;
}

#wpforms-entries-single.wpforms-entries-single-sample .wpforms-sample-notification #wpforms-hide-sample-data {
  display: none;
}

#wpforms-entries-single.wpforms-entries-single-sample #poststuff {
  padding-top: 20px;
}

.rtl #wpforms-entries-single .wpforms-entry-details-meta p, .rtl #wpforms-entries-single .wpforms-entry-actions-meta p, .rtl #wpforms-entries-single .wpforms-entry-payment-meta p {
  padding: 6px 42px 6.5px 12px;
}

.rtl #wpforms-entries-single .wpforms-entries-settings-container .wpforms-entries-settings-menu {
  left: 10px;
  right: auto;
}

@media (max-width: 600px) {
  #wpforms-entries-single.wpforms-admin-wrap .page-title a.page-title-action {
    display: none !important;
  }
  #wpforms-entries-single .wpforms-admin-single-navigation {
    display: flex;
  }
  #wpforms-entries-single .wpforms-admin-single-navigation .wpforms-admin-single-navigation-text {
    display: none;
  }
}

#publishing-action .button,
.wpforms-input-disabled {
  cursor: not-allowed;
}

#wpforms-entries-single .button:hover {
  background-color: #056aab;
  border-color: #056aab;
  color: #ffffff;
}

#wpforms-admin-single-navigation-next-link:hover {
  background-color: #f0f0f1;
}
