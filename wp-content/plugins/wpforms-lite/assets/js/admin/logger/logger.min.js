const WPFormsLogger=window.WPFormsLogger||function(n,t){const s={init(){t(s.ready)},ready(){s.bindEvents()},bindPopup(){t(".wpforms-list-table--logs .wp-list-table").on("click",".js-single-log-target",function(o){o.preventDefault(),s.showPopup(t(this).attr("data-log-id"))})},bindEvents(){s.bindPopup(),t("#wpforms-setting-logs-enable").change(function(){s.toggleLogs(t(this).is(":checked"))})},toggleLogs(o){t(".wpforms-logs-settings").toggleClass("wpforms-hidden",!o)},showPopup(o){if(o){const e=wp.template("wpforms-log-record");t.dialog({title:!1,boxWidth:Math.min(1200,.8*t(n).width()),content(){const n=this;return t.get(wpforms_admin.ajax_url,{action:"wpforms_get_log_record",nonce:wpforms_admin.nonce,recordId:o}).done(function(o){o.success&&o.data?n.setContent(e(o.data)):(s.error(o.data),n.close())}).fail(function(o,e){s.error(e+" "+o.responseText),n.close()})},animation:"scale",columnClass:"medium",closeIcon:!0,closeAnimation:"scale",backgroundDismiss:!0})}},error(o){wpforms_admin.debug&&(o=o?": "+o:"",console.log("WPForms Debug: Error receiving log record data"+o))}};return s}((document,window),jQuery);WPFormsLogger.init();