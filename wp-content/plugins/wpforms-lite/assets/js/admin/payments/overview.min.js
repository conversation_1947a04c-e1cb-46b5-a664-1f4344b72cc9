const WPFormsPaymentsOverview=window.WPFormsPaymentsOverview||function(e,t,i,r,a){const o={},l={chart:null,datepicker:null,locale:a.locale,currency:a.currency,currencyDecimals:a.decimals,nonce:a.nonce,data:[],type:1===a.settings.graph_style?"bar":"line",delimiter:a.delimiter,tooltipFormat:a.date_format,get currentPageUri(){return new URL(a.page_uri)},classNames:{hide:"wpforms-hide",ready:"is-ready",fetching:"doing-ajax",selected:"is-selected",calculated:"is-calculated"},timespan:"",report:a.active_report,isAmount:!1,get colors(){var e="line"===this.type;return{total_payments:{hoverBorderColor:"#055f9a",hoverBackgroundColor:"#055f9a",borderColor:"#056aab",backgroundColor:e?"#e6f0f7":"#056aab"},total_sales:{hoverBorderColor:"#00831e",hoverBackgroundColor:"#00831e",borderColor:"#008a20",backgroundColor:e?"#e3f3e4":"#008a20"},total_refunded:{hoverBorderColor:"#373e45",hoverBackgroundColor:"#373e45",borderColor:"#50575e",backgroundColor:e?"#ebebec":"#50575e"},default:{hoverBorderColor:"#cd6622",hoverBackgroundColor:"#cd6622",borderColor:"#e27730",backgroundColor:e?"#fcf1ea":"#e27730"}}},get i18n(){return a.i18n},get xAxesDisplayFormat(){var e;return!this.timespan.length||(e=this.timespan.split(this.delimiter),!Array.isArray(e))||2!==e.length||moment(e[0]).format("YYYY")===moment(e[1]).format("YYYY")?"MMM D":"MMM D YYYY"},get amountFormatter(){return new Intl.NumberFormat(this.locale,{style:"currency",useGrouping:!0,currencyDisplay:"narrowSymbol",currency:this.currency,minimumFractionDigits:this.currencyDecimals,maximumFractionDigits:this.currencyDecimals})},get datasetLabel(){var e=i(`[data-stats=${this.report}]`);return e.length?e.find(".statcard-label").text():this.i18n?.label},get settings(){var e=i("body").hasClass("rtl");return{type:this.type,data:{labels:[],datasets:[{data:[],label:"",borderWidth:2,pointRadius:4,pointBorderWidth:1,maxBarThickness:100,...{pointBackgroundColor:"#ffffff",...this.colors[this.report]||this.colors.default}}]},options:{maintainAspectRatio:!1,layout:{padding:{left:15,right:19,top:25,bottom:9}},scales:{x:{type:"timeseries",offset:"bar"===this.type,time:{tooltipFormat:this.tooltipFormat},reverse:e,ticks:{padding:10,font:{size:13,color:"#a7aaad"},labelOffset:10,minRotation:25,maxRotation:25,callback(e,t,a){var r=Math.floor(a.length/7);return r<1||(a.length-t-1)%r==0?moment(e).format(l.xAxesDisplayFormat):void 0}}},y:{beginAtZero:!0,ticks:{maxTicksLimit:6,padding:20,font:{size:13,color:"#a7aaad"},callback:e=>this.isAmount?this.amountFormatter.format(e):Math.floor(e)===e?e:void 0}}},elements:{line:{tension:0,fill:!0}},animation:!1,plugins:{legend:{display:!1},tooltip:{displayColors:!1,rtl:e,callbacks:{label:({raw:{y:e}})=>{let t=this.datasetLabel+" ";return this.isAmount?t+=this.amountFormatter.format(e):t+=e}}}}}}}},n={init(){i(n.ready)},ready(){n.setup(),n.bindEvents(),n.initDatePicker(),n.initChart(),n.initMultiSelect()},setup(){o.$document=i(e),o.$wrapper=i(".wpforms-payments-wrap-payments"),o.$form=i("#wpforms-payments-table"),o.$spinner=i(".wpforms-overview-chart .spinner"),o.$canvas=i("#wpforms-payments-overview-canvas"),o.$filterBtn=i("#wpforms-datepicker-popover-button"),o.$datepicker=i("#wpforms-payments-overview-datepicker"),o.$filterForm=i(".wpforms-overview-top-bar-filter-form"),o.$activeStat=o.$filterForm.find('input[name="statcard"]'),o.$table=i(".wpforms-table-list"),o.$notice=i(".wpforms-overview-chart-notice"),o.$reports=i(".wpforms-payments-overview-reports"),o.$multiSelect=i(".wpforms-multiselect")},bindEvents(){o.$document.on("click",{selectors:[".wpforms-datepicker-popover",".wpforms-dash-widget-settings-menu"]},n.handleOnClickOutside),o.$wrapper.on("submit",".wpforms-overview-top-bar-filter-form",n.handleOnSubmitDatepicker).on("submit","#wpforms-payments-table",n.handleOnSubmitOverviewTable).on("click","#doaction",n.handleOnBulkAction).on("click",'.wpforms-overview-top-bar-filter-form [type="reset"]',n.handleOnResetDatepicker).on("change",'.wpforms-overview-top-bar-filter-form [type="radio"]',n.handleOnUpdateDatepicker).on("click",".wpforms-payments-overview-reports button",n.handleOnChangeStatCard).on("click",".wpforms-dash-widget-settings-menu-save",n.handleOnSaveSettings).on("click","#wpforms-payments-mode-toggle",n.handleOnToggleMode).on("click","#wpforms-dash-widget-settings-button",{selector:".wpforms-dash-widget-settings-menu",hide:".wpforms-datepicker-popover"},n.handleOnToggle).on("click","#wpforms-datepicker-popover-button",{selector:".wpforms-datepicker-popover",hide:".wpforms-dash-widget-settings-menu"},n.handleOnToggle)},initDatePicker(){o.$datepicker.length&&(l.timespan=o.$datepicker.val(),l.datepicker=flatpickr(o.$datepicker,{mode:"range",inline:!0,allowInput:!1,enableTime:!1,clickOpens:!1,altInput:!0,altFormat:"M j, Y",dateFormat:"Y-m-d",locale:{...flatpickr.l10ns[l.locale]||{},rangeSeparator:l.delimiter},onChange(e,t,a){var r=o.$filterForm.find('input[value="custom"]');r.prop("checked",!0),n.selectDatepickerChoice(r.parent()),t&&o.$filterBtn.text(a.altInput.value)}}),this.handleOnUpdateDatepicker({},o.$filterForm.find('input[value="custom"]').prop("checked")))},handleOnSubmitDatepicker(){i(this).find('input[type="radio"]').attr("name",""),n.hideElm(o.$filterBtn.next())},handleOnBulkAction(e){e.preventDefault();var t,e=o.$wrapper.find('select[name="action"]').val();["trash","delete"].includes(e)&&(e=o.$wrapper.find('input[name="payment_id[]"]:checked')).length&&e.closest("tr").hasClass("subscription-has-renewal")?({subscription_delete_confirm:e,delete_button:t}=l["i18n"],i.confirm({title:wpforms_admin.heads_up,content:e,icon:"fa fa-exclamation-circle",type:"orange",buttons:{confirm:{text:t,btnClass:"btn-confirm",keys:["enter"],action(){o.$form.submit()}},cancel:{text:wpforms_admin.cancel,keys:["esc"],action(){o.$form.trigger("reset")}}}})):o.$form.submit()},handleOnSubmitOverviewTable(){o.$multiSelect.length&&i('.wpforms-multiselect-checkbox-input[value=""]').removeAttr("name")},handleOnResetDatepicker(e){e.preventDefault(),o.$filterForm.get(0).reset(),n.hideElm(o.$filterBtn.next()),n.handleOnUpdateDatepicker()},handleOnUpdateDatepicker(e=0,t=!1){var a=o.$filterForm.find("input:checked"),r=a.parent(),a=t?o.$datepicker:a,s=a.val().split(l.delimiter);o.$filterBtn.text(t?a.next().val():r.text()),n.selectDatepickerChoice(r),Array.isArray(s)&&2===s.length?l.datepicker.setDate(s):l.datepicker.clear()},initChart(){var e,t;o.$canvas.length&&(e=o.$canvas.get(0).getContext("2d"),t=o.$reports.find("."+l.classNames.selected),l.report=t.data("stats"),l.isAmount=t.hasClass("is-amount"),l.chart=new Chart(e,l.settings),this.updateChartByReport())},initMultiSelect(){o.$multiSelect.length&&t.WPFormsMultiSelectCheckbox&&o.$multiSelect.each(function(){new t.WPFormsMultiSelectCheckbox(this,{showMask:!0,delimiter:"|"}).init()})},handleOnChangeStatCard(e){e.preventDefault();e=i(this);e.hasClass(l.classNames.selected)||e.hasClass("disabled")||(n.spinner(),l.report=e.data("stats"),l.isAmount=e.hasClass("is-amount"),o.$reports.find("button").removeClass(l.classNames.selected),e.addClass(l.classNames.selected),o.$activeStat.length||(o.$filterForm.append('<input type="hidden" name="statcard">'),o.$activeStat=o.$filterForm.find('input[name="statcard"]')),o.$activeStat.val(l.report),n.updateChartByReport())},handleOnSaveSettings(e){e.preventDefault();var e=i(this).closest(".wpforms-dash-widget-settings-container").find('input[name="wpforms-style"]:checked').val(),t=(l.type=1===Number(e)?"bar":"line",Object.assign({},l.settings)),a=(t.data.labels=l.chart.data.labels,t.data.datasets[0].data=l.chart.data.datasets[0].data,l.chart.destroy(),o.$canvas.get(0).getContext("2d"));l.chart=new Chart(a,t),i.post(r,{graphStyle:e,_ajax_nonce:l.nonce,action:"wpforms_payments_overview_save_chart_preference_settings"}).done(function(){o.$wrapper.find(".wpforms-dash-widget-settings-menu").hide()})},handleOnToggleMode(){var e=l["currentPageUri"];e.searchParams.set("mode",this.checked?"test":"live"),e.searchParams.set("_wpnonce",l.nonce),t.location.href=e.href},handleOnToggle(e){e.preventDefault(),e.stopPropagation();const{selector:t,hide:a}=e["data"];o.$wrapper.find(t).toggle(0,function(){var e=i(t);e.attr("aria-expanded",e.is(":visible"))}),n.hideElm(o.$wrapper.find(a))},handleOnClickOutside(e){const{target:a,data:{selectors:t}}=e;i.each(t,function(e,t){i(a).closest(t+":visible").length||n.hideElm(o.$wrapper.find(t))})},processDatasetData(e){const r=[],s=[];if(i.isPlainObject(e)&&0<Object.keys(e).length)o.$notice.addClass(l.classNames.hide),i.each(e||[],function(e,t){var a=moment(t.day);r.push(a),s.push({x:a,y:t?.count||0})});else{var t,e=l["i18n"]["no_dataset"],a=(e?.[l.report]&&o.$notice.find("h2").text(e[l.report]),o.$notice.removeClass(l.classNames.hide),moment().startOf("day"));for(let e=1;e<=30;e++)t=a.clone().subtract(e,"days"),r.push(t),s.push({x:t,y:Math.floor(16*Math.random())+5})}return{labels:r,datasets:s}},updateChart(e){var{labels:e,datasets:t}=n.processDatasetData(e||[]);l.chart.data.labels=e,l.chart.data.datasets[0]=l.settings.data.datasets[0],l.chart.data.datasets[0].data=t,l.chart.update(),o.$spinner.addClass(l.classNames.hide)},updateChartByReport(e){l.report&&Object.hasOwn(l.data,l.report)?n.updateChart(l.data[l.report]?.data||[]):(o.$reports.addClass(l.classNames.fetching),i.post(r,i.extend({},{report:l.report,dates:l.timespan,_ajax_nonce:l.nonce,action:"wpforms_payments_overview_refresh_chart_dataset_data"},e),function({data:e}){l.data=Object.assign({[l.report]:e},l.data),n.updateChart(e?.data||[]),n.updateReports(e?.reports||{})}).done(function(){o.$reports.addClass(l.classNames.ready),o.$reports.removeClass(l.classNames.fetching)}))},updateReports(n){i.isEmptyObject(n)||o.$reports.find("li").each(function(){var e=i(this),t=e.find("button");if(t.hasClass("disabled"))return!0;var a=t.data("stats"),r=n[a]||0,a=Number(n[a+"_delta"])||0,s=e.find(".statcard-value"),e=e.find(".statcard-delta");s.addClass(l.classNames.calculated).html(r),e.addClass(l.classNames.calculated).html(Math.abs(a)),0!==a&&e.addClass(Number(0<a)?"is-upward":"is-downward"),t.hasClass("is-amount")&&s.attr("title",s.text())})},selectDatepickerChoice(e){o.$filterForm.find("label").removeClass(l.classNames.selected),e.addClass(l.classNames.selected)},spinner(){o.$spinner.removeClass(l.classNames.hide)},hideElm(e){e.attr("aria-expanded","false").hide()}};return n}(document,window,jQuery,ajaxurl,wpforms_admin_payments_overview);WPFormsPaymentsOverview.init();