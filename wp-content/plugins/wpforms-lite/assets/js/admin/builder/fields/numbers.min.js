var WPForms=window.WPForms||{};WPForms.Admin=WPForms.Admin||{},WPForms.Admin.Builder=WPForms.Admin.Builder||{},WPForms.Admin.Builder.FieldNumbers=WPForms.Admin.Builder.FieldNumbers||function(u){const i={$builder:null,tagClicked:!1,init(){u(i.ready)},ready(){i.$builder=u("#wpforms-builder"),i.numbersEvents()},numbersEvents(){i.$builder.on("change",".wpforms-field-option-number .wpforms-numbers-min",i.onChangeNumbersMin),i.$builder.on("change",".wpforms-field-option-number .wpforms-numbers-max",i.onChangeNumbersMax),i.$builder.on("input",".wpforms-field-option-number .wpforms-field-option-row-default_value .wpforms-smart-tags-widget-original",_.debounce(i.onChangeNumbersDefaultValue,500)),i.$builder.on("click",".wpforms-smart-tags-widget .tag",i.smartTagClickTracking)},smartTagClickTracking(){i.tagClicked=!0,setTimeout(()=>{i.tagClicked=!1},200)},parseFieldValue(e){return!e.length||""===e.val()||(e=parseFloat(e.val()),isNaN(e))?null:e},isInvalidMinMaxRange(e,a){e=i.parseFieldValue(e),a=i.parseFieldValue(a);return null!==e&&null!==a&&a<e},syncNumberMinAttribute(e,a){a.attr("min",i.parseFieldValue(e))},syncNumberMaxAttribute(e,a){e.attr("max",i.parseFieldValue(a))},adjustValue(e,a){a.val(i.parseFieldValue(e)).trigger("input").trigger("wpformsSmartTagsInputSync")},onChangeNumbersMin(e){var e=u(e.target),a=e.closest(".wpforms-field-option-group"),r=a.find(".wpforms-numbers-max"),a=a.find(".wpforms-field-option-row-default_value input.wpforms-smart-tags-widget-original");i.isInvalidMinMaxRange(e,r)&&i.adjustValue(r,e),i.isNeedAdjustDefaultValueByMinValue(a,e)&&i.adjustValue(e,a),i.syncNumberMinAttribute(e,r)},onChangeNumbersMax(e){var e=u(e.target),a=e.closest(".wpforms-field-option-group"),r=a.find(".wpforms-numbers-min"),a=a.find(".wpforms-field-option-row-default_value input.wpforms-smart-tags-widget-original");i.isInvalidMinMaxRange(r,e)&&i.adjustValue(r,e),i.isNeedAdjustDefaultValueByMaxValue(a,e)&&i.adjustValue(e,a),i.syncNumberMaxAttribute(r,e)},normalizeFloatValue(e){var a=e.val(),r=a.replace(",",".");wpf.isNumber(r)&&a!==parseFloat(a).toString()&&e.val(parseFloat(r)).trigger("input")},isNeedAdjustDefaultValueByMinValue(e,a){e=i.parseFieldValue(e),a=i.parseFieldValue(a);return wpf.isNumber(e)&&null!==a&&e<a},isNeedAdjustDefaultValueByMaxValue(e,a){e=i.parseFieldValue(e),a=i.parseFieldValue(a);return wpf.isNumber(e)&&null!==a&&a<e},onChangeNumbersDefaultValue(e){var a,r;i.tagClicked||"focusout"===e.handleObj?.type||(a=(r=(e=u(e.target)).closest(".wpforms-field-option-group")).find(".wpforms-numbers-min"),r=r.find(".wpforms-numbers-max"),i.normalizeFloatValue(e),i.isNeedAdjustDefaultValueByMinValue(e,a)&&i.adjustValue(a,e),i.isNeedAdjustDefaultValueByMaxValue(e,r)&&i.adjustValue(r,e))}};return i}((document,window,jQuery)),WPForms.Admin.Builder.FieldNumbers.init();