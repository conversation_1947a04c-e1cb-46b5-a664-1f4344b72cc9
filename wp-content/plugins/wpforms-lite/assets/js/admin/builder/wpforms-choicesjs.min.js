"use strict";var WPForms=window.WPForms||{};WPForms.Admin=WPForms.Admin||{},WPForms.Admin.Builder=WPForms.Admin.Builder||{},WPForms.Admin.Builder.WPFormsChoicesJS=WPForms.Admin.Builder.WPFormsChoicesJS||function(o,t){const r={setup:function(i,e,n){e.searchEnabled=!0,e.allowHTML=!1,e.searchChoices=null===n.nonce,e.renderChoiceLimit=-1,e.noChoicesText=e.noChoicesText||wpforms_builder.no_pages_found,e.noResultsText=e.noResultsText||wpforms_builder.no_pages_found;const a=new Choices(i,e);return null!==n.nonce&&(t(i).data("choicesjs",a),a.input.element.addEventListener("keyup",function(e){8!==e.which&&46!==e.which||0<e.target.value.length||r.performSearch(a,"",n)}),a.passedElement.element.addEventListener("search",_.debounce(function(e){0!==a.input.element.value.length&&r.performSearch(a,e.detail.value,n)},800)),a.passedElement.element.addEventListener("change",function(){var e,n,o=t(this);o.prop("multiple")&&(e=o.data("field-id"),o=o.data("field-name"),n=a.getValue().map(function(e){return e.value}),t(`#wpforms-field-${e}-${o}-select-multiple-options`).val(JSON.stringify(n)))}),a.containerOuter.element.addEventListener("click",function(){t(this).hasClass("is-open")&&a.hideDropdown()}),t(o).on("wpformsFieldOptionGroupToggled",function(){wpf.showMoreButtonForChoices(a.containerOuter.element)}).on("wpformsBeforeFieldDuplicate",function(e,n){if(t(i).data("field-id")===n){const o=a.getValue(!0);t(i).data("choicesjs").destroy(),t(i).find("option").each(function(e,n){o.includes(t(n).val())&&t(n).prop("selected",!0)})}}).on("wpformsFieldDuplicated",function(e,n){t(i).data("field-id")===n&&t(i).data("choicesjs").init()})),a},performSearch(n,e,o){o.action&&o.nonce&&(r.displayLoading(n),r.ajaxSearch(o.action,e,o.nonce,n.getValue(!0)).done(function(e){n.setChoices(e.data,"value","label",!0)}))},displayLoading(e){e.setChoices([{value:"",label:wpforms_builder.loading+"...",disabled:!0}],"value","label",!0)},ajaxSearchPages(e,n,o){return console.warn("WPForms.Admin.Builder.WPFormsChoicesJS.ajaxSearchPages is deprecated. Use WPForms.Admin.Builder.WPFormsChoicesJS.ajaxSearch instead."),r.ajaxSearch(e,n,o)},ajaxSearch(e,n,o,i=[]){return t.get(wpforms_builder.ajax_url,{action:e,search:n,_wpnonce:o,exclude:i}).fail(function(e){console.error(e)})}};return r}(document,(window,jQuery));