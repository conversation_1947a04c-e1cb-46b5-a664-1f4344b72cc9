var WPForms=window.WPForms||{};WPForms.Admin=WPForms.Admin||{},WPForms.Admin.Builder=WPForms.Admin.Builder||{},WPForms.Admin.Builder.SmartTags=WPForms.Admin.Builder.SmartTags||function(g,s,o){const i={},r=new WeakMap,m={init(){o(m.ready)},ready(){m.setup(),m.events()},setup(){i.$builder=o("#wpforms-builder")},events(){i.$builder.on("wpformsBuilderReady",()=>{m.initWidgets(i.$builder)}).on("wpformsBuilderReady",m.initDropdowns).on("connectionsDataLoaded",m.initWidgetsInConnections).on("connectionRendered",m.initWidgetsInConnections).on("wpformsSettingsBlockAdded wpformsSettingsBlockCloned ",m.reinitWidgetInClone).on("wpformsFieldAdd",m.fieldAdd).on("wpformsFieldDuplicated",m.fieldDuplicated).on("change",".wpforms-field-option-row-label input",m.fieldLabelChangeEvent),i.$builder.on("click",".wpforms-show-smart-tags, .mce-wpforms-smart-tags-mce-button",function(){m.showSmartTagDropdown(o(this))}),o(g).on("wpformsFieldUpdate",m.initDropdowns).on("click",".wpforms-smart-tags-widget .tag",m.smartTagClick)},initWidgets(e=i.$builder){e.find(".wpforms-smart-tags-enabled").each(function(){if(!o(this).hasClass("wpforms-smart-tags-widget-original")){const n=o(this),s=n.is("input")?"input":"textarea";var e="wpforms-smart-tags-widget-"+s,t=n.prop("readonly")||n.prop("disabled"),a=t?"wpforms-readonly":"",r=o("<div>",{class:"wpforms-smart-tags-widget-container"});n.before(r);const i=o("<div>",{class:["wpforms-smart-tags-widget",e,a].filter(Boolean).join(" "),contenteditable:t?"false":"true",spellcheck:"false",text:n.val()});r.append(i),r.append('<span class="wpforms-show-smart-tags"><i class="fa fa-tags"></i></span>'),n.addClass("wpforms-smart-tags-widget-original"),m.setupOriginalInputObserver(n),n.on("wpformsSmartTagsInputSync",function(){m.syncWidgetContent(n,i)}),i.on("input",m.renderWidgetContent),i.on("focus blur keyup mouseup",function(){m.saveCaretPosition(i[0])}),i.on("focusout",function(){n.trigger("focusout")}),i.on("keydown",function(e){"input"==s&&"Enter"===e.key&&e.preventDefault(),"textarea"!=s||"Enter"!==e.key||e.shiftKey||(e.preventDefault(),m.insertLineBreak())}),i.data("pasteHandlerAttached")||(i.on("paste",e=>{var t;e.preventDefault(),i.hasClass("wpforms-readonly")||(t=g.defaultView.getSelection()).rangeCount&&(e=e.originalEvent.clipboardData.getData("text/plain"),(t=t.getRangeAt(0)).deleteContents(),t.insertNode(g.createTextNode(e)),t.collapse(!1),m.renderWidgetContent({target:i[0]},!0,"end"))}),i.data("pasteHandlerAttached",!0)),m.renderWidgetContent({target:i[0]},!0),WPFormsUtils.triggerEvent(i,"wpformsSmartTagWidgetInitialized"),m.initTooltip(i)}})},reinitWidgets(e){e.find(".wpforms-smart-tags-widget-container").each(function(){var e=o(this);e.next(".wpforms-smart-tags-enabled").removeClass("wpforms-smart-tags-widget-original"),e.remove()}),m.initWidgets(e)},reinitWidgetInClone(e,t){m.reinitWidgets(t)},initWidgetsInConnections(e){e=o(e.target);m.initWidgets(e)},renderWidgetContent(e,t=!1,a=""){const l=e.target;if(!l.classList.contains("tag")){l.normalize();e=g.defaultView.getSelection();if(e.rangeCount||t){var r=Array.from(l.childNodes);let d=null;r.forEach(t=>{if(t.nodeType===Node.TEXT_NODE){let e=t.nodeValue;for(var a=/{([^{}]+)}/g;null!==(i=a.exec(e));){var r=wpf.sanitizeHTML(i[1].trim()),n=i[0],s=m.handleEmailTag(n,r,i,e,t);if(s.handled)e=s.text,t=s.node;else{s=m.getSmartTagTitle(r);const o=g.createElement("span");o.classList.add("tag"),o.contentEditable="false",o.setAttribute("data-value",r),o.innerText=s;var r=g.createElement("i"),s=(r.classList.add("fa","fa-times-circle"),r.setAttribute("title",wpforms_builder.smart_tags_delete_button),r.addEventListener("click",()=>{o.remove(),m.updateOriginalInput(l)}),o.appendChild(r),e.slice(0,i.index)),r=e.slice(i.index+n.length),i=g.createTextNode(s),n=g.createTextNode(r),s=t.parentNode;s.insertBefore(i,t),s.insertBefore(o,t),s.insertBefore(n,t),s.removeChild(t),e=r,t=n,d=o,a.lastIndex=0}}}}),d&&!t&&((r=g.createRange()).setStartAfter(d),r.collapse(!0),e.removeAllRanges(),e.addRange(r)),"end"===a&&((t=g.createRange()).selectNodeContents(l),t.collapse(!1),e.removeAllRanges(),e.addRange(t)),m.updateOriginalInput(l)}}},updateOriginalInput(e){var t=o(e),a=t.parent().next(".wpforms-smart-tags-widget-original");a.length&&(e=m.getWidgetContent(e),a.val(e),t.hasClass("wpforms-readonly")||a.trigger("input"))},getSmartTagTitle(e){if(!e)return"";e=e.toString().trim();var t=m.getSmartTagFieldTitle(e)||m.getSmartTagWithArgsTitle(e)||wpforms_builder.smart_tags[e];return t||e},getSmartTagFieldTitle(e){e=e.match(/^(field_id|field_value_id|field_html_id)="(\d+)?(\|[^"]+)?"$/);if(!e||!e.length)return"";var t,a=e[1],r=e[2],e=e[3]?e[3].replace("|",""):"";let n=`[ ${wpforms_builder.smart_tags_edit} ID ]`;return r&&(t=e.length?e.charAt(0).toUpperCase()+e.slice(1):"",e=e.length?" - "+t:"",t=((t=r?o(`#wpforms-field-option-${r}-label`).val():"")||wpforms_builder.smart_tags_unknown_field)+e,n=`#${r}: `+t),wpforms_builder.smart_tags_templates[a].replace("%1$s",n)},getSmartTagWithArgsTitle(e){var t,a,e=e.match(/^(query_var|user_meta|date|entry_date) (key|format)="([^"]+)?"$/);return e&&e.length?(t=e[1],a=e[2]||wpforms_builder.smart_tags_arg,e=e[3]||`[ ${wpforms_builder.smart_tags_edit} ${a} ]`,wpforms_builder.smart_tags_templates[t].replace("%1$s",e)):""},fieldLabelChangeEvent(){m.updateSmartTagsTitles(i.$builder)},updateSmartTagsTitles(e){e.find(".wpforms-smart-tags-widget .tag").each(function(){var e=o(this),t=e.find("i").detach();e.text(m.getSmartTagTitle(e.data("value"))).append(t)})},getWidgetContent(e){if(!e||!e.childNodes)return"";let t="";return e.childNodes.forEach(e=>{e.nodeType===Node.TEXT_NODE?t+=e.nodeValue.replaceAll("​",""):e.nodeType===Node.ELEMENT_NODE&&"BR"===e.nodeName?t+=`
`:e.nodeType===Node.ELEMENT_NODE&&e.classList.contains("tag")&&(e=o(e).data("value"),t+=`{${e}}`)}),t.trim()},initTooltip(e){var e=e.next(".wpforms-show-smart-tags"),t={content:wpforms_builder.smart_tags_button_tooltip,contentAsHTML:!0,interactive:!0,animationDuration:100,delay:[1500,200],side:["top"],maxWidth:270,functionBefore(e,t){if(o(t.origin).hasClass("active"))return!1}};e.tooltipster(t)},smartTagClick(e){const t=o(this);var a,r;t.is(e.target)&&"true"!==t.attr("contenteditable")&&(e.preventDefault(),e=t.data("value"),a=t.find("i").detach(),r=o('<i class="tag-edit-ok fa fa-check-circle"></i>').attr("title",wpforms_builder.smart_tags_edit_ok_button),t.attr("contenteditable",!0).data("restore",e).data("close",a).css("min-width",t.outerWidth()).text(e).after(r).parent().attr("contenteditable",!1),t.data("bind-events")||t.on("blur",m.smartTagBlurEvent).on("keydown",m.smartTagKeyDown).data("bind-events",!0),m.setCaretSmartTagEnd(t),setTimeout(()=>t.focus(),0))},smartTagBlurEvent(){var e=o(this);"true"===e.attr("contenteditable")&&m.smartTagBlur(e)},smartTagBlur(e,t=!1){let a=t?e.data("restore"):e.text();a=a.replace(/\{|\}/g,"").trim(),a=wpf.sanitizeHTML(a),e.data("value",a).attr("data-value",a).text(m.getSmartTagTitle(a)).attr("style",null).append(e.data("close")).attr("contenteditable",!1).next(".tag-edit-ok").remove(),e.parent().attr("contenteditable",!0).trigger("input"),m.setCaretAfterSmartTag(e)},setCaretAfterSmartTag(t){if(t&&t.length){var a=g.defaultView.getSelection(),r=g.createRange(),n=t[0].parentNode;for(let e=0;e<n.childNodes.length;e++)if(n.childNodes[e]===t[0])return r.setStart(n,e+1),r.collapse(!0),a.removeAllRanges(),void a.addRange(r)}},setCaretSmartTagEnd(e){var t,a;e&&e.length&&(t=g.defaultView.getSelection(),(a=g.createRange()).selectNodeContents(e[0]),a.collapse(!1),t.removeAllRanges(),t.addRange(a))},smartTagKeyDown(e){var t=o(this);switch(e.code){case"Enter":e.preventDefault(),e.stopImmediatePropagation(),m.smartTagBlur(t,!1);break;case"Escape":e.preventDefault(),e.stopImmediatePropagation(),m.smartTagBlur(t,!0)}},initDropdowns(){i.$builder.find(".wpforms-show-smart-tags, .mce-wpforms-smart-tags-mce-button").each(function(){var e=o(this),t=e.data("dropdown-list");t&&t.destroy(),m.getDropdownListInstance(e),e.removeClass("active")})},getDropdownListInstance(a){var e=a.data("dropdown-list");if(!e){var t=a.hasClass("mce-wpforms-smart-tags-mce-button");const n=t?a.closest(".wp-editor-container").find("textarea"):a.closest(".wpforms-smart-tags-widget-container").next(".wpforms-smart-tags-widget-original");var r=n?.attr("id"),r=!!r&&r.includes("wpforms-field-option-"),r=(["location","type","fields","allow-repeated-fields"].forEach(e=>{var t=n.data(e);void 0!==t&&a.attr("data-"+e,t)}),m.getSmartTagsList(a,r));if(!r.length)return a.addClass("disabled"),null;a.removeClass("disabled"),e=m.initDropdownInstance(a,r,t),a.data("dropdown-list",e)}return e},initDropdownInstance(e,t,a=!1){a=m.getDropdownContainer(e,a);return WPForms.Admin.Builder.DropdownList.init({class:"insert-smart-tag-dropdown",title:wpforms_builder.smart_tags_dropdown_title,list:t,container:a,button:e,search:{enabled:!0,searchBy:["wpforms-smart-tags-widget-item"],placeholder:wpforms_builder.search,noResultsText:wpforms_builder.no_results_found},noLeftOffset:!0,itemFormat(e){var t=e.additional?` data-additional="${e.additional}"`:"";let a=`<span class="wpforms-smart-tags-widget-item"
						data-type="${e.type}"${t}>
						${e.text}
				    </span>`;return a=e?.heading?`<span class="heading">${e.text}</span>`:a},onSelect(e,t,a,r,n){0<r.find(".heading").length||m.smartTagInsert(r,t,n)}})},getDropdownContainer(e,t){let a=t?e.closest(".wp-editor-wrap"):e.closest(".wpforms-smart-tags-widget-container");return a=e.closest("td").length?e.parent().parent().parent():a},showSmartTagDropdown(e){o(".insert-smart-tag-dropdown").each(function(){o(this).addClass("closed")});var t,a=m.getDropdownListInstance(e);a&&(t=e.hasClass("active"),e.toggleClass("active",!t),t?a.close():(o(".wpforms-show-smart-tags").not(e).removeClass("active"),a.open()))},getSmartTagsList(e,t){return[...m.getSmartTagsListFieldsElements(e),...m.getSmartTagsListOtherElements(e,t)]},getSmartTagsListFieldsElements(e){var t=e.data("type");if(!["fields","all"].includes(t))return[];var a=m.getSmartTagsFields(e);if(!a)return[{value:0,heading:!0,text:wpforms_builder.fields_unavailable}];var r=[];r.push({value:0,text:wpforms_builder.fields_available,heading:!0});for(const n in a)r.push(...m.getSmartTagsListFieldsElement(a[n]));return r},getSmartTagsFields(e){var t=e.data("fields"),e=e.data("allow-repeated-fields"),t=t?t.split(","):void 0;return wpf.getFields(t,!0,e)},getSmartTagsListFieldsElement(a){const r=a.label?wpf.encodeHTMLEntities(wpf.sanitizeHTML(a.label)):wpforms_builder.field+" #"+a.id,n=[{value:a.id,text:r,type:"field"}];var e=a.additional||[];return 1<e.length&&e.forEach(e=>{var t=e.charAt(0).toUpperCase()+e.slice(1).replace(/(\D)(\d)/g,"$1 $2");n.push({value:a.id,text:r+" – "+t,type:"field",additional:e})}),n},getSmartTagsListOtherElements(e,t){var a=e.data("type"),r=[];if("other"===a||"all"===a){r.push({value:0,text:wpforms_builder.other,heading:!0});for(const n in wpforms_builder.smart_tags)t&&wpforms_builder.smart_tags_disabled_for_fields.includes(n)||"confirmations"===e.data("location")&&wpforms_builder.smart_tags_disabled_for_confirmations.includes(n)||r.push({value:n,type:"other",text:wpforms_builder.smart_tags[n]})}return r},smartTagInsert(e,t,a){var r=e.find("span"),e=e.parent().parent().parent().find(".wpforms-smart-tags-widget"),n=r.data("additional")?"|"+r.data("additional"):"",r=" "+("field"===r.data("type")?'{field_id="'+t+n+'"}':"{"+t+"}")+" ";a.$button.hasClass("mce-wpforms-smart-tags-mce-button")?m.insertSmartTagToTinyMCE(a,r):(m.restoreCaretPosition(e[0]),m.insertTagAtCaret(e[0],r)),a.$button.removeClass("active"),a.close()},insertSmartTagToTinyMCE(e,t){"undefined"!=typeof tinyMCE&&(e=e.$button.closest(".wp-editor-container").find("textarea").attr("id"),e=tinyMCE.get(e))&&(e.hasFocus()||e.focus(!0),e.insertContent(t))},saveCaretPosition(e){var t=s.getSelection();t.rangeCount&&(t=t.getRangeAt(0),e.contains(t.startContainer))&&r.set(e,t)},restoreCaretPosition(e){var t=r.get(e),a=e.ownerDocument.defaultView.getSelection();t?(a.removeAllRanges(),a.addRange(t)):((t=g.createRange()).selectNodeContents(e),t.collapse(!1),a.removeAllRanges(),a.addRange(t)),e.focus()},insertTagAtCaret(e,t){var a=g.defaultView.getSelection(),r=a.getRangeAt(0),t=g.createTextNode(t);r.deleteContents(),r.insertNode(t),r.setStartAfter(t),r.setEndAfter(t),a.removeAllRanges(),a.addRange(r),m.renderWidgetContent({target:e},!0),e.focus(),this.scrollToCursorPosition(e)},scrollToCursorPosition(i){setTimeout(()=>{var e,t,a,r,n=g.createElement("span"),s=(n.style.display="inline-block",n.style.width="0px",n.style.height="0px",g.getSelection());s.rangeCount&&((s=s.getRangeAt(0).cloneRange()).collapse(!0),s.insertNode(n),s=n.getBoundingClientRect(),e=i.getBoundingClientRect(),r=s.left>=e.left&&s.right<=e.right,t=s.top<e.top,a=s.bottom>e.bottom,!r&&0<s.left&&(r=s.left-e.left-e.width/2,i.scrollLeft+=r),t?i.scrollTop-=e.top-s.top+20:a&&(i.scrollTop+=s.bottom-e.bottom+20),n.parentNode)&&n.parentNode.removeChild(n)},50)},insertLineBreak(){var e,t,a,r=g.getSelection();r.rangeCount&&(e=r.getRangeAt(0),t=g.createElement("br"),e.deleteContents(),e.insertNode(t),m.removeZeroWidthSpaces(t.parentNode),a=g.createTextNode("​"),e.setStartAfter(t),e.insertNode(a),e.setStartAfter(a),e.setEndAfter(a),r.removeAllRanges(),r.addRange(e),this.scrollToCursorPosition(t.parentNode),a=t.closest(".wpforms-smart-tags-widget"))&&this.updateOriginalInput(a)},handleEmailTag(e,t,a,r,n){var s;return/^{[^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*@([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}}$/.test(e)?(s=r.slice(0,a.index),r=r.slice(a.index+e.length),a=g.createTextNode(s),e=g.createTextNode(t),s=g.createTextNode(r),(t=n.parentNode).insertBefore(a,n),t.insertBefore(e,n),t.insertBefore(s,n),t.removeChild(n),{handled:!0,text:r,node:s}):{handled:!1}},fieldAdd(e,t){t=o("#wpforms-field-option-"+t);m.initWidgets(t)},fieldDuplicated(e,t,a,r){r=o("#wpforms-field-option-"+r);m.reinitWidgets(r)},setupOriginalInputObserver(t){if(s.MutationObserver&&t&&t[0]){const e=i.$builder[0],a=t[0];if(e.contains(a)){const r=new MutationObserver(e=>{e.length&&m.reinitWidgets(t.parent())}),n=(r.observe(a,{attributes:!0,attributeFilter:["readonly","disabled"]}),new MutationObserver(()=>{e.contains(a)||(r.disconnect(),n.disconnect())}));n.observe(e,{childList:!0,subtree:!0,attributes:!1})}}},syncWidgetContent(e,t){t.text()!==e.val()&&m.reinitWidgets(t.parent().parent())},removeZeroWidthSpaces(e){for(var t,a=g.createTreeWalker(e,NodeFilter.SHOW_TEXT,null,!1);t=a.nextNode();)t.nodeValue.includes("​")&&(t.nodeValue=t.nodeValue.replaceAll("​",""))}};return m}(document,window,jQuery),WPForms.Admin.Builder.SmartTags.init();