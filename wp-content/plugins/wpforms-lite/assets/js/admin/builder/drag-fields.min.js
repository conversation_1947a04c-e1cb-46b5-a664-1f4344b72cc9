var WPForms=window.WPForms||{};WPForms.Admin=WPForms.Admin||{},WPForms.Admin.Builder=WPForms.Admin.Builder||{},WPForms.Admin.Builder.DragFields=WPForms.Admin.Builder.DragFields||function(g){let w={};const c={};let F;const b={init(){g(b.ready)},ready(){b.setup(),b.initSortableFields(),b.events()},setup(){w={$builder:g("#wpforms-builder"),$sortableFieldsWrap:g("#wpforms-panel-fields .wpforms-field-wrap"),$addFieldsButtons:g(".wpforms-add-fields-button").not(".not-draggable").not(".warning-modal").not(".education-modal")}},events(){w.$builder.on("wpformsFieldDragToggle",b.fieldDragToggleEvent)},disableDragAndDrop(){w.$addFieldsButtons.filter(".ui-draggable").draggable("disable"),w.$sortableFieldsWrap.sortable("disable"),w.$sortableFieldsWrap.find(".wpforms-layout-column.ui-sortable").sortable("disable")},enableDragAndDrop(){w.$addFieldsButtons.filter(".ui-draggable").draggable("enable"),w.$sortableFieldsWrap.sortable("enable"),w.$sortableFieldsWrap.find(".wpforms-layout-column.ui-sortable").sortable("enable")},fieldDragDisable(e,i=!0){if(e.hasClass("ui-draggable-disabled"))e.draggable("enable");else{let l;e.draggable({revert:!0,axis:"y",delay:100,opacity:1,cursor:"move",start(e,r){l=r.position.top},drag(e,r){if(15<Math.abs(r.position.top)-Math.abs(l))return i&&b.youCantReorderFieldPopup(),!1}})}},fieldDragEnable(e){e.hasClass("ui-draggable")||e.draggable("disable")},youCantReorderFieldPopup(){g.confirm({title:wpforms_builder.heads_up,content:wpforms_builder.field_cannot_be_reordered,icon:"fa fa-exclamation-circle",type:"red",buttons:{confirm:{text:wpforms_builder.ok,btnClass:"btn-confirm",keys:["enter"]}}})},fieldDragToggleEvent(e,r){r=g("#wpforms-field-"+r);r.hasClass("wpforms-field-not-draggable")||r.hasClass("wpforms-field-stick")?b.fieldDragDisable(r):b.fieldDragEnable(r)},initSortableFields(){b.initSortableContainer(w.$sortableFieldsWrap),w.$builder.find(".wpforms-layout-column").each(function(){b.initSortableContainer(g(this))}),b.fieldDragDisable(g(".wpforms-field-not-draggable, .wpforms-field-stick")),b.initDraggableFields()},initSortableContainer(o){const d=g("#wpforms-field-options"),a=g("#wpforms-panel-fields .wpforms-panel-content-wrap");let t,s,n,f,m,p,u=!1;o.sortable({items:"> .wpforms-field:not(.wpforms-field-stick):not(.no-fields-preview)",connectWith:".wpforms-field-wrap, .wpforms-layout-column",delay:100,opacity:1,cursor:"move",cancel:".wpforms-field-not-draggable",placeholder:"wpforms-field-drag-placeholder",appendTo:"#wpforms-panel-fields",zindex:1e4,tolerance:"pointer",distance:1,start(e,r){t=r.item.data("field-id"),s=r.item.data("field-type"),n=void 0===t,f=g("#wpforms-field-option-"+t),c.fieldReceived=!1,c.fieldRejected=!1,c.$sortableStart=o,c.startPosition=r.item.first().index(),w.$builder.trigger("wpformsFieldDragStart",[t])},beforeStop(e,r){!c.glitchChange||F.isFieldAllowedInColum(s,r.item.first().parent())||(c.fieldRejected=!0)},stop(e,r){var l,i=r.item.first();r.placeholder.removeClass("wpforms-field-drag-not-allowed"),i.removeClass("wpforms-field-drag-not-allowed"),c.fieldRejected?(l=n?o:i.parent(),b.revertMoveFieldToColumn(i),w.$builder.trigger("wpformsFieldMoveRejected",[i,r,l])):(p=i.prev(".wpforms-field, .wpforms-alert").data("field-id"),0<(m=g("#wpforms-field-option-"+p)).length?m.after(f):d.prepend(f),!n&&i.closest(".wpforms-layout-column").is(o)&&F.positionFieldInColumn(t,i.index()-1,o),l=i.closest(".wpforms-field-layout, .wpforms-field-repeater"),F.fieldOptionsUpdate(null,t),F.reorderLayoutFieldsOptions(l),n||i.removeClass("wpforms-field-dragging").removeClass("wpforms-field-drag-over"),i.attr("style",""),w.$builder.trigger("wpformsFieldMove",r),c.fieldReceived=!1)},over(e,r){var r=r.item.first(),e=g(e.target),l=e.find(".wpforms-field-drag-placeholder"),i=e.hasClass("wpforms-layout-column"),d={width:e.outerWidth(),height:r.outerHeight()};let o=i?" wpforms-field-drag-to-column":"";i&&(a=e.attr("class").match(/wpforms-layout-column-(\d+)/)[1],o=(o+=" wpforms-field-drag-to-column-"+a)+" wpforms-field-drag-to-"+e.parents(".wpforms-field").data("field-type")),t=r.data("field-id"),s=r.data("field-type")||c.fieldType,n=void 0===t,r.addClass("wpforms-field-dragging"+o),i&&F.isLayoutBasedField(s)||r.css({width:i?d.width-5:d.width,height:"auto"});var a=i?90:d.height;l.removeClass("wpforms-field-drag-not-allowed").css({height:n?a+18:d.height}),i&&!F.isFieldAllowedInColum(s,e)&&(l.addClass("wpforms-field-drag-not-allowed"),r.addClass("wpforms-field-drag-not-allowed")),w.$builder.trigger("wpformsFieldDragOver",[t,e]),n&&r.addClass("wpforms-field-drag-over").removeClass("wpforms-field-drag-out")},out(e,r){var l=r.item.first(),i=void 0===l.data("field-id");l.removeClass("wpforms-field-drag-not-allowed").removeClass("wpforms-field-drag-to-repeater").removeClass("wpforms-field-drag-to-layout").removeClass(function(e,r){return(r.match(/wpforms-field-drag-to-column(-\d+|)/g)||[]).join(" ")}),c.fieldReceived?l.attr("style",""):i?l.addClass("wpforms-field-drag-out").removeClass("wpforms-field-drag-over"):g(r.sender).closest(".wpforms-field-layout, .wpforms-field-repeater").removeClass("wpforms-field-child-hovered")},receive(e,r){var l=g(r.helper||r.item),i=(t=l.data("field-id"),s=l.data("field-type")||c.fieldType,void 0===t),d=o.hasClass("wpforms-layout-column");d&&!F.isFieldAllowedInColum(s,o)?c.fieldRejected=!0:(c.fieldReceived=!0,l.removeClass("wpforms-field-drag-over"),i?(i=o.data("ui-sortable").currentItem.index(),l.addClass("wpforms-field-drag-over wpforms-field-drag-pending").removeClass("wpforms-field-drag-out").append(WPFormsBuilder.settings.spinnerInline).css("width","100%"),w.$builder.find(".no-fields-preview").remove(),WPFormsBuilder.fieldAdd(c.fieldType,{position:d?i-1:i,placeholder:l,$sortable:o}),c.fieldType=void 0):F.receiveFieldToColumn(t,r.item.index()-1,l.parent()))},change(e,r){r=r.placeholder.parent(),e=g(e.target);c.glitchChange=!1,!o.is(r)&&o.hasClass("wpforms-field-wrap")&&r.hasClass("wpforms-layout-column")&&(c.glitchChange=!0),w.$builder.trigger("wpformsFieldDragChange",[t,e])},sort(r){if(!u){var r=r.clientY,l=a.offset(),i=a.height(),d=l.top+i;let e;if(r>l.top&&r<l.top+50)e="-=";else{if(!(d-50<r&&r<d))return;e="+="}u=!0,a.animate({scrollTop:e+i/3+"px"},800,function(){u=!1})}}})},initDraggableFields(){w.$addFieldsButtons.draggable({connectToSortable:".wpforms-field-wrap, .wpforms-layout-column",delay:200,cancel:!1,scroll:!1,opacity:1,appendTo:"#wpforms-panel-fields",zindex:1e4,helper(){var e=g(this),r=g('<div class="wpforms-field-drag-out wpforms-field-drag">');return c.fieldType=e.data("field-type"),r.html(e.html())},start(e,r){if(WPFormsUtils.triggerEvent(w.$builder,"wpformsFieldAddDragStart",[c.fieldType,r]).isDefaultPrevented())return!1},stop(e,r){if(WPFormsUtils.triggerEvent(w.$builder,"wpformsFieldAddDragStop",[c.fieldType,r]).isDefaultPrevented())return!1}})},revertMoveFieldToColumn(e){var r;void 0===e.data("field-id")?e.remove():(e=e.detach(),r=c.$sortableStart.find("> .wpforms-field").eq(c.startPosition),e.removeClass("wpforms-field-dragging").removeClass("wpforms-field-drag-over").attr("style",""),r.length?r.before(e):c.$sortableStart.append(e))}};return F={positionFieldInColumn(e,r,l){WPForms.Admin.Builder.FieldLayout&&WPForms.Admin.Builder.FieldLayout.positionFieldInColumn(e,r,l)},receiveFieldToColumn(e,r,l){WPForms.Admin.Builder.FieldLayout&&WPForms.Admin.Builder.FieldLayout.receiveFieldToColumn(e,r,l)},fieldOptionsUpdate(e,r){WPForms.Admin.Builder.FieldLayout&&WPForms.Admin.Builder.FieldLayout.fieldOptionsUpdate(e,r)},reorderLayoutFieldsOptions(e){WPForms.Admin.Builder.FieldLayout&&WPForms.Admin.Builder.FieldLayout.reorderLayoutFieldsOptions(e)},isFieldAllowedInColum(e,r){var l;return!WPForms.Admin.Builder.FieldLayout||(l=WPForms.Admin.Builder.FieldLayout.isFieldAllowedInColum(e,r),wp.hooks.applyFilters("wpforms.LayoutField.isFieldAllowedDragInColumn",l,e,r))},isLayoutBasedField(e){return!!WPForms.Admin.Builder.FieldLayout&&WPForms.Admin.Builder.FieldLayout.isLayoutBasedField(e)}},b}((document,window,jQuery)),WPForms.Admin.Builder.DragFields.init();