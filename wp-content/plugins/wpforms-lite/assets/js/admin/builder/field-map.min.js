var WPForms=window.WPForms||{};WPForms.Admin=WPForms.Admin||{},WPForms.Admin.Builder=WPForms.Admin.Builder||{},WPForms.Admin.Builder.FieldMap=WPForms.Admin.Builder.FieldMap||function(e,a){const o={},t={init(){a(t.ready)},ready(){t.setup(),t.events()},setup(){o.$builder=a("#wpforms-builder")},events(){o.$builder.on("input",".wpforms-field-map-table .key-source",function(){var e=a(this).val(),t=a(this).parent().parent().find(".key-destination"),i=t.data("name");e&&t.attr("name",i.replace("{source}",e.replace(/[^0-9a-zA-Z_-]/gi,"")))}),o.$builder.on("click",".wpforms-field-map-table .remove",function(e){e.preventDefault(),t.fieldMapTableDeleteRow(e,a(this))}),o.$builder.on("click",".wpforms-field-map-table .add",function(e){e.preventDefault(),t.fieldMapTableAddRow(e,a(this))}),a(e).on("wpformsFieldUpdate",t.fieldMapSelect)},fieldMapTableDeleteRow(e,t){var t=a(t),i=t.closest("tr"),t=t.closest("table"),l=i.closest(".wpforms-builder-settings-block");"1"<t.find("tr").length&&(i.remove(),o.$builder.trigger("wpformsFieldMapTableDeletedRow",[l]))},fieldMapTableAddRow(e,t){var t=a(t).closest("tr"),i=t.closest(".wpforms-builder-settings-block"),t=t.clone().insertAfter(t);t.find("input").val(""),t.find("select :selected").prop("selected",!1),t.find(".key-destination").attr("name",""),o.$builder.trigger("wpformsFieldMapTableAddedRow",[i,t])},fieldMapSelect(e,r){WPFormsUtils.triggerEvent(o.$builder,"wpformsBeforeFieldMapSelectUpdate").isDefaultPrevented()||a(".wpforms-field-map-select").each(function(){var t=a(this);let i=t.data("field-map-allowed"),e=t.data("field-map-placeholder");if(void 0!==e&&e||(e=wpforms_builder.select_field),void 0!==i&&i){i=i.split(" ");var l=t.find("option:selected").val();if(t.empty().append(a("<option>",{value:"",text:e})),r&&!a.isEmptyObject(r))for(const d in r){let e="";r[d]&&(e=void 0!==r[d].label&&""!==r[d].label.toString().trim()?wpf.sanitizeHTML(r[d].label.toString().trim()):wpforms_builder.field+" #"+d,0<=a.inArray(r[d].type,i)||0<=a.inArray("all-fields",i))&&t.append(a("<option>",{value:r[d].id,text:e}))}l&&t.find('option[value="'+l+'"]').prop("selected",!0);l=t.data("custom-value-support");"boolean"==typeof l&&l&&t.append(a("<option>",{value:"custom_value",text:wpforms_builder.add_custom_value_label,class:"wpforms-field-map-option-custom-value"})),o.$builder.trigger("wpformsFieldSelectMapped",[t])}})}};return t}(document,(window,jQuery)),WPForms.Admin.Builder.FieldMap.init();