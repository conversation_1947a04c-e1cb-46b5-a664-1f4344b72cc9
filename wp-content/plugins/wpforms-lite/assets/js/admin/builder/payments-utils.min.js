const WPFormsBuilderPaymentsUtils=window.WPFormsBuilderPaymentsUtils||function(o){return{toggleContent(){var e=o(this),n=e.closest(".wpforms-payment-settings"),n=(n.find(".wpforms-panel-content-section-payment-toggle-one-time .wpforms-toggle-control > input").is(":checked")&&n.find(".wpforms-panel-content-section-payment-toggle-recurring .wpforms-toggle-control > input").is(":checked")&&(e.prop("checked",!1),o.alert({title:wpforms_builder.heads_up,content:e.attr("name").includes("enable_recurring")?wpforms_builder_payments_utils.payments_disabled_recurring:wpforms_builder_payments_utils.payments_disabled_one_time,icon:"fa fa-exclamation-circle",type:"orange",buttons:{confirm:{text:wpforms_builder.ok,btnClass:"btn-confirm",keys:["enter"]}}})),e.closest(".wpforms-panel-content-section-payment")),e=e.prop("checked")&&!o("#wpforms-panel-field-settings-disable_entries").prop("checked");n.find(".wpforms-panel-content-section-payment-toggled-body").toggle(e),n.toggleClass("wpforms-panel-content-section-payment-open",e)},checkPlanName(){var e,n=o(this),t=n.closest(".wpforms-panel-content-section-payment-plan").find(".wpforms-panel-content-section-payment-plan-head-title");n.val()?t.html(n.val()):(e=wpforms_builder_payments_utils.payments_plan_placeholder,t.html(e),n.val(e))},togglePlan(){var e=o(this).closest(".wpforms-panel-content-section-payment-plan"),n=e.find(".wpforms-panel-content-section-payment-plan-head-buttons-toggle");n.toggleClass("fa-chevron-circle-up fa-chevron-circle-down"),e.find(".wpforms-panel-content-section-payment-plan-body").toggle(n.hasClass("fa-chevron-circle-down"))},deletePlan(){o(this).closest(".wpforms-panel-content-section-payment").find(".wpforms-panel-content-section-payment-button-add-plan").trigger("click")},renamePlan(){var e=o(this),n=e.closest(".wpforms-panel-content-section-payment-plan").find(".wpforms-panel-content-section-payment-plan-head-title");e.val()?n.html(e.val()):n.html("")}}}((document,window,jQuery));