var WPForms=window.WPForms||{};WPForms.Admin=WPForms.Admin||{},WPForms.Admin.Builder=WPForms.Admin.Builder||{},WPForms.Admin.Builder.DropdownList=WPForms.Admin.Builder.DropdownList||function(t){function o(e){const l=this,s={class:"",title:"",list:[],container:null,scrollableContainer:null,search:{enabled:!1,searchBy:[],placeholder:wpforms_builder.search,noResultsText:wpforms_builder.no_results_found},button:null,buttonDistance:10,noLeftOffset:!1,onSelect:null,itemFormat(e){return e.text}};l.options=t.extend(s,e),l.$el=null,l.$builder=t("#wpforms-builder"),l.searchItems=null,l.close=function(){l.$el.addClass("closed"),l.options.search.enabled&&l.clearSearch()},l.open=function(){l.$el.removeClass("closed open-down"),l.setPosition(),l.$builder.on("click.DropdownList",function(e){var e=t(e.target);e.closest(l.$el).length||e.is(".button-insert-field, .wpforms-smart-tags-enabled, .wpforms-show-smart-tags, .mce-ico")||(l.$builder.off("click.DropdownList"),(e=t(l.options.button)).hasClass("active")&&e.trigger("click"))})},l.generateHtml=function(){var e=l.options.list;if(!e||0===e.length)return"";const o=("function"==typeof l.options.itemFormat?l.options:s).itemFormat;var e=e.map(e=>`<li data-value='${e.value}'>${o(e)}</li>`),t=l.options.search.enabled?`<div class="wpforms-builder-dropdown-list-search-container">
					<input type="search" class="wpforms-builder-dropdown-list-search-input" placeholder="${l.options.search.placeholder}">
					<i class="fa fa-times-circle wpforms-builder-dropdown-list-search-close" aria-hidden="true"></i>
				</div>`:"",n=l.options.search.enabled?"list":"";return`<div class="wpforms-builder-dropdown-list closed ${l.options.class}">
				<div class="title">${l.options.title}</div>
				${t}
				<ul class="${n}">${e.join("")}</ul>
				<div class="wpforms-no-results">${l.options.search.noResultsText}</div>
			</div>`},l.attach=function(){var e=l.generateHtml();l.$el&&l.$el.length&&l.$el.remove(),l.$el=t(e),l.$button=t(l.options.button),l.$container=l.options.container?t(l.options.container):l.$button.parent(),l.$scrollableContainer=l.options.scrollableContainer?t(l.options.scrollableContainer):null,l.options.search.enabled&&(l.searchItems=new List(l.$el[0],{valueNames:l.options.search.searchBy})),l.$container.append(l.$el),l.setPosition()},l.setPosition=function(){var e=l.$button.offset(),o=l.$container.offset(),t=l.$container.position(),n=l.$el.height(),s=l.$scrollableContainer?l.$scrollableContainer.scrollTop():0;let i=e.top-o.top-n-l.options.buttonDistance;s+t.top-n<0&&(i=e.top-o.top+l.$button.height()+l.options.buttonDistance-11,l.$el.addClass("open-down")),l.$el.css("top",i),l.options.noLeftOffset||0===l.$container.closest(".wpforms-field-option").length&&l.$el.css("left",e.left-o.left)},l.events=function(){l.$el.find("li").off().on("click",function(e){var o;"function"==typeof l.options.onSelect&&(o=t(this),l.options.search.enabled&&l.clearSearch(),l.options.onSelect(e,o.data("value"),o.text(),o,l))}),l.options.search.enabled&&(l.$el.find('input[type="search"]').on("keyup search",l.search),l.$el.find(".wpforms-builder-dropdown-list-search-close").on("click",l.clearSearch))},l.init=function(e=null){l.options.list=e||l.options.list,l.attach(),l.events(),l.$button.data("dropdown-list",l)},l.destroy=function(){l.$button.data("dropdown-list",null),l.$el.remove()},l.search=function(e){var e=e.target.value.toLowerCase(),o=l.$el.find(".wpforms-no-results");""!==e&&l.$el.find(".wpforms-builder-dropdown-list-search-close").addClass("active"),l.searchItems.search(e),o.toggle(0===l.searchItems.visibleItems.length)},l.clearSearch=function(){l.$el.find('input[type="search"]').val(""),l.$el.find(".wpforms-no-results").hide(),l.$el.find(".wpforms-builder-dropdown-list-search-close").removeClass("active"),l.searchItems.search()},l.init()}return{init(e){return new o(e)}}}((document,window,jQuery));