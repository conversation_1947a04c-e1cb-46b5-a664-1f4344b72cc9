"use strict";var WPForms=window.WPForms||{};WPForms.Admin=WPForms.Admin||{},WPForms.Admin.Builder=WPForms.Admin.Builder||{},WPForms.Admin.Builder.Help=WPForms.Admin.Builder.Help||function(l){var n,p={init:function(){l(p.ready)},ready:function(){p.setup(),p.initCategories(),p.events()},setup:function(){n={$builder:l("#wpforms-builder"),$builderForm:l("#wpforms-builder-form"),$helpBtn:l("#wpforms-help"),$help:l("#wpforms-builder-help"),$closeBtn:l("#wpforms-builder-help-close"),$search:l("#wpforms-builder-help-search"),$result:l("#wpforms-builder-help-result"),$noResult:l("#wpforms-builder-help-no-result"),$categories:l("#wpforms-builder-help-categories"),$footer:l("#wpforms-builder-help-footer")}},events:function(){n.$helpBtn.on("click",a.openHelp),n.$closeBtn.on("click",a.closeHelp),n.$categories.on("click",".wpforms-builder-help-category header",a.toggleCategory),n.$categories.on("click",".wpforms-builder-help-category button.viewall",a.viewAllCategoryDocs),n.$search.on("keyup","input",_.debounce(a.inputSearch,250)),n.$search.on("click","#wpforms-builder-help-search-clear",a.clearSearch)},initCategories:function(){var e,r;wpf.empty(wpforms_builder_help.docs)?n.$categories.html(wp.template("wpforms-builder-help-categories-error")):(e=wp.template("wpforms-builder-help-categories"),r={categories:wpforms_builder_help.categories,docs:p.getDocsByCategories()},n.$categories.html(e(r)))},getDocsByCategories:function(){var e=wpforms_builder_help.categories,t=wpforms_builder_help.docs||[],i={};return _.each(e,function(e,r){var o=[];_.each(t,function(e){e.categories&&-1<e.categories.indexOf(r)&&o.push(e)}),i[r]=o}),i},getRecommendedDocs:function(e){if(wpf.empty(e))return[];e=e.toLowerCase();var r=wpforms_builder_help.docs,o=[];return wpf.empty(wpforms_builder_help.context.docs[e])?[]:(_.each(wpforms_builder_help.context.docs[e],function(e){wpf.empty(r[e])||o.push(r[e])}),o)},getFilteredDocs:function(r){var e,o;return wpf.empty(r)?[]:(e=wpforms_builder_help.docs,o=[],r=r.toLowerCase(),_.each(e,function(e){e.title&&-1<e.title.toLowerCase().indexOf(r)&&o.push(e)}),o)},getBuilderContext:function(){if(wpf.empty(n.$builderForm.data("id")))return"new_form";var e=n.$builder.find("#wpforms-panels-toggle button.active").data("panel"),r=n.$builder.find("#wpforms-panel-"+e),o="",t="";switch(e){case"fields":o=r.find(".wpforms-panel-sidebar .wpforms-tab a.active").parent().attr("id");break;case"setup":o="";break;default:o=r.find(".wpforms-panel-sidebar a.active").data("section")}return[e,o=wpf.empty(o)?"":o.replace(/-/g,"_"),t="field_options"===o?r.find("#wpforms-field-options .wpforms-field-option:visible .wpforms-field-option-hidden-type").val():t].filter(function(e){return!wpf.empty(e)&&"default"!==e}).join("/")},getBuilderContextTerm:function(){return wpforms_builder_help.context.terms[p.getBuilderContext()]||""}},t={config:{speed:300},fadeIn:function(e){e.length&&(e.css({display:"",transition:`opacity ${t.config.speed}ms ease-in 0s`}),setTimeout(function(){e.css("opacity","1")},0))},fadeOut:function(e){e.length&&(e.css({opacity:"0",transition:`opacity ${t.config.speed}ms ease-in 0s`}),setTimeout(function(){e.css("display","none")},t.config.speed))},collapseAllCategories:function(){n.$categories.find(".wpforms-builder-help-category").removeClass("opened"),n.$categories.find(".wpforms-builder-help-docs").slideUp()}},a={openHelp:function(e){e.preventDefault(),l("body").addClass("wpforms-builder-help-open");var e=n.$categories.find(".wpforms-builder-help-category").first(),r=p.getBuilderContextTerm();""!==r||e.hasClass("opened")?t.collapseAllCategories():e.find("header").first().trigger("click"),n.$search.find("input").val(r).trigger("keyup"),t.fadeIn(n.$help),setTimeout(function(){t.fadeIn(n.$result),t.fadeIn(n.$categories),t.fadeIn(n.$footer)},t.config.speed)},closeHelp:function(e){e.preventDefault(),l("body").removeClass("wpforms-builder-help-open"),t.fadeOut(n.$result),t.fadeOut(n.$categories),t.fadeOut(n.$footer),t.fadeOut(n.$help)},toggleCategory:function(e){var r=l(this).parent(),o=r.find(".wpforms-builder-help-docs");o.is(":visible")?r.removeClass("opened"):r.addClass("opened"),o.stop().slideToggle(t.config.speed)},viewAllCategoryDocs:function(e){var r=l(this);r.prev("div").stop().slideToggle(t.config.speed,function(){r.closest(".wpforms-builder-help-category").addClass("viewall")}),t.fadeOut(r),r.slideUp()},inputSearch:function(e){var r=l(this).val(),o=wp.template("wpforms-builder-help-docs"),t=p.getRecommendedDocs(r),i=a.removeDuplicates(t,p.getFilteredDocs(r)),s="";n.$search.toggleClass("wpforms-empty",!r),wpf.empty(t)||(s+=o({docs:t})),wpf.empty(i)||(s+=o({docs:i})),n.$noResult.toggle(""===s&&""!==r),n.$result.html(s),n.$help[0].scrollTop=0},removeDuplicates:function(e,r){if(wpf.empty(e)||wpf.empty(r))return r;for(var o=[],t=0;e.length,t++;)for(var i=0;r.length,i++;)r[i].url!==e[t].url&&o.push(r[i]);return o},clearSearch:function(e){n.$search.find("input").val("").trigger("keyup")}};return p}((document,window,jQuery)),WPForms.Admin.Builder.Help.init();