var WPFormsBuilder=window.WPFormsBuilder||function(r,a,v){let m,y;const C={},i={};let o=!0,l=!1,t=null;const k={settings:{spinner:'<i class="wpforms-loading-spinner"></i>',spinnerInline:'<i class="wpforms-loading-spinner wpforms-loading-inline"></i>',tinymceDefaults:{tinymce:{toolbar1:"bold,italic,underline,blockquote,strikethrough,bullist,numlist,alignleft,aligncenter,alignright,undo,redo,link"},quicktags:!0},pagebreakTop:!1,pagebreakBottom:!1,upload_img_modal:!1,choicesLimit:20,choicesLimitLong:250},init(){const e=this;wpforms_panel_switch=!0,m=this.settings,v(k.ready),v(a).on("load",function(){"function"==typeof v.ready.then?v.ready.then(k.load):k.load()}),v(a).on("beforeunload",function(){if(!e.formIsSaved()&&o)return wpforms_builder.are_you_sure_to_close})},load(){if(wpf.getQueryString("newform")&&k.formSave(!1),"revisions"===v("#wpforms-panels-toggle .active").data("panel")&&k.updateRevisionPreview(),WPFormsUtils.triggerEvent(y,"wpformsBuilderReady").isDefaultPrevented())return!1;k.hideLoadingOverlay(),"1"==wpforms_builder.template_modal_display&&"fields"===wpf.getQueryString("view")&&v.alert({title:wpforms_builder.template_modal_title,content:wpforms_builder.template_modal_msg,icon:"fa fa-info-circle",type:"blue",buttons:{confirm:{text:wpforms_builder.close,btnClass:"btn-confirm",keys:["enter"]}}})},initElementsCache(){y=v("#wpforms-builder"),i.isWindows=/Win/.test(navigator.userAgent),i.isLinux=/Linux/.test(navigator.userAgent),i.isMac=/Mac/.test(navigator.userAgent),C.$helpButton=v("#wpforms-help"),C.$previewButton=v("#wpforms-preview-btn"),C.$embedButton=v("#wpforms-embed"),C.$saveButton=v("#wpforms-save"),C.$exitButton=v("#wpforms-exit"),C.$noFieldsOptions=v("#wpforms-panel-fields .wpforms-no-fields-holder .no-fields"),C.$noFieldsPreview=v("#wpforms-panel-fields .wpforms-no-fields-holder .no-fields-preview"),C.$formPreview=v("#wpforms-panel-fields .wpforms-preview-wrap"),C.$revisionPreview=v("#wpforms-panel-revisions .wpforms-panel-content"),C.defaultEmailSelector=".wpforms-field-option-email .wpforms-field-option-row-default_value input",C.$defaultEmail=v(C.defaultEmailSelector),C.$focusOutTarget=null,C.$nextFieldId=v("#wpforms-field-id"),C.$addFieldsTab=v("#add-fields a"),C.$fieldOptions=v("#wpforms-field-options"),C.$fieldsPreviewWrap=v("#wpforms-panel-fields .wpforms-panel-content-wrap"),C.$sortableFieldsWrap=v("#wpforms-panel-fields .wpforms-field-wrap"),C.$addFieldsButtons=v(".wpforms-add-fields-button").not(".not-draggable").not(".warning-modal").not(".education-modal"),C.$fieldsSidebar=v("#wpforms-panel-fields .wpforms-add-fields"),C.$searchInput=v("#wpforms-search-fields-input"),C.$sidebarToggle=v(".wpforms-panels .wpforms-panel-sidebar-content .wpforms-panel-sidebar-toggle")},ready(){k.isVisitedViaBackButton()?location.reload():(k.initElementsCache(),v.ajaxSetup({data:{_wp_http_referer:wpf.updateQueryString("_wp_http_referer",null)}}),k.isBuilderInPopup()&&(C.$embedButton.remove(),C.$previewButton.addClass("wpforms-alone")),k.loadMsWinCSS(),k.bindUIActions(),m.formID=v("#wpforms-builder-form").data("id"),m.pagebreakTop=v(".wpforms-pagebreak-top").length,m.pagebreakBottom=v(".wpforms-pagebreak-bottom").length,y.on("keypress","#wpforms-builder-form :input:not(textarea)",function(e){13===e.keyCode&&e.preventDefault()}),k.determineActiveSections(),k.loadEntryPreviewFields(),k.fieldChoiceSortable("select"),k.fieldChoiceSortable("radio"),k.fieldChoiceSortable("checkbox"),k.fieldChoiceSortable("payment-multiple"),k.fieldChoiceSortable("payment-checkbox"),k.fieldChoiceSortable("payment-select"),v(".wpforms-add-fields-group").each(function(e,i){k.fieldGroupToggle(v(this),"load")}),k.registerTemplates(),k.trimFormTitle(),wpf.initTooltips(),k.loadColorPickers(),k.captchaToggle(),k.confirmationsSetup(),k.notificationToggle(),k.notificationsByStatusAlerts(),k.builderHotkeys(),jconfirm.defaults={closeIcon:!1,backgroundDismiss:!1,escapeKey:!0,animationBounce:1,useBootstrap:!1,theme:"modern",boxWidth:"400px",animateFromElement:!1,content:wpforms_builder.something_went_wrong},k.dropdownField.init(),k.iconChoices.init(),k.disabledFields.init(),k.checkEmptyDynamicChoices(),k.initSomeFieldOptions(),k.dismissNotice(),wpf.initializeChoicesEventHandlers())},checkEmptyDynamicChoices(){var e=wpf.orders.choices||{};Object.keys(e).length&&wpf.orders.fields.forEach(function(i){if(k.dropdownField.helpers.isDynamicChoices(i)){var o=v("#wpforms-field-"+i),t=k.dropdownField.helpers.getDynamicChoicesOptionType(i),r=k.dropdownField.helpers.getDynamicChoicesOptionSource(i),s=k.dropdownField.helpers.isDynamicChoicesOptionModern(i);let e=s?o.find(".has-no-choices").length:0===o.find(".primary-input option:not(.placeholder), .primary-input li").length;s&&!e&&(o=v("#wpforms-field-option-"+i+"-placeholder").val(),s=k.dropdownField.helpers.getInitialChoices(i),e=1===s.length&&s[0].label===o&&!0===s[0].placeholder),e&&k.emptyChoicesNotice(i,r,t)}})},loadMsWinCSS(){i.isMac||v("<link>").appendTo("head").attr({type:"text/css",rel:"stylesheet",href:wpforms_builder.scrollbars_css_url})},isVisitedViaBackButton(){if(!performance)return!1;let i=!1;return performance.getEntriesByType("navigation").forEach(function(e){"back_forward"===e.type&&(i=!0)}),i},hideLoadingOverlay(){const e=v("#wpforms-builder-overlay");e.addClass("fade-out"),setTimeout(function(){e.hide()},250)},showLoadingOverlay(){var e=v("#wpforms-builder-overlay");e.removeClass("fade-out"),e.show()},initSomeFieldOptions(){k.toggleAllOptionGroups(y),y.find(".wpforms-field-option-row-date .type select").trigger("change")},dropdownField:{config:{modernClass:"choicesjs-select",args:{searchEnabled:!1,searchChoices:!1,renderChoiceLimit:1,shouldSort:!1,callbackOnInit(){var e=v(this.containerOuter.element),i=e.closest(".wpforms-field").find("select");e.hasClass("is-disabled")&&e.removeClass("is-disabled"),i.is("[readonly]")&&(this.disable(),i.prop("disabled",!1)),this.passedElement.element.multiple&&this.getValue(!0).length&&v(this.input.element).addClass("choices__input--hidden"),e.find(".choices__item--selectable").each(function(){var e=v(this),i=wpf.decodeAllowedHTMLEntities(e.text());e.text(i)})}}},init(){y.find("."+k.dropdownField.config.modernClass).each(function(){k.dropdownField.events.choicesInit(v(this))}),y.on("change",".wpforms-field-option-select .wpforms-field-option-row-multiple input",k.dropdownField.events.multiple),y.on("change",".wpforms-field-option-select .wpforms-field-option-row-style select, .wpforms-field-option-payment-select .wpforms-field-option-row-style select",k.dropdownField.events.applyStyle),y.on("click",".choices",function(e){var i=v(this),o=i.find("select").data("choicesjs");o&&i.hasClass("is-open")&&e.target.classList.contains("choices__inner")&&o.hideDropdown()})},events:{choicesInit(e){var i=1===e.data("choicesjs-use-ajax");let o;o="select_pages"===e.data("choicesjs-callback-fn")?WPForms.Admin.Builder.WPFormsChoicesJS.setup(e[0],k.dropdownField.config.args,{action:"wpforms_ajax_search_pages_for_dropdown",nonce:i?wpforms_builder.nonce:null}):new Choices(e[0],k.dropdownField.config.args),k.dropdownField.helpers.setInstance(e,o),k.dropdownField.helpers.addPlaceholderChoice(e,o),e.closest(".choices").toggleClass("wpforms-hidden",!o.config.choices.length)},multiple(e){var i=v(this).closest(".wpforms-field-option-row-multiple").data().fieldId,o=k.dropdownField.helpers.getPrimarySelector(i),t=v("#wpforms-field-option-row-"+i+"-choices input.default"),r=o.find(".placeholder"),s=k.dropdownField.helpers.isDynamicChoices(i),e=e.target.checked,n=e?"checkbox":"radio",n=(o.prop("multiple",e),t.prop("type",n),s&&o.find("option:selected").prop("selected",!1),t.filter(":checked"));!e&&n.length&&(t.prop("checked",!1),v(n.get(0)).prop("checked",!0)),r.length&&r.prop("selected",!e),k.dropdownField.helpers.update(i,s)},applyStyle(){var e=v(this),i=e.closest(".wpforms-field-option-row-style").data().fieldId;"modern"===e.val()?k.dropdownField.helpers.convertClassicToModern(i):k.dropdownField.helpers.convertModernToClassic(i)}},helpers:{convertModernToClassic:e=>{var i=k.dropdownField.helpers.getPrimarySelector(e),o=k.dropdownField.helpers.isDynamicChoices(e),i=k.dropdownField.helpers.getInstance(i),t=v("#wpforms-field-option-row-"+e+"-choices").find(".choices-list").find("li").length;i&&"function"==typeof i.destroy&&(i.destroy(),k.dropdownField.helpers.updatePlaceholderChoice(i,e)),o||k.fieldChoiceUpdate("select",e,t)},getInitialChoices(e){e=k.dropdownField.helpers.getPrimarySelector(e);return k.dropdownField.helpers.getInstance(e).config.choices},convertClassicToModern(e){var i=k.dropdownField.helpers.getPrimarySelector(e);k.dropdownField.helpers.isDynamicChoices(e)||k.fieldChoiceUpdate("select",e),k.dropdownField.events.choicesInit(i)},update(e,i){var o=k.dropdownField.helpers.getPrimarySelector(e);k.dropdownField.helpers.isModernSelect(o)?(k.dropdownField.helpers.convertModernToClassic(e),i||k.dropdownField.events.choicesInit(o)):i||k.fieldChoiceUpdate("select",e)},addPlaceholderChoice(e,i){e=e.closest(".wpforms-field");if(e.length<=0)return!1;var o,t,r,e=e.data().fieldId;let s=k.dropdownField.helpers.hasDefaults(e);return k.dropdownField.helpers.isDynamicChoices(e)&&(s=!1),!1===k.dropdownField.helpers.searchPlaceholderChoice(i)&&!!i.config.choices.length&&(e=v("#wpforms-field-option-"+e+"-placeholder").val(),o=wpf.decodeAllowedHTMLEntities(e),r=!((t=v(i.passedElement.element).prop("multiple"))||s),i.setChoices([{value:"",label:o,selected:r,placeholder:!0}],"value","label",!1),t&&v(i.input.element).prop("placeholder",e),!0)},searchPlaceholderChoice(e){let t=!1;return e.config.choices.forEach(function(e,i,o){if(void 0!==e.placeholder&&!0===e.placeholder)return!(t={key:i,item:e})}),t},updatePlaceholderChoice(e,i){var o=v(e.passedElement.element),i=wpf.sanitizeHTML(v("#wpforms-field-option-"+i+"-placeholder").val()),e=k.dropdownField.helpers.searchPlaceholderChoice(e);let t={};"object"==typeof e&&(t=v(o.find("option").get(e.key))),""!==i?!v.isEmptyObject(t)&&t.length?t.addClass("placeholder").text(i):o.prepend('<option value="" class="placeholder">'+i+"</option>"):t.length&&t.remove()},isModernSelect(e){e=k.dropdownField.helpers.getInstance(e);return"object"==typeof e&&!v.isEmptyObject(e)&&e.initialised},setInstance(e,i){e.data("choicesjs",i)},getInstance(e){return e.data("choicesjs")},getDynamicChoicesOption(e){e=v("#wpforms-field-option-"+e+"-dynamic_choices");return!!e.length&&e},isDynamicChoices(e){e=k.dropdownField.helpers.getDynamicChoicesOption(e);return!!e.length&&""!==e.val()},isDynamicChoicesOptionModern(e){e=v("#wpforms-field-option-"+e+"-style");return!!e.length&&"modern"===e.val()},getDynamicChoicesOptionType(e){e=k.dropdownField.helpers.getDynamicChoicesOption(e);return!!e.length&&e.val()},getDynamicChoicesOptionSource(e){var i=k.dropdownField.helpers.getDynamicChoicesOptionType(e),e=v("#wpforms-field-option-"+e+"-dynamic_"+i);return!!e.length&&e.find("option:selected").text()},hasDefaults(e){return!!v("#wpforms-field-option-row-"+e+"-choices .choices-list").find("input.default:checked").length},getPrimarySelector(e){return v("#wpforms-field-"+e+" .primary-input")}}},numberSliderEvents(e){e.on("focusout",".wpforms-field-option-row-min_max .wpforms-input-row .wpforms-number-slider-min",k.fieldNumberSliderUpdateMin),e.on("focusout",".wpforms-field-option-row-min_max .wpforms-input-row .wpforms-number-slider-max",k.fieldNumberSliderUpdateMax),e.on("input",".wpforms-number-slider-default-value",_.debounce(k.changeNumberSliderDefaultValue,500)),e.on("focusout",".wpforms-number-slider-default-value",k.changeNumberSliderEmptyDefaultValue),e.find(".wpforms-number-slider-default-value").trigger("input"),e.on("input",".wpforms-number-slider-step",_.debounce(k.changeNumberSliderStep,500)),e.on("focusout",".wpforms-number-slider-step",k.checkNumberSliderStep),e.on("input",".wpforms-number-slider-value-display",_.debounce(k.changeNumberSliderValueDisplay,500)),e.on("input",".wpforms-number-slider-min",_.debounce(k.changeNumberSliderMin,500)),e.on("input",".wpforms-number-slider-max",_.debounce(k.changeNumberSliderMax,500))},changeNumberSliderMin(e){var i=parseFloat(e.target.value);isNaN(i)||(i=v(e.target).parents(".wpforms-field-option-row").data("fieldId"),k.updateNumberSliderDefaultValueAttr(i,e.target.value,"min"))},changeNumberSliderMax(e){var i=parseFloat(e.target.value);isNaN(i)||(i=v(e.target).parents(".wpforms-field-option-row").data("fieldId"),k.updateNumberSliderDefaultValueAttr(i,e.target.value,"max").updateNumberSliderStepValueMaxAttr(i,e.target.value))},changeNumberSliderValueDisplay(e){var i=e.target.value,e=v(e.target).parents(".wpforms-field-option-row").data("fieldId"),o=r.getElementById("wpforms-field-option-"+e+"-default_value");o&&k.updateNumberSliderHintStr(e,i).updateNumberSliderHint(e,o.value)},changeNumberSliderStep(e){var i,o,t,r=v(this),s=parseFloat(r.val());isNaN(s)||s<=0||(o=v(r).closest(".wpforms-field-option"),(t=((i=parseFloat(o.find(".wpforms-number-slider-max").val()))-(o=parseFloat(o.find(".wpforms-number-slider-min").val()))).toFixed(2))<s?(e.target.value=t,r.trigger("input")):(t=v(e.target).parents(".wpforms-field-option-row").data("fieldId"),r=v("#wpforms-field-option-"+t+"-default_value").val(),k.checkMultiplicitySliderDefaultValue(t,r,s,o,i).updateNumberSliderAttr(t,s,"step").updateNumberSliderDefaultValueAttr(t,s,"step")))},checkMultiplicitySliderDefaultValue(e,i,o,t=0,r=0){e=v(`#wpforms-field-option-row-${e}-default_value`);return((i=parseFloat(i))-t)%o==0?k.removeNotice(e):({closestSmallerMultiple:o,closestLargerMultiple:t}=this.calculateClosestMultiples(i,o,t,r),i=(r=e=>e%1==0?e.toString():e.toFixed(2))(i),(o=r(o))===(r=r(t))||o===i||r===i?k.removeNotice(e):(t=wpforms_builder.number_slider_error_valid_default_value.replace("{from}",o).replace("{to}",r),k.printNotice(t,e))),this},calculateClosestMultiples(e,i,o,t){let r=o+Math.floor((e-o)/i)*i,s=o+Math.ceil((e-o)/i)*i;return e===(s=e===r&&e!==o?r+i:s)&&e!==t&&(r=s-i),e===o&&(s=o+i),e===t&&(r=t-i),r=Math.max(r,o),s=Math.min(s,t),{closestSmallerMultiple:r,closestLargerMultiple:s}},printNotice(e,i,o=!1){i.length&&(this.removeNotice(i),i.append(`<div class="wpforms-alert-warning wpforms-alert ${o?"wpforms-alert-warning-wide":""}"><p>${e}</p></div>`))},removeNotice(e){e.length&&e.find(".wpforms-alert").length&&e.find(".wpforms-alert").remove()},checkNumberSliderStep(e){e=parseFloat(e.target.value);if(isNaN(e)||!(0<e)){const i=v(this);v.confirm({title:wpforms_builder.heads_up,content:wpforms_builder.error_number_slider_increment,icon:"fa fa-exclamation-circle",type:"orange",buttons:{confirm:{text:wpforms_builder.ok,btnClass:"btn-confirm",keys:["enter"],action(){i.val("").trigger("focus")}}}})}},changeNumberSliderEmptyDefaultValue(e){var i,o,t,r=parseFloat(e.target.value);isNaN(r)&&(r=parseFloat(e.target.min),e.target.value=r,i=parseFloat(e.target.step),o=parseFloat(e.target.min),t=parseFloat(e.target.max),e=v(e.target).parents(".wpforms-field-option-row-default_value").data("fieldId"),k.checkMultiplicitySliderDefaultValue(e,r,i,o,t).updateNumberSlider(e,r).updateNumberSliderHint(e,r))},changeNumberSliderDefaultValue(e){var i,o,t,r=parseFloat(e.target.value);isNaN(r)||((i=parseFloat(e.target.max))<r?e.target.value=i:r<(o=parseFloat(e.target.min))?e.target.value=o:(t=parseFloat(e.target.step),e=v(e.target).parents(".wpforms-field-option-row-default_value").data("fieldId"),k.checkMultiplicitySliderDefaultValue(e,r,t,o,i).updateNumberSlider(e,r).updateNumberSliderHint(e,r)))},updateNumberSliderDefaultValueAttr(e,i,o){var t,e=r.getElementById("wpforms-field-option-"+e+"-default_value");return e&&(t=parseFloat(e.value),e.setAttribute(o,i),i=parseFloat(i),"max"===o&&i<t&&(e.value=i),"min"===o)&&t<i&&(e.value=i),this},updateNumberSlider(e,i){e=r.getElementById("wpforms-number-slider-"+e);return e&&(e.value=i),this},updateNumberSliderAttr(e,i,o){e=r.getElementById("wpforms-number-slider-"+e);return e&&e.setAttribute(o,i),this},updateNumberSliderHintStr(e,i){e=r.getElementById("wpforms-number-slider-hint-"+e);return e&&(e.dataset.hint=i),this},updateNumberSliderHint(e,i){e=r.getElementById("wpforms-number-slider-hint-"+e);return e&&(e.innerHTML=wpf.sanitizeHTML(e.dataset.hint).replaceAll("{value}","<b>"+i+"</b>")),this},fieldNumberSliderUpdateMin(e){var i,o,t=parseFloat(e.target.value);isNaN(t)||(i=v(e.target).parents(".wpforms-field-option-row-min_max"),(o=parseFloat(i.find(".wpforms-number-slider-max").val()))<=t?(e.preventDefault(),this.value=o):(e=i.data("field-id"),y.find("#wpforms-field-"+e+' input[type="range"]').attr("min",t)))},fieldNumberSliderUpdateMax(e){var i,o,t=parseFloat(e.target.value);isNaN(t)||(i=v(e.target).parents(".wpforms-field-option-row-min_max"),t<=(o=parseFloat(i.find(".wpforms-number-slider-min").val()))?(e.preventDefault(),this.value=o):(e=i.data("field-id"),y.find("#wpforms-field-"+e+' input[type="range"]').attr("max",t)))},updateNumberSliderStepValueMaxAttr(e,i){var o,e=r.getElementById("wpforms-field-option-"+e+"-step");return e&&(o=parseFloat(e.value),e.setAttribute("max",i),(i=parseFloat(i))<o)&&(e.value=i,v(e).trigger("input")),this},fieldFileUploadPreviewUpdate(e){var e=v(e).parents(".wpforms-field-option-file-upload"),i=e.data("field-id"),o=e.find("#wpforms-field-option-"+i+"-style").val(),e=e.find("#wpforms-field-option-row-"+i+"-max_file_number"),t=parseInt(e.find("input").val(),10),i=v("#wpforms-field-"+i),r=".wpforms-file-upload-builder-classic",s=".wpforms-file-upload-builder-modern";"classic"===o?(v(r,i).removeClass("wpforms-hide"),v(s,i).addClass("wpforms-hide"),e.addClass("wpforms-hidden")):(1<t?(i.find(".modern-title").text(wpforms_builder.file_upload.preview_title_plural),i.find(".modern-hint").text(wpforms_builder.file_upload.preview_hint.replace("{maxFileNumber}",t)).removeClass("wpforms-hide")):(i.find(".modern-title").text(wpforms_builder.file_upload.preview_title_single),i.find(".modern-hint").text(wpforms_builder.file_upload.preview_hint.replace("{maxFileNumber}",1)).addClass("wpforms-hide")),v(r,i).addClass("wpforms-hide"),v(s,i).removeClass("wpforms-hide"),e.removeClass("wpforms-hidden"))},updateTextFieldsLimitControls(e,i){i?v("#wpforms-field-option-row-"+e+"-limit_controls").removeClass("wpforms-hide"):v("#wpforms-field-option-row-"+e+"-limit_controls").addClass("wpforms-hide")},updateDisableTodaysDateControls(e,i){v(`#wpforms-field-option-row-${e}-date_disable_todays_date`).toggleClass("wpforms-hide",!i)},updatePasswordStrengthControls(e,i){e=v("#wpforms-field-option-row-"+e+"-password-strength-level");i?e.removeClass("wpforms-hidden"):e.addClass("wpforms-hidden")},updateRichTextMediaFieldsLimitControls(){var e=v(this),i=e.closest(".wpforms-field-option-row-media_enabled").data("field-id"),o=v("#wpforms-field-option-row-"+i+"-media_controls"),i=v("#wpforms-field-"+i+" .wpforms-richtext-wrap .mce-toolbar-grp");e.is(":checked")?(o.show(),i.addClass("wpforms-field-richtext-media-enabled")):(o.hide(),i.removeClass("wpforms-field-richtext-media-enabled"))},updateRichTextStylePreview(){var e=v(this),i=e.closest(".wpforms-field-option-row-style").data("field-id");v("#wpforms-field-"+i+" .wpforms-richtext-wrap .mce-toolbar-grp").toggleClass("wpforms-field-richtext-toolbar-basic","full"!==e.val())},bindUIActions(){k.bindUIActionsPanels(),k.bindUIActionsFields(),k.bindUIActionsSettings(),k.bindUIActionsRevisions(),k.bindUIActionsSaveExit(),k.bindUIActionsGeneral(),k.bindUIActionsPreview()},bindUIActionsPreview(){C.$previewButton.on("click",function(e){e.preventDefault();e=v(this).attr("href");let i=!1;if(t&&!t.closed)try{i=t.location.href.includes("wpforms_form_preview")}catch(e){i=!1}i?t.focus():t=a.open(e,"_blank")}),y.on("wpformsSaved",function(){if(t&&!t.closed)try{t.location.href.includes("wpforms_form_preview")&&t.location.reload()}catch(e){}})},bindUIActionsPanels(){y.on("click","#wpforms-panels-toggle button, .wpforms-panel-switch",function(e){e.preventDefault(),k.panelSwitch(v(this).data("panel"))}),y.on("click",".wpforms-panel .wpforms-panel-sidebar-section",function(e){k.panelSectionSwitch(this,e)}),y.on("click",".wpforms-panels .wpforms-panel-sidebar-content .wpforms-panel-sidebar-toggle",function(){v(this).parent().toggleClass("wpforms-panel-sidebar-closed")})},panelSwitch(i){var o=v("#wpforms-panel-"+i);if(!o.hasClass("active")){if(WPFormsUtils.triggerEvent(y,"wpformsPanelSwitch",[i]).isDefaultPrevented()||!wpforms_panel_switch)return!1;v("#wpforms-panels-toggle").find("button").removeClass("active"),v(".wpforms-panel").removeClass("active"),v(".wpforms-panel-"+i+"-button").addClass("active"),o.addClass("active"),history.replaceState({},null,wpf.updateQueryString("view",i));let e;o=o.find(".active");o.length&&"default"!==o.data("section")&&(e=o.data("section")),history.replaceState({},null,wpf.updateQueryString("section",e)),y.trigger("wpformsPanelSwitched",[i])}},panelSectionSwitch(e,i){i&&i.preventDefault();i=v(e);if(!i.hasClass("upgrade-modal")&&!i.hasClass("education-modal")){var e=i.parent().parent(),i=i.data("section"),o=e.find(`.wpforms-panel-sidebar-section[data-section="${i}"]`);if(!o.hasClass("active")){if(WPFormsUtils.triggerEvent(y,"wpformsPanelSectionSwitch",i).isDefaultPrevented()||!wpforms_panel_switch)return!1;e.find(".wpforms-panel-sidebar-section").removeClass("active"),o.addClass("active"),e.find(".wpforms-panel-content-section").hide(),e.find(".wpforms-panel-content-section-"+i).show(),history.replaceState({},null,wpf.updateQueryString("section",i))}}},bindUIActionsSetup(){console.warn('WARNING! Function "WPFormsBuilder.bindUIActionsSetup()" has been deprecated, please use the new "WPForms.Admin.Builder.Setup.events()" function instead!'),WPForms.Admin.Builder.Setup.events()},templateSelect(e,i){console.warn('WARNING! Function "WPFormsBuilder.templateSelect()" has been deprecated, please use the new "WPForms.Admin.Builder.Setup.selectTemplate()" function instead!'),WPForms.Admin.Builder.Setup.selectTemplate(i)},bindUIActionsFields(){y.on("wpformsPanelSwitched",function(e,i){"fields"===i&&v("#field-options a").hasClass("active")&&0===v(".wpforms-field-wrap .wpforms-field.active").length&&k.fieldTabToggle("field-options")}),y.on("click",".wpforms-tab a",function(e){e.preventDefault(),k.fieldTabToggle(v(this).parent().attr("id"))}),y.on("click",".wpforms-add-fields-heading",function(e){e.preventDefault(),k.fieldGroupToggle(v(this),"click")}),y.on("click",".wpforms-field",function(e){k.isFieldPreviewActionsDisabled(this)||e.target.classList.contains("wpforms-dismiss-button")||(WPForms.Admin.Builder.ContextMenu&&WPForms.Admin.Builder.ContextMenu.hideMainContextMenu(e),e.stopPropagation(),k.fieldTabToggle(v(this).data("field-id")))}),y.on("mousedown click",".wpforms-field input, .wpforms-field select, .wpforms-field textarea",function(e){e.preventDefault(),this.blur()}),y.on("click",".wpforms-field-delete",function(e){e.preventDefault(),e.stopPropagation(),k.isFormPreviewActionsDisabled(this)||(WPForms.Admin.Builder.ContextMenu&&WPForms.Admin.Builder.ContextMenu.hideMenu(),k.fieldDelete(v(this).parent().data("field-id")))}),y.on("click",".wpforms-field-duplicate",function(e){e.preventDefault(),e.stopPropagation(),k.isFormPreviewActionsDisabled(this)||(WPForms.Admin.Builder.ContextMenu&&WPForms.Admin.Builder.ContextMenu.hideMenu(),k.fieldDuplicate(v(this).parent().data("field-id")))}),y.on("click",".wpforms-add-fields-button",function(e){e.preventDefault();var i,e=v(this);e.hasClass("ui-draggable-disabled")||(i=e.data("field-type"),WPFormsUtils.triggerEvent(y,"wpformsBeforeFieldAddOnClick",[i,e]).isDefaultPrevented())||k.fieldAdd(i,{$sortable:"default"})}),y.on("wpformsFieldAdd",function(e,i,o){-1!==v.inArray(o,["select","radio","checkbox","payment-multiple","payment-checkbox","payment-select"])&&k.fieldChoiceSortable(o,`#wpforms-field-option-row-${i}-choices ul`)}),y.on("wpformsFieldOptionTabToggle",function(e,i){k.fieldLayoutSelectorInit(i)}),y.on("click",".wpforms-field-option-row-choices .add",function(e){k.fieldChoiceAdd(e,v(this))}),y.on("click",".wpforms-field-option-row-choices .remove",function(e){k.fieldChoiceDelete(e,v(this))}),y.on("mousedown",".wpforms-field-option-row-choices input[type=radio]",function(e){var i=v(this);i.is(":checked")?i.attr("data-checked","1"):i.attr("data-checked","0")}),y.on("click",".wpforms-field-option-row-choices input[type=radio]",function(e){var i=v(this),o=i.parent().parent();i.parent().parent().find("input[type=radio]").not(this).prop("checked",!1),"1"===i.attr("data-checked")&&(i.prop("checked",!1),i.attr("data-checked","0")),k.fieldChoiceUpdate(o.data("field-type"),o.data("field-id"),o.find("li").length)}),y.on("change",".wpforms-field-option-row-choices input[type=checkbox]",function(e){var i=v(this).parent().parent();k.fieldChoiceUpdate(i.data("field-type"),i.data("field-id"),i.find("li").length)}),y.on("change",".wpforms-field-option-row-show_values input",function(e){v(this).closest(".wpforms-field-option").find(".wpforms-field-option-row-choices ul").toggleClass("show-values")}),y.on("change",".wpforms-field-option-row-choices_images input",function(){var e=v(this),i=e.closest(".wpforms-field-option-row"),o=i.data("field-id"),t=v("#wpforms-field-option-"+o),e=e.is(":checked"),r=t.find(".wpforms-field-option-hidden-type").val(),s=i.siblings(".wpforms-field-option-row-choices_icons").find("input");e&&s.is(":checked")&&s.prop("checked",!1).trigger("change"),i.find(".wpforms-alert").toggleClass("wpforms-hidden"),t.find(".wpforms-field-option-row-choices ul").toggleClass("show-images"),t.find(".wpforms-field-option-row-choices_images_style").toggleClass("wpforms-hidden"),t.find(".wpforms-field-option-row-dynamic_choices").toggleClass("wpforms-hidden",e),(e?v("#wpforms-field-option-"+o+"-input_columns").val("inline"):v("#wpforms-field-option-"+o+"-input_columns").val("")).trigger("change"),k.fieldChoiceUpdate(r,o)}),y.on("wpformsImageUploadAdd wpformsImageUploadRemove",function(e,i,o){var o=o.closest(".choices-list"),t=o.data("field-id"),o=o.data("field-type");k.fieldChoiceUpdate(o,t)}),y.on("change",".wpforms-field-option-row-choices_images_style select",function(){var e=v(this).parent().data("field-id"),i=v("#wpforms-field-option-"+e).find(".wpforms-field-option-hidden-type").val();k.fieldChoiceUpdate(i,e)}),y.on("keyup",".wpforms-field-option-row-choices input.label, .wpforms-field-option-row-choices input.value",function(e){var i=v(this).parent().parent();k.fieldChoiceUpdate(i.data("field-type"),i.data("field-id"))}),y.on("focusout",".wpforms-field-option-row-choices input.label, .wpforms-field-option-row-choices input.value",function(e){var i=v(this);i.val(wpf.sanitizeHTML(i.val(),wpforms_builder.allowed_label_html_tags))}),y.on("click",".toggle-bulk-add-display",function(e){e.preventDefault(),k.fieldChoiceBulkAddToggle(this)}),y.on("click",".toggle-bulk-add-presets",function(e){e.preventDefault();e=v(this).closest(".bulk-add-display").find("ul");"block"===e.css("display")?v(this).text(wpforms_builder.bulk_add_presets_show):v(this).text(wpforms_builder.bulk_add_presets_hide),e.stop().slideToggle()}),y.on("click",".bulk-add-preset-insert",function(e){e.preventDefault();var e=v(this),i=e.data("preset"),e=e.closest(".bulk-add-display"),o=e.find("ul"),t=e.find(".toggle-bulk-add-presets"),e=e.find("textarea");e.val(""),e.insertAtCaret(wpforms_preset_choices[i].choices.join("\n")),t.text(wpforms_builder.bulk_add_presets_show),o.slideUp()}),y.on("click",".bulk-add-insert",function(e){e.preventDefault(),k.fieldChoiceBulkAddInsert(this)}),y.on("click",".wpforms-field-option-group-toggle:not(.education-modal)",function(e){if(WPFormsUtils.triggerEvent(y,"wpformsFieldOptionGroupToggle").isDefaultPrevented())return!1;e.preventDefault();e=v(this).closest(".wpforms-field-option-group");e.siblings(".wpforms-field-option-group").removeClass("active"),e.addClass("active"),y.trigger("wpformsFieldOptionGroupToggled",[e])}),y.on("change",".wpforms-field-option-address input.wpforms-subfield-hide",function(e){var i=v(this).closest(".wpforms-field-option-row"),o=i.data("field-id"),i=i.data("subfield");v("#wpforms-field-"+o).find(".wpforms-"+i).toggleClass("wpforms-hide")}),y.on("input",".wpforms-field-option-row-label input, .wpforms-field-option-row-name input",function(e){var i=v(this),o=i.parent().data("field-id"),o=v("#wpforms-field-"+o),t=o.data("field-type");let r=i.val(),s=0===r.length;(s="html"===t?!1:s)&&(r=wpforms_builder.empty_label),o.toggleClass("label_empty",s).find("> .label-title .text").text(r)}),y.on("input",".wpforms-field-option-row-description textarea",function(){var e=v(this),i=wpf.sanitizeHTML(e.val()),o=e.parent().data("field-id"),t=v(`#wpforms-field-${o} > .description,
						#wpforms-field-${o} .format-selected-single > .description,
						#wpforms-field-${o} .wpforms-field-internal-information-row-description`);k.updateDescription(t,i),e.trigger("wpformsDescriptionFieldUpdated",{id:o,descField:t,value:i})}),y.on("change",".wpforms-field-option-row-required input",function(e){var i=v(this).closest(".wpforms-field-option-row").data("field-id");v("#wpforms-field-"+i).toggleClass("required")}),y.on("change",".wpforms-field-option-row-summary input",function(){var e=v(this),i=e.closest(".wpforms-field-option-row").data("field-id");v("#wpforms-field-"+i).toggleClass("wpforms-summary-enabled"),e.closest(".wpforms-field-option-group-inner").find(".wpforms-total-summary-alert").toggleClass("wpforms-hidden")}),y.on("change",".wpforms-field-option-row-confirmation input",function(){var e=v(this).closest(".wpforms-field-option-row").data("field-id");v("#wpforms-field-"+e).find(".wpforms-confirm").toggleClass("wpforms-confirm-enabled wpforms-confirm-disabled"),v("#wpforms-field-option-"+e).toggleClass("wpforms-confirm-enabled wpforms-confirm-disabled")}),y.on("change",".wpforms-field-option-row-filter_type select",function(){var e=v(this).parent().data("field-id"),e=v("#wpforms-field-option-"+e);v(this).val()?(e.removeClass("wpforms-filter-allowlist"),e.removeClass("wpforms-filter-denylist"),e.addClass("wpforms-filter-"+v(this).val())):(e.removeClass("wpforms-filter-allowlist"),e.removeClass("wpforms-filter-denylist"))}),y.on("focusout",".wpforms-field-option-row-allowlist textarea,.wpforms-field-option-row-denylist textarea",function(){const o=v(this);let e="allow";var i,t;o.next(".wpforms-alert").remove(),""!==o.val()&&(i=v(".wpforms-field-option-row-allowlist textarea"),t=v(".wpforms-field-option-row-denylist textarea"),o.is(t)&&(e="deny"),v.get(wpforms_builder.ajax_url,{nonce:wpforms_builder.nonce,content:JSON.stringify({allow:i.val(),deny:t.val(),current:e}),action:"wpforms_sanitize_restricted_rules"},function(e){var i;e.success&&(o.val(e.data.currentField),0!==(i=e.data.intersect).length&&(i="<p>"+wpforms_builder.allow_deny_lists_intersect+'</p><p class="bold">'+i+"</p>",v.alert({title:wpforms_builder.heads_up,content:i,icon:"fa fa-exclamation-circle",type:"red",buttons:{confirm:{text:wpforms_builder.ok,btnClass:"btn-confirm",keys:["enter"]}}})),e.data.restricted||0)&&o.after('<div class="wpforms-alert-warning wpforms-alert"><p>'+wpforms_builder.restricted_rules+"</p></div>")}))}),y.on("focusout",C.defaultEmailSelector,function(){C.$focusOutTarget=v(this),k.focusOutEvent()}),y.on("change",".wpforms-field-option-row-size select",function(e){var i=v(this),o=i.val(),i=i.parent().data("field-id");v("#wpforms-field-"+i).removeClass("size-small size-medium size-large").addClass("size-"+o)}),y.on("input",".wpforms-field-option-row-placeholder input",function(){var e=v(this),i=e.parent().data("field-id"),o=v("#wpforms-field-"+i),t=o.find(".primary-input");let r=wpf.sanitizeHTML(e.val());"payment-single"===o.data("field-type")&&""===r&&(r=v("#wpforms-field-option-"+i+"-price").prop("placeholder")),t.is("select")?k.dropdownField.helpers.isModernSelect(t)?(e=k.dropdownField.helpers.getInstance(t),t.prop("multiple")?v(e.input.element).prop("placeholder",r):(e.setChoiceByValue(""),t.closest(".choices").find(".choices__inner .choices__placeholder").text(r),o=v("#wpforms-field-option-"+i+"-dynamic_choices").val(),k.dropdownField.helpers.update(i,o))):(e=t.find(".placeholder"),!r.length&&e.length?e.remove():(e.length?e.text(r):t.prepend('<option value="" class="placeholder">'+r+"</option>"),t.find(".placeholder").prop("selected",!t.prop("multiple")))):t.prop("placeholder",r)}),y.on("input",".wpforms-field-option-row-confirmation_placeholder input",function(e){var i=v(this),o=i.val(),i=i.parent().data("field-id");v("#wpforms-field-"+i).find(".secondary-input").attr("placeholder",o)}),y.on("input",".wpforms-field-option .format-selected input.placeholder",function(){var e=v(this),i=e.val(),e=e.closest(".wpforms-field-option-row"),o=e.data("field-id"),e=e.data("subfield");v("#wpforms-field-"+o).find(".wpforms-"+e+" input").attr("placeholder",i)}),y.on("input",".wpforms-field-option-address input.placeholder",function(){var e=v(this),i=e.closest(".wpforms-field-option-row"),o=i.data("field-id"),t=i.data("subfield"),r=v("#wpforms-field-"+o+" .wpforms-"+t).find("input, select"),i=i.find("#wpforms-field-option-"+o+"-"+t+"_default");const s=i.val(),n=i.find("option:selected").text(),a=e.val();r.each(function(){var e,i,o=v(this);o.is("select")?(e=o.find(".placeholder"),i=""===s&&""!==a?a:n,e.text(i)):o.attr("placeholder",a)})}),y.on("input",'.wpforms-field-option-row-default_value input:not([type="search"])',function(){var e=v(this),i=wpf.sanitizeHTML(e.val()),e=e.closest(".wpforms-field-option-row").data("field-id");v("#wpforms-field-"+e+" .primary-input").val(i)}),y.on("input",".wpforms-field-options-column input.default",function(){var e=v(this),i=wpf.sanitizeHTML(e.val()),e=e.closest(".wpforms-field-option-row"),o=e.data("field-id"),e=e.data("subfield");v("#wpforms-field-"+o+" .wpforms-"+e+" input").val(i)}),y.on("change",".wpforms-field-option-address select.default",function(){var e=v(this),i=e.val(),o=e.find("option:selected").text(),e=e.closest(".wpforms-field-option-row"),t=e.data("field-id"),r=e.data("subfield"),s=v("#wpforms-field-option-"+t+"-scheme").val(),e=e.find("#wpforms-field-option-"+t+"-"+r+"_placeholder").val(),t=v("#wpforms-field-"+t+" .wpforms-address-scheme-"+s+" .wpforms-"+r+" .placeholder");""===i&&0<e.trim().length?t.text(e):t.text(o)}),y.on("input",".wpforms-field-option-row-confirmation_placeholder input",function(e){var i=v(this),o=i.val(),i=i.parent().data("field-id");v("#wpforms-field-"+i).find(".secondary-input").attr("placeholder",o)}),y.on("change",".wpforms-field-option-row-label_hide input",function(e){var i=v(this).closest(".wpforms-field-option-row").data("field-id");v("#wpforms-field-"+i).toggleClass("label_hide")}),y.on("change",".wpforms-field-option-row-sublabel_hide input",function(e){var i=v(this).closest(".wpforms-field-option-row").data("field-id");v("#wpforms-field-"+i).toggleClass("sublabel_hide")}),y.on("change",".wpforms-field-option-row-enable_quantity input",function(){var e=v(this).closest(".wpforms-field-option-row").data("field-id"),i=v("#wpforms-field-"+e);v(`#wpforms-field-option-row-${e}-quantity`).toggleClass("wpforms-hidden"),i.find(".quantity-input").toggleClass("wpforms-hidden"),i.toggleClass("payment-quantity-enabled")}),y.on("input",".wpforms-field-option-row-quantity input",function(){var e=v(this),i=(e.val(Math.min(Math.abs(Math.round(e.val())),9999)),e.closest(".wpforms-field-option-row")),o=i.data("field-id"),t=e.hasClass("min-quantity-input"),r=i.find(".min-quantity-input"),i=i.find(".max-quantity-input");t&&v("#wpforms-field-"+o).find(".quantity-input option").text(e.val()),r.toggleClass("wpforms-error",parseInt(r.val(),10)>parseInt(i.val(),10))}),y.on("change",".wpforms-field-option-row-format select",function(){var e,i,o,t,r,s=v(this),n=s.val(),s=s.parent().data("field-id"),a=v("#wpforms-field-option-row-"+s+"-sublabel_hide"),l=v("#wpforms-field-"+s);l.find(".format-selected").removeClass().addClass("format-selected format-selected-"+n),v("#wpforms-field-option-"+s).find(".format-selected").removeClass().addClass("format-selected format-selected-"+n),["date-time","first-last","first-middle-last"].includes(n)?a.removeClass("wpforms-hidden"):a.addClass("wpforms-hidden"),v(`#wpforms-field-option-row-${s}-price_label`).toggleClass("wpforms-hidden","single"!==n),["single","user","hidden"].includes(n)&&(a="user"===n,e="single"===n,n="hidden"===n,i=v("#wpforms-field-option-"+s+"-enable_quantity").is(":checked"),t=v("#wpforms-field-option-"+s+"-min_price"),t=(o=wpf.amountSanitize(t.val()))>=t.data("minimum-price"),r=v("#wpforms-field-option-row-"+s+"-min_price"),v("#wpforms-field-option-row-"+s+"-placeholder").toggleClass("wpforms-hidden",!a),v("#wpforms-field-option-row-"+s+"-enable_quantity").toggleClass("wpforms-hidden",!e),v("#wpforms-field-option-row-"+s+"-quantities_alert").toggleClass("wpforms-hidden",!e),v("#wpforms-field-option-row-"+s+"-quantity").toggleClass("wpforms-hidden",!e||!i),l.find(".quantity-input").toggleClass("wpforms-hidden",!e||!i),r.toggleClass("wpforms-hidden",!a),r.find(".wpforms-item-minimum-price-alert").toggleClass("wpforms-hidden",t),l.find(".item-min-price").toggleClass("wpforms-hidden",a&&o<=0),l.toggleClass("min-price-warning",!t),l.find(".fa-exclamation-triangle").toggleClass("wpforms-hidden",t),v(`#wpforms-field-${s} .item-price-single`).toggleClass("wpforms-hidden",!e),v(`#wpforms-field-${s} .item-price-hidden`).toggleClass("wpforms-hidden",!n))}),y.on("change",".wpforms-field-option-row-scheme select",function(e){var i=v(this),o=i.val(),i=i.parent().data("field-id"),t=v("#wpforms-field-"+i),r=v(`#wpforms-field-option-row-${i}-state`),i=v(`#wpforms-field-option-row-${i}-country`),t=(t.find(".wpforms-address-scheme").addClass("wpforms-hide"),t.find(".wpforms-address-scheme-"+o).removeClass("wpforms-hide"),t.find(`.wpforms-address-scheme-${o} .wpforms-country select, .wpforms-address-scheme-${o} .wpforms-country input`)),t=(0===t.length?i.addClass("wpforms-hidden"):i.removeClass("wpforms-hidden"),r.find(".default .default").not(".wpforms-hidden-strict")),r=r.find(`.default [data-scheme="${o}"]`),s=i.find(".default .default").not(".wpforms-hidden-strict"),i=i.find(`.default [data-scheme="${o}"]`);r.attr({id:t.attr("id"),name:t.attr("name")}).removeClass("wpforms-hidden-strict"),t.attr({id:"",name:""}).addClass("wpforms-hidden-strict"),i.attr({id:s.attr("id"),name:s.attr("name")}).removeClass("wpforms-hidden-strict"),s.attr({id:"",name:""}).addClass("wpforms-hidden-strict")}),y.on("change",".wpforms-field-option-row-date .type select",function(e){var i=v(this),o=i.val(),t=v(this).closest(".wpforms-field-option-row").data("field-id"),r="datepicker"===o?"wpforms-date-type-datepicker":"wpforms-date-type-dropdown",s="datepicker"===o?"wpforms-date-type-dropdown":"wpforms-date-type-datepicker",r=(v("#wpforms-field-"+t).find(".wpforms-date").addClass(r).removeClass(s),v("#wpforms-field-option-"+t).addClass(r).removeClass(s),i.closest(".wpforms-field-option-group-advanced").find(".wpforms-field-option-row-date_limit_days, .wpforms-field-option-row-date_limit_days_options, .wpforms-field-option-row-date_disable_past_dates")),s=v("#wpforms-field-option-row-"+t+"-date_limit_days_options");"dropdown"===o?((i=v("#wpforms-field-option-"+t+"-date_format")).find("option:selected").hasClass("datepicker-only")&&i.prop("selectedIndex",0).trigger("change"),r.hide()):(r.show(),v("#wpforms-field-option-"+t+"-date_limit_days").is(":checked")?s.show():s.hide())}),y.on("change",".wpforms-field-option-row-date .format select",function(e){var i=v(this).val(),o=v(this).closest(".wpforms-field-option-row").data("field-id"),o=v("#wpforms-field-"+o);"m/d/Y"===i?(o.find(".wpforms-date-dropdown .first option").text(wpforms_builder.date_select_month),o.find(".wpforms-date-dropdown .second option").text(wpforms_builder.date_select_day)):"d/m/Y"===i&&(o.find(".wpforms-date-dropdown .first option").text(wpforms_builder.date_select_day),o.find(".wpforms-date-dropdown .second option").text(wpforms_builder.date_select_month))}),y.on("change",".wpforms-field-option-row-time .format select",function(e){const i=v(this),r=i.closest(".wpforms-field-option-row").data("field-id");let s="",o;const n=i.val().match(/[gh]/)?12:24,t=12==n?1:0,a=12==n?13:24;for(let e=t;e<a;e++)o=e<10?"0"+e:e,s+='<option value="{hh}">{hh}</option>'.replace(/{hh}/g,o);_.forEach(["start","end"],function(e){var i=y.find("#wpforms-field-option-"+r+"-time_limit_hours_"+e+"_hour"),e=y.find("#wpforms-field-option-"+r+"-time_limit_hours_"+e+"_ampm");let o=parseInt(i.val(),10),t=e.val();o=(o=24==n?"pm"===t?o+12:o:(t=12<o?"pm":"am",12<o?o-12:o))<10?"0"+o:o,i.html(s).val(o),e.toggleClass("wpforms-hidden-strict",24==n).val(t),e.nextAll("div").toggleClass("wpforms-hidden-strict",12==n)})}),y.on("click",".wpforms-pagebreak-button",function(e){e.preventDefault(),v(this).closest(".wpforms-field").trigger("click")}),k.fieldPageBreakInitDisplayPrevious(y.find(".wpforms-field-pagebreak.wpforms-pagebreak-normal").first()),y.on("input",".wpforms-field-option-row-next input",function(e){var i=v(this),o=i.val(),i=v("#wpforms-field-"+i.parent().data("field-id")).find(".wpforms-pagebreak-next");o?i.css("display","inline-block").text(o):i.css("display","none").empty()}).on("input",".wpforms-field-option-row-prev input",function(e){var i=v(this),o=i.val().trim(),i=v("#wpforms-field-"+i.parent().data("field-id")),t=i.find(".wpforms-pagebreak-prev");o&&0<i.prevAll(".wpforms-field-pagebreak.wpforms-pagebreak-normal").length?t.removeClass("wpforms-hidden").text(o):t.addClass("wpforms-hidden").empty()}).on("change",".wpforms-field-option-row-prev_toggle input",function(e){var i=v(this),o=i.closest(".wpforms-field-option-row-prev_toggle");if(!o.hasClass("wpforms-entry-preview-block")){var o=i.closest(".wpforms-field-option-group-inner").find(".wpforms-field-option-row-prev"),t=o.find("input"),r=v("#wpforms-field-"+i.closest(".wpforms-field-option").data("field-id")).find(".wpforms-pagebreak-prev");if(o.toggleClass("wpforms-hidden",!i.prop("checked")),r.toggleClass("wpforms-hidden",!i.prop("checked")),i.prop("checked")&&!t.val()){let e=t.data("last-value");e=e&&e.trim()?e.trim():wpforms_builder.previous,t.val(e)}i.prop("checked")||(t.data("last-value",t.val()),t.val("")),t.trigger("input")}}).on("wpformsFieldAdd",k.fieldPagebreakAdd).on("wpformsFieldDelete",k.fieldPagebreakDelete).on("wpformsFieldAdd",k.toggleOrderSummaryConfirmation).on("wpformsFieldDelete",k.toggleOrderSummaryConfirmation).on("wpformsBeforeFieldDelete",k.fieldEntryPreviewDelete),y.on("wpformsFieldMove wpformsFieldAdd wpformsFieldDelete",function(e){y.find(".wpforms-field-pagebreak.wpforms-pagebreak-normal").each(function(e){k.fieldPageBreakInitDisplayPrevious(v(this))})}),y.on("input",".wpforms-field-option-row-title input",function(e){var i=v(this),o=i.val(),i=i.parent().data("field-id");o?v("#wpforms-field-"+i).find(".wpforms-pagebreak-title").text(o):v("#wpforms-field-"+i).find(".wpforms-pagebreak-title").empty()}),y.on("change",".wpforms-field-option-row-nav_align select",function(e){let i=v(this).val();i=i||"center",v(".wpforms-pagebreak-buttons").removeClass("wpforms-pagebreak-buttons-center wpforms-pagebreak-buttons-left wpforms-pagebreak-buttons-right wpforms-pagebreak-buttons-split").addClass("wpforms-pagebreak-buttons-"+i)}),y.on("input",".wpforms-field-option-row-price input",function(){var e=v(this),i=e.val(),o=wpf.amountFormat(wpf.amountSanitize(i)),e=e.parent().data("field-id"),t=v("#wpforms-field-option-"+e+"-placeholder").val().trim(),e=v("#wpforms-field-"+e),t=""===i&&""!==t?"":o;e.find(".primary-input").val(t),e.find(".price").text(wpf.amountFormatCurrency(i))}),y.on("input",".wpforms-field-option-row-min_price input",function(){var e=v(this),i=e.val(),o=wpf.amountSanitize(i),t=o<=0,o=o>=e.data("minimum-price"),e=e.parent(),r=v("#wpforms-field-"+e.data("field-id"));e.find(".wpforms-item-minimum-price-alert").toggleClass("wpforms-hidden",o),r.find(".item-min-price").toggleClass("wpforms-hidden",t),r.toggleClass("min-price-warning",!o),r.find(".fa-exclamation-triangle").toggleClass("wpforms-hidden",o),t||r.find(".min-price").text(wpf.amountFormatCurrency(i))}),y.on("input",".wpforms-single-item-price-label-display",function(){var e=v(this),i=wpf.sanitizeHTML(e.val(),"<>"),o=e.parent().data("field-id"),t=v("#wpforms-field-"+o),o=wpf.amountFormatCurrency(v(`#wpforms-field-option-${o}-price`).val());i?t.find(".price-label").html(i.replaceAll("{price}",`<span class="price"> ${o}  </span>`)):(e.val("{price}"),t.find(".price-label").html(`<span class="price"> ${o}  </span>`))}),y.on("change",".wpforms-field-option-credit-card .payment-icons input",function(){var e=v(this),i=e.data("card"),e=e.parent().data("field-id");v("#wpforms-field-"+e).find("img.icon-"+i).toggleClass("card_hide")}),y.on("input",".wpforms-field-option input.placeholder-update",function(e){var i=v(this),o=i.val(),t=i.data("field-id"),i=i.data("subfield");v("#wpforms-field-"+t).find(".wpforms-"+i+" input").attr("placeholder",o)}),y.on("change",".wpforms-field-option-row-input_columns select",function(){var e=v(this),i=e.val(),e=e.parent().data("field-id");let o="";"2"===i?o="wpforms-list-2-columns":"3"===i?o="wpforms-list-3-columns":"inline"===i&&(o="wpforms-list-inline"),v("#wpforms-field-"+e).removeClass("wpforms-list-2-columns wpforms-list-3-columns wpforms-list-inline").addClass(o)}),y.on("change",".wpforms-field-option-row .wpforms-toggle-control input",function(e){var i=v(this),o=i.closest(".wpforms-toggle-control").find(".wpforms-toggle-control-status"),i=i.is(":checked")?"on":"off";o.html(o.data(i))}),y.on("change",".wpforms-field-option-row-dynamic_choices select",function(e){k.fieldDynamicChoiceToggle(v(this))}),y.on("change",".wpforms-field-option-row-dynamic_taxonomy select, .wpforms-field-option-row-dynamic_post_type select",function(e){k.fieldDynamicChoiceSource(v(this))}),y.on("click",".toggle-layout-selector-display",function(e){e.preventDefault(),k.fieldLayoutSelectorToggle(this)}),y.on("click",".layout-selector-display-layout",function(e){e.preventDefault(),k.fieldLayoutSelectorLayout(this)}),y.on("click",".layout-selector-display-columns span",function(e){e.preventDefault(),k.fieldLayoutSelectorInsert(this)}),v(r).on("change",".wpforms-field-option-row-scale select",function(){const e=v(this),i=e.val(),o=e.parent().data("field-id"),t=v("#wpforms-field-"+o+" .rating-icon");let r=1;t.each(function(e){r<=i?v(this).show():v(this).hide(),r++})}),v(r).on("change",".wpforms-field-option-row-icon select",function(){var e=v(this),i=e.val(),e=e.parent().data("field-id"),e=v("#wpforms-field-"+e+" .rating-icon");let o="fa-star";"heart"===i?o="fa-heart":"thumb"===i?o="fa-thumbs-up":"smiley"===i&&(o="fa-smile-o"),e.removeClass("fa-star fa-heart fa-thumbs-up fa-smile-o").addClass(o)}),v(r).on("change",".wpforms-field-option-row-icon_size select",function(){var e=v(this),i=e.val(),e=e.parent().data("field-id"),e=v("#wpforms-field-"+e+" .rating-icon");let o="28";"small"===i?o="18":"large"===i&&(o="38"),e.css("font-size",o+"px")}),v(r).on("input",".wpforms-field-option-row-icon_color input.wpforms-color-picker",function(){var e=v(this),i=e.closest(".wpforms-field-option-row").data("field-id");v("#wpforms-field-"+i+" > i.fa").css("color",k.getValidColorPickerValue(e))}),v(r).on("change",".wpforms-field-option-row-disclaimer_format input",function(){var e=v(this).closest(".wpforms-field-option-row").data("field-id");v("#wpforms-field-"+e+" .description").toggleClass("disclaimer")}),y.on("change",".wpforms-field-option-row-limit_enabled input",function(e){k.updateTextFieldsLimitControls(v(e.target).closest(".wpforms-field-option-row-limit_enabled").data().fieldId,e.target.checked)}),y.on("change",".wpforms-field-option-row-date_disable_past_dates input",function(e){k.updateDisableTodaysDateControls(v(e.target).closest(".wpforms-field-option-row-date_disable_past_dates").data().fieldId,e.target?.checked)}),y.on("change",".wpforms-field-option-row-password-strength input",function(e){k.updatePasswordStrengthControls(v(e.target).parents(".wpforms-field-option-row-password-strength").data().fieldId,e.target.checked)}),y.on("change",".wpforms-field-option-richtext .wpforms-field-option-row-media_enabled input",k.updateRichTextMediaFieldsLimitControls),y.on("change",".wpforms-field-option-richtext .wpforms-field-option-row-style select",k.updateRichTextStylePreview),y.on("change",".wpforms-field-option-file-upload .wpforms-field-option-row-style select, .wpforms-field-option-file-upload .wpforms-field-option-row-max_file_number input",function(e){k.fieldFileUploadPreviewUpdate(e.target)}),k.numberSliderEvents(y),k.fieldDynamicChoiceToggleImageChoices(),k.fieldDynamicChoiceToggleIconChoices(),y.on("change",".wpforms-field-option-row-show_price_after_labels input",function(e){var i=v(this).closest(".wpforms-field-option-group-basic").find(".wpforms-field-option-row-choices .choices-list");k.fieldChoiceUpdate(i.data("field-type"),i.data("field-id"))}),y.on("input",".wpforms-field-option-row-preview-notice textarea",k.updatePreviewNotice).on("change",".wpforms-field-option-row-preview-notice-enable input",k.toggleEntryPreviewNotice).on("wpformsFieldAdd",k.maybeLockEntryPreviewGroupOnAdd).on("wpformsFieldMove",k.maybeLockEntryPreviewGroupOnMove).on("click",".wpforms-entry-preview-block",k.entryPreviewBlockField),k.defaultStateEntryPreviewNotice()},focusOutEvent(){if(null!==C.$focusOutTarget){if(C.$defaultEmail.is(C.$focusOutTarget)){const i=C.$focusOutTarget;if(i.next(".wpforms-alert").remove(),""===i.val())return;v.get(wpforms_builder.ajax_url,{nonce:wpforms_builder.nonce,content:i.val(),action:"wpforms_sanitize_default_email"},function(e){e.success&&(i.val(e.data),i.trigger("input"),e.data||i.after('<div class="wpforms-alert-warning wpforms-alert"><p>'+wpforms_builder.restricted_default_email+"</p></div>"))})}C.$focusOutTarget=null}},isFieldPreviewActionsDisabled(e){return k.isFormPreviewActionsDisabled(e)||v(e).closest(".wpforms-field").hasClass("ui-sortable-disabled")},isFormPreviewActionsDisabled(e){return v(e).closest(".wpforms-field-wrap").hasClass("ui-sortable-disabled")},fieldGroupToggle(e,i){e=v(e);let o=e.next(".wpforms-add-fields-buttons");const t=o.parent();let r=e.find("i"),s=e.data("group"),n="wpforms_field_group_"+s;"click"===i?(t.hasClass("wpforms-closed")?wpCookies.remove(n):wpCookies.set(n,"true",2592e3),r.toggleClass("wpforms-angle-right"),o.stop().slideToggle("",function(){t.toggleClass("wpforms-closed")})):"load"===i&&(o=e.find(".wpforms-add-fields-buttons"),r=e.find(".wpforms-add-fields-heading i"),s=e.find(".wpforms-add-fields-heading").data("group"),"true"===wpCookies.get("wpforms_field_group_"+s))&&(r.toggleClass("wpforms-angle-right"),o.hide(),e.toggleClass("wpforms-closed"))},updateDescription(e,i){e.hasClass("nl2br")&&(i=i.replace(/\n/g,"<br>")),e.html(i)},defaultStateEntryPreviewNotice(){v(".wpforms-field-option-row-preview-notice-enable input").each(function(){v(this).trigger("change")})},updatePreviewNotice(){var e=v(this),i=wpf.sanitizeHTML(e.val()).trim(),e=e.parent().data("field-id"),e=v("#wpforms-field-"+e).find(".wpforms-entry-preview-notice"),i=i||wpforms_builder.entry_preview_default_notice;k.updateDescription(e,i)},toggleEntryPreviewNotice(){var e=v(this),i=e.closest(".wpforms-field-option").data("field-id"),o=v("#wpforms-field-"+i),i=v("#wpforms-field-option-"+i+" .wpforms-field-option-row-preview-notice"),t=o.find(".wpforms-entry-preview-notice"),o=o.find(".wpforms-alert-info");(e.is(":checked")?(o.hide(),t.show(),i):(i.hide(),t.hide(),o)).show()},fieldDelete(e){var i=v("#wpforms-field-"+e),o=i.data("field-type");"pagebreak"===o&&i.hasClass("wpforms-field-entry-preview-not-deleted")?k.youCantRemovePageBreakFieldPopup():i.hasClass("no-delete")?k.youCantRemoveFieldPopup():k.confirmFieldDeletion(e,o)},youCantRemovePageBreakFieldPopup(){v.alert({title:wpforms_builder.heads_up,content:wpforms_builder.entry_preview_require_page_break,icon:"fa fa-exclamation-circle",type:"red",buttons:{confirm:{text:wpforms_builder.ok,btnClass:"btn-confirm",keys:["enter"]}}})},youCantReorderFieldPopup(){console.warn('WARNING! Function "WPFormsBuilder.youCantReorderFieldPopup()" has been deprecated, please use the new "WPForms.Admin.Builder.DragFields.youCantReorderFieldPopup()" function instead!'),WPForms.Admin.Builder.DragFields.youCantReorderFieldPopup()},youCantRemoveFieldPopup(){v.alert({title:wpforms_builder.field_locked,content:wpforms_builder.field_locked_no_delete_msg,icon:"fa fa-info-circle",type:"blue",buttons:{confirm:{text:wpforms_builder.close,btnClass:"btn-confirm",keys:["enter"]}}})},validationErrorNotificationPopup(e){console.warn('WARNING! Function "WPFormsBuilder.validationErrorNotificationPopup()" has been deprecated.'),v.alert({title:wpforms_builder.heads_up,content:e,icon:"fa fa-exclamation-circle",type:"red",buttons:{confirm:{text:wpforms_builder.close,btnClass:"btn-confirm",keys:["enter"]}}})},confirmFieldDeletion(e,i){var o={id:e,message:wpforms_builder.delete_confirm};WPFormsUtils.triggerEvent(y,"wpformsBeforeFieldDeleteAlert",[o,i]).isDefaultPrevented()||v.confirm({title:!1,content:o.message,icon:"fa fa-exclamation-circle",type:"orange",buttons:{confirm:{text:wpforms_builder.ok,btnClass:"btn-confirm",keys:["enter"],action(){k.fieldDeleteById(e)}},cancel:{text:wpforms_builder.cancel}}})},fieldDeleteById(t,r="",e=400){v("#wpforms-field-"+t).fadeOut(e,function(){var e=v(this),i=e.parents(".wpforms-field-layout-columns"),e=(r=e.data("field-type"),y.trigger("wpformsBeforeFieldDelete",[t,r]),e.remove(),v("#wpforms-field-option-"+t).remove(),v(".wpforms-field, .wpforms-title-desc").removeClass("active"),k.fieldTabToggle("add-fields"),v(".wpforms-field-option")),o=y.find(".wpforms-field-submit");e.length<1&&(C.$sortableFieldsWrap.append(C.$noFieldsPreview.clone()),C.$fieldOptions.append(C.$noFieldsOptions.clone()),o.hide()),e.filter(":not(.wpforms-field-option-layout)").length||o.hide(),y.trigger("wpformsFieldDelete",[t,r,i])})},determineActiveSections(){const s=wpf.getQueryString("section");v(".wpforms-panel").each(function(e,i){var o,t=v(this),r=(o=t,((r=s)&&o.hasClass("active")&&(o=o.find(`.wpforms-panel-sidebar-section[data-section="${r}"]`)).length?o:null)||(e=>{e=e.find(".wpforms-panel-sidebar-section.configured").first();return e.length?e:null})(t)||t.find(".wpforms-panel-sidebar-section:first-of-type"));o=t,(t=r)&&(r=t.data("section"),t.addClass("active"),(t=o.find(".wpforms-panel-content-section-"+r)).length?(t.show().addClass("active"),o.find(".wpforms-panel-content-section-default").toggle("default"===r)):o.find(".wpforms-panel-content-section-default").show().addClass("active"),WPFormsUtils.triggerEvent(y,"wpformsPanelSectionSwitch",r))})},loadEntryPreviewFields(){var e=v("#wpforms-panel-fields .wpforms-field-wrap .wpforms-field-entry-preview");e.length&&e.each(function(){k.lockEntryPreviewFieldsPosition(v(this).data("field-id"))})},fieldEntryPreviewDelete(e,i,o){"entry-preview"===o&&(i=(o=v("#wpforms-field-"+i)).prevAll(".wpforms-field-pagebreak").first(),o=o.nextAll(".wpforms-field-pagebreak").first().data("field-id"),o=v("#wpforms-field-option-"+o),i.removeClass("wpforms-field-not-draggable wpforms-field-entry-preview-not-deleted"),o.find(".wpforms-entry-preview-block").removeClass("wpforms-entry-preview-block"),y.trigger("wpformsFieldDragToggle",[i.data("field-id"),i.data("field-type")]))},maybeLockEntryPreviewGroupOnMove(e,i){i.item.hasClass("wpforms-field-pagebreak")&&k.maybeLockEntryPreviewGroupOnAdd(e,i.item.data("field-id"),"pagebreak")},maybeLockEntryPreviewGroupOnAdd(e,i,o){var t,r,s,n,a;"pagebreak"===o&&(r=(t=v("#wpforms-field-"+i)).prevAll(".wpforms-field-entry-preview,.wpforms-field-pagebreak").first(),a=t.nextAll(".wpforms-field-entry-preview,.wpforms-field-pagebreak").first(),r.hasClass("wpforms-field-entry-preview")||a.hasClass("wpforms-field-entry-preview"))&&(s=(n=v("#wpforms-field-option-"+i+" .wpforms-field-option-row-prev_toggle")).find("input"),a=v("#wpforms-field-option-"+a.data("field-id")+" .wpforms-field-option-row-prev_toggle"),r.hasClass("wpforms-field-entry-preview")?(s.attr("checked","checked").trigger("change"),n.addClass("wpforms-entry-preview-block"),a.removeClass("wpforms-entry-preview-block")):(s=r.data("field-id"),a=(n=v("#wpforms-field-option-"+s+" .wpforms-field-option-row-prev_toggle")).find("input"),t.addClass("wpforms-field-not-draggable wpforms-field-entry-preview-not-deleted"),y.trigger("wpformsFieldDragToggle",[i,o]),r.removeClass("wpforms-field-not-draggable wpforms-field-entry-preview-not-deleted"),y.trigger("wpformsFieldDragToggle",[s,r.data("field-type")]),r.prevAll(".wpforms-field-entry-preview,.wpforms-field-pagebreak").first().hasClass("wpforms-field-entry-preview")&&(a.attr("checked","checked").trigger("change"),n.addClass("wpforms-entry-preview-block"))))},entryPreviewBlockField(e){e.preventDefault(),v.alert({title:wpforms_builder.heads_up,content:wpforms_builder.entry_preview_require_previous_button,icon:"fa fa-exclamation-circle",type:"red",buttons:{confirm:{text:wpforms_builder.ok,btnClass:"btn-confirm",keys:["enter"]}}})},isUncheckedEntryPreviewField(e,i){return!("entry-preview"!==e||i&&i.passed)},addEntryPreviewField(e,i){var o,t,r,s=v("#wpforms-add-fields-entry-preview");s.hasClass("wpforms-entry-preview-adding")||(r=v("#wpforms-panel-fields .wpforms-field-wrap > .wpforms-field"),o=i?.position?i.position:r.length,t=k.isEntryPreviewFieldRequiresPageBreakBefore(r,o),r=k.isEntryPreviewFieldRequiresPageBreakAfter(r,o),s.addClass("wpforms-entry-preview-adding"),(i=i||{}).passed=!0,t||r?t?k.addPageBreakAndEntryPreviewFields(i,o):k.addEntryPreviewAndPageBreakFields(i,o):k.fieldAdd("entry-preview",i).done(function(e){k.lockEntryPreviewFieldsPosition(e.data.field.id)}))},addEntryPreviewFieldAfterPageBreak(e){const i=setInterval(function(){2===v("#wpforms-panel-fields .wpforms-field-wrap").find(".wpforms-pagebreak-bottom, .wpforms-pagebreak-top").length&&(k.fieldAdd("entry-preview",e).done(function(e){k.lockEntryPreviewFieldsPosition(e.data.field.id)}),clearInterval(i))},100)},addPageBreakAndEntryPreviewFields(i,o){const t=3<=v("#wpforms-panel-fields .wpforms-field-wrap > .wpforms-field-pagebreak").length;k.fieldAdd("pagebreak",{position:o}).done(function(e){i.position=t?o+1:o+2,k.addEntryPreviewFieldAfterPageBreak(i);e=v("#wpforms-field-option-"+e.data.field.id).find(".wpforms-field-option-row-prev_toggle");e.find("input").attr("checked","checked").trigger("change"),e.addClass("wpforms-entry-preview-block")})},fieldDuplicate(o){const t=v("#wpforms-field-"+o);t.hasClass("no-duplicate")?v.alert({title:wpforms_builder.field_locked,content:wpforms_builder.field_locked_no_duplicate_msg,icon:"fa fa-info-circle",type:"blue",buttons:{confirm:{text:wpforms_builder.close,btnClass:"btn-confirm",keys:["enter"]}}}):v.confirm({title:!1,content:wpforms_builder.duplicate_confirm,icon:"fa fa-exclamation-circle",type:"orange",buttons:{confirm:{text:wpforms_builder.ok,btnClass:"btn-confirm",keys:["enter"],action(){var e,i;this.$$confirm.prop("disabled",!0),WPFormsUtils.triggerEvent(y,"wpformsBeforeFieldDuplicate",[o,t]).isDefaultPrevented()||(e=k.fieldDuplicateRoutine(o,!0),i=v("#wpforms-field-"+e),k.increaseNextFieldIdAjaxRequest(),WPFormsUtils.triggerEvent(y,"wpformsFieldDuplicated",[o,t,e,i]))}},cancel:{text:wpforms_builder.cancel}}})},increaseNextFieldIdAjaxRequest(){v.post(wpforms_builder.ajax_url,{form_id:m.formID,field_id:C.$nextFieldId.val(),nonce:wpforms_builder.nonce,action:"wpforms_builder_increase_next_field_id"})},fieldDuplicateRoutine(e,i=!0){var o=v("#wpforms-field-"+e),t=v("#wpforms-field-option-"+e),r=C.$sortableFieldsWrap.find(">.active"),s=C.$fieldOptions.find(">:visible"),n=s.find(">.active"),a=o.data("field-type"),l=t.attr("class"),d=k.dropdownField.helpers.isModernSelect(o.find("> .choices .primary-input"));wpf.restoreTooltips(t),d&&k.dropdownField.helpers.convertModernToClassic(e);let p=t.html();const f=o.clone(),c=parseInt(C.$nextFieldId.val(),10),m=v(`#wpforms-field-option-${e}-label`),w=(m.length?m:v(`#wpforms-field-option-${e}-name`)).val(),u=c+1,h={};var g=""!==w?w+" "+wpforms_builder.duplicate_copy:wpforms_builder.field+` #${e} `+wpforms_builder.duplicate_copy;h.fieldOptionsID=new RegExp("ID #"+e,"g"),h.fieldID=new RegExp("fields\\["+e+"\\]","g"),h.dataFieldID=new RegExp('data-field-id="'+e+'"',"g"),h.referenceID=new RegExp('data-reference="'+e+'"',"g"),h.elementID=new RegExp('\\b(id|for)="wpforms-(.*?)'+e+'(.*?)"',"ig"),o.after(f),r.removeClass("active"),f.addClass("active").attr({id:"wpforms-field-"+c,"data-field-id":c}),h.elementIdReplace=function(e,i,o,t,r,s){return`${i}="wpforms-${o}${c}${t}"`},p=(p=(p=(p=(p=p.replace(h.fieldOptionsID,"ID #"+c)).replace(h.fieldID,`fields[${c}]`)).replace(h.dataFieldID,`data-field-id="${c}"`)).replace(h.referenceID,`data-reference="${c}"`)).replace(h.elementID,h.elementIdReplace),s.hide(),t.after(`<div class="${l}" id="wpforms-field-option-${c}" data-field-id="${c}">${p}</div>`);const b=v("#wpforms-field-option-"+c);r.data("field-id")===e&&n.length&&(o=n.attr("class").match(/wpforms-field-option-group-\S*/i)[0],s=b.find(">."+o),b.find(">").removeClass("active"),s.addClass("active")),r.data("field-id")!==e&&n.length&&(b.find(">").removeClass("active"),b.find(">.wpforms-field-option-group-basic").addClass("active")),t.find(":input").each(function(e,i){var o=v(this),t=o.attr("name");if(!t)return"continue";var t=t.replace(h.fieldID,`fields[${c}]`),r=o.attr("type");"checkbox"===r||"radio"===r?o.is(":checked")?b.find(`[name="${t}"]`).prop("checked",!0).attr("checked","checked"):b.find(`[name="${t}"]`).prop("checked",!1).attr("checked",!1):o.is("select")?o.find("option:selected").length&&(r=o.find("option:selected").val(),b.find(`[name="${t}"]`).find(`[value="${r}"]`).prop("selected",!0)):""===(r=o.val())&&o.hasClass("wpforms-money-input")?b.find(`[name="${t}"]`).val(wpf.numberFormat("0",wpforms_builder.currency_decimals,wpforms_builder.currency_decimal,wpforms_builder.currency_thousands)):b.find(`[name="${t}"]`).val(r)}),b.find(".wpforms-field-option-hidden-id").val(c),C.$nextFieldId.val(u);l=v("html"===a?`#wpforms-field-option-${c}-name`:`#wpforms-field-option-${c}-label`);return i&&l.val(g).trigger("input"),y.trigger("wpformsFieldAdd",[c,a]),wpf.initTooltips(),d&&(k.dropdownField.helpers.convertClassicToModern(e),k.dropdownField.helpers.convertClassicToModern(c)),k.fieldChoiceUpdate(f.data("field-type"),c),k.loadColorPickers(),c},addEntryPreviewAndPageBreakFields(e,o){k.fieldAdd("entry-preview",e).done(function(e){const i=e.data.field.id;k.fieldAdd("pagebreak",{position:o+1}).done(function(e){k.lockEntryPreviewFieldsPosition(i);e=v("#wpforms-field-"+e.data.field.id).nextAll(".wpforms-field-pagebreak, .wpforms-field-entry-preview").first();e.hasClass("wpforms-field-entry-preview")&&k.lockEntryPreviewFieldsPosition(e.data("field-id"))})})},lockEntryPreviewFieldsPosition(e){var i=v("#wpforms-field-"+e),o=i.prevAll(".wpforms-field-pagebreak:not(.wpforms-pagebreak-bottom)").first(),t=i.nextAll(".wpforms-field-pagebreak").first().data("field-id"),t=v("#wpforms-field-option-"+t).find(".wpforms-field-option-row-prev_toggle"),r=t.find("input");i.addClass("wpforms-field-not-draggable"),o.addClass("wpforms-field-not-draggable wpforms-field-entry-preview-not-deleted"),r.prop("checked","checked").trigger("change"),t.addClass("wpforms-entry-preview-block"),v("#wpforms-add-fields-entry-preview").removeClass("wpforms-entry-preview-adding"),y.trigger("wpformsFieldDragToggle",[e,i.data("field-type")]),y.trigger("wpformsFieldDragToggle",[o.data("field-id"),o.data("field-type")])},isEntryPreviewFieldRequiresPageBreakBefore(e,i){e=e.slice(0,i).filter(".wpforms-field-pagebreak,.wpforms-field-entry-preview");let o=!0;return e.length&&v(e.get().reverse()).each(function(){var e=v(this);return!e.hasClass("wpforms-field-entry-preview")&&(e.hasClass("wpforms-field-pagebreak")&&!e.hasClass("wpforms-field-stick")?o=!1:void 0)}),o},isEntryPreviewFieldRequiresPageBreakAfter(e,i){e=e.slice(i).filter(".wpforms-field-pagebreak,.wpforms-field-entry-preview");let o=Boolean(e.length);return e.length&&e.each(function(){var e=v(this);return!e.hasClass("wpforms-field-entry-preview")&&(e.hasClass("wpforms-field-pagebreak")?o=!1:void 0)}),o},fieldAdd(s,n){var e=v("#wpforms-add-fields-"+s);if(!(e.hasClass("upgrade-modal")||e.hasClass("education-modal")||e.hasClass("warning-modal")))if(["captcha_turnstile","captcha_hcaptcha","captcha_recaptcha","captcha_none"].includes(s))k.captchaUpdate();else{if(l=!0,WPForms.Admin.Builder.DragFields.disableDragAndDrop(),k.disableFormActions(),!k.isUncheckedEntryPreviewField(s,n))return n=v.extend({},{position:"bottom",$sortable:"base",placeholder:!1,scroll:!0,defaults:!1},n),e={action:"wpforms_new_field_"+s,id:m.formID,type:s,defaults:n.defaults,nonce:wpforms_builder.nonce},v.post(wpforms_builder.ajax_url,e,function(i){if(i.success){var o=C.$sortableFieldsWrap,t=v(i.data.preview),r=v(i.data.options);let e=n.$sortable;l=!1,t.css("display","none"),n.placeholder&&n.placeholder.remove(),"default"!==n.$sortable&&n.$sortable.length||(e=o.find(".wpforms-fields-sortable-default")),"base"!==n.$sortable&&e.length||(e=o);o=WPFormsUtils.triggerEvent(y,"wpformsBeforeFieldAddToDOM",[n,t,r,e]);o.isDefaultPrevented()||(o.skipAddFieldToBaseLevel||k.fieldAddToBaseLevel(n,t,r),t.fadeIn(),y.find(".no-fields, .no-fields-preview").remove(),v(".wpforms-field-option:not(.wpforms-field-option-layout)").length&&y.find(".wpforms-field-submit").show(),n.scroll&&n.position.length&&k.scrollPreviewToField(i.data.field.id),C.$nextFieldId.val(i.data.field.id+1),wpf.initTooltips(),k.loadColorPickers(),k.toggleAllOptionGroups(),y.trigger("wpformsFieldAdd",[i.data.field.id,s]))}else wpf.debug("Add field AJAX call is unsuccessful:",i)}).fail(function(e,i,o){l=!1,wpf.debug("Add field AJAX call failed:",e.responseText)}).always(function(){l||(WPForms.Admin.Builder.DragFields.enableDragAndDrop(),k.enableFormActions())});k.addEntryPreviewField(s,n)}},fieldAddToBaseLevel(e,i,o){var t,r=C.$sortableFieldsWrap,s=r.children(":not(.wpforms-field-drag-pending, .no-fields-preview)"),n=s.length,a=C.$fieldOptions;"top"===e.position?(r.prepend(i),a.prepend(o)):(t=s.last(),"bottom"!==e.position||t.length&&t.hasClass("wpforms-field-stick")?("bottom"===e.position&&(e.position=n),e.position===n&&t.length&&t.hasClass("wpforms-field-stick")?(n=t.data("field-id"),t.before(i),a.find("#wpforms-field-option-"+n).before(o)):(t=s.eq(e.position)).length?(n=t.data("field-id"),t.before(i),a.find("#wpforms-field-option-"+n).before(o)):(r.append(i),a.append(o))):(r.append(i),a.append(o)))},scrollPreviewToField(e){var e=v("#wpforms-field-"+e),i=C.$fieldsPreviewWrap.scrollTop(),o=e.closest(".wpforms-field-layout");let t=e.position().top;e=(t=o.length?o.position().top+t+20:t)>i?t-i:t+i;C.$fieldsPreviewWrap.scrollTop(e)},captchaUpdate(){var e={action:"wpforms_update_field_captcha",id:m.formID,nonce:wpforms_builder.nonce};return v.post(wpforms_builder.ajax_url,e,function(i){if(i.success){const o={title:!1,content:!1,icon:"fa fa-exclamation-circle",type:"orange",boxWidth:"450px",buttons:{confirm:{text:wpforms_builder.ok,btnClass:"btn-confirm",keys:["enter"]}}},t=v("#wpforms-panel-field-settings-recaptcha");let e=i.data.current;t.data("provider",i.data.provider),"configured_not_enabled"!==e&&"configured_enabled"!==e||(e=t.prop("checked")?"configured_enabled":"configured_not_enabled",o.buttons.confirm.action=function(){t.prop("checked","configured_not_enabled"===e).trigger("change")}),o.title=i.data.cases[e].title,o.content=i.data.cases[e].content,i.data.cases[e].cancel&&(o.buttons.cancel={text:wpforms_builder.cancel,keys:["esc"]}),v.confirm(o)}else console.log(i)}).fail(function(e,i,o){console.log(e.responseText)})},disableDragAndDrop(){console.warn('WARNING! Function "WPFormsBuilder.disableDragAndDrop()" has been deprecated, please use the new "WPForms.Admin.Builder.DragFields.disableDragAndDrop()" function instead!'),WPForms.Admin.Builder.DragFields.disableDragAndDrop()},enableDragAndDrop(){console.warn('WARNING! Function "WPFormsBuilder.enableDragAndDrop()" has been deprecated, please use the new "WPForms.Admin.Builder.DragFields.enableDragAndDrop()" function instead!'),WPForms.Admin.Builder.DragFields.enableDragAndDrop()},disableFormActions(){v.each([C.$previewButton,C.$embedButton,C.$saveButton,C.$exitButton],function(e,i){i.prop("disabled",!0).addClass("wpforms-disabled")})},enableFormActions(){v.each([C.$previewButton,C.$embedButton,C.$saveButton,C.$exitButton],function(e,i){i.prop("disabled",!1).removeClass("wpforms-disabled")})},fieldSortable(){console.warn('WARNING! Function "WPFormsBuilder.fieldSortable()" has been deprecated, please use the new "WPForms.Admin.Builder.DragFields.initSortableFields()" function instead!'),WPForms.Admin.Builder.DragFields.initSortableFields()},fieldDragDisable(e,i=!0){console.warn('WARNING! Function "WPFormsBuilder.fieldDragDisable()" has been deprecated, please use the new "WPForms.Admin.Builder.DragFields.fieldDragDisable()" function instead!'),WPForms.Admin.Builder.DragFields.fieldDragDisable(e,i)},fieldDragEnable(e){console.warn('WARNING! Function "WPFormsBuilder.fieldDragEnable()" has been deprecated, please use the new "WPForms.Admin.Builder.DragFields.fieldDragEnable()" function instead!'),WPForms.Admin.Builder.DragFields.fieldDragEnable(e)},fieldChoiceAdd(e,i){e.preventDefault();var e=v(i),i=e.parent(),o=i.find("input.default").is(":checked"),e=e.closest(".wpforms-field-option-row-choices").data("field-id"),t=i.parent().attr("data-next-id"),r=i.parent().data("field-type"),s=i.clone().insertAfter(i);s.attr("data-key",t),s.find("input.label").val("").attr("name","fields["+e+"][choices]["+t+"][label]"),s.find("input.value").val("").attr("name","fields["+e+"][choices]["+t+"][value]"),s.find(".wpforms-image-upload input.source").val("").attr("name","fields["+e+"][choices]["+t+"][image]"),s.find(".wpforms-icon-select input.source-icon").val(wpforms_builder.icon_choices.default_icon).attr("name","fields["+e+"][choices]["+t+"][icon]"),s.find(".wpforms-icon-select input.source-icon-style").val(wpforms_builder.icon_choices.default_icon_style).attr("name","fields["+e+"][choices]["+t+"][icon_style]"),s.find(".wpforms-icon-select .ic-fa-preview").removeClass().addClass("ic-fa-preview ic-fa-"+wpforms_builder.icon_choices.default_icon_style+" ic-fa-"+wpforms_builder.icon_choices.default_icon),s.find(".wpforms-icon-select .ic-fa-preview + span").text(wpforms_builder.icon_choices.default_icon),s.find("input.default").attr("name","fields["+e+"][choices]["+t+"][default]").prop("checked",!1),s.find(".preview").empty(),s.find(".wpforms-image-upload-add").show(),s.find(".wpforms-money-input").trigger("focusout"),!0===o&&i.find("input.default").prop("checked",!0),t++,i.parent().attr("data-next-id",t),y.trigger("wpformsFieldChoiceAdd",[e]),k.fieldChoiceUpdate(r,e)},fieldChoiceDelete(e,i){e.preventDefault();const o=v(i),t=o.parent().parent(),r=t.find("li").length,s={id:t.data("field-id"),choiceId:o.closest("li").data("key"),message:"<strong>"+wpforms_builder.delete_choice_confirm+"</strong>",trigger:!1};if(y.trigger("wpformsBeforeFieldDeleteAlert",[s]),1===r)k.fieldChoiceDeleteAlert();else{const n=function(){o.parent().remove(),k.fieldChoiceUpdate(t.data("field-type"),t.data("field-id")),y.trigger("wpformsFieldChoiceDelete",[t.data("field-id")])};s.trigger?v.confirm({title:!1,content:s.message,icon:"fa fa-exclamation-circle",type:"orange",buttons:{confirm:{text:wpforms_builder.ok,btnClass:"btn-confirm",keys:["enter"],action(){n()}},cancel:{text:wpforms_builder.cancel}}}):n()}},fieldChoiceDeleteAlert(){v.alert({title:!1,content:wpforms_builder.error_choice,icon:"fa fa-info-circle",type:"blue",buttons:{confirm:{text:wpforms_builder.ok,btnClass:"btn-confirm",keys:["enter"]}}})},fieldChoiceSortable(t,e=void 0){v(e=void 0!==e?e:".wpforms-field-option-"+t+" .wpforms-field-option-row-choices ul").sortable({items:"li",axis:"y",delay:100,opacity:.6,handle:".move",stop(e,i){var o=i.item.parent().data("field-id");k.fieldChoiceUpdate(t,o),y.trigger("wpformsFieldChoiceMove",i)},update(e,i){}})},fieldChoiceLabel(e,i){var o=["payment-multiple","payment-checkbox"].includes(e.settings.type),t=e.settings.choices_icons||e.settings.choices_images,r=void 0===e.settings.choices[i].label||0===e.settings.choices[i].label.length;if(r&&!o&&t)return"";t=o?wpforms_builder.payment_choice_empty_label_tpl:wpforms_builder.choice_empty_label_tpl;let s=r?t.replace("{number}",i):wpf.sanitizeHTML(e.settings.choices[i].label,wpforms_builder.allowed_label_html_tags);return e.settings.show_price_after_labels&&(s+=" - "+wpf.amountFormatCurrency(e.settings.choices[i].value)),s},fieldChoiceUpdate:(n,e,i=-1)=>{const a=k.dropdownField.helpers.isDynamicChoices(e);if(!k.replaceChoicesWithTemplate(n,e,a)){-1===i&&(i=k.settings.choicesLimitLong),"payment-select"===n&&(n="select");const l=v("#wpforms-field-"+e+" .primary-input");let s="";"select"===n?a||(s='<option value="{label}">{label}</option>',l.find("option").not(".placeholder").remove()):"radio"!==n&&"checkbox"!==n&&"gdpr-checkbox"!==n||(n="gdpr-checkbox"===n?"checkbox":n,l.find("li").remove(),s='<li><input type="'+n+'" disabled>{label}</li>');const t=v("#wpforms-field-option-row-"+e+"-choices .choices-list"),r=t.find("li").slice(0,i),d=!!t.find("input.default:checked").length,p=[],f=v("#wpforms-field-option-"+e+"-show_price_after_labels").prop("checked"),c=k.dropdownField.helpers.isModernSelect(l);var o;r.get().forEach(function(e){var e=v(e),i=e.find("input.value").val(),o=e.data("key");let t=wpf.sanitizeHTML(e.find("input.label").val().trim(),wpforms_builder.allowed_label_html_tags),r;if(t=""!==t?t:wpforms_builder.choice_empty_label_tpl.replace("{number}",o),t+=f&&i?" - "+wpf.amountFormatCurrency(i):"",c?p.push({value:t,label:t}):a||(r=v(s.replace(/{label}/g,t)),l.append(r)),!0===e.find("input.default").is(":checked"))switch(n){case"select":c?p[p.length-1].selected=!0:k.setClassicSelectedChoice(r);break;case"radio":case"checkbox":r.find("input").prop("checked","true")}}),c&&(i=l.prop("multiple")?"input.choices__input":".choices__inner .choices__placeholder",o=k.dropdownField.helpers.getInstance(l),a||o.removeActiveItems(),o.setChoices(p,"value","label",!0),k.dropdownField.helpers.update(e,a),l.closest(".choices").find(i).toggleClass("wpforms-hidden",d))}},replaceChoicesWithTemplate:(e,i,o)=>{if("radio"!==e&&"checkbox"!==e&&"payment-multiple"!==e&&"payment-checkbox"!==e)return!1;var t=wpf.getChoicesOrder(i),r=wp.template("wpforms-field-preview-checkbox-radio-payment-multiple");const s=wpf.getField(i),n={},a=t.slice(0,k.settings.choicesLimit),l={settings:s,order:a,type:"radio"};return s.choices_icons&&(l.settings.choices_icons_color=k.getValidColorPickerValue(v("#wpforms-field-option-"+i+"-choices_icons_color"))),a.forEach(function(e){n[e]=s.choices[e]}),s.choices=n,"checkbox"!==e&&"payment-checkbox"!==e||(l.type="checkbox"),o||v("#wpforms-field-"+i).find("ul.primary-input").replaceWith(r(l)),k.firstNChoicesAlert(i,t.length),!0},setClassicSelectedChoice(e){void 0!==e&&e.prop("selected","true")},fieldChoiceBulkAddToggle(i){var i=v(i),o=i.closest("label");if(i.hasClass("bulk-add-showing")){const e=o.next(".bulk-add-display");e.slideUp(400,function(){e.remove()}),i.find("span").text(wpforms_builder.bulk_add_show)}else{let e='<div class="bulk-add-display unfoldable-cont">';e=e+('<p class="heading wpforms-clear">'+wpforms_builder.bulk_add_heading+' <a href="#" class="toggle-bulk-add-presets">'+wpforms_builder.bulk_add_presets_show+"</a></p>")+"<ul>";for(const t in wpforms_preset_choices)e+='<li><a href="#" data-preset="'+t+'" class="bulk-add-preset-insert">'+wpforms_preset_choices[t].name+"</a></li>";e=(e=(e+="</ul>")+('<textarea placeholder="'+wpforms_builder.bulk_add_placeholder+'"></textarea>'))+('<button class="bulk-add-insert wpforms-btn wpforms-btn-sm wpforms-btn-blue">'+wpforms_builder.bulk_add_button+"</button>")+"</div>",o.after(e),o.next(".bulk-add-display").slideDown(400,function(){v(this).find("textarea").trigger("focus")}),i.find("span").text(wpforms_builder.bulk_add_hide)}i.toggleClass("bulk-add-showing")},fieldChoiceBulkAddInsert(e){var i,e=v(e),o=e.closest(".wpforms-field-option-row"),t=o.find("textarea"),r=o.find(".choices-list"),s=r.find("li:first-of-type").clone().wrap("<div>").parent(),n=o.data("field-id"),a=r.data("field-type");let l=Number(r.attr("data-next-id"));var d=t.val().split("\n");let p="";e.prop("disabled",!0).html(e.html()+" "+m.spinner),s.find("input.value,input.label").attr("value",""),s.find("input.default").attr("checked",!1),s.find("input.source-icon").attr("value",wpforms_builder.icon_choices.default_icon),s.find("input.source-icon-style").attr("value",wpforms_builder.icon_choices.default_icon_style),s.find(".ic-fa-preview").removeClass().addClass(`ic-fa-preview ic-fa-${wpforms_builder.icon_choices.default_icon_style} ic-fa-`+wpforms_builder.icon_choices.default_icon),s.find(".ic-fa-preview + span").text(wpforms_builder.icon_choices.default_icon),i=s.html();for(const c in d)if(d.hasOwnProperty(c)){var f=wpf.sanitizeHTML(d[c]).trim().replace(/"/g,"&quot;");let e=i;e=(e=(e=(e=e.replace(/\[choices\]\[(\d+)\]/g,"[choices]["+l+"]")).replace(/data-key="(\d+)"/g,'data-key="'+l+'"')).replace(/value="" class="label"/g,'value="'+f+'" class="label"')).replace(/class="label" type="text" value=""/g,'class="label" type="text" value="'+f+'"'),p+=e,l++}r.attr("data-next-id",l).append(p),k.fieldChoiceUpdate(a,n,l),y.trigger("wpformsFieldChoiceAdd"),k.fieldChoiceBulkAddToggle(o.find(".toggle-bulk-add-display"))},triggerBuilderEvent(e){y.trigger(e)},fieldTabToggle(e){var i;if(WPFormsUtils.triggerEvent(y,"wpformsFieldTabToggle",[e]).isDefaultPrevented())return!1;v(".wpforms-tab a").removeClass("active"),v(".wpforms-field, .wpforms-title-desc").removeClass("active"),"add-fields"===e?(C.$addFieldsTab.addClass("active"),v(".wpforms-field-options").hide(),v(".wpforms-add-fields").show()):(v("#field-options a").addClass("active"),"field-options"===e?((i=v(".wpforms-field").first()).addClass("active"),e=i.data("field-id")):v("#wpforms-field-"+e).addClass("active"),v(".wpforms-field-option").hide(),v("#wpforms-field-option-"+e).show(),v(".wpforms-add-fields").hide(),v(".wpforms-field-options").show(),y.trigger("wpformsFieldOptionTabToggle",[e]))},fieldPagebreakAdd(e,i,o){if("pagebreak"===o){let e;m.pagebreakTop?m.pagebreakBottom||(m.pagebreakBottom=!0,e={position:"bottom",scroll:!1,defaults:{position:"bottom"}},k.fieldAdd("pagebreak",e).done(function(e){m.pagebreakBottom=e.data.field.id;var i=v("#wpforms-field-"+e.data.field.id);v("#wpforms-field-option-"+e.data.field.id).find(".wpforms-field-option-group").addClass("wpforms-pagebreak-bottom"),i.addClass("wpforms-field-stick wpforms-pagebreak-bottom")})):(m.pagebreakTop=!0,e={position:"top",scroll:!1,defaults:{position:"top",nav_align:"left"}},k.fieldAdd("pagebreak",e).done(function(e){m.pagebreakTop=e.data.field.id;var i=v("#wpforms-field-"+e.data.field.id);v("#wpforms-field-option-"+e.data.field.id).find(".wpforms-field-option-group").addClass("wpforms-pagebreak-top"),i.addClass("wpforms-field-stick wpforms-pagebreak-top")}))}},fieldPagebreakDelete(e,i,o){var t,r,s;"pagebreak"!==o||v("#wpforms-panel-fields .wpforms-field-pagebreak").not(".wpforms-pagebreak-top, .wpforms-pagebreak-bottom").length||(r=(t=(o=v("#wpforms-panel-fields .wpforms-preview-wrap")).find(".wpforms-pagebreak-top")).data("field-id"),s=(o=o.find(".wpforms-pagebreak-bottom")).data("field-id"),t.remove(),v("#wpforms-field-option-"+r).remove(),m.pagebreakTop=!1,o.remove(),v("#wpforms-field-option-"+s).remove(),m.pagebreakBottom=!1)},fieldPageBreakInitDisplayPrevious(e){var i=e.data("field-id"),o=v("#wpforms-field-option-row-"+i+"-prev_toggle"),i=v("#wpforms-field-option-row-"+i+"-prev"),t=e.find(".wpforms-pagebreak-prev");0<e.prevAll(".wpforms-field-pagebreak.wpforms-pagebreak-normal").length?(o.removeClass("hidden"),i.removeClass("hidden"),o.find("input").is(":checked")&&t.removeClass("wpforms-hidden").text(i.find("input").val())):(o.addClass("hidden"),i.addClass("hidden"),t.addClass("wpforms-hidden"))},fieldDynamicChoiceToggle(o){let t;const e=v(o),r=e.parent(),i=e.val(),s=r.data("field-id");var o=v("#wpforms-field-option-row-"+s+"-choices"),n=v("#wpforms-field-option-"+s+"-choices_images"),a=v("#wpforms-field-option-"+s+"-choices_icons"),l=v("#wpforms-field-option-basic-"+s);if(k.fieldDynamicChoiceToggleImageChoices(),k.fieldDynamicChoiceToggleIconChoices(),y.trigger("wpformsFieldDynamicChoiceToggle",[s]),wpf.fieldOptionLoading(r),v("#wpforms-field-option-row-"+s+"-dynamic_post_type").remove(),v("#wpforms-field-option-row-"+s+"-dynamic_taxonomy").remove(),""!==i)n.addClass("wpforms-hidden"),a.addClass("wpforms-hidden"),o.find(".toggle-bulk-add-display").addClass("wpforms-hidden"),l.find(".wpforms-ai-choices-button").addClass("wpforms-hidden"),o.find(".wpforms-help-tooltip").addClass("wpforms-hidden"),d={type:i,field_id:s,action:"wpforms_builder_dynamic_choices",nonce:wpforms_builder.nonce},v.post(wpforms_builder.ajax_url,d,function(e){e.success?r.after(e.data.markup):console.log(e),wpf.fieldOptionLoading(r,!0),wpf.initTooltips();e=v("#wpforms-field-option-"+s+"-dynamic_"+i);e.find("option").first().prop("selected",!0),e.trigger("change")}).fail(function(e,i,o){console.log(e.responseText)});else{let i=v("#wpforms-field-option-"+s).find(".wpforms-field-option-hidden-type").val();n.removeClass("wpforms-hidden"),a.removeClass("wpforms-hidden"),o.find(".toggle-bulk-add-display").removeClass("wpforms-hidden"),l.find(".wpforms-ai-choices-button").removeClass("wpforms-hidden"),o.find(".wpforms-help-tooltip").removeClass("wpforms-hidden");var d=v("#wpforms-field-"+s);if(d.find(".wpforms-alert").remove(),-1<["checkbox","radio","payment-multiple","payment-checkbox"].indexOf(i))k.fieldChoiceUpdate(i,s),o.find("ul").removeClass("wpforms-hidden"),o.find(".wpforms-alert").addClass("wpforms-hidden");else{n=d;const f=[],c=n.find(".primary-input");let e;if(v("#wpforms-field-option-row-"+s+"-choices li").each(function(){var e=v(this);f.push({label:wpf.sanitizeHTML(e.find(".label").val()),selected:e.find(".default").is(":checked")})}),n.hasClass("wpforms-field-select")){var p,a=k.dropdownField.helpers.isModernSelect(c);if(c.find("option").not(".placeholder").remove(),a&&f.length)k.dropdownField.helpers.update(s,!1);else for(e in f)p=f[e].selected,t="<option",t=(t+=p?" selected>":">")+f[e].label+"</option>",c.append(t)}else for(e in i="radio",n.hasClass("wpforms-field-checkbox")&&(i="checkbox"),c.empty(),f)t='<li><input type="'+i+'" disabled',t=(t+=f[e].selected?" selected>":">")+f[e].label+"</li>",c.append(t);o.find("ul").removeClass("wpforms-hidden"),o.find(".wpforms-alert").addClass("wpforms-hidden"),c.removeClass("wpforms-hidden")}wpf.fieldOptionLoading(r,!0)}},fieldDynamicChoiceSource(e){const i=v(e),o=i.parent(),t=i.val(),r=o.data("field-id"),s=v("#wpforms-builder-form").data("id"),n=v("#wpforms-field-option-row-"+r+"-choices"),a=v("#wpforms-field-"+r),l=v("#wpforms-field-option-"+r+"-dynamic_choices option:selected").val();let d=20;wpf.fieldOptionLoading(o);e={type:l,source:t,field_id:r,form_id:s,action:"wpforms_builder_dynamic_source",nonce:wpforms_builder.nonce};v.post(wpforms_builder.ajax_url,e,function(i){if(i.success){if(n.find(".dynamic-name").text(i.data.source_name),n.find(".dynamic-type").text(i.data.type_name),n.find("ul").addClass("wpforms-hidden"),n.find(".wpforms-alert").removeClass("wpforms-hidden"),k.fieldDynamicChoiceSourceItems(a,i.data.items),a.hasClass("wpforms-field-select")&&(d=200),a.find(".wpforms-notice-dynamic-empty").remove(),Number(i.data.total)>d){let e=wpforms_builder.dynamic_choices.limit_message;e=(e=(e=(e=e.replace("{source}",i.data.source_name)).replace("{type}",i.data.type_name)).replace("{limit}",d)).replace("{total}",i.data.total),v.alert({title:wpforms_builder.heads_up,content:e,icon:"fa fa-info-circle",type:"blue",buttons:{confirm:{text:wpforms_builder.ok,btnClass:"btn-confirm",keys:["enter"]}}})}k.firstNChoicesAlert(r,i.data.total),0===Number(i.data.total)&&k.emptyChoicesNotice(r,i.data.source_name,i.data.type)}else console.log(i);wpf.fieldOptionLoading(o,!0)}).fail(function(e,i,o){console.log(e.responseText)})},fieldDynamicChoiceSourceItems(i,o){var t=i.find(".primary-input");let r=0;if(i.hasClass("wpforms-field-select"))k.dropdownField.helpers.isModernSelect(t)?k.fieldDynamicChoiceSourceForModernSelect(t,o):k.fieldDynamicChoiceSourceForClassicSelect(t,o);else{let e="radio";for(r in i.hasClass("wpforms-field-checkbox")&&(e="checkbox"),t.empty(),o)t.append('<li><input type="'+e+'" disabled> '+wpf.sanitizeHTML(o[r])+"</li>")}},fieldDynamicChoiceSourceForModernSelect(e,i){var o=k.dropdownField.helpers.getInstance(e),t=e.closest(".wpforms-field").data().fieldId;o.destroy(),k.dropdownField.helpers.updatePlaceholderChoice(o,t),k.fieldDynamicChoiceSourceForClassicSelect(e,i),k.dropdownField.events.choicesInit(e)},fieldDynamicChoiceSourceForClassicSelect(e,i){let o=0;var t=i.length;for(e.find("option").not(".placeholder").remove();o<t;o++){var r=wpf.sanitizeHTML(i[o]);e.append('<option value="'+r+'">'+r+"</option>")}e.toggleClass("wpforms-hidden",!t)},fieldDynamicChoiceToggleImageChoices(){v("#wpforms-builder .wpforms-field-options .wpforms-field-option").each(function(e,i){var i=v(i),o=i.find(".wpforms-field-option-row-dynamic_choices select").val(),o=void 0!==o&&""!==o,t=i.find(".wpforms-field-option-row-choices_images input").is(":checked");i.find(".wpforms-field-option-row-choices_images").toggleClass("wpforms-hidden",o),t&&!o||i.find(".wpforms-field-option-row-choices_images_style").addClass("wpforms-hidden")})},fieldDynamicChoiceToggleIconChoices(){v("#wpforms-builder .wpforms-field-options .wpforms-field-option").each(function(e,i){var i=v(i),o=i.find(".wpforms-field-option-row-dynamic_choices select").val(),o=void 0!==o&&""!==o,t=i.find(".wpforms-field-option-row-choices_icons input").is(":checked");i.find(".wpforms-field-option-row-choices_icons").toggleClass("wpforms-hidden",o),t&&!o||(i.find(".wpforms-field-option-row-choices_icons_color").addClass("wpforms-hidden"),i.find(".wpforms-field-option-row-choices_icons_size").addClass("wpforms-hidden"),i.find(".wpforms-field-option-row-choices_icons_style").addClass("wpforms-hidden"))})},firstNChoicesAlert:(e,i)=>{var o,t,r,e=v("#wpforms-field-"+e);e.hasClass("wpforms-field-select")||(o=wp.template("wpforms-choices-limit-message"),t={total:i},r=k.settings.choicesLimit,e.find(".wpforms-alert-dynamic").remove(),r<i&&e.find(".primary-input").after(o(t)))},emptyChoicesNotice(e,i,o){e=v("#wpforms-field-"+e),i=wpforms_builder.dynamic_choices.empty_message.replace("{source}",i).replace("{type}",wpforms_builder.dynamic_choices.entities[o]),o=wp.template("wpforms-empty-choice-message"),i={message:i};e.find(".label-title").after(o(i))},fieldLayoutSelectorToggle(e){var e=v(e),i=e.closest("label").next(".layout-selector-display");e.hasClass("layout-selector-showing")?(i.stop().slideUp(400),e.find("span").text(wpforms_builder.layout_selector_show)):(i.stop().slideDown(),e.find("span").text(wpforms_builder.layout_selector_hide)),e.toggleClass("layout-selector-showing")},fieldLayoutSelectorInit(o){var e=v(`#wpforms-field-option-row-${o}-css > .layout-selector-display`);if(!e.length){var t={"layout-1":[{class:"one-half",data:"wpforms-one-half wpforms-first"},{class:"one-half",data:"wpforms-one-half"}],"layout-2":[{class:"one-third",data:"wpforms-one-third wpforms-first"},{class:"one-third",data:"wpforms-one-third"},{class:"one-third",data:"wpforms-one-third"}],"layout-3":[{class:"one-fourth",data:"wpforms-one-fourth wpforms-first"},{class:"one-fourth",data:"wpforms-one-fourth"},{class:"one-fourth",data:"wpforms-one-fourth"},{class:"one-fourth",data:"wpforms-one-fourth"}],"layout-4":[{class:"one-third",data:"wpforms-one-third wpforms-first"},{class:"two-third",data:"wpforms-two-thirds"}],"layout-5":[{class:"two-third",data:"wpforms-two-thirds wpforms-first"},{class:"one-third",data:"wpforms-one-third"}],"layout-6":[{class:"one-fourth",data:"wpforms-one-fourth wpforms-first"},{class:"one-fourth",data:"wpforms-one-fourth"},{class:"two-fourth",data:"wpforms-two-fourths"}],"layout-7":[{class:"two-fourth",data:"wpforms-two-fourths wpforms-first"},{class:"one-fourth",data:"wpforms-one-fourth"},{class:"one-fourth",data:"wpforms-one-fourth"}],"layout-8":[{class:"one-fourth",data:"wpforms-one-fourth wpforms-first"},{class:"two-fourth",data:"wpforms-two-fourths"},{class:"one-fourth",data:"wpforms-one-fourth"}]};let e,i=`<div class="layout-selector-display unfoldable-cont">
					<p class="heading">${wpforms_builder.layout_selector_layout}</p>
					<div class="layouts">`;for(const r in t){e=t[r],i+='<div class="layout-selector-display-layout">';for(const s in e)i+=`<span class="${e[s].class}" data-classes="${e[s].data}"></span>`;i+="</div>"}i+="</div></div>",v(`#wpforms-field-option-row-${o}-css > label`).after(i)}},fieldLayoutSelectorLayout(e){e=v(e);e.parent().find(".layout-selector-display-layout").not(e).remove(),e.parent().find(".heading").text(wpforms_builder.layout_selector_column),e.toggleClass("layout-selector-display-layout layout-selector-display-columns")},fieldLayoutSelectorInsert(e){const i=v(e),o=i.closest(".layout-selector-display"),t=o.parent(),r=t.data("field-id"),s=t.find("label"),n=t.find("input[type=text]");let a=i.data("classes"),l=n.val();l&&(["wpforms-one-half","wpforms-first","wpforms-one-third","wpforms-one-fourth","wpforms-two-thirds","wpforms-two-fourths"].forEach(e=>{l=l.replace(new RegExp("\\b"+e+"\\b","gi"),"")}),l=l.replace(/\s\s+/g," ").trim(),a+=" "+l),n.val(a),o.slideUp(400,function(){o.remove(),k.fieldLayoutSelectorInit(r)}),s.find(".toggle-layout-selector-display").removeClass("layout-selector-showing"),s.find(".toggle-layout-selector-display span").text(wpforms_builder.layout_selector_show)},toggleOrderSummaryConfirmation(e,i,o){"payment-total"===o&&v(".wpforms-confirmation").each(function(){v(this).find(".wpforms-panel-field-confirmations-message_order_summary").closest(".wpforms-panel-field").toggle(0!==v("#wpforms-panel-fields .wpforms-field-payment-total").length)})},bindUIActionsSettings(){y.on("click","#wpforms-panel-fields .wpforms-title-desc, #wpforms-panel-fields .wpforms-field-submit-button, .wpforms-center-form-name",function(e){e.preventDefault(),k.panelSwitch("settings"),(v(this).hasClass("wpforms-center-form-name")||v(this).hasClass("wpforms-title-desc"))&&setTimeout(function(){v("#wpforms-panel-field-settings-form_title").trigger("focus")},300)}),y.on("click",".wpforms-field-pagebreak-last button",function(e){e.preventDefault(),k.panelSwitch("settings"),v("#wpforms-panel-field-settings-pagebreak_prev").trigger("focus")}),y.on("click",".wpforms-panel-content-also-available-item-add-captcha",function(e){e.preventDefault();e=y.find("#wpforms-add-fields-captcha");e.data("action")?e.trigger("click"):k.fieldAdd("captcha",{}).done(function(e){k.panelSwitch("fields"),v("#wpforms-field-"+e.data.field.id).trigger("click")})}),y.on("input","#wpforms-panel-field-settings-pagebreak_prev",function(){v(".wpforms-field-pagebreak-last button").text(v(this).val())}),y.on("input","#wpforms-panel-field-settings-form_title, #wpforms-setup-name",function(){var e=v(this).val().toString().trim();v(".wpforms-preview .wpforms-form-name").text(e),v(".wpforms-center-form-name.wpforms-form-name").text(e),k.trimFormTitle()}),y.on("input","#wpforms-panel-field-settings-form_desc",function(){v(".wpforms-form-desc").text(v(this).val())}),y.on("input","#wpforms-panel-field-settings-submit_text",function(){var e=v(this).val()||wpforms_builder.submit_text;v(".wpforms-field-submit input[type=submit]").val(e)}),y.on("change","#wpforms-panel-field-settings-recaptcha",function(){k.captchaToggle()}),y.on("change",".wpforms-panel-field-confirmations-type",function(){k.confirmationFieldsToggle(v(this))}),y.on("change",".wpforms-panel-field-confirmations-message_entry_preview",k.confirmationEntryPreviewToggle),y.on("change","#wpforms-panel-field-settings-notification_enable",k.notificationToggle),y.on("click",".wpforms-builder-settings-block-add",function(e){e.preventDefault(),wpforms_builder.pro&&k.settingsBlockAdd(v(this))}),y.on("click",".wpforms-builder-settings-block-edit",function(e){e.preventDefault();e=v(this);e.parents(".wpforms-builder-settings-block-header").find(".wpforms-builder-settings-block-name").hasClass("editing")?k.settingsBlockNameEditingHide(e):k.settingsBlockNameEditingShow(e)}),y.on("blur",".wpforms-builder-settings-block-name-edit input",function(e){v(e.relatedTarget).hasClass("wpforms-builder-settings-block-edit")||k.settingsBlockNameEditingHide(v(this))}),y.on("keypress",".wpforms-builder-settings-block-name-edit input",function(e){13===e.keyCode&&(k.settingsBlockNameEditingHide(v(this)),e.preventDefault())}),y.on("click",".wpforms-builder-settings-block-clone",function(e){e.preventDefault(),k.settingsBlockPanelClone(v(this))}),y.on("click",".wpforms-builder-settings-block-toggle",function(e){e.preventDefault(),k.settingsBlockPanelToggle(v(this))}),y.on("click",".wpforms-builder-settings-block-delete",function(e){e.preventDefault(),k.settingsBlockDelete(v(this))}),y.on("wpformsSettingsBlockAdded wpformsSettingsBlockCloned",function(e,i){i.hasClass("wpforms-notification")&&k.notificationUpdateStatus(i)}),y.on("click",".wpforms-notification .wpforms-status-button",function(){k.notificationChangeStatus(v(this))}),y.on("change","#wpforms-panel-field-settings-ajax_submit",function(){k.hideOpenConfirmationsInNewTabOptions(!v(this).is(":checked"))})},hideOpenConfirmationsInNewTabOptions(e){var i=y.find(".wpforms-panel-content-section-confirmation").find(".wpforms-builder-settings-block").find(".wpforms-panel-field-confirmations-redirect_new_tab");i.length&&i.each(function(){v(this).closest(".wpforms-panel-field").toggle(!e)})},captchaToggle(){var e=y.find(".wpforms-field-recaptcha"),i=v("#wpforms-panel-field-settings-recaptcha"),o=i.data("provider")||"recaptcha";e.length&&(i.is(":checked")?e.show().toggleClass("is-recaptcha","recaptcha"===o):e.hide())},initConfirmationsType(){v(".wpforms-panel-field-confirmations-type").each(function(){k.confirmationFieldsToggle(v(this))})},initElementsTinyMCE(e){"undefined"!=typeof tinymce&&void 0!==wp.editor&&e.each(function(){var e=v(this).attr("id"),i=(wp.editor.remove(e),{...m.tinymceDefaults});i.tinymce.toolbar1.includes("wpf_insert_smart_tag")||(i.tinymce.toolbar1+=",wpf_insert_smart_tag"),i.tinymce.setup=function(e){e.addButton("wpf_insert_smart_tag",{text:"",tooltip:wpforms_builder.smart_tags_dropdown_title,icon:!1,image:wpforms_builder.smart_tags_dropdown_mce_icon,classes:"wpforms-smart-tags-mce-button"})},wp.editor.initialize(e,i)})},confirmationsSetup(){k.initConfirmationsType(),k.initElementsTinyMCE(v(".wpforms-panel-field-confirmations-message")),y.on("focusout",".wpforms-panel-field-confirmations-redirect",function(e){const i=v(this);var o=i.val().trim();i.val(o),wpf.isURL(o)||""===o||k.confirmationRedirectValidationError(function(){i.trigger("focus")})}),y.on("wpformsBeforeSave wpformsPanelSectionSwitch wpformsPanelSwitch",function(t){v(".wpforms-confirmation").each(function(e,i){i=v(i);const o=i.find(".wpforms-panel-field-confirmations-redirect");if(!o.is(":hidden")){i=i.find(".wpforms-panel-field-confirmations-type");if(!("redirect"!==i.val()||0<o.val().trim().length))return k.confirmationRedirectValidationError(function(){o.trigger("focus")}),t.stopImmediatePropagation(),t.preventDefault(),!1}})})},confirmationRedirectValidationError(e){v.confirm({title:wpforms_builder.heads_up,content:wpforms_builder.redirect_url_field_error,icon:"fa fa-exclamation-circle",type:"orange",buttons:{confirm:{text:wpforms_builder.ok,btnClass:"btn-confirm",keys:["enter"]}},onDestroy:e})},confirmationFieldsToggle(e){var i,o;e.length&&(i=e.val(),(o=e.closest(".wpforms-builder-settings-block-content")).find(".wpforms-panel-field").not(e.parent()).not(".wpforms-conditionals-enable-toggle").hide(),o.find(".wpforms-panel-field-confirmations-"+i).closest(".wpforms-panel-field").show(),"message"===i&&(o.find(".wpforms-panel-field-confirmations-message_scroll").closest(".wpforms-panel-field").show(),o.find(".wpforms-panel-field-confirmations-message_entry_preview").trigger("change").closest(".wpforms-panel-field").show(),o.find(".wpforms-panel-field-confirmations-message_order_summary").closest(".wpforms-panel-field").toggle(0!==v("#wpforms-panel-fields .wpforms-field-payment-total").length)),v("#wpforms-panel-field-settings-ajax_submit").is(":checked"))&&o.find(".wpforms-panel-field-confirmations-redirect_new_tab").closest(".wpforms-panel-field").toggle(["redirect","page"].includes(i))},confirmationEntryPreviewToggle(){var e=v(this),i=e.closest(".wpforms-builder-settings-block-content").find(".wpforms-panel-field-confirmations-message_entry_preview_style").parent();e.is(":checked")?i.show():i.hide()},notificationToggle(){var e=v("#wpforms-panel-field-settings-notification_enable"),i=e.closest(".wpforms-panel-content-section").find(".wpforms-builder-settings-block"),e=e.is(":checked");v(".wpforms-notifications-add").toggleClass("wpforms-hidden",!e),y.trigger("wpformsNotificationsToggle",[e]),e?i.show():i.hide()},notificationsByStatusAlerts(){y.on("change",".wpforms-panel-content-section-notifications .wpforms-notification-by-status-alert",function(e){var i=v(this);if(i.prop("checked")){var o=v(".wpforms-radio-group-"+i.attr("data-radio-group")+":checked:not(#"+i.attr("id")+")");let e;e=(e=0===o.length?wpforms_builder.notification_by_status_enable_alert:(e=wpforms_builder.notification_by_status_switch_alert).replace(/%2\$s/g,o.data("provider-title"))).replace(/%1\$s/g,i.data("provider-title")),v.confirm({title:wpforms_builder.heads_up,content:e,icon:"fa fa-exclamation-circle",type:"orange",buttons:{confirm:{text:wpforms_builder.ok,btnClass:"btn-confirm"}}})}})},settingsBlockAdd(l){const d=Number(l.attr("data-next-id")),p=l.closest(".wpforms-panel-content-section").data("panel"),f=l.data("block-type"),e=wpforms_builder[f+"_prompt"],i='<input autofocus="" type="text" id="settings-block-name" placeholder="'+wpforms_builder[f+"_ph"]+'">',o='<p class="error">'+wpforms_builder[f+"_error"]+"</p>",t=e+i+o,r=v.confirm({container:y,title:!1,content:t,icon:"fa fa-info-circle",type:"blue",buttons:{confirm:{text:wpforms_builder.ok,btnClass:"btn-confirm",keys:["enter"],action(){var e=this.$content.find("input#settings-block-name").val().toString().trim(),i=this.$content.find(".error");if(""===e)return i.show(),!1;var i=l.closest(".wpforms-panel-content-section").find(".wpforms-builder-settings-block").first(),o=(wpf.restoreTooltips(i),i.clone()),t=i.data("block-id");let r;o.attr("data-block-id",d),o.find(".wpforms-builder-settings-block-name-holder span").text(e);o.find("input, textarea, select").each(function(){var e=v(this),i=e.parent();e.hasClass("wpforms-disabled")&&(i.hasClass("from-name")||i.hasClass("from-email"))||(i=e).attr("name")&&(i.val("").attr("name",i.attr("name").replace(/\[(\d+)\]/,"["+d+"]")),i.is("select")?(i.find("option").prop("selected",!1).attr("selected",!1),i.find("option").first().prop("selected",!0).attr("selected","selected")):"checkbox"===i.attr("type")?(e=i.closest(".wpforms-panel-field").hasClass("js-wpforms-enabled-notification"),i.prop("checked",e).attr("checked",e).val("1")):i.val("").attr("value",""))});const s="wpforms-panel-field-"+p+"-",n=s+t,a=(o.find('[id^="'+n+'"], [for^="'+n+'"]').each(function(e,i){var o=v(this),t="LABEL"===o.prop("tagName")?"for":"id",r=o.attr(t).replace(new RegExp(n,"g"),s+d);o.attr(t,r)}),t+"-notification-by-status");o.find('[data-radio-group="'+a+'"]').each(function(e,i){v(this).removeClass("wpforms-radio-group-"+a).addClass("wpforms-radio-group-"+d+"-notification-by-status").attr("data-radio-group",d+"-notification-by-status")}),o.find(".wpforms-builder-settings-block-name-holder input").val(e).attr("value",e),"notification"===f&&(o.find(".email-msg textarea").val("{all_fields}").text("{all_fields}").attr("value","{all_fields}"),o.find(".email-recipient input").val("{admin_email}").attr("value","{admin_email}")),o.removeClass("wpforms-builder-settings-block-default"),"confirmation"===f&&(o.find(".wpforms-panel-field-tinymce").remove(),"undefined"!=typeof WPForms)&&o.find(".wpforms-panel-field-confirmations-type-wrap").after(WPForms.Admin.Builder.Templates.get("wpforms-builder-confirmations-message-field")({id:d}));t=o.find(".wpforms-conditional-block"),t.length&&"undefined"!=typeof WPForms&&t.html(WPForms.Admin.Builder.Templates.get("wpforms-builder-conditional-logic-toggle-field")({id:d,type:f,actions:JSON.stringify(o.find(".wpforms-panel-field-conditional_logic-checkbox").data("actions")),actionDesc:o.find(".wpforms-panel-field-conditional_logic-checkbox").data("action-desc"),reference:o.find(".wpforms-panel-field-conditional_logic-checkbox").data("reference")})),e=o.find(".wpforms-field-map-table"),e.length&&e.each(function(e,i){var i=v(i),o=(i.find("tr:not(:first-child)").remove(),i.find(".key input")),i=i.find(".field select"),t=i.data("name");o.attr("value",""),i.attr("name","").attr("data-name",t.replace(/\[(\d+)\]/,"["+d+"]"))}),r=(r=o.wrap("<div>").parent().html()).replace(/\[conditionals\]\[(\d+)\]\[(\d+)\]/g,"[conditionals][0][0]"),i.before(r),t=i.prev();"confirmation"===f&&(k.prepareChoicesJSField(t,d),k.confirmationFieldsToggle(v(".wpforms-panel-field-confirmations-type").first())),"undefined"!=typeof tinymce&&void 0!==wp.editor&&"confirmation"===f&&wp.editor.initialize("wpforms-panel-field-confirmations-message-"+d,m.tinymceDefaults),wpf.initTooltips(),y.trigger("wpformsSettingsBlockAdded",[t]),l.attr("data-next-id",d+1)}},cancel:{text:wpforms_builder.cancel}}});y.on("keypress","#settings-block-name",function(e){13===e.keyCode&&v(r.buttons.confirm.el).trigger("click")})},prepareChoicesJSField(e,i){var o,e=e.find(`#wpforms-panel-field-confirmations-${i}-page-wrap`);e.length<=0||(i=e.find(`#wpforms-panel-field-confirmations-${i}-page`)).length<=0&&!i.hasClass("choicesjs-select")||(o=e.find(".choices")).length<=0||((i=i.first()).removeAttr("data-choice"),i.removeAttr("hidden"),i.removeClass("choices__input"),v(i).appendTo(e.first()),o.first().remove(),k.dropdownField.events.choicesInit(i))},settingsBlockNameEditingShow(e){e=e.parents(".wpforms-builder-settings-block-name-holder");e.find(".wpforms-builder-settings-block-name").addClass("editing").hide(),e.find(".wpforms-builder-settings-block-name-edit").addClass("active"),wpf.focusCaretToEnd(e.find("input"))},settingsBlockNameEditingHide(e){var i=e.parents(".wpforms-builder-settings-block-header"),o=i.find(".wpforms-builder-settings-block-name"),i=i.find(".wpforms-builder-settings-block-name-edit");let t=i.find("input").val().trim();e=e.closest(".wpforms-builder-settings-block").data("block-type");t.length||(t=wpforms_builder[e+"_def_name"]),i.find("input").val(t),o.text(t),o.removeClass("editing").show(),i.removeClass("active")},settingsBlockPanelClone(e){const i=e.closest(".wpforms-panel-content-section"),o=i.find(".wpforms-builder-settings-block-add"),t=e.closest(".wpforms-builder-settings-block"),r=t.find(".wpforms-builder-settings-block-content"),s=parseInt(o.attr("data-next-id"),10),n=t.data("block-type"),a=t.find(".wpforms-builder-settings-block-name").text().trim()+wpforms_builder[n+"_clone"],l=r.is(":hidden"),d=(wpf.restoreTooltips(t),t.clone(!1,!0));k.settingsBlockUpdateState(l,s,n),d.data("block-id",s),d.find(".wpforms-builder-settings-block-name-holder span").text(a),d.find(".wpforms-builder-settings-block-name-holder input").val(a),d.removeClass("wpforms-builder-settings-block-default"),o.attr("data-next-id",s+1),d.find("input, textarea, select").each(function(){var e=v(this);e.attr("name")&&e.attr("name",e.attr("name").replace(/\[(\d+)\]/,"["+s+"]")),e.data("name")&&e.data("name",e.data("name").replace(/\[(\d+)\]/,"["+s+"]")),e.attr("class")&&e.attr("class",e.attr("class").replace(/-(\d+)/,"-"+s)),e.attr("data-radio-group")&&e.attr("data-radio-group",e.attr("data-radio-group").replace(/(\d+)-/,s+"-"))}),d.find("*").each(function(){var e=v(this);e.attr("id")&&e.attr("id",e.attr("id").replace(/-(\d+)/,"-"+s)),e.attr("for")&&e.attr("for",e.attr("for").replace(/-(\d+)-/,"-"+s+"-")),e.data("input-name")&&e.data("input-name",e.data("input-name").replace(/\[(\d+)\]/,"["+s+"]"))}),t.find("select").each(function(){var e=v(this).attr("name"),i=v(this).attr("name").replace(/\[(\d+)\]/,"["+s+"]");d.find('select[name="'+i+'"]').val(v(this).attr("name",e).val())}),d.css("display","none").insertBefore(t).show("fast",function(){wpf.initTooltips()}),y.trigger("wpformsSettingsBlockCloned",[d,t.data("block-id")])},settingsBlockPanelToggle(e){const i=e.closest(".wpforms-builder-settings-block"),o=i.data("block-id"),t=i.data("block-type"),r=i.find(".wpforms-builder-settings-block-content"),s=r.is(":visible");r.stop().slideToggle({duration:400,start(){k.settingsBlockUpdateState(s,o,t)},always(){r.is(":visible")?e.html('<i class="fa fa-chevron-circle-up"></i>'):e.html('<i class="fa fa-chevron-circle-down"></i>')}})},settingsBlockDelete(e){var i=e.closest(".wpforms-panel-content-section");if(!(i.find(".wpforms-builder-settings-block").length<2)){const o=e.closest(".wpforms-builder-settings-block"),t=o.data("block-type");v.confirm({title:!1,content:wpforms_builder[t+"_delete"],icon:"fa fa-exclamation-circle",type:"orange",buttons:{confirm:{text:wpforms_builder.ok,btnClass:"btn-confirm",keys:["enter"],action(){var e=o.data("block-id"),i=o.data("block-type");v.post(wpforms_builder.ajax_url,{action:"wpforms_builder_settings_block_state_remove",nonce:wpforms_builder.nonce,block_id:e,block_type:i,form_id:m.formID}),o.remove(),y.trigger("wpformsSettingsBlockDeleted",[t,e])}},cancel:{text:wpforms_builder.cancel}}})}},settingsBlockUpdateState(e,i,o){v.post(wpforms_builder.ajax_url,{action:"wpforms_builder_settings_block_state_save",state:e?"closed":"opened",form_id:m.formID,block_id:i,block_type:o,nonce:wpforms_builder.nonce})},notificationsUpdateElementsVisibility(){console.warn('WARNING! Function "WPFormsBuilder.notificationsUpdateElementsVisibility()" has been deprecated.')},notificationUpdateStatus(e){var i=e.data("block-id"),i=v(`#wpforms-panel-field-notifications-${i}-enable`),e=e.find(".wpforms-builder-settings-block-status");k.changeStatusButton(e,"0"!==i.val()),i.val()||i.val("1")},notificationChangeStatus(e){var i=e.closest(".wpforms-notification").data("block-id"),i=v(`#wpforms-panel-field-notifications-${i}-enable`),o=e.data("active");k.changeStatusButton(e,!o),i.val(o?"0":"1")},changeStatusButton(e,i){e.removeClass(["wpforms-badge-green","wpforms-badge-silver"]);var o=e.find(".fa"),t=e.find(".wpforms-status-label");o.removeClass(["fa-check","fa-times"]),i?(e.addClass("wpforms-badge-green"),o.addClass("fa-check"),t.text(wpforms_builder.active),e.attr("title",wpforms_builder.deactivate)):(e.addClass("wpforms-badge-silver"),o.addClass("fa-times"),t.text(wpforms_builder.inactive),e.attr("title",wpforms_builder.activate)),e.data("active",i)},bindUIActionsRevisions(){y.on("wpformsPanelSwitched",function(e,i){"revisions"===i&&(k.updateRevisionsList(),k.updateRevisionPreview())}),y.on("wpformsSaved",function(e){"revisions"===wpf.getQueryString("view")&&k.updateRevisionsList()})},updateRevisionsList(){const i=v(".wpforms-panel-revisions-button .badge-exclamation");if(i.length&&v.post(wpforms_builder.ajax_url,{action:"wpforms_mark_panel_viewed",form_id:m.formID,nonce:wpforms_builder.nonce}).done(function(e){e.success?i.remove():wpf.debug(e)}).fail(function(e,i,o){wpf.debug(e.responseText||i||"")}),y.hasClass("wpforms-revisions-enabled")){const t=v("#wpforms-panel-revisions .wpforms-revisions-content");t.fadeTo(250,.25,function(){v.post(wpforms_builder.ajax_url,{action:"wpforms_get_form_revisions",form_id:m.formID,revision_id:wpf.getQueryString("revision_id"),nonce:wpforms_builder.nonce}).done(function(e){e.success?t.replaceWith(e.data.html):wpf.debug(e)}).fail(function(e,i,o){wpf.debug(e.responseText||i||""),t.fadeTo(250,1)})})}},updateRevisionPreview(){var e=C.$formPreview.clone();e.find(".wpforms-field-duplicate, .wpforms-field-delete, .wpforms-field-helper, .wpforms-debug").remove().end(),e.find(".wpforms-field-wrap").removeClass("ui-sortable").addClass("ui-sortable-disabled"),e.find(".wpforms-field").removeClass("ui-sortable-handle ui-draggable ui-draggable-handle active").removeAttr("id data-field-id data-field-type").removeData(),e.find(".wpforms-field-submit-button").prop("disabled",!0),C.$revisionPreview.hasClass("has-preview")?C.$revisionPreview.find(".wpforms-preview-wrap").replaceWith(e):C.$revisionPreview.append(e).addClass("has-preview")},confirmSaveRevision(){v.confirm({title:wpforms_builder.heads_up,content:wpforms_builder.revision_update_confirm,icon:"fa fa-exclamation-circle",type:"orange",closeIcon:!1,buttons:{confirm:{text:wpforms_builder.save,btnClass:"btn-confirm",keys:["enter"],action(){y.addClass("wpforms-revision-is-saving"),WPFormsBuilder.formSave(!1).done(k.revisionSavedReload)}},cancel:{text:wpforms_builder.cancel,action(){WPFormsBuilder.setCloseConfirmation(!0)}}}})},revisionSavedReload(){wpf.updateQueryString("view",wpf.getQueryString("view")),wpf.removeQueryParam("revision_id"),a.location.reload()},bindUIActionsSaveExit(){y.on("click","#wpforms-embed",function(e){e.preventDefault(),v(this).hasClass("wpforms-disabled")||v(this).hasClass("wpforms-btn-light-grey-disabled")||WPFormsFormEmbedWizard.openPopup()}),y.on("click","#wpforms-save",function(e){e.preventDefault(),k.formSave(!1)}),y.on("click","#wpforms-exit",function(e){e.preventDefault(),k.formExit()}),y.on("wpformsSaved",function(e,i){wpf.removeQueryParam("newform")})},formSave(i){if(y.hasClass("wpforms-is-revision")&&!y.hasClass("wpforms-revision-is-saving"))k.confirmSaveRevision();else{"undefined"!=typeof tinyMCE&&tinyMCE.triggerSave();var e=WPFormsUtils.triggerEvent(y,"wpformsBeforeSave");if(!e.isDefaultPrevented()){const o=C.$saveButton,t=o.find("i.fa-check"),r=o.find("i.wpforms-loading-spinner"),s=o.find("span"),n=s.text();s.text(wpforms_builder.saving),o.prop("disabled",!0),t.addClass("wpforms-hidden"),r.removeClass("wpforms-hidden");e={action:"wpforms_save_form",data:JSON.stringify(k.serializeAllData(v("#wpforms-builder-form"))),id:m.formID,nonce:wpforms_builder.nonce};return v.post(wpforms_builder.ajax_url,e,function(e){e.success?(wpf.savedState=wpf.getFormState("#wpforms-builder-form"),wpf.initialSave=!1,y.trigger("wpformsSaved",e.data),!0===i&&k.isBuilderInPopup()?k.builderInPopupClose("saved"):!0===i&&(a.location.href=wpforms_builder.exit_url)):(wpf.debug(e),k.formSaveError(e.data))}).fail(function(e,i,o){wpf.debug(e),k.formSaveError()}).always(function(){s.text(n),o.prop("disabled",!1),r.addClass("wpforms-hidden"),t.removeClass("wpforms-hidden")})}}},serializeAllData(e){const o=e.serializeArray();return e.find(".wpforms-field-option-layout .wpforms-field-option-row-label_hide input[type=checkbox]").each(function(){var e=v(this),i=e.attr("name"),e=e.is(":checked")?"1":"";e||o.push({name:i,value:e})}),o},formSaveError(e=""){wpf.empty(e)&&(e=wpforms_builder.error_save_form),v.confirm({title:wpforms_builder.heads_up,content:"<p>"+e+"</p><p>"+wpforms_builder.error_contact_support+"</p>",icon:"fa fa-exclamation-circle",type:"orange",buttons:{confirm:{text:wpforms_builder.ok,btnClass:"btn-confirm",keys:["enter"]}}})},formExit(){k.isBuilderInPopup()&&k.formIsSaved()?k.builderInPopupClose("saved"):k.formIsSaved()?a.location.href=wpforms_builder.exit_url:v.confirm({title:!1,content:wpforms_builder.exit_confirm,icon:"fa fa-exclamation-circle",type:"orange",closeIcon:!0,buttons:{confirm:{text:wpforms_builder.save_exit,btnClass:"btn-confirm",keys:["enter"],action(){k.formSave(!0)}},cancel:{text:wpforms_builder.exit,action(){o=!1,k.isBuilderInPopup()?k.builderInPopupClose("canceled"):a.location.href=wpforms_builder.exit_url}}}})},setCloseConfirmation(e){o=!!e},formIsSaved(){return wpf.savedState===wpf.getFormState("#wpforms-builder-form")},isBuilderInPopup(){return a.self!==a.parent&&"wpforms-builder-iframe"===a.self.frameElement.id},builderInPopupClose(e){var i=a.parent.jQuery(".wpforms-builder-popup"),o=v(".wpforms-center-form-name").text();i.find("#wpforms-builder-iframe").attr("src","about:blank"),i.fadeOut(),i.trigger("wpformsBuilderInPopupClose",[e,m.formID,o])},bindUIActionsGeneral(){y.on("click",".toggle-smart-tag-display",k.smartTagToggle),y.on("click",".smart-tags-list-display a",k.smartTagInsert),y.on("click",".wpforms-panel-fields-group.unfoldable .wpforms-panel-fields-group-title",k.toggleUnfoldableGroup),y.on("click",".wpforms-field-helper-hide ",k.hideFieldHelper),y.on("input",".wpforms-money-input",function(e){var i=v(this),o=i.val(),t=i[0].selectionStart,r=i[0].selectionEnd;i.val(o.replace(/[^0-9.,]/g,"")),i[0].setSelectionRange(t,r)}),y.on("focusout",".wpforms-money-input",function(e){var i=v(this),o=i.val();if(!o)return o;o=wpf.amountSanitize(o),o=wpf.amountFormat(o);i.val(o)}),y.on("change",".wpforms-panel-field-toggle",function(){var e=v(this);e.prop("disabled")||(e.prop("disabled",!0),k.toggleOptionsGroup(e))}),y.on("change",k.getPaymentsTogglesSelector(),function(e){var i=v(this),o=i.attr("id").replace(/wpforms-panel-field-|-enable|_one_time|_recurring/gi,""),t=v('.wpforms-panel-content-section-notifications [id*="-'+o+'-wrap"]');i.prop("checked")||v("#wpforms-panel-field-"+o+"-enable_one_time").prop("checked")||v("#wpforms-panel-field-"+o+"-enable_recurring").prop("checked")?v("#wpforms-panel-field-settings-disable_entries").prop("checked")?(v.confirm({title:wpforms_builder.heads_up,content:wpforms_builder.payments_entries_off,icon:"fa fa-exclamation-circle",type:"orange",buttons:{confirm:{text:wpforms_builder.ok,btnClass:"btn-confirm",keys:["enter"]}}}),i.prop("checked",!1)):t.removeClass("wpforms-hidden"):(t.addClass("wpforms-hidden"),t.find('input[id*="-'+o+'"]').prop("checked",!1))}),y.on("change","#wpforms-panel-field-settings-disable_entries",function(e){var i=v(this);v("#wpforms-panel-field-settings-store_spam_entries-wrap").toggleClass("wpforms-hidden",i.prop("checked")),i.prop("checked")&&(k.isPaymentsEnabled()?(v.confirm({title:wpforms_builder.heads_up,content:wpforms_builder.payments_on_entries_off,icon:"fa fa-exclamation-circle",type:"orange",buttons:{confirm:{text:wpforms_builder.ok,btnClass:"btn-confirm",keys:["enter"]}}}),i.prop("checked",!1)):v.alert({title:wpforms_builder.heads_up,content:wpforms_builder.disable_entries,icon:"fa fa-exclamation-circle",type:"orange",buttons:{confirm:{text:wpforms_builder.ok,btnClass:"btn-confirm",keys:["enter"]}}}))}),y.on("click",".wpforms-image-upload-add",function(e){e.preventDefault();const o=v(this),t=o.parent(),r=wpf.initMediaLibrary({title:wpforms_builder.upload_image_title,extensions:wpforms_builder.upload_image_extensions,extensionsError:wpforms_builder.upload_image_extensions_error,buttonText:wpforms_builder.upload_image_button});r.on("select",function(){var e=r.state().get("selection").first().toJSON(),i=t.find(".preview");t.find(".source").val(e.url),i.empty(),i.prepend('<img src="'+e.url+'" alt=""><a href="#" title="'+wpforms_builder.upload_image_remove+'" class="wpforms-image-upload-remove"><i class="fa fa-trash-o"></i></a>'),"hide"===o.data("after-upload")&&o.hide(),y.trigger("wpformsImageUploadAdd",[o,t])}).on("close",function(){r.off("library:selection:add")}),r.open()}),y.on("click",".wpforms-image-upload-remove",function(e){e.preventDefault();e=v(this).parent().parent();e.find(".preview").empty(),e.find(".wpforms-image-upload-add").show(),e.find(".source").val(""),y.trigger("wpformsImageUploadRemove",[v(this),e])}),y.on("blur",'.wpforms-notification .wpforms-panel-field-text input:not([type="search"])',function(){k.validateEmailSmartTags(v(this))}),y.on("blur",".wpforms-notification .wpforms-panel-field-textarea textarea",function(){k.validateEmailSmartTags(v(this))}),y.on("focusout",'.wpforms-notification .wpforms-panel-field.js-wpforms-from-email-validation input:not([type="search"])',k.validateFromEmail),y.on("wpformsPanelSectionSwitch",k.notificationsPanelSectionSwitch),y.on("click","#wpforms-builder-mobile-notice .wpforms-fullscreen-notice-button-primary, #wpforms-builder-mobile-notice .close",function(){a.location.href=wpforms_builder.exit_url}),y.on("click","#wpforms-builder-mobile-notice .wpforms-fullscreen-notice-button-secondary",function(){a.location.href=wpf.updateQueryString("force_desktop_view",1,a.location.href)}),v("#wpforms-builder-license-alert .close").on("click",function(){a.location.href=wpforms_builder.exit_url}),v("#wpforms-builder-license-alert .dismiss").on("click",function(e){e.preventDefault(),v("#wpforms-builder-license-alert").remove(),wpCookies.set("wpforms-builder-license-alert","true",3600)}),y.on("change","#wpforms-panel-field-settings-akismet.wpforms-akismet-disabled",function(e){const i=v(this),o=i.data("akismet-status");i.prop("checked")&&v.alert({title:wpforms_builder.heads_up,content:wpforms_builder[o],icon:"fa fa-exclamation-circle",type:"orange",buttons:{confirm:{text:wpforms_builder.ok,btnClass:"btn-confirm",keys:["enter"]}},onClose(){i.prop("checked",!1)}})}),y.on("wpformsPanelSectionSwitch wpformsPanelSwitched",function(){wpf.reInitShowMoreChoices(v("#wpforms-panel-providers, #wpforms-panel-settings"))})},notificationsPanelSectionSwitch(e,i){"notifications"===i&&v(".wpforms-notification .wpforms-panel-field.js-wpforms-from-email-validation input").trigger("focusout")},isPaymentsEnabled(){let e=!1;return v(k.getPaymentsTogglesSelector()).each(function(){if(v(this).prop("checked"))return!(e=!0)}),e},getPaymentsTogglesSelector(){return`.wpforms-panel-content-section-payment-toggle-one-time input,
			.wpforms-panel-content-section-payment-toggle-recurring input,
			#wpforms-panel-field-stripe-enable,
			#wpforms-panel-field-paypal_standard-enable,
			#wpforms-panel-field-authorize_net-enable,
			#wpforms-panel-field-square-enable`},toggleOptionsGroup(e){var i=e.attr("name");let o="";function t(){e.prop("disabled",!1)}const r=v('.wpforms-panel-field-toggle-body[data-toggle="'+i+'"]');k.toggleProviderActiveIcon(e),0===r.length?t():(i=e.attr("type"),o="checkbox"!==i&&"radio"!==i||e.prop("checked")?e.val():"0",r.each(function(){var e=v(this);e.attr("data-toggle-value").toString()===o.toString()?e.slideDown("",t):e.slideUp("",t)}))},toggleProviderActiveIcon(e){var o=e.closest(".wpforms-panel-content-section").data("provider"),t=["wpforms-panel-field-"+o+"-enable-wrap","wpforms-panel-field-"+o+"-enable_one_time-wrap","wpforms-panel-field-"+o+"-enable_recurring-wrap"];if(o&&t.includes(e.attr("id"))){let i=!1;t.forEach(e=>{e=v("#"+e);e.length&&e.find("input").is(":checked")&&(i=!0)}),v(`.wpforms-panel-sidebar-section[data-section=${o}]`).find(".fa-check-circle-o").toggleClass("wpforms-hidden",!i)}},toggleAllOptionGroups(e){(e=e||y||v("#wpforms-builder")||v("body"))&&e.find(".wpforms-panel-field-toggle").each(function(){var e=v(this);e.prop("disabled",!0),k.toggleOptionsGroup(e)})},toggleUnfoldableGroup(e){e.preventDefault();const i=v(e.target),o=i.closest(".wpforms-panel-fields-group"),t=o.find(".wpforms-panel-fields-group-inner"),r="wpforms_fields_group_"+o.data("group");o.hasClass("opened")?(wpCookies.remove(r),t.stop().slideUp(150,function(){o.removeClass("opened")})):(wpCookies.set(r,"true",2592e3),o.addClass("opened"),t.stop().slideDown(150))},hideFieldHelper(e){e.preventDefault(),e.stopPropagation();e=v(".wpforms-field-helper");wpCookies.set("wpforms_field_helper_hide","true",2592e3),e.hide()},smartTagToggle(e){console.warn('WARNING! Function "WPFormsBuilder.smartTagToggle()" has been deprecated.'),e.preventDefault(),C.$focusOutTarget=null;var e=v(this),i=e.closest(".wpforms-panel-field,.wpforms-field-option-row");i.hasClass("smart-tags-toggling")||(i.addClass("smart-tags-toggling"),e.hasClass("smart-tag-showing")?k.removeSmartTagsList(e):k.insertSmartTagsList(e))},removeSmartTagsList(e){console.warn('WARNING! Function "WPFormsBuilder.removeSmartTagsList()" has been deprecated.');const i=e.closest(".wpforms-panel-field,.wpforms-field-option-row"),o=i.find(".smart-tags-list-display");e.find("span").text(wpforms_builder.smart_tags_show),o.slideUp("",function(){o.remove(),e.removeClass("smart-tag-showing"),i.removeClass("smart-tags-toggling")})},insertSmartTagsList(e){console.warn('WARNING! Function "WPFormsBuilder.insertSmartTagsList()" has been deprecated.');const i=e.closest(".wpforms-panel-field,.wpforms-field-option-row");let o=e.closest("label"),t=!0;o.length||(o=i.find("label"),t=!1);var r=k.getSmartTagsList(e,-1!==o.attr("for").indexOf("wpforms-field-option-"));(t?o:e).after(r),e.find("span").text(wpforms_builder.smart_tags_hide),i.find(".smart-tags-list-display").slideDown("",function(){e.addClass("smart-tag-showing"),i.removeClass("smart-tags-toggling")})},getSmartTagsList(e,i){var o;return console.warn('WARNING! Function "WPFormsBuilder.getSmartTagsList()" has been deprecated.'),o='<ul class="smart-tags-list-display unfoldable-cont">',(o+=k.getSmartTagsListFieldsElements(e))+k.getSmartTagsListOtherElements(e,i)+"</ul>"},getSmartTagsListFieldsElements(e){console.warn('WARNING! Function "WPFormsBuilder.getSmartTagsListFieldsElements()" has been deprecated.');var i=e.data("type");if(!["fields","all"].includes(i))return"";var o=k.getSmartTagsFields(e);if(!o)return'<li class="heading">'+wpforms_builder.fields_unavailable+"</li>";let t="";t+='<li class="heading">'+wpforms_builder.fields_available+"</li>";for(const r in o)t+=k.getSmartTagsListFieldsElement(o[r]);return t},getSmartTagsFields(e){console.warn('WARNING! Function "WPFormsBuilder.getSmartTagsFields()" has been deprecated.');var i=e.data("fields"),e=e.data("allow-repeated-fields"),i=i?i.split(","):void 0;return wpf.getFields(i,!0,e)},getSmartTagsListFieldsElement(o){console.warn('WARNING! Function "WPFormsBuilder.getSmartTagsListFieldsElement()" has been deprecated.');const t=o.label?wpf.encodeHTMLEntities(wpf.sanitizeHTML(o.label)):wpforms_builder.field+" #"+o.id;let r=`<li><a href="#" data-type="field" data-meta="${o.id}">${t}</a></li>`;var e=o.additional||[];return 1<e.length&&e.forEach(e=>{var i=e.charAt(0).toUpperCase()+e.slice(1).replace(/(\D)(\d)/g,"$1 $2");r+=`<li><a href="#" data-type="field" data-meta="${o.id}" data-additional='${e}'>${t} – ${i}</a></li>`}),r},getSmartTagsListOtherElements(e,i){console.warn('WARNING! Function "WPFormsBuilder.getSmartTagsListOtherElements()" has been deprecated.');var o=e.data("type");let t;if("other"!==o&&"all"!==o)return"";t='<li class="heading">'+wpforms_builder.other+"</li>";for(const r in wpforms_builder.smart_tags)i&&wpforms_builder.smart_tags_disabled_for_fields.includes(r)||"confirmations"===e.data("location")&&wpforms_builder.smart_tags_disabled_for_confirmations.includes(r)||(t+='<li><a href="#" data-type="other" data-meta=\''+r+"'>"+wpforms_builder.smart_tags[r]+"</a></li>");return t},smartTagInsert(e){console.warn('WARNING! Function "WPFormsBuilder.smartTagInsert()" has been deprecated.'),e.preventDefault();const i=v(this),o=i.closest(".smart-tags-list-display"),t=o.closest(".wpforms-panel-field,.wpforms-field-option-row"),r=t.find(".toggle-smart-tag-display"),s=t.find("input[type=text], textarea"),n=i.data("meta"),a=i.data("additional")?"|"+i.data("additional"):"",l=i.data("type");let d="field"===l?'{field_id="'+n+a+'"}':"{"+n+"}",p;"undefined"!=typeof tinyMCE&&(p=tinyMCE.get(s.prop("id")))&&!p.hasFocus()&&p.focus(!0),p&&!p.isHidden()?p.insertContent(d):(s.insertAtCaret(" "+d+" "),s.val(s.val().trim().replace("  "," ")),s.trigger("focus").trigger("input")),o.slideUp("",function(){o.remove()}),r.find("span").text(wpforms_builder.smart_tags_show),t.find(".toggle-smart-tag-display").removeClass("smart-tag-showing")},validateEmailSmartTags(e){console.warn('WARNING! Function "WPFormsBuilder.validateEmailSmartTags()" has been deprecated.');let i=e.val();i&&(i=i.replace(/{(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))}/g,function(e){return e.slice(1,-1)}),e.val(i))},getEmailFieldSmartTagError(e){var i=/\{field_id="(\d+)"\}/g;if(!i.test(e))return null;i.lastIndex=0;i=i.exec(e),e=i?i[1]:null,i=wpf.getField(e);return i&&"email"===i.type?"":wpforms_builder.allow_only_email_fields},validateFromEmail(){var e,i,o;wpf.isRepeatedCall("validateFromEmail")||(e=(i=v(this)).val(),i.data("value")!==e&&(i.data("value",e),i=i.parent(),o="wpforms-panel-field-warning",/[\s,;]/g.test(e.trim())?(i.addClass(o),k.printNotice(wpforms_builder.allow_only_one_email,i)):k.shouldCallAjaxValidation(e,i,o)&&k.ajaxValidation(e,i,o)))},shouldCallAjaxValidation(e,i,o){let t="",r=!0;return""===(t=""===e?wpforms_builder.empty_email_address:"")&&(t=k.getEmailFieldSmartTagError(e),r=null===t),t?(i.addClass(o),k.printNotice(t,i,""===e),!1):!!r||(i.removeClass(o),k.removeNotice(i),!1)},ajaxValidation(e,i,o){e={form_id:m.formID,email:e,nonce:wpforms_builder.nonce,action:"wpforms_builder_notification_from_email_validate"};v.post(wpforms_builder.ajax_url,e,function(e){k.removeNotice(i),e.success?i.removeClass(o):(i.addClass(o),i.append(e.data))}).fail(function(e,i,o){console.log(e.responseText)})},disabledFields:{init(){k.disabledFields.initCouponsChoicesJS(),k.disabledFields.initFileUploadChoicesJS()},initCouponsChoicesJS(){"function"!=typeof a.Choices||WPForms.Admin.Builder.Coupons||v(".wpforms-field-option-row-allowed_coupons select:not(.choices__input)").each(function(){var e=v(this),i=new Choices(e.get(0),{shouldSort:!1,removeItemButton:!0,renderChoicesLimit:5,callbackOnInit(){wpf.showMoreButtonForChoices(this.containerOuter.element)}});e.data("choicesjs",i)})},initFileUploadChoicesJS(){"function"!=typeof a.Choices||WPForms.Admin.Builder.FieldFileUpload||v(".wpforms-file-upload-user-roles-select, .wpforms-file-upload-user-names-select").each(function(){new Choices(v(this)[0],{removeItemButton:!0})})}},iconChoices:{cache:{},config:{colorPropertyName:"--wpforms-icon-choices-color"},init(){k.iconChoices.extendJqueryConfirm(),y.on("wpformsBuilderReady",function(e){wpforms_builder.icon_choices.is_active&&!wpforms_builder.icon_choices.is_installed&&(k.iconChoices.openInstallPromptModal(!0),e.preventDefault())}),y.on("change",".wpforms-field-option-row-choices_icons input",k.iconChoices.toggleIconChoices),y.on("change",".wpforms-field-option-row-choices_icons_color .wpforms-color-picker",k.iconChoices.changeIconsColor),y.on("change",".wpforms-field-option-row-choices_icons_style select, .wpforms-field-option-row-choices_icons_size select",function(){var e=v(this).parent().data("field-id"),i=v("#wpforms-field-option-"+e).find(".wpforms-field-option-hidden-type").val();k.fieldChoiceUpdate(i,e)}),y.on("click",".wpforms-field-option-row-choices .choices-list .wpforms-icon-select",k.iconChoices.openIconPickerModal)},toggleIconChoices(){var e,i,o,t=v(this),r=t.is(":checked");r&&!wpforms_builder.icon_choices.is_installed?(k.iconChoices.cache.toggle=t,k.iconChoices.openInstallPromptModal()):(t=t.closest(".wpforms-field-option-row").data("field-id"),o=(e=v("#wpforms-field-option-"+t)).find(`#wpforms-field-option-${t}-choices_images`),i=e.find(`#wpforms-field-option-row-${t}-choices ul`),r&&o.is(":checked")&&o.prop("checked",!1).trigger("change"),e.find(`#wpforms-field-option-row-${t}-dynamic_choices`).toggleClass("wpforms-hidden",r),e.find(`#wpforms-field-option-row-${t}-choices_icons_color`).toggleClass("wpforms-hidden"),e.find(`#wpforms-field-option-row-${t}-choices_icons_size`).toggleClass("wpforms-hidden"),e.find(`#wpforms-field-option-row-${t}-choices_icons_style`).toggleClass("wpforms-hidden"),o=e.find(`#wpforms-field-option-${t}-choices_icons_color`),o=_.isEmpty(o.val())?wpforms_builder.icon_choices.default_color:o.val(),i.prop("style",k.iconChoices.config.colorPropertyName+`: ${o};`),i.toggleClass("show-icons",r),e.find(`#wpforms-field-option-${t}-input_columns`).val(r?"inline":"").trigger("change"),k.fieldChoiceUpdate(e.find(".wpforms-field-option-hidden-type").val(),t))},changeIconsColor(){var e=v(this),i=e.parents(".wpforms-field-option-row").data("field-id"),o=v("#wpforms-field-option-"+i),t=o.find(".wpforms-field-option-hidden-type").val(),o=o.find(".wpforms-field-option-row-choices .choices-list"),e=k.getValidColorPickerValue(e);o.prop("style",k.iconChoices.config.colorPropertyName+`: ${e};`),k.fieldChoiceUpdate(t,i)},openInstallPromptModal(e=!1){var i=e?wpforms_builder.icon_choices.strings.reinstall_prompt_content:wpforms_builder.icon_choices.strings.install_prompt_content,i=v.confirm({title:wpforms_builder.heads_up,content:i,icon:"fa fa-info-circle",type:"orange",buttons:{continue:{text:wpforms_builder.continue,btnClass:"btn-confirm",keys:["enter"],action(){return this.setIcon("fa fa-cloud-download"),this.setTitle(wpforms_builder.icon_choices.strings.install_title),this.setContent(wpforms_builder.icon_choices.strings.install_content),v.each(this.buttons,function(e,i){i.hide()}),k.iconChoices.installIconLibrary(),!1}}},onOpen(){!e&&k.iconChoices.cache.toggle&&k.iconChoices.cache.toggle.prop("checked",!1),k.iconChoices.cache.previousModal=this}});e||(i.buttons.cancel={text:wpforms_builder.cancel,keys:["esc"],action(){k.iconChoices.cache.toggle.prop("checked",!1)}})},installIconLibrary(){var e={_wp_http_referer:wpf.updateQueryString("_wp_http_referer",null),nonce:wpforms_builder.nonce,action:"wpforms_icon_choices_install"};v.ajaxSetup({type:"POST",timeout:12e4}),v.post(wpforms_builder.ajax_url,e,function(e){e.success?k.iconChoices.openInstallSuccessModal():k.iconChoices.openInstallErrorModal(e)}).fail(function(e){k.iconChoices.openInstallErrorModal(e)})},openInstallSuccessModal(){v.confirm({title:wpforms_builder.done,content:wpforms_builder.icon_choices.strings.install_success_content,icon:"fa fa-check-circle",type:"green",buttons:{confirm:{text:wpforms_builder.ok,btnClass:"btn-confirm",keys:["enter"],action(){var e;k.iconChoices.cache.toggle&&(k.iconChoices.cache.toggle.prop("checked",!0),e=k.iconChoices.cache.toggle.parents(".wpforms-field-option-row").data("field-id"),(e=y.find(`#wpforms-field-option-${e}-choices_images`)).is(":checked"))&&e.prop("checked",!1),k.formSave(!1).done(function(){a.location.reload()})}}},onOpen(){var e;k.iconChoices.cache.toggle&&(e=k.iconChoices.cache.toggle.parents(".wpforms-field-option-row-choices_icons").data("field-id"),y.find(`#wpforms-field-option-${e}-input_columns`).val("inline")),k.iconChoices.cache.previousModal.close()}})},openInstallErrorModal(e){v.confirm({title:wpforms_builder.uh_oh,content:wpforms_builder.icon_choices.strings.install_error_content,icon:"fa fa-exclamation-circle",type:"red",buttons:{confirm:{text:wpforms_builder.ok,btnClass:"btn-confirm",keys:["enter"],action(){k.iconChoices.cache.toggle?k.iconChoices.cache.toggle.prop("checked",!1):k.formSaveError()}}},onOpen(){wpf.debug(e),k.iconChoices.cache.previousModal.close()},onDestroy(){delete k.iconChoices.cache.previousModal,delete k.iconChoices.cache.toggle}})},extendJqueryConfirm(){a.Jconfirm.prototype._updateContentMaxHeight=function(){var e=v(a).height()-(this.$jconfirmBox.outerHeight()-this.$contentPane.outerHeight())-(this.offsetTop+this.offsetBottom),i=this.contentMaxHeight||e;this.$contentPane.css({"max-height":Math.min(i,e)+"px"})}},openIconPickerModal(){var e=v(this);const i={fieldId:e.parents(".wpforms-field-option-row").data("field-id"),choiceId:e.parent().data("key"),selectedIcon:e.find(".source-icon").val(),selectedIconStyle:e.find(".source-icon-style").val()};var e=`
					${wpforms_builder.icon_choices.strings.icon_picker_title}
					<span class="wpforms-icon-picker-description">${wpforms_builder.icon_choices.strings.icon_picker_description}</span>
					<input type="text" placeholder="${wpforms_builder.icon_choices.strings.icon_picker_search_placeholder}" class="search" id="wpforms-icon-picker-search">
				`,o=`
					<div class="wpforms-icon-picker-container" id="wpforms-icon-picker-icons">
						<ul class="wpforms-icon-picker-icons" data-field-id="${i.fieldId}" data-choice-id="${i.choiceId}"></ul>
						<ul class="wpforms-icon-picker-pagination"></ul>
						<p class="wpforms-icon-picker-not-found wpforms-hidden" data-message="${wpforms_builder.icon_choices.strings.icon_picker_not_found}"></>
					</div>`;v.confirm({title:e,titleClass:"wpforms-icon-picker-title",content:o,icon:!1,closeIcon:!0,type:"orange",backgroundDismiss:!0,boxWidth:800,contentMaxHeight:368,smoothContent:!1,buttons:!1,onOpenBefore(){this.$body.addClass("wpforms-icon-picker-jconfirm-box"),this.$contentPane.addClass("wpforms-icon-picker-jconfirm-content-pane")},onContentReady(){const e=this;k.iconChoices.initIconsList(i),e.$title.find(".search").focus(),e.$content.find(".wpforms-icon-picker-icons").on("click","li",function(){k.iconChoices.selectIcon(e,v(this))})}})},initIconsList(o){var e={valueNames:["name"],listClass:"wpforms-icon-picker-icons",page:wpforms_builder.icon_choices.icons_per_page,pagination:{paginationClass:"wpforms-icon-picker-pagination"},item(e){var i=e.icon===o.selectedIcon&&e.style===o.selectedIconStyle?'class="selected"':"";return`
								<li data-icon="${e.icon}" data-icon-style="${e.style}"${i}>
									<i class="ic-fa-${e.style} ic-fa-${e.icon}"></i>
									<span class="name">${e.icon}</span>
								</li>`},indexAsync:!0};const t=new List("wpforms-icon-picker-icons",e,wpforms_builder.icon_choices.icons);k.iconChoices.infiniteScrollPagination(t),v("#wpforms-icon-picker-search").on("keyup",function(){t.search(v(this).val(),["name"],function(o,e){for(let e=0,i=t.items.length;e<i;e++)t.items[e].found=new RegExp(o).test(t.items[e].values().icon)})}),t.on("searchComplete",function(){var e=v(".wpforms-icon-picker-not-found");e.html(e.data("message").replace("{keyword}",v("#wpforms-icon-picker-search").val())),e.toggleClass("wpforms-hidden",!_.isEmpty(t.matchingItems))})},infiniteScrollPagination(i){let o=1;var e={root:r.querySelector(".wpforms-icon-picker-jconfirm-content-pane"),rootMargin:"600px"};new IntersectionObserver(function(e){e[0].isIntersecting&&(o++,i.show(0,o*wpforms_builder.icon_choices.icons_per_page))},e).observe(r.querySelector(".wpforms-icon-picker-pagination"))},selectIcon(e,i){var o=i.parent().data("field-id"),t=i.parent().data("choice-id"),r=i.data("icon"),s=i.data("icon-style"),t=v("#wpforms-field-option-row-"+o+"-choices ul li[data-key="+t+"]"),n=v("#wpforms-field-option-row-"+o+"-choices ul").data("field-type");i.addClass("selected"),i.siblings(".selected").removeClass("selected"),t.find(".wpforms-icon-select span").text(r),t.find(".wpforms-icon-select .ic-fa-preview").removeClass().addClass(`ic-fa-preview ic-fa-${s} ic-fa-`+r),t.find(".wpforms-icon-select .source-icon").val(r),t.find(".wpforms-icon-select .source-icon-style").val(s),k.fieldChoiceUpdate(n,o),e.close()}},dismissNotice(){y.on("click",".wpforms-alert-field-not-available .wpforms-dismiss-button",function(e){e.preventDefault();const i=v(this),o=i.closest(".wpforms-alert"),t=i.data("field-id");o.addClass("out"),setTimeout(function(){o.remove()},250),t&&v("#wpforms-field-option-"+t).remove()})},trimFormTitle(){var e,i=v(".wpforms-center-form-name");38<i.text().length&&(e=i.text().trim().substring(0,38).split(" ").slice(0,-1).join(" ")+"...",i.text(e))},loadColorPickers(){v(".wpforms-color-picker").each(function(){var e=v(this);e.hasClass("minicolors-input")&&e.minicolors("destroy"),e.minicolors({defaultValue:e.data("fallback-color")||""})})},getValidColorPickerValue(e){var i=e.minicolors("value"),o=_.isEqual(e.minicolors("rgbObject"),{r:0,g:0,b:0}),t=_.includes(["#000","#000000"],i),e=e.data("fallback-color")||"#000000";return o&&!t?e:i},builderHotkeys(){v(r).on("keydown",function(e){if((i.isLinux||i.isWindows)&&e.altKey&&83===e.keyCode)v(C.$sidebarToggle,y).trigger("click");else if(e.ctrlKey){switch(e.keyCode){case 72:v(C.$helpButton,y).trigger("click");break;case 80:a.open(wpforms_builder.preview_url);break;case 66:v(C.$embedButton,y).trigger("click");break;case 69:a.open(wpforms_builder.entries_url);break;case 83:v(C.$saveButton,y).trigger("click");break;case 81:v(C.$exitButton,y).trigger("click");break;case 191:k.openKeyboardShortcutsModal();break;case 84:v(C.$sidebarToggle,y).trigger("click");break;case 70:C.$addFieldsTab.trigger("click"),C.$fieldsSidebar.scrollTop(0),C.$searchInput.focus();break;default:return}return!1}})},openKeyboardShortcutsModal(){v(".wpforms-builder-keyboard-shortcuts").length?jconfirm.instances[jconfirm.instances.length-1].close():v.alert({title:wpforms_builder.shortcuts_modal_title,content:wpforms_builder.shortcuts_modal_msg+wp.template("wpforms-builder-keyboard-shortcuts")(),icon:"fa fa-keyboard-o",type:"blue",boxWidth:"550px",smoothContent:!1,buttons:{confirm:{text:wpforms_builder.close,btnClass:"btn-confirm",keys:["enter"]}},onOpenBefore(){this.$body.addClass("wpforms-builder-keyboard-shortcuts"),(i.isLinux||i.isWindows)&&this.$body.find(".shortcut-key.shortcut-key-ctrl-t").html("<i>alt</i><i>s</i>")}})},registerTemplates(){"undefined"!=typeof WPForms&&WPForms.Admin.Builder.Templates.add(["wpforms-builder-confirmations-message-field","wpforms-builder-conditional-logic-toggle-field"])},exitBack(){console.warn('WARNING! Function "WPFormsBuilder.exitBack()" has been deprecated.')}};return k}(document,window,jQuery);WPFormsBuilder.init();