!function(c){var s,a={settings:{spinner:'<i class="wpforms-loading-spinner wpforms-loading-inline"></i>',spinnerWhite:'<i class="wpforms-loading-spinner wpforms-loading-inline wpforms-loading-white"></i>'},init:function(){s=this.settings,c(a.ready),a.bindUIActions()},ready:function(){s.form=c("#wpforms-builder-form")},bindUIActions:function(){c(document).on("click",".wpforms-provider-connection-delete",function(e){a.connectionDelete(this,e)}),c(document).on("click",".wpforms-provider-connections-add",function(e){a.connectionAdd(this,e)}),c(document).on("click",".wpforms-provider-account-add button",function(e){a.accountAdd(this,e)}),c(document).on("change",".wpforms-provider-accounts select",function(e){a.accountSelect(this,e)}),c(document).on("change",".wpforms-provider-lists select",function(e){a.accountListSelect(this,e)}),c(document).on("wpformsPanelSwitch",function(e,n){a.providerPanelConfirm(n)}),c(document).on("wpformsSaved",function(){var r=[],e=c("#wpforms-panel-providers").find(".wpforms-connection-block");e.length&&e.each(function(){var e,n,o=!1;c(this).find("table span.required").each(function(){""===c(this).parent().parent().find("select").val()&&(o=!0)}),o&&((e=c(this).closest(".wpforms-panel-content-section").find(".wpforms-panel-content-section-title").clone()).find("button").remove(),e=e.text().trim(),n=wpforms_builder.provider_required_flds,-1<r.indexOf(e)||(c.alert({title:wpforms_builder.heads_up,content:n.replace("{provider}",e),icon:"fa fa-exclamation-circle",type:"orange",buttons:{confirm:{text:wpforms_builder.ok,btnClass:"btn-confirm",keys:["enter"]}}}),r.push(e)))})})},connectionDelete:function(e,n){n.preventDefault();var o=c(e);c.confirm({title:!1,content:wpforms_builder_providers.confirm_connection,icon:"fa fa-exclamation-circle",type:"orange",buttons:{confirm:{text:wpforms_builder.ok,btnClass:"btn-confirm",keys:["enter"],action:function(){var e=o.closest(".wpforms-panel-content-section"),n=(o.closest(".wpforms-provider-connection").remove(),o.closest(".wpforms-provider-connection").data("provider"));c(".wpforms-panel-sidebar-section-"+n).find(".fa-check-circle-o").toggleClass("wpforms-hidden",c(e).find(".wpforms-provider-connection").length<=0),e.find(".wpforms-provider-connection").length||e.find(".wpforms-builder-provider-connections-default").removeClass("wpforms-hidden")}},cancel:{text:wpforms_builder.cancel}}})},connectionAdd:function(e,n){n.preventDefault();var o=c(e),r=o.parent().parent(),i=o.parent(),t=o.data("provider"),n=a.getDefaultConnectionName(t).trim(),e=o.data("type"),n=(n=wpforms_builder_providers.prompt_connection+("<input "+(""===n?' autofocus=""':"")+' type="text" id="provider-connection-name" placeholder="'+wpforms_builder_providers.prompt_placeholder+'" value="'+n+'">')+('<p class="error">'+wpforms_builder_providers.error_name+"</p>")).replace(/%type%/g,e);c.confirm({title:!1,content:n,icon:"fa fa-info-circle",type:"blue",buttons:{confirm:{text:wpforms_builder.ok,btnClass:"btn-confirm",keys:["enter"],action:function(){var e=this.$content.find("input#provider-connection-name").val().trim(),n=this.$content.find(".error");if(""===e)return n.show(),!1;a.inputToggle(o,"disable");n={action:"wpforms_provider_ajax_"+t,provider:t,task:"new_connection",name:e,id:s.form.data("id"),nonce:wpforms_builder.nonce};a.fireAJAX(o,n,function(e){var n;e.success?(r.find(".wpforms-builder-provider-connections-default").addClass("wpforms-hidden"),r.find(".wpforms-provider-connections").prepend(e.data.html),(n=r.find(".wpforms-provider-connection").first()).find(".wpforms-provider-accounts option:selected")&&(n.find(".wpforms-provider-accounts option").first().prop("selected",!0),n.find(".wpforms-provider-accounts select").trigger("change"))):a.errorDisplay(e.data.error,i)})}},cancel:{text:wpforms_builder.cancel}}})},accountAdd:function(e,n){n.preventDefault();var n=c(e),e=n.data("provider"),o=n.closest(".wpforms-provider-connection"),r=n.parent(),i=r.find(":input"),t=a.requiredCheck(i,r);if(a.inputToggle(n,"disable"),t)return n.prop("disabled",!1).find("i").remove(),!1;t={action:"wpforms_provider_ajax_"+e,provider:e,connection_id:o.data("connection_id"),task:"new_account",data:a.fakeSerialize(i)};a.fireAJAX(n,t,function(e){e.success?(r.nextAll(".wpforms-connection-block").remove(),r.nextAll(".wpforms-conditional-block").remove(),r.after(e.data.html),r.slideUp(),o.find(".wpforms-provider-accounts select").trigger("change")):a.errorDisplay(e.data.error,r)})},accountSelect:function(e,n){n.preventDefault();var n=c(e),o=n.closest(".wpforms-provider-connection"),r=n.parent(),e=o.data("provider");a.inputToggle(n,"disable"),r.nextAll(".wpforms-connection-block").remove(),r.nextAll(".wpforms-conditional-block").remove(),n.val()?(o.find(".wpforms-provider-account-add").slideUp(),e={action:"wpforms_provider_ajax_"+e,provider:e,connection_id:o.data("connection_id"),task:"select_account",account_id:n.find(":selected").val()},a.fireAJAX(n,e,function(e){e.success?(r.after(e.data.html),o.find(".wpforms-provider-lists option").first().prop("selected",!0),o.find(".wpforms-provider-lists select").trigger("change")):a.errorDisplay(e.data.error,r)})):(o.find(".wpforms-provider-account-add input").val(""),o.find(".wpforms-provider-account-add").slideDown(),a.inputToggle(n,"enable"))},accountListSelect:function(e,n){n.preventDefault();var n=c(e),e=n.closest(".wpforms-provider-connection"),o=n.parent(),r=e.data("provider"),r=(a.inputToggle(n,"disable"),o.nextAll(".wpforms-connection-block").remove(),o.nextAll(".wpforms-conditional-block").remove(),{action:"wpforms_provider_ajax_"+r,provider:r,connection_id:e.data("connection_id"),task:"select_list",account_id:e.find(".wpforms-provider-accounts option:selected").val(),list_id:n.find(":selected").val(),form_id:s.form.data("id")});a.fireAJAX(n,r,function(e){e.success?(o.after(e.data.html),wpf.initTooltips()):a.errorDisplay(e.data.error,o)})},providerPanelConfirm:function(o){wpforms_panel_switch=!0,"providers"!==o||s.form.data("revision")||wpf.savedState!=wpf.getFormState("#wpforms-builder-form")&&(wpforms_panel_switch=!1,c.confirm({title:!1,content:wpforms_builder_providers.confirm_save,icon:"fa fa-info-circle",type:"blue",buttons:{confirm:{text:wpforms_builder.ok,btnClass:"btn-confirm",keys:["enter"],action:function(){c("#wpforms-save").trigger("click"),c(document).on("wpformsSaved",function(){let e=wpforms_builder_providers.url;var n=c(`#wpforms-panel-${o} .wpforms-panel-sidebar-section.active`),n=n.length&&"default"!==n.data("section")?n.data("section"):null;n&&(e+="&section="+n),window.location.href=e})}},cancel:{text:wpforms_builder.cancel}}}))},fireAJAX:function(e,n,o){var r=c(e),e={id:c("#wpforms-builder-form").data("id"),nonce:wpforms_builder.nonce};c.extend(e,n),c.post(wpforms_builder.ajax_url,e,function(e){o(e),a.inputToggle(r,"enable")}).fail(function(e,n,o){console.log(e.responseText)})},inputToggle:function(e,n){e=c(e);"enable"===n?(e.is("select")?e.prop("disabled",!1).next("i"):e.prop("disabled",!1).find("i")).remove():"disable"===n&&(e.is("select")?e.prop("disabled",!0).after(s.spinner):e.prop("disabled",!0).prepend(s.spinnerWhite))},errorDisplay:function(e,n){n.find(".wpforms-error-msg").remove(),n.prepend('<p class="wpforms-alert-danger wpforms-alert wpforms-error-msg">'+e+"</p>")},requiredCheck:function(e,n){var o=!1;return n.find(".wpforms-alert-required").remove(),e.each(function(e,n){c(n).hasClass("wpforms-required")&&0===c(n).val().length?(c(n).addClass("wpforms-error"),o=!0):c(n).removeClass("wpforms-error")}),o&&n.prepend('<p class="wpforms-alert-danger wpforms-alert wpforms-alert-required">'+wpforms_builder_providers.required_field+"</p>"),o},fakeSerialize:function(e){e=e.clone();return e.each(function(e,n){c(n).data("name")&&c(n).attr("name",c(n).data("name"))}),e.serialize()},getDefaultConnectionName(e){var n=c(`#${e}-provider`).data("provider-name"),e=a.getCountConnectionsOf(e),n=n+" "+wpforms_builder.connection_label;return e<1?n:""},getCountConnectionsOf(e){return c(`#${e}-provider .wpforms-provider-connection`).length},getProviderClass(e){console.warn('WARNING! Function "WPFormsProviders.getProviderClass()" has been deprecated!');e=e.split("-").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join("");return void 0===WPForms?.Admin?.Builder?.Providers?.[e]?null:WPForms.Admin.Builder.Providers[e]}};a.init()}(jQuery);