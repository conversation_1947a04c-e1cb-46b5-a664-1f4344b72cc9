var WPForms=window.WPForms||{};WPForms.Admin=WPForms.Admin||{},WPForms.Admin.Builder=WPForms.Admin.Builder||{},WPForms.Admin.Builder.Providers=WPForms.Admin.Builder.Providers||function(e,u){const m={cache:{},config:{templates:["wpforms-providers-builder-content-connection-fields","wpforms-providers-builder-content-connection-conditionals"]},fields:{}},v={panelHolder:{},form:u("#wpforms-builder-form"),spinner:'<i class="wpforms-loading-spinner wpforms-loading-inline"></i>',ajax:{_mergeData(e,r){e={id:v.form.data("id"),revision_id:v.form.data("revision"),nonce:wpforms_builder.nonce,action:"wpforms_builder_provider_ajax_"+e};return u.extend(e,r),e},request(t,e){const n=v.getProviderHolder(t),i=n.find(".wpforms-builder-provider-connections-save-lock");var r={url:wpforms_builder.ajax_url,type:"post",dataType:"json",beforeSend(){n.addClass("loading"),i.val(1),v.ui.getProviderError(t).hide()}};return"connections_get"!==e.data.task&&n.find(".wpforms-builder-provider-title-spinner").removeClass("wpforms-hidden"),e.data=v.ajax._mergeData(t,e.data||{}),u.extend(r,e),u.ajax(r).fail(function(e,r,o){console.error("provider:",t),console.error(e),console.error(r),i.val(1),v.ui.showError(t)}).always(function(e,r,o){n.removeClass("loading"),"success"===r&&(i.val(0),setTimeout(function(){wpf.savedState=wpf.getFormState("#wpforms-builder-form")},0))})}},cache:{get(e,r){return void 0!==m.cache[e]&&m.cache[e]instanceof Map?m.cache[e].get(r):null},getById(e,r,o){return void 0===this.get(e,r)||void 0===this.get(e,r)[o]?null:this.get(e,r)[o]},set(e,r,o){return void 0!==m.cache[e]&&m.cache[e]instanceof Map||(m.cache[e]=new Map),m.cache[e].set(r,o)},addTo(e,r,o,t){void 0!==m.cache[e]&&m.cache[e]instanceof Map||(m.cache[e]=new Map,this.set(e,r,{}));var n=this.get(e,r);return n[o]=t,this.set(e,r,n)},delete(e,r){return void 0!==m.cache[e]&&m.cache[e]instanceof Map?m.cache[e].delete(r):null},deleteFrom(e,r,o){var t;return void 0!==m.cache[e]&&m.cache[e]instanceof Map?(delete(t=this.get(e,r))[o],this.set(e,r,t)):null},clear(e){void 0!==m.cache[e]&&m.cache[e]instanceof Map&&m.cache[e].clear()}},init(){u(v.ready)},ready(){m.fields=u.extend({},wpf.getFields(!1,!0)),v.panelHolder=u("#wpforms-panel-providers, #wpforms-panel-settings"),v.Templates=WPForms.Admin.Builder.Templates,v.Templates.add(m.config.templates),v.bindActions(),v.ui.bindActions(),v.panelHolder.trigger("WPForms.Admin.Builder.Providers.ready")},bindActions(){u(e).on("wpformsSaved",function(){var e=v.panelHolder.find(".wpforms-builder-provider-connection");if(e.length){let t=!1;e.each(function(){let o=!1;var e,r;u(this).find("input.wpforms-required, select.wpforms-required, textarea.wpforms-required").each(function(){var e=u(this),r=e.val();_.isEmpty(r)&&!e.closest(".wpforms-builder-provider-connection-block").hasClass("wpforms-hidden")?(u(this).addClass("wpforms-error"),o=!0):u(this).removeClass("wpforms-error")}),o&&!t&&((e=u(this).closest(".wpforms-builder-provider").find(".wpforms-builder-provider-title").clone()).find("button").remove(),r=wpforms_builder.provider_required_flds,u.alert({title:wpforms_builder.heads_up,content:r.replace("{provider}","<strong>"+e.text().trim()+"</strong>"),icon:"fa fa-exclamation-circle",type:"red",buttons:{confirm:{text:wpforms_builder.ok,btnClass:"btn-confirm",keys:["enter"]}}}),t=!0)}),"fields"===wpf.getQueryString("view")&&v.updateMapSelects(e)}}),v.panelHolder.on("connectionRendered",function(){!0===wpf.initialSave&&(wpf.savedState=wpf.getFormState("#wpforms-builder-form"))})},updateMapSelects(r){var e=u.extend({},wpf.getFields()),o=_.mapObject(e,function(e,r){return e.label}),t=_.mapObject(m.fields,function(e,r){return e.label});if(!(_.isEmpty(o)&&_.isEmpty(t)||JSON.stringify(o)===JSON.stringify(t))){const s=Object.keys(o).map(function(e){return parseInt(e,10)});var n=Object.keys(t).map(function(e){return parseInt(e,10)}).filter(function(e){return!s.includes(e)});for(let e=0;e<n.length;e++)u('.wpforms-builder-provider-connection-fields-table .wpforms-builder-provider-connection-field-value option[value="'+n[e]+'"]',r).remove();const a=[],l=[];for(const c in e){const p=e[c];var i=p.id,d=p.type;const f=wpf.sanitizeHTML(p.label?.toString().trim()||wpforms_builder.field+" #"+i);a.push({value:i,text:f,type:d}),"name"===p.type&&p.format?u.each(wpforms_builder.name_field_formats,function(e,r){-1===p.format.indexOf(e)&&"full"!==e||l.push({value:p.id+"."+e,text:f+" ("+r+")"})}):l.push({value:i,text:f})}v.panelHolder.trigger("WPForms.Admin.Builder.Providers.FilterOptions",[a]),v.panelHolder.trigger("WPForms.Admin.Builder.Providers.FilterOptionsWithSubfields",[l]),u(".wpforms-builder-provider-connection-fields-table .wpforms-builder-provider-connection-field-value").each(function(){var e=u(this);const r=e.val(),o=e.clone().empty();var t=e.data("support-subfields")||Boolean(e.find('option[value$=".first"]').length)?l:a,n=e.data("placeholder")&&e.data("placeholder").length?e.data("placeholder"):wpforms_builder_providers.custom_fields_placeholder;o.append(u("<option>",{value:"",text:n})),t.forEach(function(e){o.append(u("<option>",{value:e.value,text:e.text,selected:r.toString()===e.value.toString()}))}),e.replaceWith(o)}),wpf.savedState!==wpf.getFormState("#wpforms-builder-form")&&(wpf.savedState=wpf.getFormState("#wpforms-builder-form")),m.fields=e,v.panelHolder.trigger("WPForms.Admin.Builder.Providers.updatedMapSelects",[r,e])}},ui:{bindActions(){v.panelHolder.on("click",".js-wpforms-builder-provider-account-add",function(e){e.preventDefault(),v.ui.account.setProvider(u(this).data("provider")),v.ui.account.add()}).on("click",".js-wpforms-builder-provider-connection-add",function(e){e.preventDefault(),v.ui.connectionAdd(u(this).data("provider"))}).on("click",".js-wpforms-builder-provider-connection-delete",function(e){var r=u(this);e.preventDefault(),v.ui.connectionDelete(r.closest(".wpforms-builder-provider").data("provider"),r.closest(".wpforms-builder-provider-connection"))}),v.panelHolder.on("click",".js-wpforms-builder-provider-connection-fields-add",function(e){e.preventDefault();var e=u(this).parents(".wpforms-builder-provider-connection-fields-table"),r=e.find("tr").last().clone(!0),o=parseInt(/\[.+]\[.+]\[.+]\[(\d+)]/.exec(r.find(".wpforms-builder-provider-connection-field-name").attr("name"))[1],10)+1;r.find(".wpforms-builder-provider-connection-field-name").attr("name",r.find(".wpforms-builder-provider-connection-field-name").attr("name").replace(/\[fields_meta\]\[(\d+)\]/g,"[fields_meta]["+o+"]")).val(""),r.find(".wpforms-builder-provider-connection-field-value").attr("name",r.find(".wpforms-builder-provider-connection-field-value").attr("name").replace(/\[fields_meta\]\[(\d+)\]/g,"[fields_meta]["+o+"]")).val(""),r.find(".js-wpforms-builder-provider-connection-fields-delete").removeClass("hidden"),e.find("tbody").append(r.get(0))}).on("click",".js-wpforms-builder-provider-connection-fields-delete",function(e){e.preventDefault(),u(this).parents(".wpforms-builder-provider-connection-fields-table tr").remove()}),v.panelHolder.on("connectionGenerated",function(e,r){wpf.initTooltips(),u(this).find('.wpforms-builder-provider-connection[data-connection_id="'+r.connection.id+'"]').closest(".wpforms-panel-content-section").find(".wpforms-builder-provider-connections-default").addClass("wpforms-hidden")}),v.panelHolder.on("connectionRendered",function(e,r,o){if(wpf.initTooltips(),void 0===o){if(!_.isObject(r)||!_.has(r,"connection_id"))return;o=r.connection_id}u(this).find('.wpforms-builder-provider-connection[data-connection_id="'+o+'"] .wpforms-field-map-select').length&&wpf.fieldUpdate()}),v.panelHolder.on("change",".wpforms-builder-provider select.wpforms-required",function(){var e=u(this);e.hasClass("wpforms-error")&&0!==e.val().length&&e.removeClass("wpforms-error")}),v.panelHolder.on("connectionDeleted",function(e){v.ui.updateStatus(e)})},connectionAdd(o){const r=v.ui.getDefaultConnectionName(o).trim();u.confirm({title:!1,content:wpforms_builder_providers.prompt_connection.replace(/%type%/g,"connection")+"<input "+(""===r?' autofocus=""':"")+'type="text" id="wpforms-builder-provider-connection-name" placeholder="'+wpforms_builder_providers.prompt_placeholder+'" value="'+r+'"><p class="error">'+wpforms_builder_providers.error_name+"</p>",icon:"fa fa-info-circle",type:"blue",buttons:{confirm:{text:wpforms_builder.ok,btnClass:"btn-confirm",keys:["enter"],action(){var e=this.$content.find("#wpforms-builder-provider-connection-name").val().trim(),r=this.$content.find(".error");if(""===e)return r.show(),!1;v.getProviderHolder(o).trigger("connectionCreate",[e])}},cancel:{text:wpforms_builder.cancel}},onContentReady(){var e=this.$content.find("#wpforms-builder-provider-connection-name")[0];r&&(e.setSelectionRange(r.length,r.length),e.focus())}})},connectionDelete(r,o){u.confirm({title:!1,content:wpforms_builder_providers.confirm_connection,icon:"fa fa-exclamation-circle",type:"orange",buttons:{confirm:{text:wpforms_builder.ok,btnClass:"btn-confirm",keys:["enter"],action(){v.getProviderHolder(r).trigger("connectionDelete",[o]);const e=o.closest(".wpforms-panel-content-section");o.fadeOut("fast",function(){u(this).remove(),v.getProviderHolder(r).trigger("connectionDeleted",[o]),e.find(".wpforms-builder-provider-connection").length||e.find(".wpforms-builder-provider-connections-default").removeClass("wpforms-hidden")})}},cancel:{text:wpforms_builder.cancel}}})},getDefaultConnectionName(e){var r=v.getProviderHolder(e).data("provider-name"),e=v.ui.getCountConnectionsOf(e),r=r+" "+wpforms_builder.connection_label;return e<1?r:""},getCountConnectionsOf(e){return v.getProviderHolder(e).find(".wpforms-builder-provider-connection").length},updateStatus(e){var r=e.target.closest(".wpforms-panel-content-section");u(".wpforms-panel-sidebar-section-"+e.target.dataset.provider).find(".fa-check-circle-o").toggleClass("wpforms-hidden",u(r).find(".wpforms-builder-provider-connection").length<=0)},getProviderError(e){return u(`#wpforms-${e}-builder-provider-error`)},showError(e){var r,o=v.ui.getProviderError(e);(o.length?o:(o=`wpforms-${e}-builder-content-connection-default-error`,r=v.getProviderHolder(e).find(".wpforms-builder-provider-connections"),v.Templates.add([o]),r.prepend(v.Templates.get(o)()),v.ui.getProviderError(e))).show()},account:{provider:"",submitHandlers:[],setProvider(e){this.provider=e},add(){const t=this;u.confirm({title:!1,smoothContent:!0,content(){const r=this;return v.ajax.request(t.provider,{data:{task:"account_template_get"}}).done(function(e){e.success&&(e.data.title.length&&r.setTitle(e.data.title),e.data.content.length&&r.setContent(e.data.content),e.data.type.length&&r.setType(e.data.type),v.getProviderHolder(t.provider).trigger("accountAddModal.content.done",[r,t.provider,e]))}).fail(function(){v.getProviderHolder(t.provider).trigger("accountAddModal.content.fail",[r,t.provider])}).always(function(){v.getProviderHolder(t.provider).trigger("accountAddModal.content.always",[r,t.provider])})},contentLoaded(e,r,o){this.buttons.add.enable(),this.buttons.cancel.enable(),v.getProviderHolder(t.provider).trigger("accountAddModal.contentLoaded",[this])},onOpenBefore(){this.buttons.add.disable(),this.buttons.cancel.disable(),this.$body.addClass("wpforms-providers-account-add-modal"),v.getProviderHolder(t.provider).trigger("accountAddModal.onOpenBefore",[this])},onClose(){!0===v.ui.account.isConfigured(t.provider)&&v.ui.connectionAdd(t.provider)},icon:"fa fa-info-circle",type:"blue",buttons:{add:{text:wpforms_builder.provider_add_new_acc_btn,btnClass:"btn-confirm",keys:["enter"],action(){if(v.getProviderHolder(t.provider).trigger("accountAddModal.buttons.add.action.before",[this]),!_.isEmpty(t.provider)&&void 0!==t.submitHandlers[t.provider])return t.submitHandlers[t.provider](this)}},cancel:{text:wpforms_builder.cancel}}})},registerAddHandler(e,r){"string"==typeof e&&"function"==typeof r&&(this.submitHandlers[e]=r)},isConfigured(e){return v.getProviderHolder(e).find(".js-wpforms-builder-provider-account-add").hasClass("hidden")}}},getProviderHolder(e){return u("#"+e+"-provider")},getProviderClass(e){console.warn('WARNING! Function "WPForms.Admin.Builder.Providers.getProviderClass()" has been deprecated!');e=e.split("-").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join("");return void 0===WPForms.Admin.Builder.Providers[e]?null:WPForms.Admin.Builder.Providers[e]}};return v}(document,(window,jQuery)),WPForms.Admin.Builder.Providers.init();