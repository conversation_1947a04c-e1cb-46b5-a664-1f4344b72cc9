const WPFormsEmailSettings=window.WPFormsEmailSettings||function(c,g,s){const i={},m={cache:{appearance:{light:"#email-appearance-light"},colors:{light:{background:["#wpforms-setting-email-background-color","#wpforms-setting-email-color-scheme-email_background_color"],text:"#wpforms-setting-email-color-scheme-email_text_color"},dark:{background:["#wpforms-setting-email-background-color-dark","#wpforms-setting-email-color-scheme-dark-email_background_color_dark"],text:"#wpforms-setting-email-color-scheme-dark-email_text_color_dark"}}},classNames:{hide:"wpforms-hide",appearance:"email-appearance-mode-toggle",legacyTemplate:"legacy-template",hideForPlainText:"hide-for-template-none",headerImage:"wpforms-email-header-image",colorScheme:"email-color-scheme",typography:"email-typography",noticeWarning:"notice-warning",noticeLegacy:"wpforms-email-legacy-notice",settingsRow:"wpforms-setting-row",settingField:"wpforms-setting-field"}},d={init(){g(d.ready)},ready(){d.setup(),d.bindEvents(),d.relocateImageSize(),d.handleOnContrastChange(),d.handleOnChangeBackgroundColor()},setup(){i.$wrapper=g(".wpforms-admin-settings-email"),i.$appearance=g("."+m.classNames.appearance),i.$colorScheme=g("."+m.classNames.colorScheme),i.$typography=g("."+m.classNames.typography)},bindEvents(){i.$wrapper.on("change",'.wpforms-email-template input[type="radio"]',d.handleOnUpdateTemplate).on("change",".wpforms-email-header-image input",d.handleOnChangeHeaderImage).on("click",".wpforms-setting-remove-image",d.handleOnRemoveHeaderImage).on("change",".has-preview-changes :input",d.handleOnPreviewChanges).on("change",".email-appearance-mode-toggle input",d.handleOnAppearanceModeToggle).on("change",'[id*="email-background-color"], [id*="email_background_color"]',d.handleOnChangeBackgroundColor).on("change",'[id*="email_body_color"], [id*="email_text_color"]',d.handleOnContrastChange)},handleOnUpdateTemplate(e){var e=g(e.currentTarget).val(),a=i.$wrapper.find("."+m.classNames.hideForPlainText),r=i.$wrapper.find(`.${m.classNames.headerImage} .choices`),o=i.$wrapper.find(".email-background-color"),n=i.$wrapper.find("."+m.classNames.noticeLegacy),s=0===i.$wrapper.find(".education-modal").length,t="none"===e;const l="default"===e;r.each((e,a)=>{var a=g(a),r=a.closest("."+m.classNames.settingField).find("img").length;a.toggle(!l&&!!r)}),a.toggle(!t),n.toggle(l),o.toggle((l||!s)&&!t),l&&i.$appearance.find(m.cache.appearance.light).trigger("click");e=m.classNames.legacyTemplate;i.$appearance.toggleClass(e,l),i.$colorScheme.toggleClass(e,l),i.$typography.toggleClass(e,l),d.handleOnChangeBackgroundColor()},handleOnChangeHeaderImage(){d.handleOnChangeBackgroundColor(),!d.isLegacyTemplate()&&g(this).prev("img").length&&g(this).parent().find(".choices").show()},handleOnRemoveHeaderImage(){g(this).closest("."+m.classNames.settingsRow).removeClass("has-external-image-url")},handleOnUpdateImageSize(){var e=g(this).closest("."+m.classNames.settingsRow),a=g(this).val();e.removeClass((e,a)=>(a.match(/has-image-size-\w+/g)||[]).join(" ")),e.addClass("has-image-size-"+a)},handleOnChangeBackgroundColor(){var[e,a]=d.getBackgroundColors();d.syncBackgroundColors(e,a)},handleOnContrastChange(){if(c.WPFormsColorContrastChecker){const{noticeWarning:o,settingsRow:n}=m.classNames;[m.cache.colors.light.text,m.cache.colors.dark.text].forEach(e=>{var e=g(e),a=e.parent().prev().prev().find("input"),a=new c.WPFormsColorContrastChecker({textColor:e.val(),bgColor:a.val(),message:{contrastPass:"",contrastFail:s?.contrast_fail||""}}).checkContrast();if(a){const r=e.closest("."+n);r.find("."+o).length||r.append(`<div class="${o}"><p>${c.wp.escapeHtml.escapeHTML(a)}</p></div>`)}else{const r=e.closest("."+n);void r.find("."+o).remove()}})}},handleOnPreviewChanges(){if(c.WPFormsXOR){var e=g(this),a=e.attr("name");const o=((a.match(/\[([^[\]]+)]/i)||[])[1]||a).replace(/-/g,"_"),n=e.val(),{isURL:r,addQueryArgs:s,getQueryArg:t}=wp.url,l="wpforms_email_style_overrides",i=new c.WPFormsXOR;g(".wpforms-btn-preview").filter((e,a)=>r(g(a).attr("href"))).attr("href",(e,a)=>{var r={...i.decrypt(t(a,l)),[o]:n},r=i.encrypt(r);return s(a,{wpforms_email_style_overrides:r})})}},handleOnAppearanceModeToggle(){var e=g(this),{hide:a,settingField:r}=m.classNames,o=e.val(),e=e.closest("."+r).find("input:not(:checked)").val();g(`.email-${o}-mode`).removeClass(a),g(`.email-${e}-mode`).addClass(a)},relocateImageSize(){var e=g(".wpforms-email-header-image-size");0!==e.length&&e.each((e,a)=>{var a=g(a),r=a.find("select");if(0!==r.length){a=a.prev();a.find(".wpforms-setting-remove-image").before(r.get(0).outerHTML),r.remove();try{var o=a.find("select");a.addClass("has-image-size-"+(o.val()||"medium")),o.on("change",d.handleOnUpdateImageSize),new Choices(o.get(0),{searchEnabled:!1,shouldSort:!1}),d.isLegacyTemplate()&&(i.$wrapper.find("."+m.classNames.noticeLegacy).show(),a.find(".choices").hide())}catch(e){console.error("Error during relocation:",e)}}})},isLegacyTemplate(){return"default"===i.$wrapper.find(".wpforms-setting-row-email_template input:checked").val()},getBackgroundColors(){var e=(e,a)=>{return i.$wrapper.find(e+":visible").val()||i.$wrapper.find(a).val()};return[e(...m.cache.colors.light.background),e(...m.cache.colors.dark.background)]},syncBackgroundColors(a,r){if(a&&r){var o=[m.cache.colors.light.background,m.cache.colors.dark.background];for(let e=0;e<o.length;e++){const s=0===e?a:r;var n=0===e?g("#wpforms-setting-row-email-header-image"):g("#wpforms-setting-row-email-header-image-dark");o[e].forEach(e=>{e=i.$wrapper.find(e);e.val(s),e.next().find(".minicolors-swatch-color").css("backgroundColor",s)}),n.find("img").css("backgroundColor",s)}}}};return d}((document,window),jQuery,wpforms_admin_email_settings);WPFormsEmailSettings.init();