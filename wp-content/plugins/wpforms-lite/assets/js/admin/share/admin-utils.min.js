const wpf={cachedFields:{},savedState:!1,initialSave:!0,orders:{fields:[],choices:{}},init(){wpf.bindUIActions(),wpf.initRadioGroupForCheckboxes(),jQuery(wpf.ready)},ready(){wpf.savedState=wpf.getFormState("#wpforms-builder-form"),wpf.setFieldOrders(),wpf.setChoicesOrders()},bindUIActions(){jQuery(document).on("wpformsFieldAdd",wpf.setFieldOrders).on("wpformsFieldDuplicated",wpf.setFieldOrders).on("wpformsFieldDelete",wpf.setFieldOrders).on("wpformsFieldMove",wpf.setFieldOrders).on("wpformsFieldAdd",wpf.setChoicesOrders).on("wpformsFieldChoiceAdd",wpf.setChoicesOrders).on("wpformsFieldChoiceDelete",wpf.setChoicesOrders).on("wpformsFieldChoiceMove",wpf.setChoicesOrders).on("wpformsFieldAdd",wpf.fieldUpdate).on("wpformsFieldDelete",wpf.fieldUpdate).on("wpformsFieldMove",wpf.fieldUpdate).on("wpformsFieldChoiceAdd",wpf.fieldUpdate).on("wpformsFieldChoiceDelete",wpf.fieldUpdate).on("wpformsFieldChoiceMove",wpf.fieldUpdate).on("wpformsFieldDynamicChoiceToggle",wpf.fieldUpdate).on("focusout",".wpforms-field-option-row-label input",wpf.fieldUpdate).on("focusout",".wpforms-field-option-row-choices input.label",wpf.fieldUpdate)},setFieldOrders(){wpf.orders.fields=[],jQuery(".wpforms-field-option").each(function(){wpf.orders.fields.push(jQuery(this).data("field-id"))})},setChoicesOrders(){wpf.orders.choices={},jQuery(".choices-list").each(function(){const e=jQuery(this).data("field-id");wpf.orders.choices["field_"+e]=[],jQuery(this).find("li").each(function(){wpf.orders.choices["field_"+e].push(jQuery(this).data("key"))})})},getChoicesOrder(e){const t=[];return jQuery("#wpforms-field-option-"+e).find(".choices-list li").each(function(){t.push(jQuery(this).data("key"))}),t},initMultipleSelectWithSearch(e){const t=jQuery(e.passedElement.element),r=jQuery(e.input.element);t.prop("multiple")&&(r.data("placeholder",r.attr("placeholder")),r.data("style",r.attr("style")),e.getValue(!0).length&&r.removeAttr("placeholder"),t.on("change",function(){e.getValue(!0).length?r.removeAttr("placeholder"):r.attr("placeholder",r.data("placeholder")).attr("style",r.data("style"))}))},showMoreButtonForChoices(e){var t,r;"select-one"!==jQuery(e).data("type")&&(t=jQuery(e).find(".choices__list--multiple .choices__item").first(),r=jQuery(e).find(".choices__list--multiple .choices__item").last(),jQuery(e).removeClass("choices__show-more"),0<t.length)&&0<r.length&&t.position().top!==r.position().top&&jQuery(e).addClass("choices__show-more")},initializeChoicesEventHandlers(){jQuery(document).on("addItem removeItem",".choices:not(.is-disabled)",function(){wpf.showMoreButtonForChoices(this)}),jQuery(document).on("hideDropdown",".choices:not(.is-disabled)",function(){jQuery(this).find(".choices__inner input.choices__input").trigger("blur")})},reInitShowMoreChoices(e){setTimeout(()=>{e.find(".choices select").each(function(){var e=jQuery(this).data("choicesjs");wpf.showMoreButtonForChoices(e.containerOuter.element)})},100)},fieldUpdate(){var e=wpf.getFields();jQuery(document).trigger("wpformsFieldUpdate",[e]),wpf.debug("fieldUpdate triggered")},getFields(e=void 0,t=void 0,r=void 0,i=void 0){let o;if((t=t||!1)&&!jQuery.isEmptyObject(wpf.cachedFields))o=jQuery.extend({},wpf.cachedFields),wpf.debug("getFields triggered (cached)");else{var t=wpf.formObject("#wpforms-field-options"),n=["captcha","content","divider","entry-preview","html","internal-information","pagebreak","layout"];if(!(o=t.fields))return!1;for(const l in o)(!o[l].type||-1<jQuery.inArray(o[l].type,n))&&delete o[l],"repeater"===o[l]?.type&&(Object.values(o[l]["columns-json"]??{}).forEach(e=>{Object.values(e?.fields??[]).forEach(e=>{o[e]&&(o[e].label+=" ("+o[l].label+")",o[e].isRepeater=!0)})}),delete o[l]);wpf.addAdditionalFields(o),wpf.cachedFields=jQuery.extend({},o),wpf.debug("getFields triggered")}if(!r)for(const d in o)o[d]?.isRepeater&&delete o[d];if(i)for(const p in i)delete o[p];if(e&&e.constructor===Array)for(const c in o)-1===jQuery.inArray(o[c].type,e)&&delete o[c];if(0===Object.keys(o).length)return!1;var s=[];for(const f in wpf.orders.fields){var a=wpf.orders.fields[f];o[a]&&s.push(o[a])}return Object.assign({},s)},addAdditionalFields(e){for(const r in e){var t;if(["name","date-time"].includes(e[r]?.type)&&(t=e[r].format)&&(e[r].additional=t.split("-")),"address"===e[r]?.type){const i=Object.keys(e[r]).filter(e=>e.includes("_placeholder"));i.forEach((e,t)=>{i[t]=e.replace("_placeholder","")}),e[r].additional=i}}return e},getField(e){e=wpf.formObject("#wpforms-field-option-"+e);return Object.keys(e).length?e.fields[Object.keys(e.fields)[0]]:{}},fieldOptionLoading(e,t=void 0){var e=jQuery(e),r=e.find("label");(t=void 0!==t)?(r.find(".wpforms-loading-spinner").remove(),r.find(".wpforms-help-tooltip").show(),e.find("input,select,textarea").prop("disabled",!1)):(r.append('<i class="wpforms-loading-spinner wpforms-loading-inline"></i>'),r.find(".wpforms-help-tooltip").hide(),e.find("input,select,textarea").prop("disabled",!0))},getFormState(e){return jQuery(e).serialize()},removeArrayItem(t,r){let i=0;for(let e=0;e<t.length;e++)t[e]===r&&(t.splice(e,1),i++,e--);return i},sanitizeString(e){return"string"==typeof e||e instanceof String?e.trim():e},updateQueryString(e,t,r=null){r=r||window.location.href;r=new URL(r);return null!=t?r.searchParams.set(e,t):r.searchParams.delete(e),r.toString()},getQueryString(e){e=new RegExp("[?&]"+e+"=([^&]*)").exec(window.location.search);return e&&decodeURIComponent(e[1].replace(/\+/g," "))},removeQueryParam(e){wpf.getQueryString(e)&&(e=new RegExp("[\\?&]"+e+"=[^&]+"),history.replaceState)&&history.replaceState(null,"",location.pathname+location.search.replace(e,"").replace(/^&/,"?")+location.hash)},isNumber(e){return!isNaN(parseFloat(e))&&isFinite(e)},amountSanitize(e){return e=String(e).replace(wpforms_builder.currency_symbol,"").replace(/[^0-9.,]/g,""),","===wpforms_builder.currency_decimal?("."===wpforms_builder.currency_thousands&&-1!==e.indexOf(wpforms_builder.currency_thousands)?e=e.replace(new RegExp("\\"+wpforms_builder.currency_thousands,"g"),""):""===wpforms_builder.currency_thousands&&-1!==e.indexOf(".")&&(e=e.replace(/\./g,"")),e=e.replace(wpforms_builder.currency_decimal,".")):","===wpforms_builder.currency_thousands&&-1!==e.indexOf(wpforms_builder.currency_thousands)&&(e=e.replace(new RegExp("\\"+wpforms_builder.currency_thousands,"g"),"")),wpf.numberFormat(e,wpforms_builder.currency_decimals,".","")},amountFormat(e){var t;return e=String(e),","===wpforms_builder.currency_decimal&&-1!==e.indexOf(wpforms_builder.currency_decimal)&&(t=e.indexOf(wpforms_builder.currency_decimal),e=e.substr(0,t)+"."+e.substr(t+1,e.length-1)),","===wpforms_builder.currency_thousands&&-1!==e.indexOf(wpforms_builder.currency_thousands)&&(e=e.replace(/,/g,"")),wpf.empty(e)&&(e="0"),wpf.numberFormat(e,wpforms_builder.currency_decimals,wpforms_builder.currency_decimal,wpforms_builder.currency_thousands)},amountFormatCurrency(e){e=wpf.amountSanitize(e),e=wpf.amountFormat(e);let t;return t="right"===wpforms_builder.currency_symbol_pos?e+" "+wpforms_builder.currency_symbol:wpforms_builder.currency_symbol+e},numberFormat(e,t,r,i){e=(e+"").replace(/[^0-9+\-Ee.]/g,"");var o,n,s,e=isFinite(+e)?+e:0,t=isFinite(+t)?Math.abs(t):0,i=void 0===i?",":i,r=void 0===r?".":r,a="";return 3<(a=(t?(o=e,n=t,s=Math.pow(10,n),""+(Math.round(o*s)/s).toFixed(n)):""+Math.round(e)).split("."))[0].length&&(a[0]=a[0].replace(/\B(?=(?:\d{3})+(?!\d))/g,i)),(a[1]||"").length<t&&(a[1]=a[1]||"",a[1]+=new Array(t-a[1].length+1).join("0")),a.join(r)},empty(e){var t;let r,i;var o=[void 0,null,!1,0,"","0"];for(i=0,t=o.length;i<t;i++)if(e===o[i])return!0;if("object"!=typeof e)return!1;for(r in e)if(e.hasOwnProperty(r))return!1;return!0},debug(...e){wpf.isDebug()&&console.log("%cWPForms Debug: ","color: #cd6622;",...e)},isDebug(){return window.location.hash&&"#wpformsdebug"===window.location.hash||window.wpforms_builder?.debug},focusCaretToEnd(e){e.trigger("focus");var t=e.val();e.val("").val(t)},formObject(e){var t=jQuery(e).find("[name]"),o={},n={};for(let e=0;e<t.length;e++){var s=jQuery(t[e]),a=s.prop("name").replace(/\]/gi,"").split("[");let r=s.val(),i={};if(!s.is(":radio")&&!s.is(":checkbox")||s.is(":checked")){for(let t=a.length-1;0<=t;t--){let e=a[t];if(0===(e=void 0===e?"":e).length&&(i=[],void 0===n[a[t-1]]?n[a[t-1]]=0:n[a[t-1]]+=1,e=n[a[t-1]]),t===a.length-1){if(r)if("true"===r)r=!0;else if("false"===r)r=!1;else if(isNaN(parseFloat(r))||parseFloat(r).toString()!==r)if("string"!=typeof r||"{"!==r.substr(0,1)&&"["!==r.substr(0,1)){if("object"==typeof r&&r.length&&s.is("select")){var l={};for(let e=0;e<r.length;e++)l["n"+e]=r[e];r=l}}else try{r=JSON.parse(r)}catch(e){}else r=parseFloat(r);i[e]=r}else{var d=i;(i={})[e]=d}}jQuery.extend(!0,o,i)}}return o},initTooltips(e=null){if(void 0!==jQuery.fn.tooltipster){const t=jQuery("body").hasClass("rtl")?"left":"right";(e?jQuery(e).find(".wpforms-help-tooltip"):jQuery(".wpforms-help-tooltip")).each(function(){var e=jQuery(this);e.tooltipster({contentAsHTML:!0,position:e.data("tooltip-position")||t,maxWidth:300,multiple:!0,interactive:!0,debug:!1,IEmin:11,zIndex:99999999})})}},restoreTooltips(e){(e=void 0!==e&&e&&0<e.length?e.find(".wpforms-help-tooltip"):jQuery(".wpforms-help-tooltip")).each(function(){var e=jQuery(this);0!==jQuery.tooltipster.instances(this).length&&e.attr("title",e.tooltipster("content"))})},isURL(e){return"string"==typeof e&&!!(e=e.match(/^(?:http(?:s?):)?\/\/(\S+)/))&&!!(e=e[1])&&(/^localhost[\:?\d]*(?:[^\:?\d]\S*)?$/.test(e)||/^[^\s\.]+\.\S{2,}$/.test(e))},sanitizeHTML(e=void 0,t=void 0){var r=window.DOMPurify;if(void 0===r||void 0===e)return e;"string"!=typeof e&&(e=e.toString());var i={ADD_ATTR:["target"]};return void 0!==t&&(i.ALLOWED_TAGS=t),r.sanitize(e,i).trim()},encodeHTMLEntities(e){return(e="string"!=typeof e?e.toString():e).replace(/[\u00A0-\u9999<>&]/gim,function(e){return"&#"+e.charCodeAt(0)+";"})},decodeAllowedHTMLEntities(e){"string"!=typeof e&&(e=e.toString());var t=wp.hooks.applyFilters("wpforms.allowedHTMLEntities",{"&amp;":"&","&nbsp;":" "});for(const r in t)e=e.replaceAll(r,t[r]);return e},initRadioGroupForCheckboxes(){const n=jQuery;n(document).on("change",'input[type="checkbox"].wpforms-radio-group',function(){var t=n(this);if(t.prop("checked")){const r=t.data("radio-group"),i=n(".wpforms-radio-group-"+r),o=t.attr("id");let e;i.each(function(){(e=n(this)).attr("id")!==o&&e.prop("checked",!1)})}})},listPluck(e,t){return e.map(function(e){return void 0!==e?e[t]:e})},triggerEvent(e,t){return console.warn('WARNING! Function "wpf.triggerEvent( $element, eventName )" has been deprecated, please use the new "WPFormsUtils.triggerEvent( $element, eventName, args )" function instead!'),WPFormsUtils.triggerEvent(e,t)},wpautop(i,e=!0){const o=new Map;if("string"==typeof i||i instanceof String){if(""===i.trim())return"";-1<(i+="\n").indexOf("<pre")&&(r=(t=i.split("</pre>")).pop(),i="",t.forEach(function(e,t){var r=e.indexOf("<pre");-1===r?i+=e:(t="<pre wp-pre-tag-"+t+"></pre>",o[t]=e.substring(r)+"</pre>",i+=e.substring(0,r)+t)}),i+=r);var t="(?:table|thead|tfoot|caption|col|colgroup|tbody|tr|td|th|div|dl|dd|dt|ul|ol|li|pre|form|map|area|blockquote|address|math|style|p|h[1-6]|hr|fieldset|legend|section|article|aside|hgroup|header|footer|nav|figure|figcaption|details|menu|summary)",r=(i=(i=-1<(i=-1<(i=-1<(i=0===(i=(i=(i=(i=i.replace(/<br \/>\s*<br \/>/,"\n\n")).replace(new RegExp("(<"+t+"[^>]*>)","gmi"),"\n$1")).replace(new RegExp("(</"+t+">)","gmi"),"$1\n\n")).replace(/\r\n|\r/,"\n")).indexOf("\n")?i.substring(1):i).indexOf("<option")?(i=i.replace(/(?=(\s*))\2<option'/gim,"<option")).replace(/<\/option>\s*/gim,"</option>"):i).indexOf("</object>")?(i=(i=i.replace(/(<object[^>]*>)\s*/gim,"$1")).replace(/(?=(\s*))\2<\/object>/gim,"</object>")).replace(/(?=(\s*))\2(<\/?(?:param|embed)[^>]*>)((?=(\s*))\2)/gim,"$1"):i).indexOf("<source")||-1<i.indexOf("<track")?(i=(i=i.replace(/([<\[](?:audio|video)[^>\]]*[>\]])\s*/gim,"$1")).replace(/(?=(\s*))\2([<\[]\/(?:audio|video)[>\]])/gim,"$1")).replace(/(?=(\s*))\2(<(?:source|track)[^>]*>)(?=(\s*))\2/gim,"$1"):i).replace(/\n\n+/gim,"\n\n")).split(/\n\s*\n/);i="",r.forEach(function(e){i+="<p>"+e.replace(/^(?:\s+|\s+)$/g,"")+"</p>\n"}),i=(i=(i=(i=(i=(i=(i=(i=i.replace(/<p>\s*<\/p>/gim,"")).replace(/<p>([^<]+)<\/(div|address|form)>/gim,"<p>$1</p></$2>")).replace(new RegExp("<p>s*(</?"+t+"[^>]*>)s*</p>","gmi"),"$1",i)).replace(/<p>(<li.+?)<\/p>/gim,"$1")).replace(/<p><blockquote([^>]*)>/gim,"<blockquote$1><p>")).replace(/<\/blockquote><\/p>/gim,"</p></blockquote>")).replace(new RegExp("<p>s*(</?"+t+"[^>]*>)","gmi"),"$1")).replace(new RegExp("(</?"+t+"[^>]*>)s*</p>","gmi"),"$1"),i=(i=(i=(i=e?(i=(i=i.replace(/<(script|style)(?:.|\n)*?<\/\\1>/gim,function(e){return e[0].replace("\n","<WPPreserveNewline />")})).replace(/(<br \/>)?((?=(\s*))\2)\n/gim,"<br />\n")).replace("<WPPreserveNewline />","\n"):i).replace(new RegExp("(</?"+t+"[^>]*>)s*<br />","gmi"),"$1")).replace(/<br \/>(\s*<\/?(?:p|li|div|dl|dd|dt|th|pre|td|ul|ol)[^>]*>)/gim,"$1")).replace(/\n<\/p>$/gim,"</p>"),Object.keys(o).length&&(i=i.replace(new RegExp(Object.keys(o).join("|"),"gi"),function(e){return o[e]}))}return i},initMediaLibrary(t){const r=wp.media.frames.wpforms_media_frame=wp.media({className:"media-frame wpforms-media-frame",multiple:!1,title:t.title,library:{type:t.extensions},button:{text:t.buttonText}});return r.on("uploader:ready",function(){var e=t.extensions.join(",");jQuery('.wpforms-media-frame .moxie-shim-html5 input[type="file"]').attr("accept",e)}).on("library:selection:add",function(){var e=r.state().get("selection").first().toJSON();t.extensions.includes(e.file.type)||(alert(t.extensionsError),r.state().get("selection").reset())}),r},isInViewport(e){e=e[0].getBoundingClientRect();return 0<=e.top&&0<=e.left&&e.bottom<=(window.innerHeight||document.documentElement.clientHeight)&&e.right<=(window.innerWidth||document.documentElement.clientWidth)},copyValueToClipboard(e,t,r){e.preventDefault(),navigator.clipboard?navigator.clipboard.writeText(r.val()).then(function(){t.find("span").removeClass("dashicons-admin-page").addClass("dashicons-yes-alt")}):(r.attr("disabled",!1).focus().select(),document.execCommand("copy"),t.find("span").removeClass("dashicons-admin-page").addClass("dashicons-yes-alt"),r.attr("disabled",!0))},isRepeatedCall(e,t=500){return wpf.isRepeatedCallData=wpf.isRepeatedCallData||{},!!wpf.isRepeatedCallData[e]||(wpf.isRepeatedCallData[e]=!0,setTimeout(()=>wpf.isRepeatedCallData[e]=!1,t),!1)}};wpf.init();