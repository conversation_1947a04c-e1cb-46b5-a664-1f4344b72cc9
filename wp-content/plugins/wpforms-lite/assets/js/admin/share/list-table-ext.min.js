var WPFormsAdminListTableExt=window.WPFormsAdminListTableExt||function(e,n,i){const t=["#wpforms-overview","#wpforms-entries-list"],o={cogIcon:"#wpforms-list-table-ext-edit-columns-cog",submitButton:"#wpforms-list-table-ext-edit-columns-select-submit"},c={},d={init(){d.initElements(),c.$doc.on("wpformsReady",d.initMultiSelect),i(d.ready)},ready(){d.initPagination(),d.prepareTableFootColumns(),d.initTableScrollColumns(),d.initTableSortableColumns(),d.events(),d.windowResize()},events(){c.$doc.on("click",o.cogIcon,d.onClickCog).on("wpforms_multiselect_checkbox_list_toggle",d.onMenuToggle).on("click",o.submitButton,d.onSaveChanges).on("click",".tablenav-pages a.button",d.clickPaginationButton).on("keydown","#wpforms-overview-search-term",d.searchTermKeydown).on("htmx:beforeSwap",d.htmxBeforeSwap).on("htmx:afterSettle",d.htmxAfterSettle),c.$tableScroll?.on("scroll",d.tableScroll),i(n).on("resize",_.debounce(d.windowResize,100)),c.$searchInput?.on("input",_.debounce(d.maybeShowNoResults,310))},initElements(){c.$doc=i(e),c.$body=i("body"),c.$header=i("#wpforms-header"),c.$page=i(t.join(",")),c.$table=c.$page.find(".wp-list-table"),c.$tableContainer=c.$table.parent(),c.$menu=i("#wpforms-list-table-ext-edit-columns-select-container"),c.$cog=d.initCogIcon(),c.$wpcontent=i("#wpcontent"),c.$tablenavPages=i(".tablenav-pages"),c.$tablenavPagesLinks=i(".tablenav-pages .pagination-links a"),c.$tableContainer.hasClass("wpforms-table-container")||(c.$table.wrap('<div class="wpforms-table-container"></div>'),c.$tableContainer=c.$table.parent()),c.$page.addClass("wpforms-list-table-ext-page")},initPagination(){htmx.config.historyCacheSize=2,200<i("#pagination_per_page, #wpforms_entries_per_page").val()||i(".tablenav-pages .pagination-links a").each(function(){var e=i(this),t=e.attr("href");e.attr({"hx-get":t,"hx-target":".wpforms-admin-content","hx-swap":"outerHTML","hx-select":".wpforms-admin-content","hx-replace-url":"true"}),htmx.process(e[0])})},clickPaginationButton(){c.$body.addClass("wpforms-loading")},searchTermKeydown(e){13===e.keyCode&&i("#current-page-selector").val(1)},htmxBeforeSwap(){c.$cog.detach()},htmxAfterSettle(){d.initElements(),d.initMultiSelect(),d.prepareTableFootColumns(),d.initTableSortableColumns(),d.initTableScrollColumns(),c.$tableScroll?.on("scroll",d.tableScroll),d.windowResize(),d.initPagination(),d.initMobileRowExpander(),n.WPFormsForms?.Overview.htmxAfterSettle(),n.WPFormsPagesEntries?.htmxAfterSettle(),c.$body.removeClass("wpforms-loading")},initMobileRowExpander(){i("tbody").on("click",".toggle-row",function(){i(this).closest("tr").toggleClass("is-expanded")})},prepareTableFootColumns(){c.$table.find("thead tr .manage-column").each(function(){var e=i(this).attr("id");c.$table.find("tfoot tr .column-"+e).attr("id",e+"-foot")}),c.$table.find(".manage-column.column-cog").addClass("wpforms-table-cell-sticky")},initTableSortableColumns(){let a,s;c.$table.find("thead tr, tfoot tr").each(function(){i(this).sortable({items:"> th:not(:first-child):not(.wpforms-table-cell-sticky)",connectWith:"",delay:100,opacity:.75,cursor:"move",cancel:".wpforms-table-column-not-draggable",placeholder:"wpforms-table-column-drag-placeholder",appendTo:c.$page,zindex:1e4,tolerance:"intersect",distance:1,helper(e,t){var t=i(t),o=t.clone(),t=t.outerWidth();return o.css("width",t+"px")},start(e,t){t.helper.addClass("wpforms-table-column-drag-helper"),t.item.addClass("wpforms-table-column-dragged-out").css("display",""),c.$wpcontent.addClass("wpforms-no-scroll"),s=t.item.attr("id").replace("-foot","")},stop(e,t){t.item.removeClass("wpforms-table-column-drag-helper").removeClass("wpforms-table-column-dragged-out"),c.$table.find("thead tr > *, tfoot tr > *").removeClass("wpforms-table-column-drag-placeholder-prev"),c.$wpcontent.removeClass("wpforms-no-scroll");var o=t.item.prev().attr("id").replace("-foot",""),n=c.$table.find("tbody tr:not(.wpforms-hidden)"),l="cb"!==o?".column-"+o:".check-column";a=n.find("td.column-"+s).detach();for(let e=0;e<a.length;e++)n.eq(e).find(l).after(a.eq(e));o=0<t.item.closest("thead").length?"tfoot":"thead",t=c.$table.find(o+" tr .column-"+s).detach();c.$table.find(o+" tr "+l).after(t),d.updateMenuColumnsOrder()},change(e,t){c.$table.find("thead tr > *, tfoot tr > *").removeClass("wpforms-table-column-drag-placeholder-prev"),t.placeholder.prev().addClass("wpforms-table-column-drag-placeholder-prev")},update(){d.saveColumnsOrder()}})})},initTableScrollColumns(){c.$page.is("#wpforms-entries-list")&&(c.$tableScroll=c.$tableContainer,c.$tableScroll.addClass("wpforms-table-scroll"),c.$tableScroll.toggleClass("wpforms-scrollbar",d.isCustomScrollbarNeeded()),c.$table.find(".check-column, .column-indicators").addClass("wpforms-table-cell-sticky").addClass("left"),c.$table.find(".column-actions").addClass("wpforms-table-cell-sticky").addClass("right"))},tableScroll(){var e,t,o;c.$tableScroll?.length&&(e=c.$tableScroll.outerWidth(),t=Math.abs(c.$tableScroll.get(0).scrollLeft),o=c.$tableScroll.get(0).scrollWidth,c.$tableScroll.find(".wpforms-table-cell-sticky.left").toggleClass("shadow",1<t),c.$tableScroll.find(".wpforms-table-cell-sticky.right").toggleClass("shadow",t<=o-e))},windowResize(){c.$table.find("thead th, tfoot th").toggleClass("wpforms-table-column-not-draggable",n.innerWidth<=782),d.closeMenu(),d.windowResizeToggleColumns(),d.tableScroll()},windowResizeToggleColumns(){var e,t;c.$page.is("#wpforms-overview")&&(e=c.$table.find("thead tr th:visible"),t=c.$table.find(".column-tags"),960<n.innerWidth&&n.innerWidth<=1280?t.toggleClass("wpforms-hidden",4<e.length):t.removeClass("wpforms-hidden"),c.$menu.find("label").removeClass("wpforms-hidden"),c.$table.find("thead tr th:not(:visible)").each(function(){var e=i(this);c.$menu.find(`input[value="${e.attr("id")}"]`).closest("label").addClass("wpforms-hidden")}))},maybeShowNoResults(){["fields","meta"].forEach(e=>{var t=c.$menu.find(".wpforms-multiselect-checkbox-optgroup-"+e).nextUntil(".wpforms-multiselect-checkbox-optgroup").filter("label"),o=t.filter(function(){return i(this).is(":hidden")});c.$menu.find(".wpforms-multiselect-checkbox-no-results-"+e).toggleClass("wpforms-hidden",t.length!==o.length)})},closeMenu(){c.$cog.hasClass("active")&&(c.$cog.removeClass("active"),c.$menu.find(".wpforms-multiselect-checkbox-list").removeClass("open"),c.$searchInput.val(""),c.$searchInput[0]?.dispatchEvent(new Event("input")))},getColumnsOrder(){var e=c.$table.find("thead tr");const t=[];return e.find("th").each(function(){t.push(i(this).attr("id"))}),t},getMenuColumnsOrder(){let t=d.getColumnsOrder();const o=[];var n=[];c.$menu.find("input:checked").each(function(){o.push(i(this).val())}),t=t.map(function(e){return d.convertColumnId(e)});for(let e=0;e<t.length;e++){var l=t[e];o.includes(l)&&(n.push(l),o.splice(o.indexOf(l),1))}return n.concat(o)},saveColumnsOrder(){var e={nonce:wpforms_admin.nonce,action:c.$menu.find('[name="action"]').val(),form_id:c.$menu.find('[name="form_id"]').val(),columns:d.getColumnsOrder()};i.post(wpforms_admin.ajax_url,e).done(function(e){e.success||d.displayErrorModal(e.data||wpforms_admin.unknown_error)}).fail(function(){d.displayErrorModal(wpforms_admin.server_error)})},displayErrorModal(e){i.alert({title:wpforms_admin.uh_oh,content:e,icon:"fa fa-exclamation-circle",type:"red",buttons:{cancel:{text:wpforms_admin.close,btnClass:"btn-confirm",keys:["enter"]}}})},updateMenuColumnsOrder(){let o=d.getColumnsOrder();var e=c.$menu.find(".wpforms-multiselect-checkbox-optgroup"),n=c.$menu.find(".wpforms-multiselect-checkbox-items"),t=n.find("label");const l=[0];l[0]=t,e.length&&e.each(function(e){l[e]=i(this).nextUntil(".wpforms-multiselect-checkbox-optgroup")}),o=o.map(function(e){return d.convertColumnId(e)});for(let t=0;t<l.length;t++){l[t]=l[t].filter(function(){return 0<i(this).find("input:checked").length}),l[t].detach();var a=e.eq(t);for(let e=o.length-1;0<=e;e--){const r=o[e];var s=l[t].filter(function(){return 0<i(this).find(`[value="${r}"]`).length});s.length&&(a.length?a.after(s):n.prepend(s))}}},convertColumnId(e){let t=e.replace("wpforms_field_","");return t="notes_count"===(t="entry_id"===(t=t.replace("-foot",""))?"-1":t)?"-2":t},initMultiSelect(){c.$cog.length&&(c.$menu.find(".wpforms-list-table-ext-edit-columns-select").each(function(){var e,t=i(this),o=10<t.find("option").length,o=c.$page.is("#wpforms-entries-list")&&o;t.parent(".wpforms-multiselect-checkbox-dropdown").length||(new n.WPFormsMultiSelectCheckbox(this,{showMask:!0,showSearch:o,customOpener:c.$cog.get(0)}).init(),e=(t=t.next(".wpforms-multiselect-checkbox-wrapper")).find(".wpforms-multiselect-checkbox-list"),d.appendNoResultsText(e),o||t.find(".wpforms-multiselect-checkbox-items").addClass("wpforms-multiselect-checkbox-items-no-search"),e.append('<button type="button" class="button button-secondary" id="wpforms-list-table-ext-edit-columns-select-submit" data-value="save-table-columns">'+wpforms_admin.save_changes+"</button>"),d.updateMenuColumnsOrder())}),c.$searchInput=i("#wpforms-list-table-ext-edit-columns-select-container .wpforms-multiselect-checkbox-search"),c.$menu.removeClass("wpforms-hidden"))},appendNoResultsText(e){e.find(".wpforms-multiselect-checkbox-optgroup").each(function(e){var t=0===e?"fields":"meta",e=0===e?wpforms_admin.column_selector_no_fields:wpforms_admin.column_selector_no_meta;i(this).addClass("wpforms-multiselect-checkbox-optgroup-"+t).after(`<span class="wpforms-multiselect-checkbox-no-results wpforms-multiselect-checkbox-no-results-${t} wpforms-hidden">${e}</span>`)})},initCogIcon(){var e,t=c.$table.find("thead th:not(.hidden):last");return t.length?c.$cog?(t.append(c.$cog),c.$cog):(e=o.cogIcon.replace("#",""),e=i(`<a href="#" title="${wpforms_admin.column_selector_title}" id="${e}"><i class="fa fa-cog" aria-hidden="true"></i></a>`),t.append(e),e):i()},onClickCog(e){e.preventDefault()},onSaveChanges(e){e.preventDefault();e={nonce:wpforms_admin.nonce,action:c.$menu.find('input[name="action"]').val(),form_id:c.$menu.find('input[name="form_id"]').val(),columns:d.getMenuColumnsOrder()};d.closeMenu(),i.post(wpforms_admin.ajax_url,e).done(function(e){e.success?n.location.reload():d.displayErrorModal(e.data||wpforms_admin.unknown_error)}).fail(function(){d.displayErrorModal(wpforms_admin.server_error)})},onMenuToggle(e){i(o.cogIcon).toggleClass("active",e.detail.isOpen),c.$menu.find(".wpforms-multiselect-checkbox-no-results").addClass("wpforms-hidden"),d.positionMultiselectColumnsMenu()},positionMultiselectColumnsMenu(){c.$cog.length&&c.$menu.css({top:c.$cog.offset().top-i("#wpbody-content").offset().top+c.$cog.outerHeight()+6})},isCustomScrollbarNeeded(){var e=navigator.userAgent;return(e.includes("Windows")||e.includes("Linux"))&&(e.includes("Chrome")||e.includes("Firefox"))}};return d}(document,window,jQuery);WPFormsAdminListTableExt.init();