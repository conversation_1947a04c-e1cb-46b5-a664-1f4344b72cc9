!function(l){"use strict";var m,p={settings:{iconActivate:'<i class="fa fa-toggle-on fa-flip-horizontal" aria-hidden="true"></i>',iconDeactivate:'<i class="fa fa-toggle-on" aria-hidden="true"></i>',iconInstall:'<i class="fa fa-cloud-download" aria-hidden="true"></i>',iconSpinner:'<i class="fa fa-spinner fa-spin" aria-hidden="true"></i>',mediaFrame:!1},init:function(){m=this.settings,l(p.ready),p.initEntriesSingle(),p.initEntriesList(),p.initWelcome(),l(document).on("wpformsReady",p.initAddons),p.initSettings(),p.initTools(),p.initUpgrades(),p.initScrollableMenu()},ready:function(){l.ajaxSetup({data:{_wp_http_referer:wpf.updateQueryString("_wp_http_referer",null)}}),p.scrollToIntegration(),l(".notice").show(),l("#screen-meta-links, #screen-meta").prependTo("#wpforms-header-temp").show(),p.initChoicesJS(),l(document).on("htmx:afterSwap",p.initChoicesJS),p.initCheckboxMultiselectColumns(),l(".wpforms-color-picker").each(function(){var e=l(this);e.minicolors({defaultValue:e.data("fallback-color")||""})}),l(".wpforms-file-upload").each(function(){var e=l(this).find("input[type=file]"),n=l(this).find("label"),o=n.html();e.on("change",function(e){var t="";this.files&&1<this.files.length?t=(this.getAttribute("data-multiple-caption")||"").replace("{count}",this.files.length):e.target.value&&(t=e.target.value.split("\\").pop()),t?n.find(".fld").html(t):n.html(o)}),e.on("focus",function(){e.addClass("has-focus")}).on("blur",function(){e.removeClass("has-focus")})}),jconfirm.defaults={closeIcon:!1,backgroundDismiss:!1,escapeKey:!0,animationBounce:1,useBootstrap:!1,theme:"modern",boxWidth:"400px",animateFromElement:!1,content:wpforms_admin.something_went_wrong},l(document).on("click",".wpforms-upgrade-modal",function(){l.alert({title:wpforms_admin.thanks_for_interest,content:wpforms_admin.upgrade_modal,icon:"fa fa-info-circle",type:"blue",boxWidth:"550px",buttons:{confirm:{text:wpforms_admin.ok,btnClass:"btn-confirm",keys:["enter"]}}})}),p.initLity(),p.initFlyoutMenu(),l(document).trigger("wpformsReady"),l("#screen-options-wrap .hide-column-tog").on("change",p.handleOnChangeScreenOptions)},handleOnChangeScreenOptions:function(){var e=l(".wpforms-table-list"),t=e.find("thead .manage-column"),n=t.filter(".hidden"),t=Boolean(5<t.length-n.length);e.toggleClass("has-many-columns",t),e.toggleClass("has-few-columns",!t)},initChoicesJS:function(){l(".choicesjs-select").each(function(){var e=l(this),t=window.wpforms_admin_choicesjs_config?{...window.wpforms_admin_choicesjs_config}:{};e.attr("multiple")&&(t.removeItemButton=void 0===t.removeItemButton||t.removeItemButton),"off"===e.data("sorting")&&(t.shouldSort=!1),e.data("search")&&(t.searchEnabled=!0),e.data("choices-position")&&(t.position=e.data("choices-position")),t.allowHTML=!0,t.callbackOnInit=function(){var e=this,t=l(e.passedElement.element).data("size-class");t&&l(e.containerOuter.element).addClass(t),wpf.initMultipleSelectWithSearch(this),wpf.showMoreButtonForChoices(e.containerOuter.element)},e.data("choicesjs",new Choices(e[0],t))}),l(document).on("click",".choices",function(e){var t=l(this),n=t.find("select").data("choicesjs");n&&t.hasClass("is-open")&&(e.target.classList.contains("choices__inner")||e.target.classList.contains("choices__arrow"))&&n.hideDropdown()}),wpf.initializeChoicesEventHandlers()},initCheckboxMultiselectColumns:function(){l(document).on("change",".checkbox-multiselect-columns input",function(){var e=l(this),t=e.parent(),n=e.closest(".checkbox-multiselect-columns"),t=t.text(),o="check-item-"+e.val(),i=n.find("#"+o);e.prop("checked")?(e.parent().addClass("checked"),i.length||n.find(".second-column ul").append('<li id="'+o+'">'+t+"</li>")):(e.parent().removeClass("checked"),n.find("#"+o).remove())}),l(document).on("click",".checkbox-multiselect-columns .all",function(e){e.preventDefault(),l(this).closest(".checkbox-multiselect-columns").find("input[type=checkbox]").prop("checked",!0).trigger("change"),l(this).remove()})},initFormOverview:function(){console.warn('WARNING! Function "WPFormsAdmin.initFormOverview()" has been deprecated, please use the new "WPFormsForms.Overview.init()" function instead!'),window.WPFormsForms.Overview.init()},initEntriesList(){l(document).on("click","#wpforms-entries-list .form-selector .toggle",function(e){e.preventDefault(),l(this).toggleClass("active").next(".form-list").toggle()}),l(document).on("click","#wpforms-entries-table #doaction",function(e){var t=l(this).closest("form"),n=t.find("table"),o=t.find("select[name=action]"),n=n.find("input[name^=entry_id]:checked");"delete"!==o.val()&&"trash"!==o.val()||!n.length||(o="delete"===o.val()?wpforms_admin.entry_delete_n_confirm:wpforms_admin.entry_trash_n_confirm,e.preventDefault(),l.confirm({title:wpforms_admin.heads_up,content:o.replace("{entry_count}",n.length),icon:"fa fa-exclamation-circle",type:"orange",buttons:{confirm:{text:wpforms_admin.ok,btnClass:"btn-confirm",keys:["enter"],action:function(){t.trigger("submit")}},cancel:{text:wpforms_admin.cancel,keys:["esc"]}}}))}),l(document).on("click","#wpforms-entries-list .wp-list-table .delete",function(e){e.preventDefault();var t=l(this).attr("href");l.confirm({title:wpforms_admin.heads_up,content:wpforms_admin.entry_delete_confirm,icon:"fa fa-exclamation-circle",type:"orange",buttons:{confirm:{text:wpforms_admin.ok,btnClass:"btn-confirm",keys:["enter"],action:function(){window.location=t}},cancel:{text:wpforms_admin.cancel,keys:["esc"]}}})}),l(document).on("click","#wpforms-entries-list .wp-list-table .trash",function(e){e.preventDefault();const t=l(this).attr("href");l.confirm({title:wpforms_admin.heads_up,content:wpforms_admin.entry_trash_confirm,icon:"fa fa-exclamation-circle",type:"orange",buttons:{confirm:{text:wpforms_admin.ok,btnClass:"btn-confirm",keys:["enter"],action:()=>{window.location=t}},cancel:{text:wpforms_admin.cancel,keys:["esc"]}}})}),l(document).on("click","#wpforms-entries-list .wp-list-table .indicator-star",function(e){e.preventDefault();var e=l(this),t=l("#wpforms-entries-list .starred-num"),n=e.parents("table");let o="",i=Number(t.text());e.hasClass("star")?(o="star",i++,e.attr("title",wpforms_admin.entry_unstar)):(o="unstar",i--,e.attr("title",wpforms_admin.entry_star)),e.toggleClass("star unstar"),n.hasClass("wpforms-entries-table-spam")||n.hasClass("wpforms-entries-table-trash")||t.text(i);n={task:o,action:"wpforms_entry_list_star",nonce:wpforms_admin.nonce,entryId:e.data("id"),formId:e.data("form-id")};l.post(wpforms_admin.ajax_url,n)}),l(document).on("click","#wpforms-entries-list .wp-list-table .indicator-read",function(e){e.preventDefault();var e=l(this),t=l("#wpforms-entries-list .unread-num"),n=e.parents("table");let o="",i=Number(t.text());e.hasClass("read")?(o="read",i--,e.attr("title",wpforms_admin.entry_unread)):(o="unread",i++,e.attr("title",wpforms_admin.entry_read)),e.toggleClass("read unread"),n.hasClass("wpforms-entries-table-spam")||n.hasClass("wpforms-entries-table-trash")||t.text(i);n={task:o,action:"wpforms_entry_list_read",nonce:wpforms_admin.nonce,entryId:e.data("id"),formId:e.data("form-id")};l.post(wpforms_admin.ajax_url,n)}),l(document).on("click","#wpforms-entries-list .form-details-actions-removeall",function(e){e.preventDefault();const t=l(this).data("page"),n=p.getDeleteAllNoticeData(t),o=l(this).attr("href"),i=l("#wpforms-entries-table"),s=i.data("filtered-count-trash")&&"trash"===n.action?parseInt(i.data("filtered-count-trash"),10):0,a={action:"wpforms_entry_list_process_"+n.action+"_all",form_id:i.find('input[name="form_id"]').val(),date:i.find('input[name="date"]').val(),page:t,search:{field:i.find('select[name="search[field]"]').val(),comparison:i.find('select[name="search[comparison]"]').val(),term:i.find('input[name="search[term]"]').val()},nonce:wpforms_admin.nonce,url:o};l.confirm({title:wpforms_admin.heads_up,content:s&&l("#wpforms-reset-filter").length?n.content.replace("{entry_count}",s):n.contentAll,icon:"fa fa-exclamation-circle",type:"orange",buttons:{confirm:{text:wpforms_admin.ok,btnClass:"btn-confirm",keys:["enter"],action:()=>{l.get(wpforms_admin.ajax_url,a).done(function(e){e.success&&(window.location=_.isEmpty(e.data)?o:e.data)})}},cancel:{text:wpforms_admin.cancel,keys:["esc"]}}})}),l(document).on("heartbeat-send",function(e,t){var n,o=l("#wpforms-entries-list");o.length&&!o.find(".wpforms-dash-widget").length&&void 0!==(n=o.find("#wpforms-entries-table").data("last-entry-id"))&&(t.wpforms_new_entries_entry_id=n,t.wpforms_new_entries_form_id=o.find("input[name=form_id]").val())}),l(document).on("heartbeat-tick",function(e,t){var n,o,i=l("#wpforms-entries-list");i.length&&t.wpforms_new_entries_notification&&(n=i.find(".wp-list-table thead tr").first().children().length,i.find(".new-entries-notification").length||i.find(".wp-list-table thead").append('<tr class="new-entries-notification"><td colspan="'+n+'"><a href=""></a></td></tr>'),(o=i.find(".new-entries-notification a")).text(t.wpforms_new_entries_notification).slideDown({start:function(){o.css("display","block")},always:function(){o.css("display","block")}}))})},initEntriesSingle:function(){"wpforms-entries"===p.getQueryString("page")&&"details"===p.getQueryString("view")&&p.entryHotkeys(),l(document).on("click","#wpforms-entries-single .wpforms-entry-delete a",function(e){e.preventDefault();const t=l(this).attr("href");l.confirm({title:wpforms_admin.heads_up,content:wpforms_admin.entry_delete_confirm,icon:"fa fa-exclamation-circle",type:"orange",buttons:{confirm:{text:wpforms_admin.ok,btnClass:"btn-confirm",keys:["enter"],action:function(){window.location=t}},cancel:{text:wpforms_admin.cancel,keys:["esc"]}}})}),l(document).on("click","#wpforms-entries-single .trash",function(e){e.preventDefault();const t=l(this).attr("href");l.confirm({title:wpforms_admin.heads_up,content:wpforms_admin.entry_trash_confirm,icon:"fa fa-exclamation-circle",type:"orange",buttons:{confirm:{text:wpforms_admin.ok,btnClass:"btn-confirm",keys:["enter"],action:()=>{window.location=t}},cancel:{text:wpforms_admin.cancel,keys:["esc"]}}})}),l(document).on("click","#wpforms-entries-single .wpforms-entry-print a",function(e){e.preventDefault(),window.open(l(this).attr("href"))}),l(document).on("click","#wpforms-entries-single .wpforms-empty-field-toggle",function(e){e.preventDefault(),"true"===wpCookies.get("wpforms_entry_hide_empty")?(wpCookies.remove("wpforms_entry_hide_empty"),l(this).text(wpforms_admin.entry_empty_fields_hide)):(wpCookies.set("wpforms_entry_hide_empty","true",2592e3),l(this).text(wpforms_admin.entry_empty_fields_show)),l(".wpforms-entry-field.empty, .wpforms-edit-entry-field.empty").toggle()}),l(document).on("click","#wpforms-entries-single .wpforms-entry-notes-new .add",function(e){e.preventDefault(),l(this).hide().next("form").stop().slideToggle()}),l(document).on("click","#wpforms-entries-single .wpforms-entry-notes-new .cancel",function(e){e.preventDefault(),l(this).closest("form").stop().slideToggle(),l(".wpforms-entry-notes-new .add").show()}),l(document).on("click","#wpforms-entries-single .wpforms-entry-notes-byline .note-delete",function(e){e.preventDefault();var t=l(this).attr("href");l.confirm({title:wpforms_admin.heads_up,content:wpforms_admin.entry_note_delete_confirm,icon:"fa fa-exclamation-circle",type:"orange",buttons:{confirm:{text:wpforms_admin.ok,btnClass:"btn-confirm",keys:["enter"],action:function(){window.location=t}},cancel:{text:wpforms_admin.cancel,keys:["esc"]}}})})},entryHotkeys:function(){l(document).on("keydown",function(e){74!==e.keyCode||e.metaKey||p.isFormTypeNode(e.target.nodeName)?75!==e.keyCode||e.metaKey||p.isFormTypeNode(e.target.nodeName)||"#"!==(e=l("#wpforms-admin-single-navigation-next-link").attr("href"))&&(window.location.href=e):"#"!==(e=l("#wpforms-admin-single-navigation-prev-link").attr("href"))&&(window.location.href=e)})},initWelcome:function(){l(document).on("click","#wpforms-welcome .play-video",function(e){e.preventDefault();l.dialog({title:!1,content:'<div class="video-container"><iframe width="1280" height="720" src="https://www.youtube-nocookie.com/embed/SQ9kV9SKz5k?rel=0&amp;showinfo=0&amp;autoplay=1" frameborder="0" allowfullscreen></iframe></div>',closeIcon:!0,boxWidth:"70%"})})},initAddons:function(){if(l("#wpforms-admin-addons").length){var n=l("#wpforms-addons-list-section-all"),o=l("#wpforms-addons-list-section-installed");if(n.length||o.length){let e,t;o.length&&(e=new List("wpforms-addons-list-section-installed",{valueNames:["addon-link"]})),n.length&&(t=new List("wpforms-addons-list-section-all",{valueNames:["addon-link"]})),l("#wpforms-addons-search").on("keyup search",function(){p.updateAddonSearchResult(this,t,e)})}l(document).on("change",".wpforms-addons-list-item .wpforms-toggle-control input",function(e){if(e.preventDefault(),l(this).hasClass("disabled"))return!1;p.addonToggleNew(l(this))}),l(document).on("click",".wpforms-addons-list-item button",function(e){if(e.preventDefault(),l(this).hasClass("disabled"))return!1;p.addonToggleNew(l(this))}),l(document).on("click","#wpforms-admin-addons .addon-item button",function(e){if(e.preventDefault(),l(this).hasClass("disabled"))return!1;p.addonToggle(l(this))})}},updateAddonSearchResult(e,t,n){let o=l(e).val();o=o.replace(/[.,]/g," ");var e=l("#wpforms-addons-no-results"),i=l("#wpforms-addons-list-section-all"),s=l("#wpforms-addons-list-section-installed"),t=t?t.search(o):[],n=n?n.search(o):[];e.toggle(0===t.length&&0===n.length),i.toggle(0<t.length),s.toggle(0<n.length)},setAddonState(e,t,n,o,i){var t={activate:"wpforms_activate_addon",install:"wpforms_install_addon",deactivate:"wpforms_deactivate_addon",incompatible:"wpforms_activate_addon"}[t];t&&(t={action:t,nonce:wpforms_admin.nonce,plugin:e,type:n},l.post(wpforms_admin.ajax_url,t,function(e){o(e)}).fail(function(e){i(e)}))},addonToggleNew(i){const s=i.parents(".wpforms-addons-list-item-footer"),a={active:"wpforms-addons-list-item-footer-active",activating:"wpforms-addons-list-item-footer-activating",incompatible:"wpforms-addons-list-item-footer-incompatible",installed:"wpforms-addons-list-item-footer-installed",missing:"wpforms-addons-list-item-footer-missing",goToUrl:"wpforms-addons-list-item-footer-go-to-url",withError:"wpforms-addons-list-item-footer-with-error"};if(s.hasClass(a.goToUrl))window.open(i.attr("data-plugin"),"_blank");else{i.prop("disabled",!0);let n=i.is(":checked"),o;var e=s.attr("data-plugin");const t=s.attr("data-type"),c=i.parents(".wpforms-addons-list-item"),d=p.getAddonState(s,a,i);function r(e){s.addClass(a.withError),"object"==typeof e.data?s.append(`<div class="wpforms-addons-list-item-footer-error"><p>${"addon"===t?wpforms_admin.addon_error:wpforms_admin.plugin_error}</p></div>`):s.append(`<div class="wpforms-addons-list-item-footer-error"><p>${e.data}</p></div>`),"install"===d?(n=!1,p.removeSpinnerFromButton(i)):"deactivate"===d?n=!0:"activate"===d&&(n=!1)}p.setAddonState(e,d,t,function(e){var t;e.success?(t=e,"install"===d?(o=a.active,n=!0,s.attr("data-plugin",t.data.basename),t.data.is_activated||(o=a.installed,n=!1),i.hide(),i=i.closest(".wpforms-addons-list-item").find(".wpforms-toggle-control input")):"activate"===d?(s.find(".wpforms-addons-list-item-footer-settings-link").fadeIn(150),o=a.active,n=!0):"deactivate"===d&&(s.find(".wpforms-addons-list-item-footer-settings-link").fadeOut(150),o=a.installed,n=!1),s.removeClass(a.active+" "+a.incompatible+" "+a.installed+" "+a.missing).addClass(o)):r(e),p.updateAddonButtonPropertiesAndUI(i,c,s,a,n)},function(){r({data:wpforms_admin.server_error}),p.updateAddonButtonPropertiesAndUI(i,c,s,a,n)})}},addSpinnerToButton(e){var t=e.width();e.data("original-text",e.html()),e.width(t).html('<i class="wpforms-loading-spinner wpforms-loading-blue wpforms-loading-inline"></i>')},removeSpinnerFromButton(e){e.html(e.data("original-text"))},getAddonState(e,t,n){return e.hasClass(t.active)||e.hasClass(t.incompatible)?"deactivate":e.hasClass(t.installed)?"activate":e.hasClass(t.missing)?(p.addSpinnerToButton(n),"install"):""},updateAddonButtonPropertiesAndUI(e,t,n,o,i){e.prop("checked",i),e.prop("disabled",!1),e.siblings(".wpforms-toggle-control-status").html(e.siblings(".wpforms-toggle-control-status").data(i?"on":"off")),0<t.find(".wpforms-addons-list-item-footer-error").length&&setTimeout(function(){n.removeClass(o.withError),t.find(".wpforms-addons-list-item-footer-error").remove()},6e3)},scrollToIntegration(){var e=window.location.href,e=new URL(e),t=e.searchParams,n=t.get("addon");n&&(n=l('.wpforms-settings-provider[id*="'+n+'"]')).length&&(l(window).scrollTop(n.offset().top),t.delete("addon"),window.history.pushState({},document.title,e.toString()))},addonToggle(n){let o,i,s,a,r,c;if(n.hasClass("status-go-to-url"))window.open(n.attr("data-plugin"),"_blank");else{n.prop("disabled",!0).addClass("loading"),n.html(m.iconSpinner);const d=n.attr("data-type");if(n.hasClass("status-active"))o="deactivate",i="status-installed","plugin"===d&&(i+=" button button-secondary"),s=wpforms_admin.addon_inactive,a=wpforms_admin.addon_activate,r=wpforms_admin.addon_deactivate,"addon"===d&&(a=m.iconActivate+a,r=m.iconDeactivate+r);else if(n.hasClass("status-installed"))o="activate",i="status-active","plugin"===d&&(i+=" button button-secondary disabled"),s=wpforms_admin.addon_active,a=wpforms_admin.addon_deactivate,"addon"===d?(a=m.iconDeactivate+a,r=m.iconActivate+wpforms_admin.addon_activate):"plugin"===d&&(a=wpforms_admin.addon_activated,r=wpforms_admin.addon_activate);else{if(!n.hasClass("status-missing"))return;o="install",i="status-active","plugin"===d&&(i+=" button disabled"),s=wpforms_admin.addon_active,a=wpforms_admin.addon_activated,r=m.iconInstall,"addon"===d&&(a=m.iconActivate+wpforms_admin.addon_deactivate,r+=wpforms_admin.addon_install)}var e=n.attr("data-plugin");p.setAddonState(e,o,d,function(e){var t=n.closest(".addon-item");e.success?("install"===o?(n.attr("data-plugin",e.data.basename),c=e.data.msg,e.data.is_activated||(s=wpforms_admin.addon_inactive,a="plugin"===d?wpforms_admin.addon_activate:m.iconActivate+wpforms_admin.addon_activate,i="plugin"===d?"status-installed button button-secondary":"status-installed")):c=e.data,t.find(".actions").append('<div class="msg success">'+c+"</div>"),t.find("span.status-label").removeClass("status-active status-installed status-missing").addClass(i).removeClass("button button-primary button-secondary disabled").text(s),n.removeClass("status-active status-installed status-missing").removeClass("button button-primary button-secondary disabled").addClass(i).html(a)):("object"==typeof e.data?"addon"===d?t.find(".actions").append('<div class="msg error"><p>'+wpforms_admin.addon_error+"</p></div>"):t.find(".actions").append('<div class="msg error"><p>'+wpforms_admin.plugin_error+"</p></div>"):t.find(".actions").append('<div class="msg error"><p>'+e.data+"</p></div>"),"install"===o&&"plugin"===d&&n.addClass("status-go-to-url").removeClass("status-missing"),n.html(r)),n.prop("disabled",!1).removeClass("loading"),t.find(".actions").find(".msg.error").length||setTimeout(function(){l(".addon-item .msg").remove()},3e3)},function(e){console.log(e.responseText)})}},initSettings:function(){l(document).on("wpformsReady",function(){var e,t;l("#wpforms-settings").length&&(e=p.getQueryString("wpforms-integration"),t=p.getQueryString("jump"),e?l("body").animate({scrollTop:l("#wpforms-integration-"+e).offset().top},1e3):t&&l("body").animate({scrollTop:l("#"+t).offset().top},1e3),l(".wpforms-admin-settings-form").conditions([{conditions:{element:"#wpforms-setting-gdpr",type:"checked",operator:"is"},actions:{if:{element:"#wpforms-setting-row-gdpr-disable-uuid,#wpforms-setting-row-gdpr-disable-details",action:"show"},else:{element:"#wpforms-setting-row-gdpr-disable-uuid,#wpforms-setting-row-gdpr-disable-details",action:"hide"}},effect:"appear"},{conditions:{element:"input[name=captcha-provider]:checked",type:"value",operator:"=",condition:"hcaptcha"},actions:{if:[{element:".wpforms-setting-row",action:"show"},{element:".wpforms-setting-recaptcha, #wpforms-setting-row-recaptcha-site-key, #wpforms-setting-row-recaptcha-secret-key, #wpforms-setting-row-recaptcha-fail-msg, .wpforms-setting-turnstile, #wpforms-setting-row-turnstile-heading, #wpforms-setting-row-turnstile-site-key, #wpforms-setting-row-turnstile-secret-key, #wpforms-setting-row-turnstile-theme, #wpforms-setting-row-turnstile-fail-msg",action:"hide"}]},effect:"appear"},{conditions:{element:"input[name=captcha-provider]:checked",type:"value",operator:"=",condition:"recaptcha"},actions:{if:[{element:".wpforms-setting-row",action:"show"},{element:"#wpforms-setting-row-hcaptcha-heading, #wpforms-setting-row-hcaptcha-site-key, #wpforms-setting-row-hcaptcha-secret-key, #wpforms-setting-row-hcaptcha-fail-msg, #wpforms-setting-row-turnstile-heading, #wpforms-setting-row-turnstile-site-key, #wpforms-setting-row-turnstile-secret-key, #wpforms-setting-row-turnstile-theme, #wpforms-setting-row-turnstile-fail-msg",action:"hide"}]},effect:"appear"},{conditions:{element:"input[name=captcha-provider]:checked",type:"value",operator:"=",condition:"turnstile"},actions:{if:[{element:".wpforms-setting-row",action:"show"},{element:"#wpforms-setting-row-hcaptcha-heading, #wpforms-setting-row-hcaptcha-site-key, #wpforms-setting-row-hcaptcha-secret-key, #wpforms-setting-row-hcaptcha-fail-msg, .wpforms-setting-recaptcha, #wpforms-setting-row-recaptcha-site-key, #wpforms-setting-row-recaptcha-secret-key, #wpforms-setting-row-recaptcha-fail-msg",action:"hide"}]},effect:"appear"},{conditions:{element:"input[name=captcha-provider]:checked",type:"value",operator:"=",condition:"none"},actions:{if:[{element:".wpforms-setting-row",action:"hide"},{element:".wpforms-setting-captcha-heading, #wpforms-setting-row-captcha-provider",action:"show"}]},effect:"appear"}]))}),l(document).on("change","#wpforms-setting-row-render-engine input",p.settingsRenderEngineChange),l(document).on("change","#wpforms-setting-disable-css",function(){p.settingsFormStylesAlert(l(this).val())}),l(document).on("click",".wpforms-setting-row-image button",function(e){e.preventDefault(),l(this).hasClass("wpforms-setting-remove-image")?l(this).closest(".wpforms-setting-row-image").find("input").val("").attr("value","").trigger("change").end().find("img").remove():p.imageUploadModal(l(this))}),l(document).on("click","#wpforms-setting-license-key-verify",function(e){e.preventDefault(),p.licenseVerify(l(this))}),l(document).on("click",".wpforms-setting-license-wrapper",function(e){e.preventDefault();e=l("#wpforms-setting-license-key");e.length&&e.prop("disabled")&&p.licenseEditMessage()}),l(document).on("click","#wpforms-setting-license-key-deactivate",function(e){e.preventDefault(),p.licenseDeactivate(l(this))}),l(document).on("click","#wpforms-setting-license-key-refresh",function(e){e.preventDefault(),p.licenseRefresh(l(this))}),l(document).on("click",".wpforms-settings-provider-connect",function(e){e.preventDefault();e=l(this);p.integrationConnect(e)}),l(document).on("click",".wpforms-settings-provider-accounts-list .remove a",function(e){e.preventDefault(),p.integrationDisconnect(l(this))}),l(document).on("click",".wpforms-settings-provider:not(.focus-out) .wpforms-settings-provider-header",function(e){e.preventDefault();var t=l(this);t.parent().find(".wpforms-settings-provider-accounts").stop(!1,!0).slideToggle("",function(){t.parent().find(".wpforms-settings-provider-logo i").toggleClass("fa-chevron-right fa-chevron-down")})}),l(document).on("click",".wpforms-settings-provider-accounts-toggle a",function(e){e.preventDefault();e=l(this).parent().next(".wpforms-settings-provider-accounts-connect");e.find("input[type=text], input[type=password]").val(""),e.stop().slideToggle()}),l(document).on("change","#wpforms-setting-row-captcha-provider input",function(){var e=l("#wpforms-setting-row-captcha-preview");"hcaptcha"===this.value||"turnstile"===this.value?e.removeClass("wpforms-hidden"):"none"===this.value?e.addClass("wpforms-hidden"):l("#wpforms-setting-row-recaptcha-type input:checked").trigger("change"),e.find(".wpforms-captcha-preview").length&&(e.find(".wpforms-captcha-preview").empty(),e.find(".wpforms-captcha-placeholder").removeClass("wpforms-hidden"))}),l(document).on("change","#wpforms-setting-row-recaptcha-type input",function(){l("#wpforms-setting-row-captcha-preview").toggleClass("wpforms-hidden","v2"!==this.value),l("#wpforms-setting-row-recaptcha-v3-threshold").toggleClass("wpforms-hidden","v3"!==this.value)}),l(document).on("change",".wpforms-toggle-control input",function(){var e=l(this),t=e.is(":checked"),n=t?"on":"off",o=e.closest(".wpforms-setting-field"),e=e.closest(".wpforms-toggle-control").find(".wpforms-toggle-control-status"),i=o.find(".wpforms-toggle-desc.desc-on"),o=o.find(".wpforms-toggle-desc.desc-off"),s=0<i.length&&0<o.length;i.toggleClass("wpforms-hidden",!t&&s),o.toggleClass("wpforms-hidden",t&&s),e.html(e.data(n))})},settingsRenderEngineChange:function(e){l(this).val()},settingsFormStylesAlert:function(e){if("2"===e)var t=wpforms_admin.settings_form_style_base;else{if("3"!==e)return;t=wpforms_admin.settings_form_style_none}l.alert({title:wpforms_admin.heads_up,content:t,icon:"fa fa-exclamation-circle",type:"orange",buttons:{confirm:{text:wpforms_admin.ok,btnClass:"btn-confirm",keys:["enter"]}}})},imageUploadModal(e){const n=e.closest(".wpforms-setting-field");m.mediaFrame=wpf.initMediaLibrary({title:wpforms_admin.upload_image_title,extensions:wpforms_admin.upload_image_extensions,extensionsError:wpforms_admin.upload_image_extensions_error,buttonText:wpforms_admin.upload_image_button}),m.mediaFrame.on("select",function(){var e=m.mediaFrame.state().get("selection").first().toJSON(),t=n.find("input[type=text]");t.val(e.url),n.find("img").remove(),n.prepend('<img src="'+e.url+'">'),t.trigger("change")}).on("close",function(){m.mediaFrame.off("library:selection:add")}),m.mediaFrame.open()},licenseVerify:function(i){var s=i.closest(".wpforms-setting-row"),a=l("#wpforms-setting-license-key"),e=i.outerWidth(),r=i.text(),t={action:"wpforms_verify_license",nonce:wpforms_admin.nonce,license:a.val()};i.html(m.iconSpinner).css("width",e).prop("disabled",!0),l.post(wpforms_admin.ajax_url,t,function(e){var t,n="fa fa-check-circle",o="green";e.success?(t=e.data.msg,i.hide(),s.find("#wpforms-setting-license-key-info-message").empty().hide(),s.find(".type, .desc, #wpforms-setting-license-key-deactivate").show(),s.find(".type strong").text(e.data.type),l(".wpforms-license-notice").remove(),a.prop("disabled",!0).addClass("wpforms-setting-license-is-valid").attr("value",a.val())):(n="fa fa-exclamation-circle",o="orange",t=e.data,s.find(".type, .desc, #wpforms-setting-license-key-deactivate").hide(),a.prop("disabled",!1)),l.alert({title:t.header??!1,content:t.msg??t,icon:n,type:o,buttons:{confirm:{text:wpforms_admin.ok,btnClass:"btn-confirm",keys:["enter"]}}}),i.html(r).css("width","auto").prop("disabled",!1)}).fail(function(e){a.prop("disabled",!1),console.log(e.responseText)})},licenseEditMessage:function(){l.alert({title:wpforms_admin.heads_up,content:wpforms_admin.edit_license,icon:"fa fa-exclamation-circle",type:"orange",buttons:{confirm:{text:wpforms_admin.ok,btnClass:"btn-confirm",keys:["enter"]}}})},licenseDeactivate:function(e){const a=l(e),r=a.closest(".wpforms-setting-row");e=a.outerWidth();const c=a.text();var t={action:"wpforms_deactivate_license",nonce:wpforms_admin.nonce};a.html(m.iconSpinner).css("width",e).prop("disabled",!0),l.post(wpforms_admin.ajax_url,t,function(e){let t="fa fa-info-circle",n="blue",o=wpforms_admin.success;var i=e.data,s=i.msg&&"string"==typeof i.msg?i.msg:wpforms_admin.something_went_wrong;e.success?(r.find("#wpforms-setting-license-key").val("").attr("value","").prop({readonly:!1,disabled:!1}).removeClass(),r.find(".wpforms-license-key-deactivate-remove").remove(),r.find("#wpforms-setting-license-key-info-message").html(i.info).show(),r.find("#wpforms-setting-license-key-verify").prop("disabled",!1).show(),r.find(".type, .desc, #wpforms-setting-license-key-deactivate").hide()):(t="fa fa-exclamation-circle",n="orange",o=wpforms_admin.oops),l.alert({title:o,content:s,icon:t,type:n,buttons:{confirm:{text:wpforms_admin.ok,btnClass:"btn-confirm",keys:["enter"]}}}),a.html(c).css("width","auto").prop("disabled",!1)}).fail(function(e){console.log(e.responseText)})},licenseRefresh:function(e){var i=l(e).closest(".wpforms-setting-row"),s=l("#wpforms-setting-license-key"),e={action:"wpforms_refresh_license",nonce:wpforms_admin.nonce,license:s.val()};l.post(wpforms_admin.ajax_url,e,function(e){var t,n="fa fa-check-circle",o="green";e.success?(t=e.data.msg,i.find(".type strong").text(e.data.type)):(n="fa fa-exclamation-circle",o="orange",t=e.data,i.find(".type, .desc").hide(),s.removeClass("wpforms-setting-license-is-valid").addClass("wpforms-setting-license-is-invalid")),l.alert({title:t.header??!1,content:t.msg??t,icon:n,type:o,buttons:{confirm:{text:wpforms_admin.ok,btnClass:"btn-confirm",keys:["enter"]}}})}).fail(function(e){console.log(e.responseText)})},integrationConnect:function(t){var e=t.outerWidth(),n=t.text(),o=t.closest(".wpforms-settings-provider"),i={action:"wpforms_settings_provider_add_"+t.data("provider"),data:t.closest("form").serialize(),provider:t.data("provider"),nonce:wpforms_admin.nonce},s=wpforms_admin.provider_auth_error;t.html(wpforms_admin.connecting).css("width",e).prop("disabled",!0),l.post(wpforms_admin.ajax_url,i,function(e){e.success?(o.find(".wpforms-settings-provider-accounts-list ul").append(e.data.html),o.addClass("connected"),t.closest(".wpforms-settings-provider-accounts-connect").stop().slideToggle()):(Object.prototype.hasOwnProperty.call(e,"data")&&Object.prototype.hasOwnProperty.call(e.data,"error_msg")&&(s+="<br>"+e.data.error_msg),p.integrationError(s))}).fail(function(){p.integrationError(s)}).always(function(){t.html(n).css("width","auto").prop("disabled",!1)})},integrationDisconnect:function(e){var n=l(e),o=n.parents(".wpforms-settings-provider"),t={action:"wpforms_settings_provider_disconnect_"+n.data("provider"),provider:n.data("provider"),key:n.data("key"),nonce:wpforms_admin.nonce},i=wpforms_admin.provider_delete_error;l.confirm({title:wpforms_admin.heads_up,content:wpforms_admin.provider_delete_confirm,icon:"fa fa-exclamation-circle",type:"orange",buttons:{confirm:{text:wpforms_admin.ok,btnClass:"btn-confirm",keys:["enter"],action:function(){l.post(wpforms_admin.ajax_url,t,function(e){var t;e.success?(n.parent().parent().remove(),void 0!==(t=o.find(".wpforms-settings-provider-accounts-list li").length)&&0!==t||o.removeClass("connected"),l(document).trigger("wpformsProviderRemoved",[o,e])):(Object.prototype.hasOwnProperty.call(e,"data")&&Object.prototype.hasOwnProperty.call(e.data,"error_msg")&&(i+="<br>"+e.data.error_msg),p.integrationError(i))}).fail(function(){p.integrationError(i)})}},cancel:{text:wpforms_admin.cancel,keys:["esc"]}}})},integrationError:function(e){l.alert({title:wpforms_admin.something_went_wrong,content:e,icon:"fa fa-exclamation-circle",type:"orange",buttons:{confirm:{text:wpforms_admin.ok,btnClass:"btn-confirm",keys:["enter"]}}})},initTools(){l(document).on("change","#wpforms-tools-form-import, #wpforms-tools-form-other-import, #wpforms-tools-form-export, #wpforms-tools-form-template",function(){var e=l(this);e.parents("form").find("button").attr("aria-disabled",0===e.val().length)}),l(document).on("click","#wpforms-system-information-copy",function(e){e.preventDefault(),p.copySystemInformation()}),l(document).on("click","#wpforms-ssl-verify",function(e){e.preventDefault(),p.verifySSLConnection()}),l(document).on("click","#wpforms-recreate-tables",function(e){e.preventDefault(),p.recreateTables()}),l(document).on("click","#wpforms-importer-forms-submit",function(e){if(e.preventDefault(),l("#wpforms-importer-forms input:checked").length){const t=[];l("#wpforms-importer-forms input:checked").each(function(e){t[e]=l(this).val()}),wpforms_admin.isPro?p.importForms(t):p.analyzeForms(t)}else l.alert({title:wpforms_admin.heads_up,content:wpforms_admin.importer_forms_required,icon:"fa fa-info-circle",type:"blue",buttons:{confirm:{text:wpforms_admin.ok,btnClass:"btn-confirm",keys:["enter"]}}})}),l(document).on("click","#wpforms-importer-continue-submit",function(e){e.preventDefault(),p.importForms(m.formIDs)})},copySystemInformation(){l("#wpforms-system-information").select(),document.execCommand("copy")},verifySSLConnection(){const t=l("#wpforms-ssl-verify"),n=t.text(),o=t.outerWidth(),i=t.parent();t.css("width",o).prop("disabled",!0).text(wpforms_admin.testing);var e={action:"wpforms_verify_ssl",nonce:wpforms_admin.nonce};l.post(wpforms_admin.ajax_url,e,function(e){p.debug(e),i.find(".wpforms-notice").remove(),e.success&&t.before('<div class="notice wpforms-notice notice-success">'+e.data.msg+"</div>"),!e.success&&e.data.msg&&t.before('<div class="notice wpforms-notice notice-error">'+e.data.msg+"</div>"),!e.success&&e.data.debug&&t.before('<div class="wpforms-ssl-error pre-error">'+e.data.debug+"</div>"),t.css("width",o).prop("disabled",!1).text(n)})},recreateTables(){const t=l("#wpforms-recreate-tables"),e=t.text(),n=t.outerWidth(),o=t.parent();t.css("width",n).prop("disabled",!0).text(wpforms_admin.recreating);var i={action:"wpforms_recreate_tables",nonce:wpforms_admin.nonce};l.post(wpforms_admin.ajax_url,i,function(e){p.debug(e),o.find(".wpforms-notice").remove(),e.success&&(t.before('<div class="notice wpforms-notice notice-success">'+e.data.msg+"</div>"),t.hide()),!e.success&&e.data.msg&&t.before('<div class="notice wpforms-notice notice-error">'+e.data.msg+"</div>"),!e.success&&e.data.debug&&t.before('<div class="wpforms-ssl-error pre-error">'+e.data.debug+"</div>")}).always(function(){t.css("width",n).prop("disabled",!1).text(e)})},analyzeForms:function(e){var t=l("#wpforms-importer-analyze");t.find(".form-total").text(e.length),t.find(".form-current").text("1"),l("#wpforms-importer-forms").hide(),t.show(),m.analyzeQueue=e,m.analyzed=0,m.analyzeUpgrade=[],m.formIDs=e,p.analyzeForm()},analyzeForm:function(){var t=l("#wpforms-importer-analyze"),n=_.first(m.analyzeQueue),e={action:"wpforms_import_form_"+p.getQueryString("provider"),analyze:1,form_id:n,nonce:wpforms_admin.nonce};l.post(wpforms_admin.ajax_url,e,function(e){e.success&&(_.isEmpty(e.data.upgrade_plain)&&_.isEmpty(e.data.upgrade_omit)||m.analyzeUpgrade.push({name:e.data.name,fields:_.union(e.data.upgrade_omit,e.data.upgrade_plain)}),m.analyzeQueue=_.without(m.analyzeQueue,n),m.analyzed++,_.isEmpty(m.analyzeQueue)?_.isEmpty(m.analyzeUpgrade)?p.importForms(m.formIDs):(e=wp.template("wpforms-importer-upgrade"),t.find(".upgrade").append(e(m.analyzeUpgrade)),t.find(".upgrade").show(),t.find(".process-analyze").hide()):(t.find(".form-current").text(m.analyzed+1),p.analyzeForm()))})},importForms:function(e){var t=l("#wpforms-importer-process");t.find(".form-total").text(e.length),t.find(".form-current").text("1"),l("#wpforms-importer-forms, #wpforms-importer-analyze").hide(),t.show(),m.importQueue=e,m.imported=0,p.importForm()},importForm:function(){var n=l("#wpforms-importer-process"),o=_.first(m.importQueue),e={action:"wpforms_import_form_"+p.getQueryString("provider"),form_id:o,nonce:wpforms_admin.nonce};l.post(wpforms_admin.ajax_url,e,function(e){var t;e.success&&(t=e.data.error?wp.template("wpforms-importer-status-error"):wp.template("wpforms-importer-status-update"),n.find(".status").prepend(t(e.data)),n.find(".status").show(),m.importQueue=_.without(m.importQueue,o),m.imported++,_.isEmpty(m.importQueue)?(n.find(".process-count").hide(),n.find(".forms-completed").text(m.imported),n.find(".process-completed").show()):(n.find(".form-current").text(m.imported+1),p.importForm()))})},initUpgrades:function(){l(document).on("click","#wpforms-upgrade-143 button",function(e){e.preventDefault();var t=l(this),e=t.outerWidth(),n=l("#wpforms-upgrade-143 .status"),o={action:"wpforms_upgrade_143",nonce:wpforms_admin.nonce,init:!0,incomplete:t.data("incomplete")};t.html(m.iconSpinner).css("width",e).prop("disabled",!0),l.post(wpforms_admin.ajax_url,o,function(e){e.success&&(m.upgraded=Number(e.data.upgraded),m.upgradeTotal=Number(e.data.total),e=Math.round(Number(m.upgraded)/Number(m.upgradeTotal)*100),t.remove(),n.find(".bar").css("width",e+"%"),n.show().find(".total").text(m.upgradeTotal),n.find(".current").text(m.upgraded),n.find(".percent").text(e+"%"),p.upgrade143())})})},upgrade143:function(){var n=l("#wpforms-upgrade-143 .status"),e={action:"wpforms_upgrade_143",nonce:wpforms_admin.nonce,upgraded:m.upgraded};l.post(wpforms_admin.ajax_url,e,function(e){var t;e.success&&(m.upgraded=Number(m.upgraded)+Number(e.data.count),t=Math.round(Number(m.upgraded)/Number(m.upgradeTotal)*100),n.find(".bar").css("width",t+"%"),Number(e.data.count)<10?(n.find(".progress-bar").addClass("complete"),n.find(".msg").text(wpforms_admin.upgrade_completed)):(n.find(".current").text(m.upgraded),n.find(".percent").text(t+"%"),p.upgrade143()))})},initFlyoutMenu:function(){var e,t,n,a,r,c=l("#wpforms-flyout");0!==c.length&&(e=c.find(".wpforms-flyout-head"),t=e.find("img"),n={state:"inactive",srcInactive:t.attr("src"),srcActive:t.data("active")},e.on("click",function(e){e.preventDefault(),"active"===n.state?(c.removeClass("opened"),t.attr("src",n.srcInactive),n.state="inactive"):(c.addClass("opened"),t.attr("src",n.srcActive),n.state="active")}),0!==(a=l("#wpfooter")).length)&&(r=l("#wpforms-overview, #wpforms-entries-list, #wpforms-tools.wpforms-tools-tab-action-scheduler, #wpforms-tools.wpforms-tools-tab-logs"),l(window).on("resize scroll",_.debounce(function(e){var t=a.offset().top,n=t+a.height(),o=0<r.length?r.offset().top+r.height()+85:0,i=l(window).scrollTop(),s=i+l(window).height();n<=s&&i<=t&&s<o?c.addClass("out"):c.removeClass("out")},50)),l(window).trigger("scroll"))},initLity:function(){l(document).on("lity:ready",function(e,t){var n=t.element(),t=t.opener(),t=void 0!==t?t.data("lity-srcset"):"";void 0!==t&&""!==t&&n.find(".lity-content img").attr("srcset",t)})},isFormTypeNode:function(e){return"TEXTAREA"===(e=e||!1)||"INPUT"===e||"SELECT"===e},getQueryString:function(e){e=new RegExp("[?&]"+e+"=([^&]*)").exec(window.location.search);return e&&decodeURIComponent(e[1].replace(/\+/g," "))},debug:function(e){p.isDebug()&&("object"==typeof e||e.constructor===Array?(console.log("WPForms Debug:"),console.log(e)):console.log("WPForms Debug: "+e))},isDebug:function(){return window.location.hash&&"#wpformsdebug"===window.location.hash},getDeleteAllNoticeData:(e="")=>["spam","trash"].includes(e)?{contentAll:wpforms_admin.entry_delete_all_confirm,content:wpforms_admin.entry_delete_n_confirm,action:"delete"}:{contentAll:wpforms_admin.entry_trash_all_confirm,content:wpforms_admin.entry_trash_n_confirm,action:"trash"},initScrollableMenu(){l(document).on("wpformsReady",function(){const e=l(".wpforms-admin-tabs");if(e.length){const t=e.find("li:last-child");wpf.isInViewport(t)||e.addClass("wpforms-admin-tabs--scrollable"),e.on("scroll",function(){e.toggleClass("wpforms-admin-tabs--scrollable",!wpf.isInViewport(t))})}})}};p.init(),window.WPFormsAdmin=p}(jQuery);