var WPFormsEducation=window.WPFormsEducation||{};WPFormsEducation.core=window.WPFormsEducation.core||function(t,a,r){const i='<i class="wpforms-loading-spinner wpforms-loading-white wpforms-loading-inline"></i>',s={init(){r(s.ready)},ready(){s.events()},events(){s.dismissEvents(),s.openModalButtonClick(),s.setDyk<PERSON>olspan(),s.gotoAdvancedTabClick(),s.proFieldDelete()},openModalButtonClick(){r(t).on("click",".education-modal:not(.wpforms-add-fields-button)",s.openModalButtonHandler).on("mousedown",".education-modal.wpforms-add-fields-button",s.openModalButtonHandler).on("click",".education-action-button",s.actionButtonHand<PERSON>)},actionButtonHandler(t){t.preventDefault();var e,t=r(this);"upgrade"===t.data("action")&&(e=t.data("utm-content"),t=t.data("license"),a.open(WPFormsEducation.core.getUpgradeURL(e,t),"_blank"))},openModalButtonHandler(t){t.preventDefault();var e=r(this);switch(e.data("action")){case"activate":s.activateModal(e);break;case"install":s.installModal(e);break;case"incompatible":s.incompatibleModal(e)}},proFieldDelete(){r("#wpforms-builder").on("wpformsFieldDelete",function(){r(".wpforms-field-wrap .wpforms-field-is-pro").length||r(".wpforms-preview .wpforms-pro-fields-notice").addClass("wpforms-hidden")})},dismissEvents(){r(t).on("click",".wpforms-dismiss-container .wpforms-dismiss-button",function(){const t=r(this),e=t.closest(".wpforms-dismiss-container"),o={action:"wpforms_education_dismiss",nonce:wpforms_education.nonce,section:t.data("section"),page:"string"==typeof a.pagenow?a.pagenow:""};let n=e.find(".wpforms-dismiss-out");0<(n=e.hasClass("wpforms-dismiss-out")?e:n).length?(n.addClass("out"),setTimeout(function(){e.remove()},300)):e.remove(),r.post(wpforms_education.ajax_url,o)})},setDykColspan(){r("#adv-settings").on("change","input.hide-column-tog",function(){var t=r(".wpforms-dyk td"),e=r(".wp-list-table thead .manage-column").not(".hidden").length;t.attr("colspan",e)})},gotoAdvancedTabClick(){r(t).on("click",".wpforms-educational-alert.wpforms-calculations a",function(t){var e=r(this);"#advanced-tab"===e.attr("href")&&(t.preventDefault(),e.closest(".wpforms-field-option").find(".wpforms-field-option-group-advanced .wpforms-field-option-group-toggle").trigger("click"))})},getUTMContentValue(t){return t.hasClass("wpforms-add-fields-button")?t.data("utm-content")+" Field":t.hasClass("wpforms-template-select")?s.slugToUTMContent(t.data("slug")):t.hasClass("wpforms-panel-sidebar-section")?s.slugToUTMContent(t.data("slug"))+" Addon":t.data("utm-content")||t.data("name")},slugToUTMContent(t){return t?t.toString().replace(/[^a-z\d ]/gi," ").replace(/\b[a-z]/g,function(t){return t.toUpperCase()}):""},getUpgradeURL(t,e){let o=wpforms_education.upgrade[e].url,n=(-1<t.toLowerCase().indexOf("template")&&(o=wpforms_education.upgrade[e].url_template),/(\?)/.test(o)?"&":"?");return-1===o.indexOf("https://wpforms.com")&&(n=encodeURIComponent(n)),o+n+"utm_content="+encodeURIComponent(t.trim())},upgradeModalThankYou:t=>{r.alert({title:wpforms_education.thanks_for_interest,content:wpforms_education.upgrade[t].modal,icon:"fa fa-info-circle",type:"blue",boxWidth:"565px",buttons:{confirm:{text:wpforms_education.ok,btnClass:"btn-confirm",keys:["enter"]}}})},getSpinner:()=>i,activateModal(t){var e=t.data("name"),o=t.data("message"),n=wpforms_education.can_activate_addons;r.alert({title:!1,content:o||wpforms_education.activate_prompt.replace(/%name%/g,e),icon:"fa fa-info-circle",type:"blue",buttons:{confirm:{text:wpforms_education.activate_confirm,btnClass:"btn-confirm"+(n?"":" hidden"),keys:["enter"],isHidden:!n,action(){return this.$$confirm.prop("disabled",!0).html(i+wpforms_education.activating),this.$$cancel.prop("disabled",!0),s.activateAddon(t,this),!1}},cancel:{text:wpforms_education.cancel}}})},activateAddon(e,o){const t=e.data("path"),n=e.data("type"),a=e.data("nonce"),i=e.data("hide-on-success");r.post(wpforms_education.ajax_url,{action:"wpforms_activate_addon",nonce:a,plugin:t,type:n},function(t){o.close(),t.success?(i&&e.hide(),s.saveModal("plugin"===n?wpforms_education.plugin_activated:wpforms_education.addon_activated)):r.alert({title:!1,content:t.data,icon:"fa fa-exclamation-circle",type:"orange",buttons:{confirm:{text:wpforms_education.close,btnClass:"btn-confirm",keys:["enter"]}}})})},saveModal(t,e=!1,o=void 0){t=t||wpforms_education.addon_activated,e=e||wpforms_education.save_prompt,r.alert({title:t.replace(/\.$/,""),content:e,icon:"fa fa-check-circle",type:"green",buttons:{confirm:{text:o?.saveConfirm||wpforms_education.save_confirm,btnClass:"btn-confirm",keys:["enter"],action(){if("undefined"==typeof WPFormsBuilder)location.reload();else{var t;if(this.$$confirm.prop("disabled",!0).html(i+wpforms_education.saving),this.$$cancel.prop("disabled",!0),!WPFormsBuilder.formIsSaved())return!(t=WPFormsBuilder.formSave(!1))||(t.done(function(){s.redirect(o?.redirectUrl)}),!1);s.redirect(o?.redirectUrl)}}},cancel:{text:wpforms_education.close}}})},redirect(t){t?location.href=t:location.reload()},installModal(t){var e,o=t.data("name"),n=t.data("url");n&&""!==n?(n=wpforms_education.can_install_addons,e=t.data("message"),r.alert({title:!1,content:e||wpforms_education.install_prompt.replace(/%name%/g,o),icon:"fa fa-info-circle",type:"blue",boxWidth:"425px",buttons:{confirm:{text:wpforms_education.install_confirm,btnClass:"btn-confirm"+(n?"":" hidden"),keys:["enter"],isHidden:!n,action(){return this.$$confirm.prop("disabled",!0).html(i+wpforms_education.installing),this.$$cancel.prop("disabled",!0),s.installAddon(t,this),!1}},cancel:{text:wpforms_education.cancel}}})):wpf.debug(`Couldn't install the ${o} addon: Empty install URL.`)},incompatibleModal(t){var e=wpforms_education.addon_incompatible.title,t=t.data("message")||wpforms_education.addon_error;r.alert({title:e,content:t,icon:"fa fa-exclamation-circle",type:"orange",buttons:{confirm:{text:wpforms_education.addon_incompatible.button_text,btnClass:"btn-confirm",keys:["enter"],action(){var t;return"undefined"==typeof WPFormsBuilder?s.redirect(wpforms_education.addon_incompatible.button_url):(this.$$confirm.prop("disabled",!0).html(i+this.$$confirm.text()),this.$$cancel.prop("disabled",!0),WPFormsBuilder.formIsSaved()?s.redirect(wpforms_education.addon_incompatible.button_url):(t=WPFormsBuilder.formSave(!1))&&t.done(function(){s.redirect(wpforms_education.addon_incompatible.button_url)})),!1}},cancel:{text:wpforms_education.cancel}}})},installAddon(t,o){const e=t.data("url"),n=t.data("type"),a=t.data("nonce"),i=t.data("hide-on-success");r.post(wpforms_education.ajax_url,{action:"wpforms_install_addon",nonce:a,plugin:e,type:n},function(e){if(o.close(),e.success)i&&t.hide(),s.saveModal(e.data.msg);else{let t=e.data;"object"==typeof e.data&&(t=wpforms_education.addon_error),r.alert({title:!1,content:t,icon:"fa fa-exclamation-circle",type:"orange",buttons:{confirm:{text:wpforms_education.close,btnClass:"btn-confirm",keys:["enter"]}}})}})},getUpgradeModalWidth(t){var e=r(a).width();return e<=300?"250px":e<=750?"350px":!t||e<=1024?"550px":1070<e?"1040px":"994px"},errorModal(t,e){r.alert({title:t||!1,content:e,icon:"fa fa-exclamation-circle",type:"orange",buttons:{confirm:{text:wpforms_education.close,btnClass:"btn-confirm",keys:["enter"]}}})}};return s}(document,window,jQuery),WPFormsEducation.core.init();