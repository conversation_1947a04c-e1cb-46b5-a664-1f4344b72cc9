!function o(n,r,s){function c(i,e){if(!r[i]){if(!n[i]){var t="function"==typeof require&&require;if(!e&&t)return t(i,!0);if(a)return a(i,!0);throw new Error("Cannot find module '"+i+"'")}e=r[i]={exports:{}};n[i][0].call(e.exports,function(e){var t=n[i][1][e];return c(t||e)},e,e.exports,o,n,r,s)}return r[i].exports}for(var a="function"==typeof require&&require,e=0;e<s.length;e++)c(s[e]);return c}({1:[function(e,t,i){"use strict";function c(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var i=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=i){var o,n,r,s,c=[],a=!0,u=!1;try{if(r=(i=i.call(e)).next,0===t){if(Object(i)!==i)return;a=!1}else for(;!(a=(o=r.call(i)).done)&&(c.push(o.value),c.length!==t);a=!0);}catch(e){u=!0,n=e}finally{try{if(!a&&null!=i.return&&(s=i.return(),Object(s)!==s))return}finally{if(u)throw n}}return c}}(e,t)||function(e,t){{var i;if(e)return"string"==typeof e?o(e,t):"Map"===(i="Object"===(i={}.toString.call(e).slice(8,-1))&&e.constructor?e.constructor.name:i)||"Set"===i?Array.from(e):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?o(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function o(e,t){(null==t||t>e.length)&&(t=e.length);for(var i=0,o=Array(t);i<t;i++)o[i]=e[i];return o}var n,r,s,a,u,l,d,p;(window.WPFormsEditPostEducation||(n=document,r=window,s=jQuery,a="core/edit-site",u="core/editor",l="core/block-editor",p={isNoticeVisible:!(d="core/notices"),pluginId:"wpforms-edit-post-product-education-guide",init:function(){s(r).on("load",function(){"function"==typeof s.ready.then?s.ready.then(p.load):p.load()})},load:function(){p.isGutenbergEditor()?(p.maybeShowGutenbergNotice(),wp.data.select(a)?p.subscribeForSiteEditor():p.subscribeForBlockEditor()):(p.maybeShowClassicNotice(),p.bindClassicEvents())},subscribeForSiteEditor:function(){var i=p.getEditorTitle(),o=null,e=wp.data,t=e.subscribe,n=e.select,r=e.dispatch;t(function(){var e=n(u).getEditorSettings().focusMode,t=(!e&&p.isNoticeVisible&&(p.isNoticeVisible=!1,o=e,r(d).removeNotice(p.pluginId)),p.getEditorTitle());i===t&&o===e||(i=t,o=e,p.maybeShowGutenbergNotice())})},subscribeForBlockEditor:function(){var t=p.getEditorTitle(),i=(0,wp.data.subscribe)(function(){var e=p.getEditorTitle();t!==e&&(t=e,p.maybeShowGutenbergNotice(),p.isNoticeVisible)&&i()})},getEditorTitle:function(){var e=wp.data.select;return e(a)?p.isEditPostFSE()?p.getPostTitle():p.getTopmostHeadingTitle():e(u).getEditedPostAttribute("title")},getTopmostHeadingTitle:function(){var e=wp.data.select,t=e(l).getBlocksByName("core/heading");return t.length&&null!=(e=null==(e=e(l).getBlock(t[0]))||null==(t=e.attributes)||null==(t=t.content)?void 0:t.text)?e:""},isEditPostFSE:function(){var e=(0,wp.data.select)(a).getPage().context;return!(null==e||!e.postType)},getPostTitle:function(){var e=wp.data.select,t=e(a).getPage().context,e=(e("core").getEditedEntityRecord("postType",t.postType,t.postId)||{}).title;return void 0===e?"":e},bindClassicEvents:function(){var e=s(n);p.isNoticeVisible||e.on("input","#title",_.debounce(p.maybeShowClassicNotice,1e3)),e.on("click",".wpforms-edit-post-education-notice-close",p.closeNotice)},isGutenbergEditor:function(){return"undefined"!=typeof wp&&void 0!==wp.blocks},showGutenbergNotice:function(){wp.data.dispatch(d).createInfoNotice(wpforms_edit_post_education.gutenberg_notice.template,p.getGutenbergNoticeSettings());var t=setInterval(function(){var e=s(".wpforms-edit-post-education-notice-body");e.length&&((e=e.closest(".components-notice")).addClass("wpforms-edit-post-education-notice"),e.find(".is-secondary, .is-link").removeClass("is-secondary").removeClass("is-link").addClass("is-primary"),(e=e.find(".components-notice__dismiss"))&&e.on("click",function(){p.updateUserMeta()}),clearInterval(t))},100)},getGutenbergNoticeSettings:function(){var o,n,e,t,r,i,s={id:p.pluginId,isDismissible:!0,HTML:!0,__unstableHTML:!0,actions:[{className:"wpforms-edit-post-education-notice-guide-button",variant:"primary",label:wpforms_edit_post_education.gutenberg_notice.button}]};return wpforms_edit_post_education.gutenberg_guide?(o=wp.components.Guide,n=wp.element.useState,e=wp.plugins,t=e.registerPlugin,r=e.unregisterPlugin,i=function(){var e=c(n(!0),2),t=e[0],i=e[1];return t?React.createElement(o,{className:"edit-post-welcome-guide",onFinish:function(){r(p.pluginId),i(!1)},pages:p.getGuidePages()}):null},s.actions[0].onClick=function(){return t(p.pluginId,{render:i})}):s.actions[0].url=wpforms_edit_post_education.gutenberg_notice.url,s},getGuidePages:function(){var t=[];return wpforms_edit_post_education.gutenberg_guide.forEach(function(e){t.push({content:React.createElement(React.Fragment,null,React.createElement("h1",{className:"edit-post-welcome-guide__heading"},e.title),React.createElement("p",{className:"edit-post-welcome-guide__text"},e.content)),image:React.createElement("img",{className:"edit-post-welcome-guide__image",src:e.image,alt:e.title})})}),t},maybeShowClassicNotice:function(){p.isNoticeVisible||p.isTitleMatchKeywords(s("#title").val())&&(p.isNoticeVisible=!0,s(".wpforms-edit-post-education-notice").removeClass("wpforms-hidden"))},maybeShowGutenbergNotice:function(){var e;p.isNoticeVisible||(e=p.getEditorTitle(),p.isTitleMatchKeywords(e)&&(p.isNoticeVisible=!0,p.showGutenbergNotice()))},isTitleMatchKeywords:function(e){return new RegExp(/\b(contact|form)\b/i).test(e)},closeNotice:function(){s(this).closest(".wpforms-edit-post-education-notice").remove(),p.updateUserMeta()},updateUserMeta:function(){s.post(wpforms_edit_post_education.ajax_url,{action:"wpforms_education_dismiss",nonce:wpforms_edit_post_education.education_nonce,section:"edit-post-notice"})}})).init()},{}]},{},[1]);