"use strict";var WPFormsChallenge=window.WPFormsChallenge||{};WPFormsChallenge.builder=window.WPFormsChallenge.builder||function(e,o,n){var t={init:function(){n(t.ready),n(o).on("load",function(){"function"==typeof n.ready.then?n.ready.then(t.load):t.load()})},ready:function(){t.setup(),t.events()},load:function(){-1<["started","paused"].indexOf(wpforms_challenge_admin.option.status)&&WPFormsChallenge.core.updateTooltipUI(),n(".wpforms-challenge").show()},setup:function(){"inited"===wpforms_challenge_admin.option.status&&(WPFormsChallenge.core.clearLocalStorage(),t.showWelcomePopup()),t.initTooltips(),n("#wpforms-embed").addClass("wpforms-disabled"),n(e).on("wpformsWizardPopupClose",t.enableEmbed)},events:function(){n("#wpforms-challenge-welcome-builder-popup").on("click","button",t.startChallenge),n(".wpforms-challenge-step1-done").on("click",function(){WPFormsChallenge.core.stepCompleted(1)}),n("#wpforms-builder").on("wpformsBuilderSetupReady",function(){t.eventSelectTemplate()}).on("wpformsPanelSwitch wpformsPanelSectionSwitch wpformsBuilderPanelLoaded",function(){WPFormsChallenge.core.updateTooltipUI()}),n(".wpforms-challenge-step3-done").on("click",t.gotoNotificationStep),n(e).on("click",".wpforms-challenge-step4-done",t.showEmbedPopup),n.tooltipster.on("ready",t.tooltipsterReady),n(e).on("wpformsBuilderReady",function(){n(".wpforms-panel-fields-button").hasClass("active")&&WPFormsChallenge.core.loadStep()<=2&&(WPFormsChallenge.core.stepCompleted(1),WPFormsChallenge.core.stepCompleted(2))})},initTooltips(){n.each(["#wpforms-setup-name",".wpforms-setup-title .wpforms-setup-title-after","#add-fields a i","#wpforms-builder-settings-notifications-title"],function(e,o){WPFormsChallenge.core.initTooltips(e+1,o,null)})},eventSelectTemplate:function(){n("#wpforms-panel-setup").off("click",".wpforms-template-select").on("click",".wpforms-template-select",function(e){t.builderTemplateSelect(this,e)})},startChallenge:function(){WPFormsChallenge.admin.saveChallengeOption({status:"started"}),WPFormsChallenge.core.initListUI("started"),n(".wpforms-challenge-popup-container").fadeOut(function(){n("#wpforms-challenge-welcome-builder-popup").hide()}),WPFormsChallenge.core.timer.run(WPFormsChallenge.core.timer.initialSecondsLeft),WPFormsChallenge.core.updateTooltipUI()},gotoStep:function(e){console.warn('WARNING! Function "WPFormsChallenge.builder.gotoStep()" has been deprecated.')},builderTemplateSelect:function(e,o){WPFormsChallenge.core.resumeChallengeAndExec(o,function(){WPFormsChallenge.core.stepCompleted(2).done(WPForms.Admin.Builder.Setup.selectTemplate.bind(e,o))})},tooltipsterReady:function(e){var e=n(e.origin).data("wpforms-challenge-step"),o=n("#wpforms-builder-form").data("id"),e=parseInt(e,10)||0,o=parseInt(o,10)||0;3===e&&0<o&&WPFormsChallenge.admin.saveChallengeOption({form_id:o})},showWelcomePopup:function(){n("#wpforms-challenge-welcome-builder-popup").show(),n(".wpforms-challenge-popup-container").fadeIn()},gotoNotificationStep:function(e){WPFormsChallenge.core.stepCompleted(3).done(function(){WPFormsBuilder.panelSwitch("settings"),WPFormsBuilder.panelSectionSwitch(n(".wpforms-panel .wpforms-panel-sidebar-section-notifications"))})},showEmbedPopup:function(){WPFormsChallenge.core.stepCompleted(4).done(WPFormsFormEmbedWizard.openPopup)},enableEmbed:function(){n("#wpforms-embed").removeClass("wpforms-disabled")}};return t}(document,window,jQuery),WPFormsChallenge.builder.init();