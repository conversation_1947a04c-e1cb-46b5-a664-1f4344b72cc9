var WPFormsAdminFormTemplates=window.WPFormsAdminFormTemplates||function(s,o){const r={init(){o(r.ready)},ready(){WPFormsFormTemplates.setup(),r.events(),wpf.initTooltips()},events(){o(".wpforms-form-setup-content").on("keyup","#wpforms-setup-template-search",_.debounce(WPFormsFormTemplates.searchTemplate,200)).on("click",".wpforms-setup-templates-categories li div",WPFormsFormTemplates.selectCategory).on("click",".wpforms-setup-templates-categories li .chevron",WPFormsFormTemplates.toggleSubcategoriesList).on("click",".wpforms-setup-templates-subcategories li",WPFormsFormTemplates.selectSubCategory).on("click",".wpforms-template-select",r.selectTemplate).on("click",".wpforms-trigger-blank",r.selectBlankTemplate).on("click",".wpforms-template-generate",r.openAIFormGenerator)},openAIFormGenerator(e){e.preventDefault(),o(this).hasClass("wpforms-inactive wpforms-help-tooltip")||(s.location=wpforms_admin_form_templates.openAIFormUrl)},selectTemplate(e){e.preventDefault();e=o(this);e.hasClass("education-modal")||(e.data("template").match(/wpforms-user-template-(\d+)/)&&e.data("create-url")?s.location.href=e.data("create-url"):(o(".wpforms-form-setup-content").find(".wpforms-template").removeClass("active"),e.closest(".wpforms-template").addClass("active"),e.data("labelOriginal",e.html()),e.html('<i class="wpforms-loading-spinner wpforms-loading-white wpforms-loading-inline"></i>'+wpforms_admin.loading),WPFormsFormTemplates.selectTemplateProcess(e.data("template-name-raw"),e.data("template"),e,r.selectTemplateProcessAjax)))},selectBlankTemplate(e){e.preventDefault(),r.selectTemplateProcessAjax("Blank Form","blank")},selectTemplateProcessAjax(t,e){var e={title:t,action:"wpforms_new_form",template:e,form_id:0,nonce:wpforms_admin_form_templates.nonce},a=o(".wpforms-setup-templates-categories li.active").data("category"),a=(a&&"all"!==a&&(e.category=a),o(".wpforms-setup-templates-subcategories li.active").data("subcategory"));a&&(e.subcategory=a),o.post(wpforms_admin.ajax_url,e).done(function(e){e.success?s.location.href=e.data.redirect:"invalid_template"===e.data.error_type?r.selectTemplateProcessInvalidTemplateError(e.data.message,t):r.selectTemplateProcessError(e.data.message)}).fail(function(){r.selectTemplateProcessError("")})},selectTemplateProcessInvalidTemplateError(e,t){o.alert({title:wpforms_admin.heads_up,content:e,icon:"fa fa-exclamation-circle",type:"orange",buttons:{confirm:{text:wpforms_admin.use_default_template,btnClass:"btn-confirm",keys:["enter"],action(){r.selectTemplateProcessAjax(t,"simple-contact-form-template")}},cancel:{text:wpforms_admin.cancel,action(){WPFormsFormTemplates.selectTemplateCancel()}}}})},selectTemplateProcessError(e){o.alert({title:e,content:wpforms_admin.error_select_template,icon:"fa fa-exclamation-circle",type:"orange",buttons:{confirm:{text:wpforms_admin.ok,btnClass:"btn-confirm",keys:["enter"],action(){WPFormsFormTemplates.selectTemplateCancel()}}}})}};return r}((document,window),jQuery);WPFormsAdminFormTemplates.init();