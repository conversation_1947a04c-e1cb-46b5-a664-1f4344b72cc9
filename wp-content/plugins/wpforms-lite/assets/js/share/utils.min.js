var WPFormsUtils=window.WPFormsUtils||function(e){const a={triggerEvent(r,t,o=[]){t=new e.Event(t);return r.trigger(t,o),t},debounce(e,s,l){let a;return function(){const r=this,t=arguments;var o=l&&!a;clearTimeout(a),a=setTimeout(function(){a=null,l||e.apply(r,t)},s),o&&e.apply(r,t)}},cssColorsUtils:{isTransparentColor(r,t=.33){r=a.cssColorsUtils.getColorAsRGBArray(r);return Number(r?.[3])<=t},getColorAsRGBArray(r){if(!a.cssColorsUtils.isValidColor(r))return!1;r="transparent"===(r=r.replace(/^#/,"").replaceAll(" ",""))?"rgba(0,0,0,0)":r;let t;return r.match(/[0-9a-f]{6,8}$/gi)?(t=r.match(/\w\w/g).map(r=>parseInt(r,16)))[3]=t[3]||0===t[3]?(t[3]/255).toFixed(2):1:t=r.split("(")[1].split(")")[0].split(","),t},isValidColor(r){var t=(new Option).style;return t.color=r,""!==t.color},getContrastColor(r){var r=a.cssColorsUtils.getColorAsRGBArray(r),t=r.reduce((r,t)=>r+t,0);return Math.round(t/3*(r[3]??1))<128?"#ffffff":"#000000"},getColorWithOpacity(r,t){r=r.trim();var o=a.cssColorsUtils.getColorAsRGBArray(r);if(!o)return r;t=t&&0!==t.length?t.toString():"1";r=4===o.length?parseFloat(o[3]):1,t=parseFloat(t)*r;return`rgba(${o[0]},${o[1]},${o[2]},${t})`.replace(/\s+/g,"")},rgbaToHex(r){var t,o,e,s,l;return/^rgb/.test(r)?!!(s=a.cssColorsUtils.getColorAsRGBArray(r))&&(t=Number(s[0]),o=Number(s[1]),e=Number(s[2]),s=s[3]?Math.round(255*Number(s[3])):255,("#"+[(l=r=>r.toString(16).padStart(2,"0"))(t),l(o),l(e),s<255?l(s):""].join("")).toLowerCase()):r}}};return a}((document,window,jQuery));