!function(e,o){const s={noStateCountries:[],init(){o(e).on("load",s.onLoad),o(document).on("wpformsRepeaterFieldCloneCreated",s.setChangeHandlers)},onLoad(){s.noStateCountries=wpforms_settings?.address_field?.list_countries_without_states||[],s.noStateCountries.length&&s.setChangeHandlers()},setChangeHandlers(){o(".wpforms-field-address").each(function(){var e=o(this).find("select.wpforms-field-address-country");e.length&&(s.handleCountryChange(e),e.off("change").on("change",function(){s.handleCountryChange(this)}))})},handleCountryChange(e){var e=o(e),t=e.closest(".wpforms-field").find(".wpforms-field-address-state"),n=t.closest(".wpforms-field-row");n.length&&(e=e.val(),s.handleStateInput(t,n,e))},handleStateInput(e,t,n){s.noStateCountries.includes(n)?(e.val("").prop("disabled",!0).prop("required",!1).on("change",function(){o(this).val("")}),t.addClass("wpforms-without-state")):(e.prop("disabled",!1).prop("required",t.find(".wpforms-first input").prop("required")).off("change"),t.removeClass("wpforms-without-state"))}};s.init(),s}(window,jQuery);