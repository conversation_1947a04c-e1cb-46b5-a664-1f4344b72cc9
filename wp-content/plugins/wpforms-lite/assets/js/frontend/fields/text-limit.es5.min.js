!function i(r,a,o){function l(n,t){if(!a[n]){if(!r[n]){var e="function"==typeof require&&require;if(!t&&e)return e(n,!0);if(s)return s(n,!0);throw new Error("Cannot find module '"+n+"'")}t=a[n]={exports:{}};r[n][0].call(t.exports,function(t){var e=r[n][1][t];return l(e||t)},t,t.exports,i,r,a,o)}return a[n].exports}for(var s="function"==typeof require&&require,t=0;t<o.length;t++)l(o[t]);return l}({1:[function(t,e,n){"use strict";function r(t){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function l(t,e,n){return t.replace("{count}",e).replace("{limit}",n).replace("{remaining}",n-e)}function s(t,e,n){var i=document.createElement("div");return t="object"===r(t)?"":t,e="object"===r(e)?"":e,i.classList.add("wpforms-field-limit-text"),i.id="wpforms-field-limit-text-"+t+"-"+e,i.setAttribute("aria-live","polite"),i.textContent=n,i}function u(e){return"string"==typeof e&&e.length?([/([A-Z]+),([A-Z]+)/gi,/([0-9]+),([A-Z]+)/gi,/([A-Z]+),([0-9]+)/gi].forEach(function(t){e=e.replace(t,"$1, $2")}),e.split(/\s+/).length):0}function c(t){return window.clipboardData&&window.clipboardData.getData?window.clipboardData.getData("Text"):t.clipboardData&&t.clipboardData.getData?t.clipboardData.getData("text/plain"):""}function d(t,e){var n="",i=/\s+/g,r=t.trim().match(i)||[],a=t.split(i);a.splice(e,a.length);for(var o=0;o<a.length;o++)n+=a[o]+(r[o]||"");return n.trim()}function i(t){return[].slice.call(t)}function f(t){(t=t.parentNode.querySelector(".wpforms-field-limit-text"))&&t.remove()}function a(){(window.WPFormsTextLimit=o).initHint("body")}var o;o={initHint:function(t){i(document.querySelectorAll(t+" .wpforms-limit-characters-enabled")).map(function(t){function e(t){n.textContent=l(window.wpforms_settings.val_limit_characters,this.value.length,i)}var n,i,r,a=parseInt(t.dataset.textLimit,10)||0,o=(t.value=t.value.slice(0,a),s(t.dataset.formId,t.dataset.fieldId,l(wpforms_settings.val_limit_characters,t.value.length,a)));n=o,i=a;f(t),t.parentNode.appendChild(o),t.addEventListener("keydown",e),t.addEventListener("keyup",e),t.addEventListener("paste",(r=a,function(t){t.preventDefault();var t=c(t),e=this.selectionStart+t.length,t=this.value.substring(0,this.selectionStart)+t+this.value.substring(this.selectionStart);this.value=t.substring(0,r),this.setSelectionRange(e,e)}))}),i(document.querySelectorAll(t+" .wpforms-limit-words-enabled")).map(function(t){function e(t){var e=u(this.value.trim());n.textContent=l(window.wpforms_settings.val_limit_words,e,i),-1<[13,32,188].indexOf(t.keyCode)&&i<=e&&t.preventDefault()}var n,i,r,a=parseInt(t.dataset.textLimit,10)||0,o=(t.value=d(t.value,a),s(t.dataset.formId,t.dataset.fieldId,l(wpforms_settings.val_limit_words,u(t.value.trim()),a)));n=o,i=a;f(t),t.parentNode.appendChild(o),t.addEventListener("keydown",e),t.addEventListener("keyup",e),t.addEventListener("paste",(r=a,function(t){t.preventDefault();var t=c(t),e=this.selectionStart+t.length,t=this.value.substring(0,this.selectionStart)+t+this.value.substring(this.selectionStart);this.value=d(t,r),this.setSelectionRange(e,e)}))})}},"loading"===document.readyState?document.addEventListener("DOMContentLoaded",a):a()},{}]},{},[1]);