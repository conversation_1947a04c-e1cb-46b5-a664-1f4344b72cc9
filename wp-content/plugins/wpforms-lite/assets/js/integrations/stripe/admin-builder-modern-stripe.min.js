var WPFormsStripeModernBuilder=window.WPFormsStripeModernBuilder||function(e,i){let r={};const s={init(){i(s.ready)},ready(){var e,n,t;s.isLegacySettings()||(r={$alert:i("#wpforms-stripe-credit-card-alert"),$panelContent:i("#wpforms-panel-content-section-payment-stripe"),$feeNotice:i(".wpforms-stripe-notice-info")},s.bindUIActions(),s.bindPlanUIActions(),wpforms_builder_stripe.is_pro)||(t=(e=".wpforms-panel-content-section-stripe")+" .wpforms-panel-content-section-payment-plan-name input",i(n=e+" .wpforms-panel-content-section-payment-toggle input").each(WPFormsBuilderPaymentsUtils.toggleContent),i(t).each(WPFormsBuilderPaymentsUtils.checkPlanName),i("#wpforms-panel-payments").on("click",n,WPFormsBuilderPaymentsUtils.toggleContent).on("click",e+" .wpforms-panel-content-section-payment-plan-head-buttons-toggle",WPFormsBuilderPaymentsUtils.togglePlan).on("click",e+" .wpforms-panel-content-section-payment-plan-head-buttons-delete",WPFormsBuilderPaymentsUtils.deletePlan).on("input",t,WPFormsBuilderPaymentsUtils.renamePlan).on("focusout",t,WPFormsBuilderPaymentsUtils.checkPlanName))},bindUIActions(){i("#wpforms-builder").on("wpformsFieldDelete",s.disableNotifications).on("wpformsSaved",s.requiredFieldsCheck).on("wpformsFieldAdd",s.fieldAdded).on("wpformsFieldDelete",s.fieldDeleted).on("wpformsPaymentsPlanCreated",s.toggleMultiplePlansWarning).on("wpformsPaymentsPlanCreated",s.bindPlanUIActions).on("wpformsPaymentsPlanDeleted",s.toggleMultiplePlansWarning)},bindPlanUIActions(){r.$panelContent.find('.wpforms-panel-content-section-payment-plan-body .wpforms-panel-field-select select[name*="email"]').on("change",s.resetEmailAlertErrorClass)},requiredFieldsCheck(){if(i("#wpforms-panel-field-stripe-enable_recurring").is(":checked")&&!r.$panelContent.hasClass("wpforms-hidden")){let n=!1;r.$panelContent.find(".wpforms-panel-content-section-payment-plan").each(function(){var e=i(this).data("plan-id"),e=i(`#wpforms-panel-field-stripe-recurring-${e}-email`);e.val()||(e.addClass("wpforms-required-field-error"),n=!0)}),n&&s.recurringEmailAlert()}},resetEmailAlertErrorClass(){i(this).toggleClass("wpforms-required-field-error",!i(this).val())},recurringEmailAlert(){let e=wpforms_builder.stripe_recurring_email;i(".wpforms-panel-content-section-stripe").is(":visible")||(e+=" "+wpforms_builder.stripe_recurring_settings),i.alert({title:wpforms_builder.stripe_recurring_heading,content:e,icon:"fa fa-exclamation-circle",type:"red",buttons:{confirm:{text:wpforms_builder.ok,btnClass:"btn-confirm",keys:["enter"]}},onOpen(){i(".wpforms-stripe-settings-redirect").on("click",s.settingsRedirect)}})},settingsRedirect(){i(".wpforms-panel-payments-button").trigger("click"),i(".wpforms-panel-sidebar-section-stripe").trigger("click"),e.location.href=e.location.pathname+e.location.search+"#wpforms-panel-field-stripe-enable_recurring-wrap",i(this).closest(".jconfirm-box").find(".btn-confirm").trigger("click")},disableNotifications(e,n,t){s.isStripeField(t)&&!s.hasStripeCreditCardFieldInBuilder()&&((t=i('.wpforms-panel-content-section-notifications [id*="-stripe-wrap"]')).find('input[id*="-stripe"]').prop("checked",!1),t.addClass("wpforms-hidden"))},isLegacySettings(){return i("#wpforms-panel-field-stripe-enable").length},fieldAdded(e,n,t){s.isStripeField(t)&&s.hasStripeCreditCardFieldInBuilder()&&(s.settingsToggle(!0),r.$feeNotice.toggleClass("wpforms-hidden"))},fieldDeleted(e,n,t){!s.isStripeField(t)||s.hasStripeCreditCardFieldInBuilder()||(s.settingsToggle(!1),s.disablePayments(),r.$feeNotice.toggleClass("wpforms-hidden"))},isStripeField(e){return e===wpforms_builder_stripe.field_slug},hasStripeCreditCardFieldInBuilder(){return 0<i(".wpforms-field.wpforms-field-"+wpforms_builder_stripe.field_slug).length},toggleMultiplePlansWarning(){r.$panelContent.find(".wpforms-stripe-multiple-plans-warning").toggleClass("wpforms-hidden",1===r.$panelContent.find(".wpforms-panel-content-section-payment-plan").length)},settingsToggle(e){(r.$alert.length||r.$panelContent.length)&&(r.$alert.toggleClass("wpforms-hidden",e),r.$panelContent.toggleClass("wpforms-hidden",!e))},toggleContent(){console.warn('WARNING! Function "WPFormsStripeModernBuilder.toggleContent()" has been deprecated, please use the new "WPFormsPaymentsUtils.toggleContent()" function instead!'),WPFormsBuilderPaymentsUtils.toggleContent()},togglePlan(){console.warn('WARNING! Function "WPFormsStripeModernBuilder.togglePlan()" has been deprecated, please use the new "WPFormsPaymentsUtils.togglePlan()" function instead!'),WPFormsBuilderPaymentsUtils.togglePlan()},deletePlan(){console.warn('WARNING! Function "WPFormsStripeModernBuilder.checkPlanName()" has been deprecated, please use the new "WPFormsPaymentsUtils.deletePlan()" function instead!'),WPFormsBuilderPaymentsUtils.deletePlan()},checkPlanName(){console.warn('WARNING! Function "WPFormsStripeModernBuilder.checkPlanName()" has been deprecated, please use the new "WPFormsPaymentsUtils.checkPlanName()" function instead!'),WPFormsBuilderPaymentsUtils.checkPlanName()},renamePlan(){console.warn('WARNING! Function "WPFormsStripeModernBuilder.renamePlan()" has been deprecated, please use the new "WPFormsPaymentsUtils.renamePlan()" function instead!'),WPFormsBuilderPaymentsUtils.renamePlan()},disablePayments(){i("#wpforms-panel-field-stripe-enable_one_time, #wpforms-panel-field-stripe-enable_recurring").prop("checked",!1).trigger("change").each(WPFormsBuilderPaymentsUtils.toggleContent)}};return s}((document,window),jQuery);WPFormsStripeModernBuilder.init();