var WPFormsStripe=window.WPFormsStripe||function(e,i,n){const r={init(){n(r.ready)},ready(){r.isLegacySettings()&&(r.settingsDisplay(),r.settingsConditions(),r.bindUIActions())},bindUIActions(){n(e).on("wpformsFieldDelete",r.disableNotifications).on("wpformsSaved",r.requiredFieldsCheck).on("wpformsFieldUpdate",r.settingsDisplay).on("wpformsFieldUpdate",r.settingsConditions),n("#wpforms-panel-field-stripe-recurring-email").on("change",r.resetEmailAlertErrorClass)},settingsDisplay(){var e=n("#wpforms-stripe-credit-card-alert"),i=n("#stripe-provider");wpforms_builder_stripe.field_slugs.filter(function(e){e=n(".wpforms-field-option-"+e);return e.length?e:null}).length?(e.hide(),i.find("#wpforms-stripe-new-interface-alert, .wpforms-stripe-notice-info, .wpforms-panel-field, .wpforms-conditional-block-panel, h2").show()):(e.show(),i.find("#wpforms-stripe-new-interface-alert, .wpforms-stripe-notice-info, .wpforms-panel-field, .wpforms-conditional-block-panel, h2").hide(),i.find("#wpforms-panel-field-stripe-enable").prop("checked",!1).trigger("change"))},settingsConditions(){n("#wpforms-panel-field-stripe-enable").conditions({conditions:{element:"#wpforms-panel-field-stripe-enable",type:"checked",operator:"is"},actions:{if:{element:".wpforms-panel-content-section-stripe-body",action:"show"},else:{element:".wpforms-panel-content-section-stripe-body",action:"hide"}},effect:"appear"}),n("#wpforms-panel-field-stripe-recurring-enable").conditions({conditions:{element:"#wpforms-panel-field-stripe-recurring-enable",type:"checked",operator:"is"},actions:{if:{element:"#wpforms-panel-field-stripe-recurring-period-wrap,#wpforms-panel-field-stripe-recurring-conditional_logic-wrap,#wpforms-conditional-groups-payments-stripe-recurring,#wpforms-panel-field-stripe-recurring-email-wrap,#wpforms-panel-field-stripe-recurring-name-wrap",action:"show"},else:{element:"#wpforms-panel-field-stripe-recurring-period-wrap,#wpforms-panel-field-stripe-recurring-conditional_logic-wrap,#wpforms-conditional-groups-payments-stripe-recurring,#wpforms-panel-field-stripe-recurring-email-wrap,#wpforms-panel-field-stripe-recurring-name-wrap",action:"hide"}},effect:"appear"})},requiredFieldsCheck(){if(n("#wpforms-panel-field-stripe-enable").is(":checked")&&n("#wpforms-panel-field-stripe-recurring-enable").is(":checked")){var i=n("#wpforms-panel-field-stripe-recurring-email");if(!i.val()){i.addClass("wpforms-required-field-error");let e=wpforms_builder.stripe_recurring_email;n(".wpforms-panel-content-section-stripe").is(":visible")||(e+=" "+wpforms_builder.stripe_recurring_settings),n.alert({title:wpforms_builder.stripe_recurring_heading,content:e,icon:"fa fa-exclamation-circle",type:"red",buttons:{confirm:{text:wpforms_builder.ok,btnClass:"btn-confirm",keys:["enter"]}},onOpen(){n(".wpforms-stripe-settings-redirect").on("click",r.settingsRedirect)}})}}},settingsRedirect(){n(".wpforms-panel-payments-button").trigger("click"),n(".wpforms-panel-sidebar-section-stripe").trigger("click"),i.location.href=i.location.pathname+i.location.search+"#wpforms-panel-field-stripe-enable_recurring-wrap",n(this).closest(".jconfirm-box").find(".btn-confirm").trigger("click")},resetEmailAlertErrorClass(){n(this).toggleClass("wpforms-required-field-error",!n(this).val())},disableNotifications(e,i,r){wpforms_builder_stripe.field_slugs.includes(r)&&((r=n('.wpforms-panel-content-section-notifications [id*="-stripe-wrap"]')).find('input[id*="-stripe"]').prop("checked",!1),r.addClass("wpforms-hidden"))},isLegacySettings(){return n("#wpforms-panel-field-stripe-enable").length}};return r}(document,window,jQuery);WPFormsStripe.init();