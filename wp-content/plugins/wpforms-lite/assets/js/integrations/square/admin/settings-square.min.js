const WPFormsSettingsSquare=window.WPFormsSettingsSquare||function(a,r){const s={sandboxModeCheckbox:r("#wpforms-setting-square-sandbox-mode"),sandboxConnectionStatusBlock:r("#wpforms-setting-row-square-connection-status-sandbox"),productionConnectionStatusBlock:r("#wpforms-setting-row-square-connection-status-production"),sandboxLocationBlock:r("#wpforms-setting-row-square-location-id-sandbox"),sandboxLocationStatusBlock:r("#wpforms-setting-row-square-location-status-sandbox"),productionLocationBlock:r("#wpforms-setting-row-square-location-id-production"),productionLocationStatusBlock:r("#wpforms-setting-row-square-location-status-production"),refreshBtn:r(".wpforms-square-refresh-btn"),copyButton:r("#wpforms-setting-row-square-webhooks-endpoint-set .wpforms-copy-to-clipboard"),webhooksEnableCheckbox:r("#wpforms-setting-square-webhooks-enabled"),webhookEndpointUrl:r("input#wpforms-square-webhook-endpoint-url"),webhookMethod:r('input[name="square-webhooks-communication"]'),webhookCommunicationStatusNotice:r("#wpforms-setting-row-square-webhooks-communication-status"),webhookConnectBtn:r("#wpforms-setting-square-webhooks-connect"),webhookConnectRow:r("#wpforms-setting-row-square-webhooks-connect"),webhookConnectStatusRow:r("#wpforms-setting-row-square-webhooks-connect-status-production, #wpforms-setting-row-square-webhooks-connect-status-sandbox")},c={init(){r(c.ready)},ready(){c.events()},events(){s.sandboxModeCheckbox.on("change",c.credentialsFieldsDisplay),s.refreshBtn.on("click",c.refreshTokensCallback),s.webhooksEnableCheckbox.on("change",c.webhooksEnableCallback),s.webhookConnectBtn.on("click",c.modals.displayWebhookConfigPopup),s.webhookMethod.on("change",c.updateWebhookEndpointUrl),s.copyButton.on("click",function(o){wpf.copyValueToClipboard(o,r(this),s.webhookEndpointUrl)})},updateWebhookEndpointUrl(){var o=s.webhookMethod.filter(":checked").val(),o=wpforms_admin.square.webhook_urls[o];s.webhookEndpointUrl.val(o),s.webhookCommunicationStatusNotice.removeClass("wpforms-hide")},webhooksEnableCallback(){s.webhookConnectRow.toggleClass("wpforms-hide",!r(this).is(":checked")),s.webhookConnectStatusRow.toggleClass("wpforms-hide",!r(this).is(":checked"))},createWebhook(o){return new Promise((e,t)=>{r.ajax({url:wpforms_admin.ajax_url,type:"post",dataType:"json",data:{action:"wpforms_square_create_webhook",nonce:wpforms_admin.nonce,token:o},success(o){(o.success?e:t)(o)},error(){t({success:!1,message:"An error occurred."})}})})},refreshTokensCallback(){const e=r(this),o=e.outerWidth(),t=e.text();var n={url:wpforms_admin.ajax_url,type:"post",dataType:"json",data:{action:"wpforms_square_refresh_connection",nonce:wpforms_admin.nonce,mode:e.data("mode")},beforeSend(){e.css("width",o).html(WPFormsAdmin.settings.iconSpinner).prop("disabled",!0)}};let s=wpforms_admin.square.refresh_error;r.ajax(n).done(function(o){o.success?(e.css("pointerEvents","none").removeClass("wpforms-btn-light-grey").addClass("wpforms-btn-grey").html("Refreshed!"),e.closest("form").css("cursor","wait"),a.location=e.data("url")):(Object.prototype.hasOwnProperty.call(o,"data")&&""!==o.data&&(s=o.data),e.css("width","auto").html(t).prop("disabled",!1),c.modals.refreshTokensError(s))}).fail(function(){e.css("width","auto").html(t).prop("disabled",!1),c.modals.refreshTokensError(s)})},credentialsFieldsDisplay(){var o=s.sandboxModeCheckbox.is(":checked");o?(s.sandboxConnectionStatusBlock.show(),s.sandboxLocationBlock.show(),s.sandboxLocationStatusBlock.show(),s.productionConnectionStatusBlock.hide(),s.productionLocationBlock.hide(),s.productionLocationStatusBlock.hide()):(s.sandboxConnectionStatusBlock.hide(),s.sandboxLocationBlock.hide(),s.sandboxLocationStatusBlock.hide(),s.productionConnectionStatusBlock.show(),s.productionLocationBlock.show(),s.productionLocationStatusBlock.show()),o&&s.sandboxConnectionStatusBlock.find(".wpforms-square-connected").length||!o&&s.productionConnectionStatusBlock.find(".wpforms-square-connected").length||c.modals.modeChangedWarning()},modals:{modeChangedWarning(){r.alert({title:wpforms_admin.heads_up,content:wpforms_admin.square.mode_update,icon:"fa fa-exclamation-circle",type:"orange",buttons:{confirm:{text:wpforms_admin.ok,btnClass:"btn-confirm",keys:["enter"]}}})},refreshTokensError(o){r.alert({title:!1,content:o,icon:"fa fa-exclamation-circle",type:"orange",buttons:{confirm:{text:wpforms_admin.ok,btnClass:"btn-confirm",keys:["enter"]}}})},displayWebhookConfigPopup(){r.confirm({title:wpforms_admin.square.webhook_create_title,content:wpforms_admin.square.webhook_create_description+'<input type="text" id="wpforms-square-personal-access-token" placeholder="'+wpforms_admin.square.webhook_token_placeholder+'" value=""><p class="wpforms-square-webhooks-connect-error error" style="display:none;">'+wpforms_admin.square.token_is_required+"</p>",icon:"fa fa-info-circle",type:"blue",buttons:{confirm:{text:wpforms_admin.ok,btnClass:"btn-confirm",keys:["enter"],action(){const e=this;var o=e.$content.find("#wpforms-square-personal-access-token");const t=e.$content.find(".error");o=o.val().trim();const n=e.$title;return s.webhookConnectBtn.addClass("inactive"),t.hide().text(""),""===o?t.text(wpforms_admin.square.token_is_required).show():(e.buttons.confirm.setText(wpforms_admin.loading),e.buttons.confirm.disable(),c.createWebhook(o).then(o=>{e.setContent("<p>"+o.data.message+"</p>"),e.buttons.confirm.hide(),n.text("").hide(),e.buttons.cancel.setText(wpforms_admin.close),e.buttons.cancel.action=function(){a.location.reload()}}).catch(o=>{t.text(o.data.message).show(),e.buttons.confirm.setText(wpforms_admin.ok),e.buttons.confirm.enable()})),!1}},cancel:{text:wpforms_admin.cancel,action(){s.webhookConnectBtn.removeClass("inactive"),this.close()}}}})}}};return c}((document,window),jQuery);WPFormsSettingsSquare.init();