const WPFormsConstantContactV3Auth=window.WPFormsConstantContactV3Auth||function(n,e,c){const i={isOpened:!1,listenURL:"",init:()=>{c(i.ready)},ready:()=>{var t=new URL(WPFormsConstantContactV3AuthVars.auth_url).searchParams.get("redirect_uri");i.listenURL=new URL(t).origin,c(n).on("click",".wpforms-constant-contact-v3-auth, .wpforms-builder-constant-contact-v3-provider-sign-up",i.showWindow).on("click","#wpforms-settings-constant-contact-v3-migration-prompt-link",i.promptMigration)},showWindow:t=>{if(t.preventDefault(),!i.isOpened){var t=WPFormsConstantContactV3AuthVars.auth_url,n=screen.width/2-250,o=screen.height/2-300,a=c(".wpforms-constant-contact-v3-auth").data("login-hint"),t=new URL(t);a&&t.searchParams.set("login_hint",a);const r=e.open(t.toString(),"authPopup","width=500, height=600, top="+o+", left="+n),s=(e.addEventListener("message",i.listenResponse),setInterval(()=>{r.closed&&(clearInterval(s),i.isOpened=!1)},1e3));i.isOpened=!0}},listenResponse:t=>{t.origin===i.listenURL&&(t.data?i.saveAccount(t.data):i.errorModal(WPFormsConstantContactV3AuthVars.strings.error))},saveAccount:t=>{const n=i.waitModal();c.post(WPFormsConstantContactV3AuthVars.ajax_url,{action:"wpforms_constant_contact_popup_auth",data:JSON.stringify({code:t}),nonce:WPFormsConstantContactV3AuthVars.nonce}).done(t=>{t.success?"undefined"==typeof WPFormsBuilder?(n.close(),e.location.href=WPFormsConstantContactV3AuthVars.page_url):WPFormsBuilder.formSave(!1).done(()=>{WPFormsBuilder.setCloseConfirmation(!1),WPFormsBuilder.showLoadingOverlay(),location.reload()}):(n.close(),t="<p>"+WPFormsConstantContactV3AuthVars.strings.error+"</p><p><strong>"+wpf.sanitizeHTML(t.data)+"</strong></p>",i.errorModal(t))})},waitModal:()=>c.alert({title:"",content:WPFormsConstantContactV3AuthVars.strings.wait,icon:"fa fa-info-circle",type:"blue",buttons:!1}),errorModal:t=>{var n=e?.wpforms_builder||e?.wpforms_admin;return c.alert({title:n.uh_oh,content:t,icon:"fa fa-exclamation-circle",type:"red",buttons:{cancel:{text:n.cancel,action:()=>{i.isOpened=!1}}}})},promptMigration(t){t.preventDefault();const n=i.waitModal();c.post({url:WPFormsConstantContactV3AuthVars.ajax_url,data:{action:"wpforms_constant_contact_migration_prompt",nonce:WPFormsConstantContactV3AuthVars.nonce},success:()=>{n.close(),e.location.href=WPFormsConstantContactV3AuthVars.page_url},error:()=>{n.close(),i.errorModal(WPFormsConstantContactV3AuthVars.strings.error)}})}};return i}(document,window,jQuery);WPFormsConstantContactV3Auth.init();