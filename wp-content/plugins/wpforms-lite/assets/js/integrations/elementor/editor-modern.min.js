"use strict";var WPFormsElementorModern=window.WPFormsElementorModern||function(i,l){var d={init:function(){d.events()},events:function(){l(i).on("elementor/frontend/init",function(e,t,o){elementor.channels.editor.on("elementorWPFormsResetStyleSettings",d.confirmResetStyleSettings),elementor.channels.editor.on("section:activated",d.checkForLeadForms),elementor.hooks.addAction("panel/open_editor/widget/wpforms",d.widgetPanelOpen),elementorFrontend.hooks.addAction("frontend/element_ready/wpforms.default",d.widgetReady)})},checkForLeadForms(e,t){var o;"field_styles"===e&&"wpforms"===t.model.attributes.widgetType&&(e=t.$childViewContainer[0],o=t.options.editedElementView.$el[0],t=t.model.attributes.settings.attributes.form_id,0!==(o=l(o).find("#wpforms-"+t)).length)&&o.hasClass("wpforms-lead-forms-container")&&(l(e).addClass("wpforms-elementor-disabled"),l(e).find(".wpforms-elementor-lead-forms-notice").css("display","block"))},widgetPanelOpen:function(e,t,o){t=t.get("settings");t.on("change:copyPasteJsonValue",e=>{d.pasteSettings(e,o)}),t.on("change",e=>{d.changeStyleSettings(e,o),e.changed.copyPasteJsonValue||e.changed.form_id||d.updateCopyPasteContent(e)}),t.on("change:form_id",e=>{e.attributes.copyPasteJsonValue||setTimeout(function(){d.updateCopyPasteContent(e)},0)})},widgetReady:function(e){var t=e.find(".wpforms-form").data("formid");d.updateAccentColors(e,t),d.loadChoicesJS(e,t),d.initRichTextField(t),d.initRepeaterField(t)},confirmResetStyleSettings:function(e){elementorCommon.dialogsManager.createWidget("confirm",{message:wpformsElementorVars.strings.reset_settings_confirm_text,headerMessage:wpformsElementorVars.strings.reset_style_settings,strings:{confirm:wpformsElementorVars.strings.continue,cancel:wpformsElementorVars.strings.cancel},defaultOption:"cancel",onConfirm:function(){d.resetStyleSettings(e)}}).show()},resetStyleSettings(e){var t=e.options.elementSettingsModel,e=e.options.container,o=e.view.$el[0];const s=t.defaults;var n=d.getStyleAttributesKeys();const r={};var a=l(o).find("#wpforms-css-vars-root").next("style");n.forEach(function(e){r[e]=s[e]}),d.resetGlobalStyleSettings(t,e),elementorCommon.api.run("document/elements/settings",{container:e,options:{external:!0},settings:r}),o.style="",a.text("")},changeStyleSettings:function(e,t){var o,s=t.$el[0],n=e.parseGlobalSettings(e);for(o in e.changed)if(d.getStyleAttributesKeys().includes(o)){t.allowRender=!1;let e=d.getParsedValue(o,n);var r=o.replace(/[A-Z]/g,e=>"-"+e.toLowerCase());switch(["fieldBorderRadius","buttonBorderRadius"].includes(o)&&(e+="px"),r){case"field-size":case"label-size":case"button-size":for(const a in wpformsElementorVars.sizes[r][e])s.style.setProperty(`--wpforms-${r}-`+a,wpformsElementorVars.sizes[r][e][a]);break;default:s.style.setProperty("--wpforms-"+r,e)}}else t.allowRender="copyPasteJsonValue"!==o},updateCopyPasteContent:function(e){var t=d.getStyleAttributesKeys();let o={},s=e.parseGlobalSettings(e);t.forEach(function(e){o[e]=d.getParsedValue(e,s)}),e.setExternalChange("copyPasteJsonValue",JSON.stringify(o))},resetGlobalStyleSettings(e,t){e.get("__globals__")&&!e.changed.__globals__&&elementorCommon.api.run("document/globals/settings",{container:t,settings:{},options:{external:!0,render:!1}})},pasteSettings(e,t){var o=e.changed.copyPasteJsonValue,s=d.parseValidateJson(o),t=t.container;s?(d.resetGlobalStyleSettings(e,t),e.set(s)):(o&&elementorCommon.dialogsManager.createWidget("alert",{message:wpformsElementorVars.strings.copy_paste_error,headerMessage:wpformsElementorVars.strings.heads_up}).show(),this.updateCopyPasteContent(e))},parseValidateJson(e){if("string"!=typeof e)return!1;let t;try{t=JSON.parse(e)}catch(e){t=!1}return t},getStyleAttributesKeys:function(){return["fieldSize","fieldBorderRadius","fieldBackgroundColor","fieldBorderColor","fieldTextColor","labelSize","labelColor","labelSublabelColor","labelErrorColor","buttonSize","buttonBorderRadius","buttonBackgroundColor","buttonTextColor"]},getParsedValue:function(e,t){t=t[e];let o;return o=void 0!==t&&("object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"value")?t.value:t)},initRichTextField:function(e){l(`#wpforms-${e} .wp-editor-wrap`).removeClass("html-active").addClass("tmce-active")},updateAccentColors:function(e,t){e=e.find("#wpforms-"+t),t=i.WPForms.FrontendModern;t.updateGBBlockPageIndicatorColor(e),t.updateGBBlockIconChoicesColor(e),t.updateGBBlockRatingColor(e)},loadChoicesJS:function(e,t){"function"==typeof i.Choices&&e.find("#wpforms-"+t).find(".choicesjs-select").each(function(e,t){var o=l(t);if("active"!==o.data("choice")){var s=i.wpforms_choicesjs_config||{},n=o.data("search-enabled"),r=o.closest(".wpforms-field");s.searchEnabled=void 0===n||n,s.callbackOnInit=function(){var e=l(this.passedElement.element),t=l(this.input.element),o=e.data("size-class");o&&l(this.containerOuter.element).addClass(o),e.prop("multiple")&&(t.data("placeholder",t.attr("placeholder")),this.getValue(!0).length)&&t.removeAttr("placeholder"),this.disable(),r.find(".is-disabled").removeClass("is-disabled")};try{var a=new Choices(t,s);o.data("choicesjs",a)}catch(e){}}})},initRepeaterField(e){l(`.wpforms-form[data-formid="${e}"] .wpforms-field-repeater > .wpforms-field-repeater-display-rows .wpforms-field-repeater-display-rows-buttons`).each(function(){var e=l(this),t=e.siblings(".wpforms-layout-column").find(".wpforms-field").first().find(".wpforms-field-label"),o=i.getComputedStyle(t.get(0))?.getPropertyValue("--wpforms-field-size-input-spacing")||0,t=(t.outerHeight()||0)+parseInt(o,10)+10;e.css({top:t})}),l(`.wpforms-form[data-formid="${e}"]`).each(function(){var e=l(this).find(".wpforms-field-repeater");e.find(".wpforms-field-repeater-display-rows-buttons").addClass("wpforms-init"),e.find(".wpforms-field-repeater-display-rows:last .wpforms-field-description").addClass("wpforms-init")})}};return d}((document,window),jQuery);WPFormsElementorModern.init();