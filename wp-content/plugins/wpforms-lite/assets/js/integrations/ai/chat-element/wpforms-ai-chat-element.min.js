!function(){const r=wpforms_ai_chat_element.modules||[];Promise.all(r.map(e=>import(e.path))).then(e=>{const s={};let i;e.forEach((e,t)=>{t=r[t].name;"api"===t?i=e.default():s[t]=e.default}),window.WPFormsAi={api:i,helpers:s},customElements.define("wpforms-ai-chat",WPFormsAIChatHTMLElement)}).catch(e=>{wpf.debug("Error importing modules:",e)})}();class WPFormsAIChatHTMLElement extends HTMLElement{eventInitializes=!1;prefillSubmitted=!1;inputHeight={min:54,max:95};constructor(){super()}connectedCallback(){this.chatMode=this.getAttribute("mode")??"text",this.fieldId=this.getAttribute("field-id")??"",this.prefill=this.getAttribute("prefill")??"",this.autoSubmit="true"===this.getAttribute("auto-submit"),this.modeStrings=wpforms_ai_chat_element[this.chatMode]??{},this.loadingState=!1,this.modeHelpers=this.getHelpers(this),this.modeHelpers?(this.innerHTML.trim()||(this.innerHTML=this.getInnerHTML()),this.wrapper=this.querySelector(".wpforms-ai-chat"),this.input=this.querySelector(".wpforms-ai-chat-message-input input, .wpforms-ai-chat-message-input textarea"),this.welcomeScreenSamplePrompts=this.querySelector(".wpforms-ai-chat-welcome-screen-sample-prompts"),this.sendButton=this.querySelector(".wpforms-ai-chat-send"),this.stopButton=this.querySelector(".wpforms-ai-chat-stop"),this.messageList=this.querySelector(".wpforms-ai-chat-message-list"),this.isTextarea="TEXTAREA"===this.input.tagName,this.preventResizeInput=!1,navigator.userAgent.includes("Macintosh")||this.messageList.classList.add("wpforms-scrollbar-compact"),this.events(),this.initAnswers(),"function"==typeof this.modeHelpers.init&&this.modeHelpers.init(),this.autoSubmit&&this.prefill&&!this.prefillSubmitted&&(this.input.value=this.prefill,this.prefillSubmitted=!0,setTimeout(()=>this.sendMessage(!0),250))):console.error(`WPFormsAI error: chat mode "${this.chatMode}" helpers not found`)}getInnerHTML(){return this.modeStrings.chatHtml?this.decodeHTMLEntities(this.modeStrings.chatHtml):`
			<div class="wpforms-ai-chat">
				<div class="wpforms-ai-chat-message-list">
					${this.getWelcomeScreen()}
				</div>
				<div class="wpforms-ai-chat-message-input">
					${this.getMessageInputField()}
					<button type="button" class="wpforms-ai-chat-send"></button>
					<button type="button" class="wpforms-ai-chat-stop wpforms-hidden"></button>
				</div>
			</div>
		`}getMessageInputField(){return"function"==typeof this.modeHelpers.getMessageInputField?this.modeHelpers.getMessageInputField():`<textarea placeholder="${this.modeStrings.placeholder}"></textarea>`}getWelcomeScreen(){let e;return e=this.modeHelpers.isWelcomeScreen()?this.getWelcomeScreenContent():(this.messagePreAdded=!0,this.modeHelpers.getWarningMessage()),`
			<div class="wpforms-ai-chat-message-item item-primary">
				<div class="wpforms-ai-chat-welcome-screen">
					<div class="wpforms-ai-chat-header">
						<h3 class="wpforms-ai-chat-header-title">${this.modeStrings.title}</h3>
						<span class="wpforms-ai-chat-header-description">${this.modeStrings.description}
							<a href="${this.modeStrings.learnMoreUrl}" target="_blank" rel="noopener noreferrer">${this.modeStrings.learnMore}</a>
						</span>
					</div>
					${e}
				</div>
			</div>
		`}getWelcomeScreenContent(){var e=this.modeStrings?.samplePrompts,t=[];if(!e&&!this.modeStrings?.initialChat)return"";if(e){for(const s in e)t.push(`
					<li>
						<i class="${e[s].icon}"></i>
						<a href="#">${e[s].title}</a>
					</li>
				`);return`
				<ul class="wpforms-ai-chat-welcome-screen-sample-prompts">
					${t.join("")}
				</ul>
			`}return 0<this.prefill.length?"":(this.messagePreAdded=!0,this.modeHelpers?.getInitialChat(this.modeStrings.initialChat))}getSpinnerSvg(){return'<svg class="wpforms-ai-chat-spinner-dots" width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><style>.spinner_S1WN{animation:spinner_MGfb .8s linear infinite;animation-delay:-.8s; fill: currentColor;}.spinner_Km9P{animation-delay:-.65s}.spinner_JApP{animation-delay:-.5s}@keyframes spinner_MGfb{93.75%,100%{opacity:.2}}</style><circle class="spinner_S1WN" cx="4" cy="12" r="3"/><circle class="spinner_S1WN spinner_Km9P" cx="12" cy="12" r="3"/><circle class="spinner_S1WN spinner_JApP" cx="20" cy="12" r="3"/></svg>'}events(){this.eventInitializes||(this.sendButton.addEventListener("click",this.sendMessage.bind(this)),this.stopButton.addEventListener("click",this.stopLoading.bind(this)),this.input.addEventListener("keydown",this.keyDown.bind(this)),this.input.addEventListener("keyup",this.keyUp.bind(this)),this.bindWelcomeScreenEvents(),this.eventInitializes=!0)}bindWelcomeScreenEvents(){null!==this.welcomeScreenSamplePrompts&&this.welcomeScreenSamplePrompts.querySelectorAll("li").forEach(e=>{e.addEventListener("click",this.clickDefaultItem.bind(this)),e.addEventListener("keydown",e=>{"Enter"===e.code&&(e.preventDefault(),this.clickDefaultItem(e))})})}initAnswers(){this.modeStrings.chatHtml&&(this.wpformsAiApi=this.getAiApi(),this.messageList.querySelectorAll(".wpforms-chat-item-answer").forEach(e=>{this.initAnswer(e)}))}keyUp(e){switch(("function"==typeof this.modeHelpers.resizeInput?this.modeHelpers:this).resizeInput(e),e.code){case"Enter":this.isTextarea&&(!this.isTextarea||e.shiftKey)||(e.preventDefault(),this.sendMessage());break;case"ArrowUp":(!this.isTextarea||this.isTextarea&&e.ctrlKey)&&(e.preventDefault(),this.arrowUp());break;case"ArrowDown":(!this.isTextarea||this.isTextarea&&e.ctrlKey)&&(e.preventDefault(),this.arrowDown());break;default:this.history.update({question:this.input.value})}}keyDown(e){this.preventResizeInput="Enter"===e.code&&!e.shiftKey,this.preventResizeInput&&(e.preventDefault(),this.setInputHeight(this.inputHeight.min))}resizeInput(){var e;this.preventResizeInput?this.preventResizeInput=!1:(this.input.style.height="",this.input.style.paddingTop="10px",this.input.style.paddingBottom="10px",e=this.input.scrollHeight,e=Math.min(e,this.inputHeight.max),e=Math.max(e,this.inputHeight.min),this.setInputHeight(e))}setInputHeight(e){e<=this.inputHeight.min&&(this.input.style.paddingTop="",this.input.style.paddingBottom=""),this.input.style.height=e+"px",this.style.setProperty("--wpforms-ai-chat-input-height",e+"px")}sendMessage(e=!1){let t=this.input.value;t&&(e||(t=this.htmlSpecialChars(t)),this.triggerEvent("wpformsAIChatBeforeSendMessage",{fieldId:this.fieldId,mode:this.chatMode}),this.addFirstMessagePre(),this.welcomeScreenSamplePrompts?.remove(),this.resetInput(),this.addMessage(t,!0),this.startLoading(),""===t.trim()?this.addEmptyResultsError():("function"==typeof this.modeHelpers.prepareMessage&&(t=this.modeHelpers.prepareMessage(t)),this.getAiApi().prompt(t,this.sessionId).then(this.addAnswer.bind(this)).catch(this.apiResponseError.bind(this))))}apiResponseError(e){var t=e?.cause??null;this.triggerEvent("wpformsAIChatBeforeError",{fieldId:this.fieldId}),429===t?this.addError(this.modeStrings.errors.rate_limit||wpforms_ai_chat_element.errors.rate_limit,this.modeStrings.reasons.rate_limit||wpforms_ai_chat_element.reasons.rate_limit):500===t?this.addEmptyResultsError():(this.addError(e.message||this.modeStrings.errors.default||wpforms_ai_chat_element.errors.default,this.modeStrings.reasons.default||wpforms_ai_chat_element.reasons.default),wpf.debug("WPFormsAI error: ",e))}addFirstMessagePre(){var e;this.sessionId||this.messagePreAdded||(this.messagePreAdded=!0,(e=document.createElement("div")).classList.add("wpforms-ai-chat-divider"),this.messageList.appendChild(e))}clickDefaultItem(e){var t=("LI"===e.target.nodeName?e.target:e.target.closest("li")).querySelector("a")?.textContent;e.preventDefault(),t&&(this.input.value=t,this.history.push({question:t}),this.sendMessage())}clickDislikeButton(e){var e=e.target,t=e?.closest(".wpforms-chat-item-answer");t&&(e.classList.add("clicked"),e.setAttribute("disabled",!0),e=t.getAttribute("data-response-id"),this.wpformsAiApi.rate(!1,e))}async clickRefreshButton(){this.triggerEvent("wpformsAIChatBeforeRefreshConfirm",{fieldId:this.fieldId}),WPFormsAIModal.confirmModal({title:wpforms_ai_chat_element.confirm.refreshTitle,content:wpforms_ai_chat_element.confirm.refreshMessage,onConfirm:()=>{this.prefill="",this.messageList.innerHTML=this.getWelcomeScreen(),this.welcomeScreenSamplePrompts=this.querySelector(".wpforms-ai-chat-welcome-screen-sample-prompts"),this.bindWelcomeScreenEvents(),this.scrollMessagesTo("top"),this.wpformsAiApi=null,this.sessionId=null,this.messagePreAdded=null,this.wrapper.removeAttribute("data-session-id"),this.history.clear(),this.triggerEvent("wpformsAIChatAfterRefresh",{fieldId:this.fieldId})},onCancel:()=>{this.triggerEvent("wpformsAIChatCancelRefresh",{fieldId:this.fieldId})}})}startLoading(){this.loadingState=!0,this.sendButton.classList.add("wpforms-hidden"),this.stopButton.classList.remove("wpforms-hidden"),this.input.setAttribute("disabled",!0),this.input.setAttribute("placeholder",this.modeStrings.waiting)}stopLoading(){this.loadingState=!1,this.messageList.querySelector(".wpforms-chat-item-answer-waiting")?.remove(),this.sendButton.classList.remove("wpforms-hidden"),this.stopButton.classList.add("wpforms-hidden"),this.input.removeAttribute("disabled"),this.input.setAttribute("placeholder",this.modeStrings.placeholder),this.input.focus()}arrowUp(){var e=this.history.prev()?.question;void 0!==e&&(this.input.value=e)}arrowDown(){var e=this.history.next()?.question;void 0!==e&&(this.input.value=e)}getAiApi(){return this.wpformsAiApi||(this.sessionId=this.wrapper.getAttribute("data-session-id")||null,this.wpformsAiApi=window.WPFormsAi.api(this.chatMode,this.sessionId)),this.wpformsAiApi}scrollMessagesTo(e="bottom"){"top"===e?this.messageList.scrollTop=0:this.messageList.scrollHeight-this.messageList.scrollTop<22||(this.messageList.scrollTop=this.messageList.scrollHeight)}addMessage(e,t,s=null){var i,r=this["messageList"],a=document.createElement("div");return a.classList.add("wpforms-chat-item"),r.appendChild(a),t?(a.innerHTML=e,a.classList.add("wpforms-chat-item-question"),t=document.createElement("div"),i=document.createElement("div"),t.classList.add("wpforms-chat-item-answer-waiting"),i.classList.add("wpforms-chat-item-spinner"),i.innerHTML=this.getSpinnerSvg(),t.appendChild(i),r.appendChild(t),this.history.push({})):((i=document.createElement("div")).classList.add("wpforms-chat-item-content"),a.appendChild(i),r.querySelector(".wpforms-chat-item-answer-waiting")?.remove(),this.messageList.querySelector(".wpforms-chat-item-answer.active")?.classList.remove("active"),a.classList.add("wpforms-chat-item-answer"),a.classList.add("active"),a.classList.add("wpforms-chat-item-typing"),a.classList.add("wpforms-chat-item-"+this.chatMode),a.setAttribute("data-response-id",s?.responseId??""),this.history.update({answer:e}),this.typeText(i,e,this.addedAnswer.bind(this))),this.scrollMessagesTo("bottom"),a}addError(e,t){this.addNotice(e,t)}addWarning(e,t){this.addNotice(e,t,"warning")}addNotice(e,t,s="error"){let i="";this.loadingState&&(e&&(i+=`<h4>${e}</h4>`),t&&(i+=`<span>${t}</span>`),e=document.createElement("div"),t=document.createElement("div"),e.classList.add("wpforms-chat-item"),e.classList.add("wpforms-chat-item-"+s),t.classList.add("wpforms-chat-item-content"),e.appendChild(t),this.messageList.querySelector(".wpforms-chat-item-answer-waiting")?.remove(),this.messageList.appendChild(e),this.typeText(t,i,()=>{this.stopLoading()}))}addEmptyResultsError(){this.addError(this.modeStrings.errors.empty||wpforms_ai_chat_element.errors.empty,this.modeStrings.reasons.empty||wpforms_ai_chat_element.reasons.empty)}addProhibitedCodeWarning(){this.addWarning(this.modeStrings.warnings.prohibited_code||wpforms_ai_chat_element.warnings.prohibited_code,this.modeStrings.reasons.prohibited_code||wpforms_ai_chat_element.reasons.prohibited_code)}addAnswer(e){var t,s;this.loadingState&&e&&(e.processingData&&wpf.debug("WPFormsAI processing data:",e.processingData),t=this.sanitizeResponse({...e}),this.hasProhibitedCode(e,t)?(this.triggerEvent("wpformsAIChatBeforeError",{fieldId:this.fieldId}),this.addProhibitedCodeWarning()):(s=this.modeHelpers.getAnswer(t))?(this.sessionId=e.sessionId,this.wrapper.setAttribute("data-session-id",this.sessionId),this.triggerEvent("wpformsAIChatBeforeAddAnswer",{chat:this,response:t}),this.addMessage(s,!1,t),this.triggerEvent("wpformsAIChatAfterAddAnswer",{fieldId:this.fieldId})):(this.triggerEvent("wpformsAIChatBeforeError",{fieldId:this.fieldId}),this.addEmptyResultsError()))}hasProhibitedCode(e,t){return"function"==typeof this.modeHelpers.hasProhibitedCode&&this.modeHelpers.hasProhibitedCode(e,t)}sanitizeResponse(e){return"function"==typeof this.modeHelpers.sanitizeResponse?this.modeHelpers.sanitizeResponse(e):e}addedAnswer(e){e.innerHTML+=this.getAnswerButtons(),e.parentElement.classList.remove("wpforms-chat-item-typing"),this.stopLoading(),this.initAnswer(e),this.modeHelpers.addedAnswer(e),this.triggerEvent("wpformsAIChatAddedAnswer",{chat:this,element:e})}initAnswer(e){e&&(e.querySelectorAll(".wpforms-help-tooltip").forEach(e=>{var t;e.getAttribute("title")||(t=e.classList.contains("dislike")?wpforms_ai_chat_element.dislike:"",t=e.classList.contains("refresh")?wpforms_ai_chat_element.refresh:t,e.setAttribute("title",t)),e.classList.remove("tooltipstered")}),wpf.initTooltips(e),e.addEventListener("click",this.setActiveAnswer.bind(this)),e.querySelector(".wpforms-ai-chat-answer-button.dislike")?.addEventListener("click",this.clickDislikeButton.bind(this)),e.querySelector(".wpforms-ai-chat-answer-button.refresh")?.addEventListener("click",this.clickRefreshButton.bind(this)))}setActiveAnswer(e){var t=(t=e.target.closest(".wpforms-chat-item-answer"))||e.target;t.classList.contains("active")||(this.messageList.querySelector(".wpforms-chat-item-answer.active")?.classList.remove("active"),t.classList.add("active"),e=t.getAttribute("data-response-id"),this.modeHelpers.setActiveAnswer&&this.modeHelpers.setActiveAnswer(t),this.triggerEvent("wpformsAIChatSetActiveAnswer",{chat:this,responseId:e}))}getAnswerButtons(){return`
			<div class="wpforms-ai-chat-answer-buttons">
				${this.modeHelpers.getAnswerButtonsPre()}
				<div class="wpforms-ai-chat-answer-buttons-response">
					<button type="button" class="wpforms-ai-chat-answer-button dislike wpforms-help-tooltip" data-tooltip-position="top" title="${wpforms_ai_chat_element.dislike}"></button>
					<button type="button" class="wpforms-ai-chat-answer-button refresh wpforms-help-tooltip" data-tooltip-position="top" title="${wpforms_ai_chat_element.refresh}">
						<i class="fa fa-trash-o"></i>
					</button>
				</div>
			</div>
		`}typeText(s,i,r){const a=this;let n=0,o="";!function e(){var t=i.substring(n,n+5);o+=t,s.innerHTML=o.replace(/<[^>]{0,300}$/g,""),(n+=5)<i.length&&a.loadingState?setTimeout(e,20):"function"==typeof r&&(a.triggerEvent("wpformsAIChatAfterTypeText",{chat:a}),r(s)),a.scrollMessagesTo("bottom")}()}getHelpers(e){return window.WPFormsAi.helpers[e.chatMode](e)??null}resetInput(){this.input.value="",this.modeHelpers.resetInput&&this.modeHelpers.resetInput()}htmlSpecialChars(e){return e.replace(/[<>]/g,e=>"&#0"+e.charCodeAt(0)+";")}decodeHTMLEntities(e){var t=document.createElement("textarea");return t.innerHTML=e,t.value}triggerEvent(e,t={}){e=new CustomEvent(e,{detail:t});return document.dispatchEvent(e),e}history={data:[],pointer:0,defaultItem:{question:"",answer:null},get(e=null){return e&&(this.pointer=e),this.pointer<1?this.pointer=0:this.pointer>=this.data.length&&(this.pointer=this.data.length-1),this.data[this.pointer]??{}},prev(){return--this.pointer,this.get()},next(){return this.pointer+=1,this.get()},push(e){e.answer?this.data[this.data.length-1].answer=e.answer:(this.data.push({...this.defaultItem,...e}),this.pointer=this.data.length-1)},update(e){var t=0<this.data.length?this.data.length-1:0,s=this.data[t]??this.defaultItem;this.pointer=t,this.data[t]={...s,...e}},clear(){this.data=[],this.pointer=0}}}