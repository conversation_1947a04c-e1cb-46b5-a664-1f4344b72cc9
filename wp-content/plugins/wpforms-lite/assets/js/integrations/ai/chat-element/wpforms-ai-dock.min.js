const wpFormsAIDock=function(f){function l(o){localStorage.setItem("wpforms-ai-chat-prefers-pinned","1"),o.find(".wpforms-ai-modal-pin").attr("title",wpforms_ai_chat_element.unpinChat);var a=f("#wpforms-builder-form .wpforms-toolbar");const n=a.offset().top+a.outerHeight();o.addClass("pinned"),f("#wpadminbar").length&&o.addClass("with-wpadminbar"),o.insertAfter(a).promise().done(function(){o.css({top:n})})}function a(o){localStorage.setItem("wpforms-ai-chat-prefers-pinned","0"),o.find(".wpforms-ai-modal-pin").attr("title",wpforms_ai_chat_element.pinChat),o.removeClass("pinned"),o.removeClass("with-wpadminbar"),o.appendTo(f("body")).promise().done(function(){o.css({top:0})}),o.find(".wpforms-ai-modal-top-bar").removeClass("scrolled")}function m(){var o;f(this).hasClass("not-allowed")||(((o=f(this).closest(".jconfirm.jconfirm-wpforms-ai-modal")).hasClass("pinned")?a:l)(o),f(".jconfirm.jconfirm-wpforms-ai-modal").not(o).each(function(){var o=f(this);(o.hasClass("pinned")?a:l)(o),o.hide()}))}function p(o){"fields"!==f(o.target).closest("button").data("panel")&&f(".jconfirm.jconfirm-wpforms-ai-modal.pinned").each(function(){f(this).hide()})}function c(o){return f('wpforms-ai-chat[field-id="'+o+'"]').closest(".jconfirm.jconfirm-wpforms-ai-modal").last()}return{init:function(o){{var a=o;const n=c(a),i=n.find(".wpforms-ai-modal-pin").length;if(!i){const s=n.find(".jconfirm-closeIcon"),t=(s.after(`<div class="wpforms-ai-modal-top-bar">
			<div class="wpforms-ai-modal-pin" title="${wpforms_ai_chat_element.pinChat}"></div>
			</div>`).promise().done(function(){var o=n.find(".wpforms-ai-modal-top-bar");s.appendTo(o)}),s.attr("title",wpforms_ai_chat_element.close),n.find(".wpforms-ai-modal-top-bar")),e=n.find(".wpforms-ai-chat-message-list");e.off("scroll"),e.on("scroll",function(){0<e.scrollTop()?t.addClass("scrolled"):t.removeClass("scrolled")}),n.on("remove",function(){e.off("scroll")})}}{const r=f(".wpforms-ai-modal-pin");f(document).off("click",".wpforms-ai-modal-pin").on("click",".wpforms-ai-modal-pin",m).on("wpformsAIChatBeforeSendMessage",()=>r.addClass("not-allowed")).on("wpformsAIChatBeforeError wpformsAIChatAfterTypeText",()=>r.removeClass("not-allowed")),f("#wpforms-panels-toggle button").off("click",p).on("click",p)}a=o,"0"!==(localStorage.getItem("wpforms-ai-chat-prefers-pinned")||"0")&&l(c(a))}}}(jQuery);