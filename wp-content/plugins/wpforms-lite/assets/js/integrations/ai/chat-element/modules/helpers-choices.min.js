export default function(c){return{getAnswer(e){if(e.choices?.length<1)return"";var t=[];for(const i in e.choices)t.push(`
					<li class="wpforms-ai-chat-choices-item">
						${c.htmlSpecialChars(e.choices[i])}
					</li>
				`);let r=`
				<h4>${c.htmlSpecialChars(e.heading??"")}</h4>
				<ol>
					${t.join("")}
				</ol>
			`;return c.sessionId||(r+=`<span>${c.modeStrings.footer}</span>`),r},getAnswerButtonsPre(){return`
				<button type="button" class="wpforms-ai-chat-choices-insert wpforms-ai-chat-answer-action wpforms-btn-sm wpforms-btn-orange" >
					<span>${c.modeStrings.insert}</span>
				</button>
			`},getWarningMessage(){return c.triggerEvent("wpformsAIModalBeforeWarningMessageInsert",{fieldId:c.fieldId}),`<div class="wpforms-ai-chat-divider"></div>
					<div class="wpforms-chat-item-notice">
						<div class="wpforms-chat-item-notice-content">
							<span>${c.modeStrings.warning}</span>
						</div>
					</div>`},isWelcomeScreen(){var t=document.getElementById(`wpforms-field-option-row-${c.fieldId}-choices`).querySelectorAll("li input.label");if(1!==t.length||t[0].value.trim()){if(3<t.length)return!1;var r=Object.values(c.modeStrings.defaults);for(let e=0;e<t.length;e++)if(!r.includes(t[e].value))return!1}return!0},addedAnswer(e){e.querySelector(".wpforms-ai-chat-choices-insert")?.addEventListener("click",this.insertButtonClick.bind(this))},sanitizeResponse(t){if(Array.isArray(t?.choices)){let e=t.choices;e=e.map(e=>wpf.sanitizeHTML(e,wpforms_builder.allowed_label_html_tags)),t.choices=e.filter(e=>""!==e.trim())}return t},hasProhibitedCode(e,t){return t?.choices?.length!==e?.choices?.length},insertButtonClick(e){var e=e.target.closest(".wpforms-chat-item.wpforms-chat-item-choices"),t=e?.getAttribute("data-response-id"),r=(e?.querySelector("ol")).querySelectorAll(".wpforms-ai-chat-choices-item"),i=[];for(const o in r)r.hasOwnProperty(o)&&r[o].textContent&&i.push(r[o].textContent.trim());c.wpformsAiApi.rate(!0,t),this.replaceChoices(i),jQuery("#wpforms-field-"+c.fieldId).click().promise().done(function(){jQuery(`#wpforms-field-option-basic-${c.fieldId} a.wpforms-field-option-group-toggle`).click()})},replaceChoices(e){var t=document.getElementById(`wpforms-field-option-row-${c.fieldId}-choices`).querySelector("ul.choices-list"),r=t.querySelector("li:first-child").cloneNode(!0);r.innerHTML=r.innerHTML.replace(/\[choices\]\[\d+\]/g,"[choices][{{key}}]"),t.innerHTML="";for(const n in e){var i=(Number(n)+1).toString(),o=e[n],s=r.cloneNode(!0),s=this.getUpdatedSingleChoiceItem(s,i,o);t.appendChild(s)}t.setAttribute("data-next-id",e.length+1);var l=document.getElementById("wpforms-field-option-"+c.fieldId).querySelector("input.wpforms-field-option-hidden-type")?.value;WPFormsBuilder.fieldChoiceUpdate(l,c.fieldId,e.length),WPFormsBuilder.triggerBuilderEvent("wpformsFieldChoiceAdd"),c.triggerEvent("wpformsAIModalAfterChoicesInsert",{fieldId:c.fieldId})},getUpdatedSingleChoiceItem(e,t,r){e.setAttribute("data-key",t.toString()),e.innerHTML=e.innerHTML.replaceAll("{{key}}",t),r=wpf.sanitizeHTML(r);e.querySelector("input.default").removeAttribute("checked");t=e.querySelector("input.label"),t.value=r,t.setAttribute("value",r),t=e.querySelector("input.value"),t.value=r,t.setAttribute("value",r),t=e.querySelector(".wpforms-image-upload"),r=t.querySelector("input.source"),r.value="",r.setAttribute("value",""),t.querySelector(".preview").innerHTML="",t.querySelector(".wpforms-image-upload-add").style.display="block",r=e.querySelector(".wpforms-icon-select");return r.querySelector(".ic-fa-preview").setAttribute("class","ic-fa-preview ic-fa-regular ic-fa-face-smile"),r.querySelector("input.source-icon").value="face-smile",r.querySelector("input.source-icon-style").value="regular",e}}}