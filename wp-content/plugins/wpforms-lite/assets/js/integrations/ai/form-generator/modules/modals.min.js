export default function(t,l){const i=wpforms_ai_form_generator,c={el:{},ajaxError:"Form Generator AJAX error:",init(){c.el.$doc=l(document),c.el.$templateCard=l("#wpforms-template-generate"),c.events()},events(){c.el.$doc.on("change",".wpforms-ai-forms-install-addons-modal-dismiss",c.dismissAddonsModal)},openAddonsModal(n){n?.preventDefault();const o="install"===i.addonsAction;n=o?i.addons.installContent:i.addons.activateContent,n={title:i.addons.installTitle,content:n,type:"purple",icon:"fa fa-info-circle",buttons:{confirm:{text:o?i.addons.installConfirmButton:i.addons.activateConfirmButton,btnClass:"btn-confirm",keys:["enter"],action(){var n=o?i.addons.installing:i.addons.activating;return this.$$confirm.prop("disabled",!0).html('<i class="wpforms-loading-spinner wpforms-loading-white wpforms-loading-inline"></i>'+n),this.$$cancel.prop("disabled",!0),c.installAddonsAjax(this),!1}},cancel:{text:i.addons.cancelButton,keys:["esc"],btnClass:"btn-cancel",action(){c.updateGenerateFormButton(!1),setTimeout(()=>{t.state.panelOpen=!0},250)}}},onOpenBefore(){var n=`
						<label class="jconfirm-checkbox">
							<input type="checkbox" class="jconfirm-checkbox-input wpforms-ai-forms-install-addons-modal-dismiss">
							${i.addons.dontShow}
						</label>
					`;this.$body.addClass("wpforms-ai-forms-install-addons-modal").find(".jconfirm-buttons").after(n)}};l.confirm(n)},installAddonsAjax(n){let o=null,t=!1;function a(n){n.success||wpf.debug(c.ajaxError,n.data.error??n.data),n.success||t||(t=!0,c.openErrorModal({title:"install"===i.addonsAction?i.addons.addonsInstallErrorTitle:i.addons.addonsActivateErrorTitle,content:i.addons.addonsInstallError}))}function e(n){var o;t||(n=n.responseText||i.addons.addonsInstallErrorNetwork,o=i.addons.addonsInstallError,o+=n&&"error"!==n?"<br>"+n:"",wpf.debug(c.ajaxError,o),c.openErrorModal({title:"install"===i.addonsAction?i.addons.addonsInstallErrorTitle:i.addons.addonsActivateErrorTitle,content:o}),t=!0)}WPFormsBuilder.setCloseConfirmation(!1);for(const d in i.addonsData){var s=i.addonsData[d]?.url;const r={action:s?"wpforms_install_addon":"wpforms_activate_addon",nonce:i.adminNonce,plugin:s||i.addonsData[d]?.path,type:"addon"};(o=null===o?l.post(i.ajaxUrl,r,a):o.then(()=>l.post(i.ajaxUrl,r,a))).fail(e)}o.then(()=>{t||c.openAddonsInstalledModal()}).always(()=>{n.close(),c.updateGenerateFormButton(!1)})},dismissAddonsModal(){var n=l(this).prop("checked"),o={action:"wpforms_dismiss_ai_form",nonce:i.nonce,element:"install-addons-modal",dismiss:n};c.updateGenerateFormButton(!n),l.post(i.ajaxUrl,o).done(function(n){n.success||(c.openErrorModal({title:i.addons.dismissErrorTitle,content:i.addons.dismissError}),wpf.debug(c.ajaxError,n.data.error??n.data))}).fail(function(n){c.openErrorModal({title:i.addons.dismissErrorTitle,content:i.addons.dismissError+"<br>"+i.addons.addonsInstallErrorNetwork}),wpf.debug(c.ajaxError,n.responseText??n.statusText)})},updateGenerateFormButton(n){n?l(".wpforms-template-generate").removeClass("wpforms-template-generate").addClass("wpforms-template-generate-install-addons"):l(".wpforms-template-generate-install-addons").removeClass("wpforms-template-generate-install-addons").addClass("wpforms-template-generate")},openAddonsInstalledModal(){var n={title:"install"===i.addonsAction?i.addons.addonsInstalledTitle:i.addons.addonsActivatedTitle,content:i.addons.addonsInstalledContent,icon:"fa fa-check-circle",type:"green",buttons:{confirm:{text:i.addons.okay,btnClass:"btn-confirm",keys:["enter"],action(){WPFormsBuilder.showLoadingOverlay(),window.location=window.location+"&ai-form"}}},onOpenBefore(){this.$body.addClass("wpforms-ai-forms-addons-installed-modal")}};l.confirm(n)},openExistingFormModal(n){l.confirm({title:wpforms_builder.heads_up,content:i.misc.warningExistingForm,icon:"fa fa-exclamation-circle",type:"orange",buttons:{confirm:{text:wpforms_builder.ok,btnClass:"btn-confirm",keys:["enter"],action(){t.main.useFormAjax(n)}},cancel:{text:wpforms_builder.cancel}}})},openErrorModal(n){n={title:n.title??!1,content:n.content??!1,icon:"fa fa-exclamation-circle",type:"red",buttons:{confirm:{text:i.addons.okay,btnClass:"btn-confirm",keys:["enter"]}}};l.confirm(n)}};return c}