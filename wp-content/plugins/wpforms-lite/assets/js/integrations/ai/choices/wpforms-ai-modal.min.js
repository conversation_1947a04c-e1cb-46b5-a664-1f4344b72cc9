var WPFormsAIModal=window.WPFormsAIModal||function(e,o,a){const s={defaultOptions:{title:!1,content:"",type:"ai",smoothContent:!0,bgOpacity:1,boxWidth:650,contentMaxHeight:600,closeIcon:!0,buttons:!1},init(){a(s.ready)},ready(){s.extendJqueryConfirm(),s.bindChoicesActions()},bindChoicesActions(){a(e).on("click",".wpforms-ai-choices-button",s.initChoicesModal).on("wpformsAIChatBeforeRefreshConfirm",s.beforeChoicesRefreshConfirm).on("wpformsAIModalBeforeWarningMessageInsert",s.refreshModalHeight).on("wpformsAIChatAfterRefresh",s.refreshModalHeight).on("wpformsAIChatCancelRefresh",s.cancelChoicesRefresh).on("wpformsAIChatBeforeSendMessage",function(e){s.resizeModalHeight(e.detail.fieldId)}).on("wpformsAIChatAfterAddAnswer",function(e){s.resizeModalHeight(e.detail.fieldId)}).on("wpformsAIModalAfterChoicesInsert",function(e){s.hideChoicesModal(e.detail.fieldId)}),a(o).on("resize",function(){a(".jconfirm-wpforms-ai-modal wpforms-ai-chat").each(function(){s.resizeModalHeight(a(this).attr("field-id"))})})},initModal(e){a.confirm({...s.defaultOptions,...e})},initChoicesModal(){var e=a(this);if(e.hasClass("wpforms-prevent-default"))e.trigger("blur");else{const o=e.data("field-id"),i=a(".jconfirm-wpforms-ai-modal-choices-"+o);if(a(`.jconfirm-wpforms-ai-modal:not(.jconfirm-wpforms-ai-modal-choices-${o})`).addClass("wpforms-hidden").fadeOut(),i.length)i.removeClass("wpforms-hidden").fadeIn();else{const n={},t=function(){return s.hideChoicesModal(o),!1};n.content=`<wpforms-ai-chat mode="choices" field-id="${o}" />`,n.theme="wpforms-ai-modal, wpforms-ai-purple, wpforms-ai-modal-choices-"+o,n.backgroundDismiss=t,n.backgroundDismissAnimation="",n.contentMaxHeight=Math.min(s.defaultOptions.contentMaxHeight,s.getMaxModalHeight()),n.onOpen=function(){this.$closeIcon.off("click"),this.$closeIcon.on("click",t)},n.onOpenBefore=function(){wpFormsAIDock.init(o)},s.initModal(n)}}},hideChoicesModal(e){a(".jconfirm-wpforms-ai-modal-choices-"+e).addClass("wpforms-hidden").fadeOut()},showChoicesModal(e){a(".jconfirm-wpforms-ai-modal-choices-"+e).removeClass("wpforms-hidden").fadeIn()},resizeModalHeight(e){var o=s.getMaxModalHeight();a(".jconfirm-wpforms-ai-modal").filter(function(){return a(this).attr("class").match(new RegExp("jconfirm-wpforms-ai-modal-.*-"+e,"i"))}).find(".jconfirm-content-pane").css({height:o,"max-height":o})},beforeChoicesRefreshConfirm(e){e=e.detail?.fieldId||0;s.hideChoicesModal(e)},cancelChoicesRefresh(e){e=e.detail?.fieldId||0;s.showChoicesModal(e)},refreshModalHeight(e){var e=e.detail?.fieldId||0,o=Math.min(s.getMaxModalHeight(),s.defaultOptions.contentMaxHeight);s.showChoicesModal(e),a(`.jconfirm-wpforms-ai-modal-choices-${e} .jconfirm-content-pane`).css({height:o,"max-height":o})},getMaxModalHeight(){return Math.min(.8*a(o).height(),800)},extendJqueryConfirm(){o.Jconfirm.prototype._updateContentMaxHeight=function(){this.$contentPane.css({"max-height":this.contentMaxHeight+"px"})}},confirmModal(e){var o={title:!1,content:"",icon:"fa fa-exclamation-circle",type:"orange",buttons:{confirm:{text:wpforms_ai_chat_element.btnYes,btnClass:"btn-confirm",keys:["enter"],action(){"function"==typeof e.onConfirm&&e.onConfirm()}},cancel:{text:wpforms_ai_chat_element.btnCancel,action(){"function"==typeof e.onCancel&&e.onCancel()}}}};a.confirm({...o,...e})}};return s}(document,window,jQuery);WPFormsAIModal.init();