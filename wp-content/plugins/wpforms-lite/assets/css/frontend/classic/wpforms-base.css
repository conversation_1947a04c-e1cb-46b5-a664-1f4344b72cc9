.flatpickr-calendar .flatpickr-current-month select {
  display: initial;
}

@media only screen and (max-width: 600px) {
  .flatpickr-calendar .flatpickr-current-month .numInputWrapper {
    width: calc(6ch - 14px);
  }
  .flatpickr-calendar .flatpickr-current-month .numInputWrapper span.arrowUp,
  .flatpickr-calendar .flatpickr-current-month .numInputWrapper span.arrowDown {
    display: none;
  }
}

.wpforms-container .wpforms-form .wpforms-error-alert {
  border: 1px solid #cccccc;
  text-shadow: 0 1px 0 rgba(255, 255, 255, 0.5);
  padding: 10px 15px;
  font-size: 14px;
  margin: 0 0 10px 0;
}

.wpforms-container .wpforms-form .wpforms-error-alert {
  color: #b94a48;
  background-color: #f2dede;
  border-color: #eed3d7;
}

/* hCAPTCHA Area
----------------------------------------------------------------------------- */
div[style*="z-index: 2147483647"] div[style*="border-width: 11px"][style*="position: absolute"][style*="pointer-events: none"] {
  border-style: none;
}

/* Cloudflare Turnstile iframe content alignment fix.
----------------------------------------------------------------------------- */
.wpforms-is-turnstile iframe {
  margin-left: -2px !important;
}

.wpforms-container .wpforms-hidden {
  display: none !important;
}

/* ==========================================================================
   Base - basic bare bones styling
   ========================================================================== */
/* Errors, Warnings, etc
----------------------------------------------------------------------------- */
.wpforms-container .wpforms-error-container,
.wpforms-container noscript.wpforms-error-noscript {
  color: #990000;
}

.wpforms-container label.wpforms-error {
  display: block;
  color: #990000;
  font-size: 0.9em;
  float: none;
  cursor: default;
}

.wpforms-container .wpforms-field input.wpforms-error,
.wpforms-container .wpforms-field input.user-invalid,
.wpforms-container .wpforms-field textarea.wpforms-error,
.wpforms-container .wpforms-field textarea.user-invalid,
.wpforms-container .wpforms-field select.wpforms-error,
.wpforms-container .wpforms-field select.user-invalid,
.wpforms-container .wpforms-field.wpforms-has-error .choices__inner {
  border: 1px solid #cc0000;
}

.wpforms-container .wpforms-field-credit-card-expiration label.wpforms-error,
.wpforms-container .wpforms-field-credit-card-code label.wpforms-error {
  display: none !important;
}

/* Page Indicator themes
----------------------------------------------------------------------------- */
.wpforms-container .wpforms-page-indicator {
  margin: 0 0 20px 0;
  overflow: hidden;
}

/** Circles theme **/
.wpforms-container .wpforms-page-indicator.circles {
  border-top: 1px solid #dfdfdf;
  border-bottom: 1px solid #dfdfdf;
  padding: 15px 10px;
}

.wpforms-container .wpforms-page-indicator.circles .wpforms-page-indicator-page {
  float: left;
  margin: 0 20px 0 0;
}

.wpforms-container .wpforms-page-indicator.circles .wpforms-page-indicator-page:last-of-type {
  margin: 0;
}

.wpforms-container .wpforms-page-indicator.circles .wpforms-page-indicator-page-number {
  height: 40px;
  width: 40px;
  border-radius: 50%;
  display: inline-block;
  margin: 0 10px 0 0;
  line-height: 40px;
  text-align: center;
  background-color: #ddd;
  color: #666;
}

.wpforms-container .wpforms-page-indicator.circles .active .wpforms-page-indicator-page-number {
  color: #fff;
}

/* Connector theme */
.wpforms-container .wpforms-page-indicator.connector .wpforms-page-indicator-page {
  float: left;
  text-align: center;
  line-height: 1.2;
}

.wpforms-container .wpforms-page-indicator.connector .wpforms-page-indicator-page-number {
  display: block;
  text-indent: -9999px;
  height: 6px;
  background-color: #ddd;
  margin: 0 0 16px 0;
  position: relative;
}

.wpforms-container .wpforms-page-indicator.connector .wpforms-page-indicator-page-triangle {
  position: absolute;
  top: 100%;
  left: 50%;
  width: 0;
  height: 0;
  margin-left: -5px;
  border-style: solid;
  border-width: 6px 5px 0 5px;
  border-color: transparent transparent transparent transparent;
}

.wpforms-container .wpforms-page-indicator.connector .wpforms-page-indicator-page-title {
  display: inline-block;
  padding: 0 15px;
  font-size: 16px;
}

/* Progress theme */
.wpforms-container .wpforms-page-indicator.progress {
  font-size: 18px;
}

.wpforms-container .wpforms-page-indicator.progress .wpforms-page-indicator-page-progress-wrap {
  display: block;
  width: 100%;
  background-color: #ddd;
  height: 18px;
  border-radius: 10px;
  overflow: hidden;
  position: relative;
  margin: 5px 0 0;
}

.wpforms-container .wpforms-page-indicator.progress .wpforms-page-indicator-page-progress {
  height: 18px;
  position: absolute;
  left: 0;
  top: 0;
}

/* Notices
----------------------------------------------------------------------------- */
div.wpforms-container .wpforms-notice {
  background-color: #fff;
  border: 1px solid #ddd;
  border-left-width: 12px;
  color: #333;
  font-size: 16px;
  line-height: 1.5;
  margin-bottom: 30px;
  padding: 20px 36px 20px 26px;
  position: relative;
}

div.wpforms-container .wpforms-notice .wpforms-delete {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -moz-appearance: none;
  -webkit-appearance: none;
  background-color: rgba(10, 10, 10, 0.2);
  border: none;
  border-radius: 290486px;
  cursor: pointer;
  display: inline-block;
  height: 20px;
  margin: 0;
  padding: 0;
  outline: none;
  vertical-align: top;
  width: 20px;
  position: absolute;
  right: 10px;
  top: 10px;
}

div.wpforms-container .wpforms-notice .wpforms-delete:before,
div.wpforms-container .wpforms-notice .wpforms-delete:after {
  background-color: #fff;
  content: "";
  display: block;
  left: 50%;
  position: absolute;
  top: 50%;
  -webkit-transform: translateX(-50%) translateY(-50%) rotate(45deg);
  transform: translateX(-50%) translateY(-50%) rotate(45deg);
  -webkit-transform-origin: center center;
  transform-origin: center center;
}

div.wpforms-container .wpforms-notice .wpforms-delete:before {
  height: 2px;
  width: 50%;
}

div.wpforms-container .wpforms-notice .wpforms-delete:after {
  height: 50%;
  width: 2px;
}

div.wpforms-container .wpforms-notice .wpforms-delete:hover,
div.wpforms-container .wpforms-notice .wpforms-delete:focus {
  background-color: rgba(10, 10, 10, 0.3);
}

div.wpforms-container .wpforms-notice a {
  text-decoration: underline;
}

div.wpforms-container .wpforms-notice p {
  margin: 0 0 20px 0;
}

div.wpforms-container .wpforms-notice p:last-of-type {
  margin-bottom: 0;
}

div.wpforms-container-full .wpforms-notice.wpforms-info .wpforms-notice-action,
div.wpforms-container-full .wpforms-notice.wpforms-info {
  border-color: #3273dc;
}

div.wpforms-container-full .wpforms-notice.wpforms-success .wpforms-notice-action,
div.wpforms-container-full .wpforms-notice.wpforms-success {
  border-color: #23d160;
}

div.wpforms-container-full .wpforms-notice.wpforms-warning .wpforms-notice-action,
div.wpforms-container-full .wpforms-notice.wpforms-warning {
  border-color: #ffdd57;
}

div.wpforms-container-full .wpforms-notice.wpforms-error .wpforms-notice-action,
div.wpforms-container-full .wpforms-notice.wpforms-error {
  border-color: #ff3860;
}

div.wpforms-container-full .wpforms-notice .wpforms-notice-actions {
  margin-top: 20px;
}

div.wpforms-container-full .wpforms-notice .wpforms-notice-action {
  border: 2px solid;
  margin-right: 20px;
  padding: 5px;
  text-decoration: none;
}

div.wpforms-container-full .wpforms-notice .wpforms-notice-action:hover,
div.wpforms-container-full .wpforms-notice .wpforms-notice-action:focus,
div.wpforms-container-full .wpforms-notice .wpforms-notice-action:active {
  color: #fff;
}

div.wpforms-container-full .wpforms-notice.wpforms-info .wpforms-notice-action:hover,
div.wpforms-container-full .wpforms-notice.wpforms-info .wpforms-notice-action:focus,
div.wpforms-container-full .wpforms-notice.wpforms-info .wpforms-notice-action:active {
  background-color: #3273dc;
}

div.wpforms-container-full .wpforms-notice.wpforms-success .wpforms-notice-action:hover,
div.wpforms-container-full .wpforms-notice.wpforms-success .wpforms-notice-action:focus,
div.wpforms-container-full .wpforms-notice.wpforms-success .wpforms-notice-action:active {
  background-color: #23d160;
}

div.wpforms-container-full .wpforms-notice.wpforms-warning .wpforms-notice-action:hover,
div.wpforms-container-full .wpforms-notice.wpforms-warning .wpforms-notice-action:focus,
div.wpforms-container-full .wpforms-notice.wpforms-warning .wpforms-notice-action:active {
  background-color: #ffdd57;
  color: inherit;
}

div.wpforms-container-full .wpforms-notice.wpforms-error .wpforms-notice-action:hover,
div.wpforms-container-full .wpforms-notice.wpforms-error .wpforms-notice-action:focus,
div.wpforms-container-full .wpforms-notice.wpforms-error .wpforms-notice-action:active {
  background-color: #ff3860;
}

div.wpforms-container .wpforms-error-container.wpforms-error-styled-container {
  padding: 10px 0;
  font-size: 15px;
}

div.wpforms-container .wpforms-error-container.wpforms-error-styled-container p {
  margin: 0;
}

/* Preview notice.
----------------------------------------------------------------------------- */
.wpforms-preview-notice-links {
  line-height: 2.4;
}

/* Form Header area
----------------------------------------------------------------------------- */
.wpforms-container .wpforms-title {
  font-size: 26px;
  margin: 0 0 10px 0;
}

.wpforms-container .wpforms-description {
  margin: 0 0 10px 0;
}

/* Form Footer area
----------------------------------------------------------------------------- */
.wpforms-container .wpforms-submit-container {
  padding: 10px 0 0 0;
  position: relative;
}

.wpforms-container .wpforms-submit-spinner {
  margin-inline-start: 0.5em;
  vertical-align: middle;
}

/* Misc
----------------------------------------------------------------------------- */
.wpforms-container {
  margin-bottom: 26px;
}

/* Honeypot Area */
.wpforms-container .wpforms-field-hp {
  display: none !important;
  position: absolute !important;
  left: -9000px !important;
}

.wpforms-container .wpforms-field.wpforms-field-hidden {
  display: none;
  padding: 0;
}

.wpforms-container .wpforms-screen-reader-element {
  position: absolute !important;
  clip: rect(0, 0, 0, 0);
  height: 1px;
  width: 1px;
  border: 0;
  overflow: hidden;
  word-wrap: normal !important;
}

div.wpforms-container .wpforms-form textarea {
  resize: vertical;
}

/*
 * Hide the form fields upon successful submission. This may not be the best approach.
 * Perhaps more robust: .wpforms-form.amp-form-submit-success > *:not([submit-success]) { display:none }
 */
.amp-form-submit-success .wpforms-field-container,
.amp-form-submit-success .wpforms-submit-container {
  display: none;
}

/* Gutenberg Block
----------------------------------------------------------------------------- */
.edit-post-visual-editor .wpforms-gutenberg-form-selector-wrap h3 {
  width: 100%;
  margin: 10px 0 5px;
  font-weight: 700;
  font-size: 20px;
}

.edit-post-visual-editor .wpforms-gutenberg-form-selector-wrap img {
  margin-right: 25px;
  width: initial;
}

.edit-post-visual-editor .wpforms-gutenberg-form-selector-wrap .components-base-control {
  width: 100%;
}

div.wpforms-gutenberg-form-selector .wpforms-form input:disabled,
div.wpforms-gutenberg-form-selector .wpforms-form textarea:disabled,
div.wpforms-gutenberg-form-selector .wpforms-form select:disabled,
div.wpforms-gutenberg-form-selector .wpforms-form button[type=submit]:disabled {
  cursor: not-allowed;
}

div.wpforms-container .wpforms-form .choices.is-open.is-flipped .choices__inner,
div.wpforms-container .wpforms-form .choices.is-open .choices__list--dropdown {
  border-radius: 0 0 2px 2px;
}

div.wpforms-container .wpforms-form .choices.is-open.is-flipped .choices__list--dropdown,
div.wpforms-container .wpforms-form .choices.is-open .choices__inner {
  border-radius: 2px 2px 0 0;
}

div.wpforms-container .wpforms-form .choices .choices__inner {
  border-radius: 2px;
  min-height: 35px;
}

div.wpforms-container .wpforms-form .choices .choices__inner .choices__list--single {
  height: auto;
}

div.wpforms-container .wpforms-form .choices .choices__inner .choices__list--multiple .choices__item {
  line-height: 1.3;
}

/* RTL support
----------------------------------------------------------------------------- */
/* Phone US format */
body.rtl .wpforms-field-phone input[type=tel] {
  direction: ltr;
  unicode-bidi: embed;
  text-align: right;
}

body.rtl .wpforms-container .wpforms-first {
  float: right;
}

body.rtl .wpforms-container .wpforms-first + .wpforms-one-half {
  margin-right: 4%;
  margin-left: 0;
}

body.rtl .wpforms-container.wpforms-edit-entry-container .wpforms-first + .wpforms-one-half {
  margin-right: 0;
}

/* ==========================================================================
   Base - basic bare bones styling
   ========================================================================== */
.wpforms-clear:before {
  content: " ";
  display: table;
}

.wpforms-clear:after {
  clear: both;
  content: " ";
  display: table;
}

.wpforms-container ul,
.wpforms-container ul li {
  background: none;
  border: 0;
  margin: 0;
  list-style: none;
}

/* Basic Field properties
----------------------------------------------------------------------------- */
/* Field sizes - medium */
.wpforms-container input.wpforms-field-medium,
.wpforms-container select.wpforms-field-medium,
.wpforms-container .wpforms-field-row.wpforms-field-medium {
  max-width: 60%;
}

.wpforms-container textarea.wpforms-field-medium {
  height: 120px;
}

/* Field sizes - small */
.wpforms-container input.wpforms-field-small,
.wpforms-container select.wpforms-field-small,
.wpforms-container .wpforms-field-row.wpforms-field-small {
  max-width: 25%;
}

.wpforms-container textarea.wpforms-field-small {
  height: 70px;
}

/* Field sizes - medium */
.wpforms-container input.wpforms-field-large,
.wpforms-container select.wpforms-field-large,
.wpforms-container .wpforms-field-row.wpforms-field-large {
  max-width: 100%;
}

.wpforms-container textarea.wpforms-field-large {
  height: 220px;
}

/* Field container*/
.wpforms-container .wpforms-field {
  padding: 10px 0;
  position: relative;
}

/* Field description */
.wpforms-container .wpforms-field-description,
.wpforms-container .wpforms-field-limit-text {
  font-size: 0.85em;
  margin: 5px 0 0 0;
}

.wpforms-container .wpforms-field-description.wpforms-disclaimer-description {
  background-color: #fff;
  border: 1px solid #ddd;
  padding: 15px 15px 0;
  height: 125px;
  overflow-y: scroll;
  overflow-x: hidden;
}

.wpforms-container .wpforms-field-description.wpforms-disclaimer-description p {
  margin: 0 0 15px 0;
}

.wpforms-container .wpforms-field-description-before,
.wpforms-container .wpforms-field-description.before {
  font-size: 0.85em;
  margin: 0 0 5px 0;
}

/* Labels and sub-labels */
.wpforms-container .wpforms-field-label {
  display: block;
  font-weight: 700;
  float: none;
  word-break: break-word;
  word-wrap: break-word;
}

.wpforms-container .wpforms-field-sublabel {
  display: block;
  font-size: 0.85em;
  float: none;
}

.wpforms-container .wpforms-field-label-inline {
  display: inline;
  vertical-align: baseline;
  font-weight: 400;
  word-break: break-word;
}

.wpforms-container .wpforms-field-label.wpforms-label-hide,
.wpforms-container .wpforms-field-sublabel.wpforms-sublabel-hide {
  position: absolute;
  clip: rect(0 0 0 0);
  width: 1px;
  height: 1px;
  margin: -1px;
  overflow: hidden;
}

.wpforms-container .wpforms-required-label {
  color: #ff0000;
  font-weight: normal;
}

/* Rows (multi-line fields: address, credit card, etc)
----------------------------------------------------------------------------- */
.wpforms-container .wpforms-field-row {
  margin-bottom: 8px;
  position: relative;
}

.wpforms-container .wpforms-field .wpforms-field-row:last-of-type {
  margin-bottom: 0;
}

.wpforms-container .wpforms-field-row:before {
  content: "";
  display: table;
}

.wpforms-container .wpforms-field-row:after {
  clear: both;
  content: "";
  display: table;
}

.wpforms-container .wpforms-form .wpforms-field-address .wpforms-one-half:only-child {
  margin-left: 0;
}

/* Columns
----------------------------------------------------------------------------- */
/* User column classes (legacy). */
.wpforms-container .wpforms-five-sixths,
.wpforms-container .wpforms-four-sixths,
.wpforms-container .wpforms-four-fifths,
.wpforms-container .wpforms-one-fifth,
.wpforms-container .wpforms-one-fourth,
.wpforms-container .wpforms-one-half,
.wpforms-container .wpforms-one-sixth,
.wpforms-container .wpforms-one-third,
.wpforms-container .wpforms-three-fourths,
.wpforms-container .wpforms-three-fifths,
.wpforms-container .wpforms-three-sixths,
.wpforms-container .wpforms-two-fourths,
.wpforms-container .wpforms-two-fifths,
.wpforms-container .wpforms-two-sixths,
.wpforms-container .wpforms-two-thirds {
  float: left;
  margin-left: 20px;
  clear: none;
}

.wpforms-container .wpforms-one-half,
.wpforms-container .wpforms-three-sixths,
.wpforms-container .wpforms-two-fourths {
  width: calc( 50% - 10px);
}

.wpforms-container .wpforms-one-third,
.wpforms-container .wpforms-two-sixths {
  width: calc( 100% / 3 - 20px);
}

.wpforms-container .wpforms-one-third.wpforms-first,
.wpforms-container .wpforms-two-sixths.wpforms-first {
  width: calc( 100% / 3);
}

.wpforms-container .wpforms-four-sixths,
.wpforms-container .wpforms-two-thirds {
  width: calc( 2 * 100% / 3 - 20px);
}

.wpforms-container .wpforms-four-sixths.wpforms-first,
.wpforms-container .wpforms-two-thirds.wpforms-first {
  width: calc( 2 * 100% / 3);
}

.wpforms-container .wpforms-one-fourth {
  width: calc( 25% - 20px);
}

.wpforms-container .wpforms-one-fourth.wpforms-first {
  width: 25%;
}

.wpforms-container .wpforms-three-fourths {
  width: calc( 75% - 20px);
}

.wpforms-container .wpforms-three-fourths.wpforms-first {
  width: 75%;
}

.wpforms-container .wpforms-one-fifth {
  width: calc( 100% / 5 - 20px);
}

.wpforms-container .wpforms-one-fifth.wpforms-first {
  width: calc( 100% / 5);
}

.wpforms-container .wpforms-two-fifths {
  width: calc( 2 * 100% / 5 - 20px);
}

.wpforms-container .wpforms-two-fifths.wpforms-first {
  width: calc( 2 * 100% / 5);
}

.wpforms-container .wpforms-three-fifths {
  width: calc( 3 * 100% / 5 - 20px);
}

.wpforms-container .wpforms-three-fifths.wpforms-first {
  width: calc( 3 * 100% / 5);
}

.wpforms-container .wpforms-four-fifths {
  width: calc( 4 * 100% / 5 - 20px);
}

.wpforms-container .wpforms-four-fifths.wpforms-first {
  width: calc( 4 * 100% / 5);
}

.wpforms-container .wpforms-one-sixth {
  width: calc( 100% / 6 - 20px);
}

.wpforms-container .wpforms-one-sixth.wpforms-first {
  width: calc( 100% / 6);
}

.wpforms-container .wpforms-five-sixths {
  width: calc( 5 * 100% / 6 - 20px);
}

.wpforms-container .wpforms-five-sixths.wpforms-first {
  width: calc( 5 * 100% / 6);
}

.wpforms-container .wpforms-first {
  clear: both !important;
  margin-left: 0 !important;
}

/* User list column classes  */
.wpforms-container .wpforms-checkbox-2-columns ul,
.wpforms-container .wpforms-multiplechoice-2-columns ul,
.wpforms-container .wpforms-list-2-columns ul,
.wpforms-container .wpforms-checkbox-3-columns ul,
.wpforms-container .wpforms-multiplechoice-3-columns ul,
.wpforms-container .wpforms-list-3-columns ul {
  display: -ms-flex;
  display: -webkit-flex;
  display: flex;
  -webkit-flex-wrap: wrap;
  flex-wrap: wrap;
}

.wpforms-container .wpforms-checkbox-2-columns ul li,
.wpforms-container .wpforms-multiplechoice-2-columns ul li,
.wpforms-container .wpforms-list-2-columns ul li {
  width: 50%;
  display: block;
  padding-right: 26px;
}

.wpforms-container .wpforms-checkbox-3-columns ul li,
.wpforms-container .wpforms-multiplechoice-3-columns ul li,
.wpforms-container .wpforms-list-3-columns ul li {
  width: 33.3333%;
  display: block;
  padding-right: 26px;
}

.wpforms-container .wpforms-list-inline ul li {
  display: inline-block;
  margin-right: 20px;
  vertical-align: top;
}

/* Legacy, for BC */
.wpforms-container .wpforms-first-half {
  float: left;
  width: 48%;
  clear: both;
}

.wpforms-container .wpforms-last-half {
  float: right;
  width: 48%;
  clear: none;
}

.wpforms-container .wpforms-first-third {
  float: left;
  width: 30.666666667%;
  clear: both;
}

.wpforms-container .wpforms-middle-third {
  float: left;
  width: 30.666666667%;
  margin-left: 4%;
  clear: none;
}

.wpforms-container .wpforms-last-third {
  float: right;
  width: 30.666666667%;
  clear: none;
}

.wpforms-container .wpforms-last {
  float: right !important;
  margin-right: 0 !important;
  clear: none;
}

/* Preset Layouts
----------------------------------------------------------------------------- */
/* Single line */
.wpforms-container.inline-fields {
  overflow: visible;
}

.wpforms-container.inline-fields .wpforms-field-container {
  display: table;
  width: calc(100% - 160px);
  float: left;
}

.wpforms-container.inline-fields .wpforms-field {
  display: table-cell;
  padding-right: 2%;
  vertical-align: top;
}

.wpforms-container.inline-fields .wpforms-submit-container {
  float: right;
  width: 160px;
}

.wpforms-container.inline-fields .wpforms-submit {
  display: block;
  width: 100%;
}

.wpforms-container.inline-fields input.wpforms-field-medium,
.wpforms-container.inline-fields select.wpforms-field-medium,
.wpforms-container.inline-fields .wpforms-field-row.wpforms-field-medium {
  max-width: 100%;
}

/* Set Styles
----------------------------------------------------------------------------- */
.wpforms-container input[type=date],
.wpforms-container input[type=datetime],
.wpforms-container input[type=datetime-local],
.wpforms-container input[type=email],
.wpforms-container input[type=month],
.wpforms-container input[type=number],
.wpforms-container input[type=password],
.wpforms-container input[type=range],
.wpforms-container input[type=search],
.wpforms-container input[type=tel],
.wpforms-container input[type=text],
.wpforms-container input[type=time],
.wpforms-container input[type=url],
.wpforms-container input[type=week],
.wpforms-container select,
.wpforms-container textarea {
  display: block;
  width: 100%;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  float: none;
  font-family: inherit;
}

.wpforms-container input[type=checkbox],
.wpforms-container input[type=radio] {
  width: 13px;
  height: 13px;
  margin: 2px 10px 0 3px;
  display: inline-block;
  vertical-align: baseline;
}

.wpforms-container amp-img > img {
  position: absolute;
  /* Override position:static from previous rule, to prevent breaking AMP layout. */
}

/* reCAPTCHA Area
----------------------------------------------------------------------------- */
.wpforms-container .wpforms-recaptcha-container {
  padding: 10px 0 20px 0;
  clear: both;
}

/* Date/time field
----------------------------------------------------------------------------- */
.wpforms-container .wpforms-field-date-time-date-sep {
  display: inline-block;
  padding: 0 5px;
}

.wpforms-container .wpforms-field-date-time-date-year,
.wpforms-container .wpforms-field-date-time-date-day,
.wpforms-container .wpforms-field-date-time-date-month {
  display: inline-block;
  width: auto;
}

/* Rating field
----------------------------------------------------------------------------- */
.wpforms-container .wpforms-field-rating-item {
  padding: 0 6px 0 0;
  margin: 0;
  display: inline-block;
}

.wpforms-container .wpforms-field-rating svg {
  cursor: pointer;
  -webkit-transform: perspective(1px) translateZ(0);
  transform: perspective(1px) translateZ(0);
  box-shadow: 0 0 1px transparent;
  opacity: 0.60;
}

.wpforms-container .wpforms-field-rating-item.selected svg,
.wpforms-container .wpforms-field-rating-item.hover svg {
  -webkit-transform: scale(1.3);
  transform: scale(1.3);
  opacity: 1;
}

/* Date/time field
----------------------------------------------------------------------------- */
.wpforms-field-container .wpforms-field-date-time .wpforms-field-row {
  display: flex;
  flex-wrap: wrap;
  align-items: start;
  gap: 10px 4%;
}

.wpforms-field-container .wpforms-field-date-time .wpforms-field-row::before, .wpforms-field-container .wpforms-field-date-time .wpforms-field-row::after {
  position: absolute;
}

.wpforms-field-container .wpforms-field-date-time .wpforms-date-type-dropdown {
  align-items: center;
  display: flex;
  flex-grow: 1;
  flex-wrap: wrap;
}

.wpforms-field-container .wpforms-field-date-time .wpforms-date-type-dropdown .wpforms-field-date-dropdown-wrap {
  width: 100%;
}

.wpforms-field-container .wpforms-field-date-time .wpforms-date-type-dropdown + .wpforms-field-row-block {
  flex: 1;
  min-width: 30%;
}

.wpforms-field-container .wpforms-field-date-time .wpforms-date-type-dropdown .wpforms-field-sublabel {
  width: 100%;
}

.wpforms-field-container .wpforms-field-date-time .wpforms-field-date-dropdown-wrap {
  align-items: center;
  display: flex;
  flex-grow: 1;
  flex-wrap: nowrap;
  margin: 0 -6px 0 -6px;
}

.wpforms-field-container .wpforms-field-date-time .wpforms-field-date-dropdown-wrap.wpforms-field-small {
  width: calc( 25% + 12px);
}

.wpforms-field-container .wpforms-field-date-time .wpforms-field-date-dropdown-wrap.wpforms-field-medium {
  width: calc( 60% + 12px);
}

.wpforms-field-container .wpforms-field-date-time .wpforms-field-date-dropdown-wrap.wpforms-field-large {
  width: calc( 100% + 12px);
}

.wpforms-field-container .wpforms-field-date-time .wpforms-field-date-dropdown-wrap select {
  margin: 0 6px 0 6px;
}

.wpforms-field-container .wpforms-field-date-time .wpforms-field-date-time-date-day,
.wpforms-field-container .wpforms-field-date-time .wpforms-field-date-time-date-month {
  width: calc( 30% - 12px);
}

.wpforms-field-container .wpforms-field-date-time .wpforms-field-date-time-date-year {
  width: calc( 40% - 12px);
}

.wpforms-field-container .wpforms-field-date-time .wpforms-date-type-datepicker {
  width: clamp(50%, 100px, 100%);
}

.wpforms-field-container .wpforms-field-date-time .wpforms-date-type-datepicker + .wpforms-field-row-block {
  width: clamp(50%, 100px, 100%);
}

.wpforms-container .wpforms-datepicker-wrap {
  position: relative;
}

.wpforms-container .wpforms-datepicker-wrap .wpforms-datepicker-clear {
  position: absolute;
  background-image: url(../../../pro/images/times-solid-white.svg);
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-color: #cccccc;
  background-size: 8px;
  width: 16px;
  height: 16px;
  cursor: pointer;
  display: block;
  border-radius: 50%;
  right: 10px;
  top: 50%;
  margin-top: -8px;
  transition: all 0.3s;
}

.wpforms-container .wpforms-datepicker-wrap .wpforms-datepicker-clear:hover {
  background-color: red;
}

.wpforms-container .wpforms-datepicker-wrap .wpforms-field-small + .wpforms-datepicker-clear {
  right: calc( 75% + 10px);
}

.wpforms-container .wpforms-datepicker-wrap .wpforms-field-medium + .wpforms-datepicker-clear {
  right: calc( 40% + 10px);
}

/* Rating field
----------------------------------------------------------------------------- */
.wpforms-container .wpforms-field-rating-item {
  padding: 0 6px 0 0;
  margin: 0;
  display: inline-block;
}

.wpforms-container .wpforms-field-rating svg {
  cursor: pointer;
  -webkit-transform: perspective(1px) translateZ(0);
  transform: perspective(1px) translateZ(0);
  box-shadow: 0 0 1px transparent;
  opacity: 0.60;
}

.wpforms-container .wpforms-field-rating-item.selected svg,
.wpforms-container .wpforms-field-rating-item.hover svg {
  -webkit-transform: scale(1.3);
  transform: scale(1.3);
  opacity: 1;
}

/* Image choices
----------------------------------------------------------------------------- */
.wpforms-container ul.wpforms-image-choices label:not(.wpforms-error) {
  cursor: pointer;
  position: relative;
}

.wpforms-container ul.wpforms-image-choices label input {
  top: 50%;
}

/* Modern style */
.wpforms-container .wpforms-list-inline .wpforms-image-choices-modern li {
  margin: 5px 5px 5px 5px;
}

.wpforms-container .wpforms-image-choices-modern img {
  display: inline-block;
  margin: 0 auto;
  max-width: 100%;
}

.wpforms-container .wpforms-image-choices-modern label:not(.wpforms-error) {
  background-color: #fff;
  display: inline-block;
  margin: 0 auto;
  cursor: pointer;
  border: 1px solid #fff;
  border-radius: 3px;
  padding: 20px 20px 18px 20px;
  transition: all 0.5s;
  text-align: center;
}

.wpforms-container .wpforms-image-choices-modern label:not(.wpforms-error):hover {
  border: 1px solid #ddd;
}

.wpforms-container .wpforms-image-choices-modern .wpforms-selected label,
.wpforms-container .wpforms-image-choices-modern li:has(input:checked) label {
  box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.1);
}

.wpforms-container .wpforms-image-choices-modern .wpforms-image-choices-image:after {
  content: "\2714";
  font-size: 22px;
  line-height: 32px;
  color: #fff;
  background: green;
  opacity: 0;
  position: absolute;
  top: 50%;
  left: 50%;
  margin: -16px 0 0 -16px;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  transition: all 0.5s;
}

.wpforms-container .wpforms-image-choices-modern .wpforms-selected .wpforms-image-choices-image:after .wpforms-container .wpforms-image-choices-modern li:has(input:checked) .wpforms-image-choices-image:after {
  opacity: 1;
}

.wpforms-container .wpforms-image-choices-modern .wpforms-image-choices-image {
  display: block;
  position: relative;
}

.wpforms-container .wpforms-image-choices-modern .wpforms-selected .wpforms-image-choices-label,
.wpforms-container .wpforms-image-choices-modern li:has(input:checked) .wpforms-image-choices-label {
  font-weight: 700;
}

.wpforms-container .wpforms-image-choices-modern .wpforms-image-choices-label {
  display: block;
  margin-top: 12px;
}

/* Classic */
.wpforms-container .wpforms-list-inline .wpforms-image-choices-classic li {
  margin: 0 10px 10px 0 !important;
}

.wpforms-container .wpforms-image-choices-classic img {
  display: inline-block;
  margin: 0 auto;
  max-width: 100%;
}

.wpforms-container .wpforms-image-choices-classic label:not(.wpforms-error) {
  background-color: #fff;
  display: inline-block;
  margin: 0 auto;
  cursor: pointer;
  border: 2px solid #fff;
  padding: 10px;
  text-align: center;
}

.wpforms-container .wpforms-image-choices-classic label:not(.wpforms-error):hover {
  border-color: #ddd;
}

.wpforms-container .wpforms-image-choices-classic .wpforms-image-choices-image {
  display: block;
}

.wpforms-container .wpforms-image-choices-classic .wpforms-selected label,
.wpforms-container .wpforms-image-choices-classic li:has(input:checked) label {
  border-color: #666 !important;
}

.wpforms-container .wpforms-image-choices-classic .wpforms-image-choices-label {
  display: block;
  margin-top: 8px;
}

/* Icon choices
----------------------------------------------------------------------------- */
.wpforms-container ul.wpforms-icon-choices,
.wpforms-container ul.wpforms-icon-choices * {
  box-sizing: border-box;
}

.wpforms-container ul.wpforms-icon-choices {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 0 1px 0 1px !important;
  margin: 12px 0 -20px 0 !important;
  /* Style: Default */
  /* Style: Modern */
  /* Style: Classic */
}

.wpforms-container ul.wpforms-icon-choices + .wpforms-field-description,
.wpforms-container ul.wpforms-icon-choices + .wpforms-error {
  margin-top: 15px;
}

.wpforms-container ul.wpforms-icon-choices li {
  min-width: 120px;
  padding-right: 0 !important;
  margin: 0 0 20px 0 !important;
}

.wpforms-container ul.wpforms-icon-choices label {
  position: relative;
  display: block;
  margin: 0;
  cursor: pointer;
}

.wpforms-container ul.wpforms-icon-choices .wpforms-icon-choices-icon {
  display: block;
}

.wpforms-container ul.wpforms-icon-choices svg {
  margin: 0 auto;
  fill: var(--wpforms-icon-choices-color);
}

.wpforms-container ul.wpforms-icon-choices.wpforms-icon-choices-none svg {
  margin: 0;
}

.wpforms-container ul.wpforms-icon-choices.wpforms-icon-choices-default li, .wpforms-container ul.wpforms-icon-choices.wpforms-icon-choices-modern li {
  margin: 0 0 22px 0 !important;
}

.wpforms-container ul.wpforms-icon-choices.wpforms-icon-choices-default label {
  text-align: center;
}

.wpforms-container ul.wpforms-icon-choices.wpforms-icon-choices-default label:focus-within .wpforms-icon-choices-icon {
  box-shadow: 0 0 0 1px var(--wpforms-icon-choices-color);
}

.wpforms-container ul.wpforms-icon-choices.wpforms-icon-choices-default .wpforms-icon-choices-icon {
  position: relative;
  z-index: 1;
  margin-bottom: 10px;
  padding: 15px 20px 45px 20px;
  background-color: #ffffff;
  box-shadow: 0 0 0 1px #cccccc;
  border-radius: 6px;
}

.wpforms-container ul.wpforms-icon-choices.wpforms-icon-choices-default .wpforms-icon-choices-icon:hover {
  box-shadow: 0 0 0 1px var(--wpforms-icon-choices-color);
}

.wpforms-container ul.wpforms-icon-choices.wpforms-icon-choices-default .wpforms-icon-choices-icon svg {
  position: relative;
  z-index: 2;
}

.wpforms-container ul.wpforms-icon-choices.wpforms-icon-choices-default .wpforms-icon-choices-icon-bg {
  display: block;
  position: absolute;
  z-index: 0;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  border-radius: 6px;
  background-color: #ffffff;
}

.wpforms-container ul.wpforms-icon-choices.wpforms-icon-choices-default .wpforms-selected .wpforms-icon-choices-icon, .wpforms-container ul.wpforms-icon-choices.wpforms-icon-choices-default li:has(input:checked) .wpforms-icon-choices-icon {
  background-color: transparent;
  box-shadow: 0 0 0 1px var(--wpforms-icon-choices-color);
  box-sizing: border-box;
}

.wpforms-container ul.wpforms-icon-choices.wpforms-icon-choices-default .wpforms-selected .wpforms-icon-choices-icon-bg, .wpforms-container ul.wpforms-icon-choices.wpforms-icon-choices-default li:has(input:checked) .wpforms-icon-choices-icon-bg {
  background-color: var(--wpforms-icon-choices-color);
  opacity: .1;
}

.wpforms-container ul.wpforms-icon-choices.wpforms-icon-choices-modern li label {
  background-color: #ffffff !important;
  box-shadow: 0 0 0 1px #cccccc;
  border-radius: 6px;
  height: 100%;
  padding: 20px 20px 15px 20px;
  text-align: center;
}

.wpforms-container ul.wpforms-icon-choices.wpforms-icon-choices-modern li label:hover {
  box-shadow: 0 0 0 1px var(--wpforms-icon-choices-color);
}

.wpforms-container ul.wpforms-icon-choices.wpforms-icon-choices-modern li:focus-within label {
  box-shadow: 0 0 0 1px var(--wpforms-icon-choices-color);
}

.wpforms-container ul.wpforms-icon-choices.wpforms-icon-choices-modern li.wpforms-selected label, .wpforms-container ul.wpforms-icon-choices.wpforms-icon-choices-modern li:has(input:checked) label {
  box-shadow: 0 0 0 1px var(--wpforms-icon-choices-color), 0 2px 10px rgba(0, 0, 0, 0.15);
}

.wpforms-container ul.wpforms-icon-choices.wpforms-icon-choices-modern li .wpforms-icon-choices-icon {
  margin-bottom: 10px;
}

.wpforms-container ul.wpforms-icon-choices.wpforms-icon-choices-classic li label {
  background-color: #ffffff !important;
  height: 100%;
  padding: 20px 20px 15px 20px;
  text-align: center;
}

.wpforms-container ul.wpforms-icon-choices.wpforms-icon-choices-classic li label:hover {
  box-shadow: 0 0 0 1px #999999;
}

.wpforms-container ul.wpforms-icon-choices.wpforms-icon-choices-classic li:focus-within label {
  box-shadow: 0 0 0 1px #999999;
}

.wpforms-container ul.wpforms-icon-choices.wpforms-icon-choices-classic li.wpforms-selected label, .wpforms-container ul.wpforms-icon-choices.wpforms-icon-choices-classic li:has(input:checked) label {
  box-shadow: 0 0 0 1px var(--wpforms-icon-choices-color);
}

.wpforms-container ul.wpforms-icon-choices.wpforms-icon-choices-classic li .wpforms-icon-choices-icon {
  margin-bottom: 10px;
}

.wpforms-container .wpforms-field-radio ul.wpforms-icon-choices-default li .wpforms-icon-choices-icon:before,
.wpforms-container .wpforms-field-payment-multiple ul.wpforms-icon-choices-default li .wpforms-icon-choices-icon:before {
  content: "";
  position: absolute;
  z-index: 2;
  bottom: 15px;
  left: calc(50% - 8px);
  display: block;
  width: 16px;
  height: 16px;
  background-color: #ffffff;
  box-shadow: 0 0 0 1px #cccccc;
  border-radius: 50%;
  margin: 15px auto 0;
}

.wpforms-container .wpforms-field-radio ul.wpforms-icon-choices-default li.wpforms-selected .wpforms-icon-choices-icon:before, .wpforms-container .wpforms-field-radio ul.wpforms-icon-choices-default li:has(input:checked) .wpforms-icon-choices-icon:before,
.wpforms-container .wpforms-field-payment-multiple ul.wpforms-icon-choices-default li.wpforms-selected .wpforms-icon-choices-icon:before,
.wpforms-container .wpforms-field-payment-multiple ul.wpforms-icon-choices-default li:has(input:checked) .wpforms-icon-choices-icon:before {
  box-shadow: 0 0 0 1px var(--wpforms-icon-choices-color);
}

.wpforms-container .wpforms-field-radio ul.wpforms-icon-choices-default li.wpforms-selected .wpforms-icon-choices-icon:after, .wpforms-container .wpforms-field-radio ul.wpforms-icon-choices-default li:has(input:checked) .wpforms-icon-choices-icon:after,
.wpforms-container .wpforms-field-payment-multiple ul.wpforms-icon-choices-default li.wpforms-selected .wpforms-icon-choices-icon:after,
.wpforms-container .wpforms-field-payment-multiple ul.wpforms-icon-choices-default li:has(input:checked) .wpforms-icon-choices-icon:after {
  content: "";
  position: absolute;
  z-index: 2;
  bottom: calc(15px + 4px);
  left: calc(50% - 4px);
  display: block;
  width: 8px;
  height: 8px;
  background-color: var(--wpforms-icon-choices-color);
  border-radius: 50%;
}

.wpforms-container .wpforms-field-checkbox ul.wpforms-icon-choices-default li .wpforms-icon-choices-icon:before,
.wpforms-container .wpforms-field-payment-checkbox ul.wpforms-icon-choices-default li .wpforms-icon-choices-icon:before {
  content: "";
  position: absolute;
  z-index: 2;
  bottom: 15px;
  left: calc(50% - 8px);
  display: block;
  width: 16px;
  height: 16px;
  background-color: #ffffff;
  box-shadow: 0 0 0 1px #cccccc;
  border-radius: 3px;
  margin: 15px auto 0;
}

.wpforms-container .wpforms-field-checkbox ul.wpforms-icon-choices-default li.wpforms-selected .wpforms-icon-choices-icon:before, .wpforms-container .wpforms-field-checkbox ul.wpforms-icon-choices-default li:has(input:checked) .wpforms-icon-choices-icon:before,
.wpforms-container .wpforms-field-payment-checkbox ul.wpforms-icon-choices-default li.wpforms-selected .wpforms-icon-choices-icon:before,
.wpforms-container .wpforms-field-payment-checkbox ul.wpforms-icon-choices-default li:has(input:checked) .wpforms-icon-choices-icon:before {
  box-shadow: 0 0 0 1px var(--wpforms-icon-choices-color);
}

.wpforms-container .wpforms-field-checkbox ul.wpforms-icon-choices-default li.wpforms-selected .wpforms-icon-choices-icon:after, .wpforms-container .wpforms-field-checkbox ul.wpforms-icon-choices-default li:has(input:checked) .wpforms-icon-choices-icon:after,
.wpforms-container .wpforms-field-payment-checkbox ul.wpforms-icon-choices-default li.wpforms-selected .wpforms-icon-choices-icon:after,
.wpforms-container .wpforms-field-payment-checkbox ul.wpforms-icon-choices-default li:has(input:checked) .wpforms-icon-choices-icon:after {
  content: "";
  box-sizing: border-box;
  display: block;
  position: absolute;
  z-index: 2;
  bottom: 23px;
  left: calc(50% - 6px);
  width: 6px;
  height: 10px;
  border-style: solid;
  border-color: var(--wpforms-icon-choices-color);
  border-width: 0 2px 2px 0;
  transform-origin: bottom left;
  transform: rotate(45deg);
}

.wpforms-container .wpforms-list-2-columns ul.wpforms-icon-choices,
.wpforms-container .wpforms-list-3-columns ul.wpforms-icon-choices,
.wpforms-container .wpforms-list-inline ul.wpforms-icon-choices {
  flex-direction: row;
  flex-wrap: wrap;
}

.wpforms-container .wpforms-list-2-columns ul.wpforms-icon-choices li,
.wpforms-container .wpforms-list-3-columns ul.wpforms-icon-choices li,
.wpforms-container .wpforms-list-inline ul.wpforms-icon-choices li {
  margin-right: 20px !important;
}

.wpforms-container .wpforms-list-2-columns ul.wpforms-icon-choices.wpforms-icon-choices-default li, .wpforms-container .wpforms-list-2-columns ul.wpforms-icon-choices.wpforms-icon-choices-modern li,
.wpforms-container .wpforms-list-3-columns ul.wpforms-icon-choices.wpforms-icon-choices-default li,
.wpforms-container .wpforms-list-3-columns ul.wpforms-icon-choices.wpforms-icon-choices-modern li,
.wpforms-container .wpforms-list-inline ul.wpforms-icon-choices.wpforms-icon-choices-default li,
.wpforms-container .wpforms-list-inline ul.wpforms-icon-choices.wpforms-icon-choices-modern li {
  margin-right: 22px !important;
  margin-bottom: 22px !important;
}

.wpforms-container .wpforms-list-2-columns ul.wpforms-icon-choices li {
  width: calc( 100% / 2 - 20px / 2);
}

.wpforms-container .wpforms-list-2-columns ul.wpforms-icon-choices li:nth-child(2n) {
  margin-right: 0 !important;
}

.wpforms-container .wpforms-list-2-columns ul.wpforms-icon-choices.wpforms-icon-choices-default li, .wpforms-container .wpforms-list-2-columns ul.wpforms-icon-choices.wpforms-icon-choices-modern li {
  width: calc( 100% / 2 - 22px / 2);
}

.wpforms-container .wpforms-list-3-columns ul.wpforms-icon-choices li {
  width: calc( 100% / 3 - 20px * 2 / 3);
}

.wpforms-container .wpforms-list-3-columns ul.wpforms-icon-choices li:nth-child(3n) {
  margin-right: 0 !important;
}

.wpforms-container .wpforms-list-3-columns ul.wpforms-icon-choices.wpforms-icon-choices-default li, .wpforms-container .wpforms-list-3-columns ul.wpforms-icon-choices.wpforms-icon-choices-modern li {
  width: calc( 100% / 3 - 22px * 2 / 3);
}

.wpforms-container .wpforms-list-inline ul.wpforms-icon-choices li {
  width: auto;
  max-width: calc( 100% / 4 - 20px);
}

/* Rich Text field
----------------------------------------------------------------------------- */
.wpforms-container .wpforms-form textarea.wpforms-field-small.wp-editor-area {
  height: 100px;
}

.wpforms-container .wpforms-form textarea.wpforms-field-medium.wp-editor-area {
  height: 250px;
}

.wpforms-container .wpforms-form textarea.wpforms-field-large.wp-editor-area {
  height: 400px;
}

.wpforms-container .wpforms-form textarea.wp-editor-area:focus {
  outline: none;
}

/* Layout field
----------------------------------------------------------------------------- */
.wpforms-container .wpforms-field-layout {
  padding: 0;
}

/* Payment fields.
----------------------------------------------------------------------------- */
.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-payment-single .wpforms-single-item-price-content {
  display: flex;
  align-items: center;
  gap: 15px;
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-payment-single .wpforms-single-item-price-content .wpforms-single-item-price {
  width: calc(60% - 85px);
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-payment-single .wpforms-single-item-price-content .wpforms-single-item-price.wpforms-field-small {
  text-wrap: balance;
  width: calc(25% - 85px);
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-payment-single .wpforms-single-item-price-content .wpforms-single-item-price.wpforms-field-large {
  width: calc(100% - 85px);
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-payment-single .wpforms-single-item-price-content select.wpforms-payment-quantity {
  margin-left: 0;
  height: 30px;
  min-height: 30px;
  font-size: 14px;
  padding-top: 0;
  padding-bottom: 0;
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled select.wpforms-payment-price {
  display: inline-block;
  max-width: calc( 60% - 85px);
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled select.wpforms-payment-price.wpforms-field-small {
  max-width: calc( 25% - 85px);
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled select.wpforms-payment-price.wpforms-field-large {
  max-width: calc( 100% - 85px);
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled select.wpforms-payment-quantity {
  display: inline-block;
  margin-inline-start: 15px;
  width: 70px;
  min-width: 70px;
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-select-style-modern {
  display: flex;
  flex-wrap: wrap;
  column-gap: 15px;
  align-items: flex-start;
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-select-style-modern .wpforms-field-label {
  min-width: 100%;
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-select-style-modern .choices {
  margin-bottom: 5px;
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-select-style-modern .wpforms-field-row {
  flex-grow: 1;
  max-width: calc( 60% - 85px);
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-select-style-modern .wpforms-field-row.wpforms-field-small {
  max-width: calc( 25% - 85px);
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-select-style-modern .wpforms-field-row.wpforms-field-large {
  max-width: calc( 100% - 85px);
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-select-style-modern .wpforms-payment-quantity {
  flex-basis: 70px;
  max-width: 70px;
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-select-style-modern .wpforms-payment-quantity .choices__list--dropdown {
  min-width: 70px;
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-select-style-modern .wpforms-field-description {
  flex-basis: 100%;
  margin-top: 0;
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-select-style-modern .wpforms-error {
  flex-basis: 100%;
}

@media only screen and (max-width: 600px) {
  .wpforms-form .wpforms-payment-quantities-enabled select.wpforms-payment-price {
    width: calc( 100% - 85px) !important;
    max-width: 100% !important;
  }
  .wpforms-form .wpforms-payment-quantities-enabled .wpforms-single-item-price-content .wpforms-single-item-price {
    width: calc( 100% - 70px) !important;
  }
  .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-select-style-modern .wpforms-field-row {
    width: calc( 100% - 85px) !important;
    max-width: 100% !important;
  }
}

#wpforms-form-page-page .wpforms-form .wpforms-payment-quantities-enabled select.wpforms-payment-price.wpforms-field-medium, #wpforms-form-page-page .wpforms-form .wpforms-payment-quantities-enabled select.wpforms-payment-price.wpforms-field-small {
  max-width: calc( 100% - 85px);
}

.wpforms-container .wpforms-order-summary-container {
  display: block;
  max-width: 60%;
}

.wpforms-container .wpforms-order-summary-container * {
  word-break: break-word;
  box-sizing: border-box;
}

.wpforms-container .wpforms-order-summary-container table.wpforms-order-summary-preview {
  width: 100%;
  table-layout: fixed;
}

.wpforms-container .wpforms-order-summary-container table.wpforms-order-summary-preview tr td, .wpforms-container .wpforms-order-summary-container table.wpforms-order-summary-preview tr th {
  text-align: center;
}

.wpforms-container .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label, .wpforms-container .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label {
  text-align: left;
}

.rtl .wpforms-container .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label, .rtl .wpforms-container .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label {
  text-align: right;
}

.wpforms-container .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity, .wpforms-container .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity {
  width: 8ch;
}

.wpforms-container .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short, .wpforms-container .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short {
  display: none;
}

@media (max-width: 600px) {
  .wpforms-container .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity, .wpforms-container .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity {
    width: 4ch;
  }
  .wpforms-container .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short, .wpforms-container .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short {
    display: inline;
  }
  .wpforms-container .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-full, .wpforms-container .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-full {
    display: none;
  }
}

.wpforms-container .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price, .wpforms-container .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price {
  width: 6ch;
  text-align: right;
}

.rtl .wpforms-container .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price, .rtl .wpforms-container .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price {
  text-align: left;
}

.wpforms-container .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td {
  text-align: left;
}

.rtl .wpforms-container .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td {
  text-align: right;
}

.wpforms-container .size-large > .wpforms-order-summary-container,
.wpforms-container .wpforms-field-large > .wpforms-order-summary-container {
  max-width: 100%;
}

.wpforms-container .size-medium > .wpforms-order-summary-container,
.wpforms-container .wpforms-field-medium > .wpforms-order-summary-container {
  max-width: 60%;
}

.wpforms-container .wpforms-order-summary-container tr.wpforms-order-summary-preview-coupon-total td.wpforms-order-summary-item-price {
  color: #990000;
}

ul.wpforms-icon-choices,
ul.wpforms-icon-choices * {
  box-sizing: border-box;
}

ul.wpforms-icon-choices {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 0 1px 0 1px !important;
  margin: 12px 0 -20px 0 !important;
  /* Style: Default */
  /* Style: Modern */
  /* Style: Classic */
}

ul.wpforms-icon-choices + .wpforms-field-description,
ul.wpforms-icon-choices + .wpforms-error {
  margin-top: 15px;
}

ul.wpforms-icon-choices li {
  min-width: 120px;
  padding-right: 0 !important;
  margin: 0 0 20px 0 !important;
}

ul.wpforms-icon-choices label {
  position: relative;
  display: block;
  margin: 0;
  cursor: pointer;
}

ul.wpforms-icon-choices .wpforms-icon-choices-icon {
  display: block;
}

ul.wpforms-icon-choices svg {
  margin: 0 auto;
  fill: var(--wpforms-icon-choices-color);
}

ul.wpforms-icon-choices.wpforms-icon-choices-none svg {
  margin: 0;
}

ul.wpforms-icon-choices.wpforms-icon-choices-default li, ul.wpforms-icon-choices.wpforms-icon-choices-modern li {
  margin: 0 0 22px 0 !important;
}

ul.wpforms-icon-choices.wpforms-icon-choices-default label {
  text-align: center;
}

ul.wpforms-icon-choices.wpforms-icon-choices-default label:focus-within .wpforms-icon-choices-icon {
  box-shadow: 0 0 0 1px var(--wpforms-icon-choices-color);
}

ul.wpforms-icon-choices.wpforms-icon-choices-default .wpforms-icon-choices-icon {
  position: relative;
  z-index: 1;
  margin-bottom: 10px;
  padding: 15px 20px 45px 20px;
  background-color: #ffffff;
  box-shadow: 0 0 0 1px #cccccc;
  border-radius: 6px;
}

ul.wpforms-icon-choices.wpforms-icon-choices-default .wpforms-icon-choices-icon:hover {
  box-shadow: 0 0 0 1px var(--wpforms-icon-choices-color);
}

ul.wpforms-icon-choices.wpforms-icon-choices-default .wpforms-icon-choices-icon svg {
  position: relative;
  z-index: 2;
}

ul.wpforms-icon-choices.wpforms-icon-choices-default .wpforms-icon-choices-icon-bg {
  display: block;
  position: absolute;
  z-index: 0;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  border-radius: 6px;
  background-color: #ffffff;
}

ul.wpforms-icon-choices.wpforms-icon-choices-default .wpforms-selected .wpforms-icon-choices-icon, ul.wpforms-icon-choices.wpforms-icon-choices-default li:has(input:checked) .wpforms-icon-choices-icon {
  background-color: transparent;
  box-shadow: 0 0 0 1px var(--wpforms-icon-choices-color);
  box-sizing: border-box;
}

ul.wpforms-icon-choices.wpforms-icon-choices-default .wpforms-selected .wpforms-icon-choices-icon-bg, ul.wpforms-icon-choices.wpforms-icon-choices-default li:has(input:checked) .wpforms-icon-choices-icon-bg {
  background-color: var(--wpforms-icon-choices-color);
  opacity: .1;
}

ul.wpforms-icon-choices.wpforms-icon-choices-modern li label {
  background-color: #ffffff !important;
  box-shadow: 0 0 0 1px #cccccc;
  border-radius: 6px;
  height: 100%;
  padding: 20px 20px 15px 20px;
  text-align: center;
}

ul.wpforms-icon-choices.wpforms-icon-choices-modern li label:hover {
  box-shadow: 0 0 0 1px var(--wpforms-icon-choices-color);
}

ul.wpforms-icon-choices.wpforms-icon-choices-modern li:focus-within label {
  box-shadow: 0 0 0 1px var(--wpforms-icon-choices-color);
}

ul.wpforms-icon-choices.wpforms-icon-choices-modern li.wpforms-selected label, ul.wpforms-icon-choices.wpforms-icon-choices-modern li:has(input:checked) label {
  box-shadow: 0 0 0 1px var(--wpforms-icon-choices-color), 0 2px 10px rgba(0, 0, 0, 0.15);
}

ul.wpforms-icon-choices.wpforms-icon-choices-modern li .wpforms-icon-choices-icon {
  margin-bottom: 10px;
}

ul.wpforms-icon-choices.wpforms-icon-choices-classic li label {
  background-color: #ffffff !important;
  height: 100%;
  padding: 20px 20px 15px 20px;
  text-align: center;
}

ul.wpforms-icon-choices.wpforms-icon-choices-classic li label:hover {
  box-shadow: 0 0 0 1px #999999;
}

ul.wpforms-icon-choices.wpforms-icon-choices-classic li:focus-within label {
  box-shadow: 0 0 0 1px #999999;
}

ul.wpforms-icon-choices.wpforms-icon-choices-classic li.wpforms-selected label, ul.wpforms-icon-choices.wpforms-icon-choices-classic li:has(input:checked) label {
  box-shadow: 0 0 0 1px var(--wpforms-icon-choices-color);
}

ul.wpforms-icon-choices.wpforms-icon-choices-classic li .wpforms-icon-choices-icon {
  margin-bottom: 10px;
}

.wpforms-field-radio ul.wpforms-icon-choices-default li .wpforms-icon-choices-icon:before,
.wpforms-field-payment-multiple ul.wpforms-icon-choices-default li .wpforms-icon-choices-icon:before {
  content: "";
  position: absolute;
  z-index: 2;
  bottom: 15px;
  left: calc(50% - 8px);
  display: block;
  width: 16px;
  height: 16px;
  background-color: #ffffff;
  box-shadow: 0 0 0 1px #cccccc;
  border-radius: 50%;
  margin: 15px auto 0;
}

.wpforms-field-radio ul.wpforms-icon-choices-default li.wpforms-selected .wpforms-icon-choices-icon:before, .wpforms-field-radio ul.wpforms-icon-choices-default li:has(input:checked) .wpforms-icon-choices-icon:before,
.wpforms-field-payment-multiple ul.wpforms-icon-choices-default li.wpforms-selected .wpforms-icon-choices-icon:before,
.wpforms-field-payment-multiple ul.wpforms-icon-choices-default li:has(input:checked) .wpforms-icon-choices-icon:before {
  box-shadow: 0 0 0 1px var(--wpforms-icon-choices-color);
}

.wpforms-field-radio ul.wpforms-icon-choices-default li.wpforms-selected .wpforms-icon-choices-icon:after, .wpforms-field-radio ul.wpforms-icon-choices-default li:has(input:checked) .wpforms-icon-choices-icon:after,
.wpforms-field-payment-multiple ul.wpforms-icon-choices-default li.wpforms-selected .wpforms-icon-choices-icon:after,
.wpforms-field-payment-multiple ul.wpforms-icon-choices-default li:has(input:checked) .wpforms-icon-choices-icon:after {
  content: "";
  position: absolute;
  z-index: 2;
  bottom: calc(15px + 4px);
  left: calc(50% - 4px);
  display: block;
  width: 8px;
  height: 8px;
  background-color: var(--wpforms-icon-choices-color);
  border-radius: 50%;
}

.wpforms-field-checkbox ul.wpforms-icon-choices-default li .wpforms-icon-choices-icon:before,
.wpforms-field-payment-checkbox ul.wpforms-icon-choices-default li .wpforms-icon-choices-icon:before {
  content: "";
  position: absolute;
  z-index: 2;
  bottom: 15px;
  left: calc(50% - 8px);
  display: block;
  width: 16px;
  height: 16px;
  background-color: #ffffff;
  box-shadow: 0 0 0 1px #cccccc;
  border-radius: 3px;
  margin: 15px auto 0;
}

.wpforms-field-checkbox ul.wpforms-icon-choices-default li.wpforms-selected .wpforms-icon-choices-icon:before, .wpforms-field-checkbox ul.wpforms-icon-choices-default li:has(input:checked) .wpforms-icon-choices-icon:before,
.wpforms-field-payment-checkbox ul.wpforms-icon-choices-default li.wpforms-selected .wpforms-icon-choices-icon:before,
.wpforms-field-payment-checkbox ul.wpforms-icon-choices-default li:has(input:checked) .wpforms-icon-choices-icon:before {
  box-shadow: 0 0 0 1px var(--wpforms-icon-choices-color);
}

.wpforms-field-checkbox ul.wpforms-icon-choices-default li.wpforms-selected .wpforms-icon-choices-icon:after, .wpforms-field-checkbox ul.wpforms-icon-choices-default li:has(input:checked) .wpforms-icon-choices-icon:after,
.wpforms-field-payment-checkbox ul.wpforms-icon-choices-default li.wpforms-selected .wpforms-icon-choices-icon:after,
.wpforms-field-payment-checkbox ul.wpforms-icon-choices-default li:has(input:checked) .wpforms-icon-choices-icon:after {
  content: "";
  box-sizing: border-box;
  display: block;
  position: absolute;
  z-index: 2;
  bottom: 23px;
  left: calc(50% - 6px);
  width: 6px;
  height: 10px;
  border-style: solid;
  border-color: var(--wpforms-icon-choices-color);
  border-width: 0 2px 2px 0;
  transform-origin: bottom left;
  transform: rotate(45deg);
}

.wpforms-list-2-columns ul.wpforms-icon-choices,
.wpforms-list-3-columns ul.wpforms-icon-choices,
.wpforms-list-inline ul.wpforms-icon-choices {
  flex-direction: row;
  flex-wrap: wrap;
}

.wpforms-list-2-columns ul.wpforms-icon-choices li,
.wpforms-list-3-columns ul.wpforms-icon-choices li,
.wpforms-list-inline ul.wpforms-icon-choices li {
  margin-right: 20px !important;
}

.wpforms-list-2-columns ul.wpforms-icon-choices.wpforms-icon-choices-default li, .wpforms-list-2-columns ul.wpforms-icon-choices.wpforms-icon-choices-modern li,
.wpforms-list-3-columns ul.wpforms-icon-choices.wpforms-icon-choices-default li,
.wpforms-list-3-columns ul.wpforms-icon-choices.wpforms-icon-choices-modern li,
.wpforms-list-inline ul.wpforms-icon-choices.wpforms-icon-choices-default li,
.wpforms-list-inline ul.wpforms-icon-choices.wpforms-icon-choices-modern li {
  margin-right: 22px !important;
  margin-bottom: 22px !important;
}

.wpforms-list-2-columns ul.wpforms-icon-choices li {
  width: calc( 100% / 2 - 20px / 2);
}

.wpforms-list-2-columns ul.wpforms-icon-choices li:nth-child(2n) {
  margin-right: 0 !important;
}

.wpforms-list-2-columns ul.wpforms-icon-choices.wpforms-icon-choices-default li, .wpforms-list-2-columns ul.wpforms-icon-choices.wpforms-icon-choices-modern li {
  width: calc( 100% / 2 - 22px / 2);
}

.wpforms-list-3-columns ul.wpforms-icon-choices li {
  width: calc( 100% / 3 - 20px * 2 / 3);
}

.wpforms-list-3-columns ul.wpforms-icon-choices li:nth-child(3n) {
  margin-right: 0 !important;
}

.wpforms-list-3-columns ul.wpforms-icon-choices.wpforms-icon-choices-default li, .wpforms-list-3-columns ul.wpforms-icon-choices.wpforms-icon-choices-modern li {
  width: calc( 100% / 3 - 22px * 2 / 3);
}

.wpforms-list-inline ul.wpforms-icon-choices li {
  width: auto;
  max-width: calc( 100% / 4 - 20px);
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-payment-single .wpforms-single-item-price-content {
  display: flex;
  align-items: center;
  gap: 15px;
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-payment-single .wpforms-single-item-price-content .wpforms-single-item-price {
  width: calc(60% - 85px);
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-payment-single .wpforms-single-item-price-content .wpforms-single-item-price.wpforms-field-small {
  text-wrap: balance;
  width: calc(25% - 85px);
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-payment-single .wpforms-single-item-price-content .wpforms-single-item-price.wpforms-field-large {
  width: calc(100% - 85px);
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-payment-single .wpforms-single-item-price-content select.wpforms-payment-quantity {
  margin-left: 0;
  height: 30px;
  min-height: 30px;
  font-size: 14px;
  padding-top: 0;
  padding-bottom: 0;
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled select.wpforms-payment-price {
  display: inline-block;
  max-width: calc( 60% - 85px);
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled select.wpforms-payment-price.wpforms-field-small {
  max-width: calc( 25% - 85px);
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled select.wpforms-payment-price.wpforms-field-large {
  max-width: calc( 100% - 85px);
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled select.wpforms-payment-quantity {
  display: inline-block;
  margin-inline-start: 15px;
  width: 70px;
  min-width: 70px;
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-select-style-modern {
  display: flex;
  flex-wrap: wrap;
  column-gap: 15px;
  align-items: flex-start;
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-select-style-modern .wpforms-field-label {
  min-width: 100%;
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-select-style-modern .choices {
  margin-bottom: 5px;
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-select-style-modern .wpforms-field-row {
  flex-grow: 1;
  max-width: calc( 60% - 85px);
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-select-style-modern .wpforms-field-row.wpforms-field-small {
  max-width: calc( 25% - 85px);
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-select-style-modern .wpforms-field-row.wpforms-field-large {
  max-width: calc( 100% - 85px);
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-select-style-modern .wpforms-payment-quantity {
  flex-basis: 70px;
  max-width: 70px;
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-select-style-modern .wpforms-payment-quantity .choices__list--dropdown {
  min-width: 70px;
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-select-style-modern .wpforms-field-description {
  flex-basis: 100%;
  margin-top: 0;
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-select-style-modern .wpforms-error {
  flex-basis: 100%;
}

@media only screen and (max-width: 600px) {
  .wpforms-form .wpforms-payment-quantities-enabled select.wpforms-payment-price {
    width: calc( 100% - 85px) !important;
    max-width: 100% !important;
  }
  .wpforms-form .wpforms-payment-quantities-enabled .wpforms-single-item-price-content .wpforms-single-item-price {
    width: calc( 100% - 70px) !important;
  }
  .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-select-style-modern .wpforms-field-row {
    width: calc( 100% - 85px) !important;
    max-width: 100% !important;
  }
}

#wpforms-form-page-page .wpforms-form .wpforms-payment-quantities-enabled select.wpforms-payment-price.wpforms-field-medium, #wpforms-form-page-page .wpforms-form .wpforms-payment-quantities-enabled select.wpforms-payment-price.wpforms-field-small {
  max-width: calc( 100% - 85px);
}

@media only screen and (max-width: 600px) {
  .wpforms-container .wpforms-field:not(.wpforms-field-phone):not(.wpforms-field-select-style-modern):not(.wpforms-field-radio):not(.wpforms-field-checkbox):not(.wpforms-field-layout):not(.wpforms-field-repeater) {
    overflow-x: hidden;
  }
  .wpforms-container .wpforms-field {
    padding-right: 1px;
    padding-left: 1px;
  }
  .wpforms-container .wpforms-form .wpforms-field > * {
    max-width: 100%;
  }
  .wpforms-container .wpforms-mobile-full {
    width: 100%;
    margin-left: 0;
    float: none;
  }
  .wpforms-container .wpforms-checkbox-2-columns ul li,
  .wpforms-container .wpforms-multiplechoice-2-columns ul li,
  .wpforms-container .wpforms-list-2-columns ul li,
  .wpforms-container .wpforms-checkbox-3-columns ul li,
  .wpforms-container .wpforms-multiplechoice-3-columns ul li,
  .wpforms-container .wpforms-list-3-columns ul li {
    float: none;
    width: 100%;
  }
  .wpforms-container .wpforms-page-indicator.circles .wpforms-page-indicator-page {
    float: none;
    display: block;
    margin: 0 0 10px 0;
  }
  .wpforms-container .wpforms-page-indicator.circles .wpforms-page-indicator-page-number {
    width: 30px;
    height: 30px;
    line-height: 30px;
  }
  .wpforms-container .wpforms-page-indicator.connector .wpforms-page-indicator-page {
    width: 100% !important;
    padding: 5px 10px;
  }
  .wpforms-container .wpforms-page-indicator.connector .wpforms-page-indicator-page-number {
    display: none;
  }
  .wpforms-container .wpforms-page-indicator.connector .wpforms-page-indicator-page.active {
    font-weight: 700;
  }
  .wpforms-container.inline-fields .wpforms-field-container,
  .wpforms-container.inline-fields .wpforms-field {
    display: block;
    width: 100%;
  }
  .wpforms-container.inline-fields .wpforms-submit-container {
    width: 100%;
  }
}
