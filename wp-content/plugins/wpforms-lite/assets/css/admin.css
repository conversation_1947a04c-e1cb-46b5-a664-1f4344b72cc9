@charset "UTF-8";
.tooltipster-base.tooltipster-sidetip .tooltipster-content {
  font-size: 14px;
  padding: 8px 16px;
}

.tooltipster-base.tooltipster-sidetip .tooltipster-arrow-border {
  display: none;
}

.tooltipster-base.tooltipster-sidetip .tooltipster-arrow-background {
  border-width: 6px;
  border-color: transparent;
}

.tooltipster-base.tooltipster-sidetip .tooltipster-box {
  background: rgba(34, 34, 34, 0.95);
  border: none;
}

.tooltipster-base.tooltipster-sidetip.tooltipster-top .tooltipster-arrow-background, .tooltipster-base.tooltipster-sidetip.tooltipster-bottom .tooltipster-arrow-background {
  top: 0;
}

.tooltipster-base.tooltipster-sidetip.tooltipster-top .tooltipster-content, .tooltipster-base.tooltipster-sidetip.tooltipster-bottom .tooltipster-content {
  text-align: center;
}

.tooltipster-base.tooltipster-sidetip.tooltipster-top .tooltipster-arrow, .tooltipster-base.tooltipster-sidetip.tooltipster-bottom .tooltipster-arrow {
  height: 6px;
  margin-left: -6px;
  width: 12px;
}

.tooltipster-base.tooltipster-sidetip.tooltipster-left .tooltipster-arrow-background, .tooltipster-base.tooltipster-sidetip.tooltipster-right .tooltipster-arrow-background {
  left: 0;
}

.tooltipster-base.tooltipster-sidetip.tooltipster-left .tooltipster-arrow, .tooltipster-base.tooltipster-sidetip.tooltipster-right .tooltipster-arrow {
  height: 12px;
  margin-top: -6px;
  width: 6px;
}

.tooltipster-base.tooltipster-sidetip.tooltipster-top .tooltipster-box {
  margin-bottom: 6px;
}

.tooltipster-base.tooltipster-sidetip.tooltipster-top .tooltipster-arrow-background {
  border-top-color: rgba(34, 34, 34, 0.95);
}

.tooltipster-base.tooltipster-sidetip.tooltipster-bottom .tooltipster-box {
  margin-top: 6px;
}

.tooltipster-base.tooltipster-sidetip.tooltipster-bottom .tooltipster-arrow-background {
  border-bottom-color: rgba(34, 34, 34, 0.95);
}

.tooltipster-base.tooltipster-sidetip.tooltipster-bottom .tooltipster-arrow-uncropped {
  top: -6px;
}

.tooltipster-base.tooltipster-sidetip.tooltipster-right .tooltipster-box {
  margin-left: 6px;
}

.tooltipster-base.tooltipster-sidetip.tooltipster-right .tooltipster-arrow-background {
  border-right-color: rgba(34, 34, 34, 0.95);
}

.tooltipster-base.tooltipster-sidetip.tooltipster-right .tooltipster-arrow-uncropped {
  left: -6px;
}

.tooltipster-base.tooltipster-sidetip.tooltipster-left .tooltipster-box {
  margin-right: 6px;
}

.tooltipster-base.tooltipster-sidetip.tooltipster-left .tooltipster-arrow-background {
  border-left-color: rgba(34, 34, 34, 0.95);
}

.choices {
  position: relative;
  margin-bottom: 24px;
}

.choices ::-webkit-input-placeholder {
  color: #999999;
}

.choices ::-moz-placeholder {
  color: #999999;
  opacity: 1;
}

.choices ::placeholder {
  color: #999999;
}

.choices:focus {
  outline: none;
}

.choices:last-child {
  margin-bottom: 0;
}

.choices.is-disabled .choices__inner,
.choices.is-disabled .choices__input {
  background-color: #bbbbbb;
  cursor: not-allowed;
  user-select: none;
}

.choices [hidden] {
  display: none !important;
}

.choices * {
  box-sizing: border-box;
}

.choices.is-open .choices__inner {
  border-radius: 4px 4px 0 0;
}

.choices.is-open.is-flipped .choices__inner {
  border-radius: 0 0 4px 4px;
}

.choices[data-type*="select-one"] {
  cursor: pointer;
}

.choices[data-type*="select-one"] .choices__inner {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  padding-top: 0 !important;
}

.choices[data-type*="select-one"] input.choices__input {
  display: block;
  width: calc(100% - 20px) !important;
  margin: 10px !important;
  padding: 7px 12px !important;
  box-sizing: border-box !important;
  border: 1px solid #8c8f94 !important;
  border-radius: 4px !important;
  background-color: #fff;
}

.choices[data-type*="select-one"] input.choices__input:focus {
  border: 1px solid #056aab !important;
  box-shadow: 0 0 0 1px #056aab !important;
  outline: none !important;
}

.choices[data-type*="select-one"] .choices__button {
  background-image: url("../images/cross-inverse.svg");
  padding: 0;
  background-size: 8px;
  position: absolute;
  top: 50%;
  right: 0;
  margin-top: -10px;
  margin-right: 25px;
  height: 20px;
  width: 20px;
  border-radius: 10em;
  opacity: .5;
}

.choices[data-type*="select-one"] .choices__button:hover, .choices[data-type*="select-one"] .choices__button:focus {
  opacity: 1;
}

.choices[data-type*="select-one"] .choices__button:focus {
  box-shadow: 0 0 0 2px #036aab;
}

.choices[data-type*="select-one"] .choices__item[data-value=''] .choices__button {
  display: none;
}

.choices[data-type*="select-one"]:after {
  content: "";
  height: 0;
  width: 0;
  border-style: solid;
  border-color: currentColor transparent transparent transparent;
  border-width: 5px;
  position: absolute;
  inset-inline-end: 11.5px;
  top: 50%;
  margin-top: -2.5px;
  pointer-events: none;
}

.choices[data-type*="select-one"].is-open:after {
  border-color: transparent transparent currentColor transparent;
  margin-top: -7.5px;
}

.choices[data-type*="select-one"][dir="rtl"]:after {
  left: 11.5px;
  right: auto;
}

.choices[data-type*="select-one"][dir="rtl"] .choices__button {
  right: auto;
  left: 0;
  margin-left: 25px;
  margin-right: 0;
}

.choices[data-type*="select-multiple"] .choices__inner {
  padding-right: 24px;
}

.choices[data-type*="select-multiple"] .choices__inner .choices__input {
  padding: 0 4px !important;
  max-width: 100%;
  background-color: transparent;
  line-height: 22px;
}

.choices[data-type*="select-multiple"]:after {
  content: "";
  height: 0;
  width: 0;
  border-style: solid;
  border-color: currentColor transparent transparent transparent;
  border-width: 5px;
  position: absolute;
  inset-inline-end: 11.5px;
  top: 50%;
  margin-top: -1.5px;
  pointer-events: none;
}

.choices[data-type*="select-multiple"].is-open:after {
  border-color: transparent transparent currentColor transparent;
  margin-top: -7.5px;
}

.choices[data-type*="select-multiple"] .choices__inner,
.choices[data-type*="text"] .choices__inner {
  cursor: text;
}

.choices[data-type*="select-multiple"] .choices__button,
.choices[data-type*="text"] .choices__button {
  position: absolute;
  display: inline-block;
  vertical-align: baseline;
  margin-top: 0;
  margin-bottom: 0;
  margin-inline-start: 5px;
  padding: 0;
  background-color: transparent;
  background-image: url("../images/cross.svg");
  background-size: 12px;
  background-position: center center;
  background-repeat: no-repeat;
  width: 12px;
  height: 12px;
  line-height: 1;
  opacity: .75;
  border-radius: 0;
  inset-inline-end: 4px;
}

.choices[data-type*="select-multiple"] .choices__button:hover, .choices[data-type*="select-multiple"] .choices__button:focus,
.choices[data-type*="text"] .choices__button:hover,
.choices[data-type*="text"] .choices__button:focus {
  opacity: 1;
}

.choices__inner {
  width: 100%;
  background-color: #ffffff;
  padding: 4px 6px 0;
  border: 1px solid #8c8f94;
  overflow: hidden;
  border-radius: 4px;
}

.choices__list {
  margin: 0;
  padding-left: 0;
  list-style: none;
}

.choices__list--single {
  display: inline-block;
  vertical-align: baseline;
  width: 100%;
  padding: 0 16px 0 4px;
  font-size: 0.875em;
}

.choices__list--single .choices__item {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-right: 20px;
  white-space: nowrap;
  color: #2c3338;
}

.choices__list--single .choices__item[data-value=''] {
  padding-right: 0;
}

.choices__list--multiple {
  display: inline;
  height: auto;
  overflow: auto;
}

.choices__list--multiple .choices__item {
  display: inline-block;
  vertical-align: middle;
  position: relative;
  align-items: center;
  border-radius: 2px;
  padding-block: 4px;
  padding-inline: 7px 20px;
  font-size: .75em;
  line-height: 1;
  font-weight: 400;
  margin: 0 6px 4px 0;
  background-color: #036aab;
  border: 1px solid #036aab;
  color: #ffffff;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: calc(100% - 10px);
}

.choices__list--multiple .choices__item.is-highlighted {
  background-color: #036aab;
}

.is-disabled .choices__list--multiple .choices__item {
  background-color: #bbbbbb;
  border: 1px solid #bbbbbb;
}

.choices__list--dropdown {
  display: none;
  z-index: 101;
  position: absolute;
  width: 100%;
  background-color: #ffffff;
  border: 1px solid #8c8f94;
  top: 100%;
  margin-top: -1px;
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
  overflow: hidden;
  overflow-wrap: break-word;
}

.choices__list--dropdown.is-active {
  display: block;
}

.choices__list--dropdown .choices__list {
  position: relative;
  max-height: 300px;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
  will-change: scroll-position;
}

.choices__list--dropdown .choices__item {
  position: relative;
  vertical-align: top;
  padding: 10px;
  font-size: .875em;
}

.choices__list--dropdown .choices__item--selectable.is-highlighted {
  background-color: #f6f6f6;
}

.choices__list--dropdown .choices__item--selectable.is-highlighted:after {
  opacity: .5;
}

.choices__list--dropdown .choices__placeholder {
  display: none;
}

.is-flipped .choices__list--dropdown {
  top: auto;
  bottom: 100%;
  margin-top: 0;
  margin-bottom: -1px;
  border-radius: 4px 4px 0 0;
}

.choices__item {
  cursor: default;
}

.choices__item--selectable {
  cursor: pointer;
}

.choices__item--disabled {
  cursor: not-allowed;
  user-select: none;
  opacity: .5;
}

.choices__heading {
  font-weight: 600;
  font-size: .75em;
  text-transform: uppercase;
  padding: 10px;
  border-top: 1px solid #b4b6b9;
  border-bottom: 1px solid #b4b6b9;
  color: #a6a6a6;
}

.choices__group[data-value="hidden"] > .choices__heading {
  display: none;
}

.choices__button {
  text-indent: -9999px;
  -webkit-appearance: none;
  appearance: none;
  border: 0;
  background-color: transparent;
  background-repeat: no-repeat;
  background-position: center;
  cursor: pointer;
}

.choices__button:focus {
  outline: none;
}

.choices__input {
  display: inline-block;
  background-color: transparent;
  margin: 0 0 2px 0 !important;
  border: 0 !important;
  border-radius: 0 !important;
  min-height: 20px !important;
  padding: 2px 4px !important;
  height: auto !important;
  min-width: 1ch;
  width: 1ch;
  vertical-align: middle;
}

.choices__input::-webkit-search-cancel-button {
  display: none;
}

.choices__input--hidden {
  clip: rect(1px, 1px, 1px, 1px) !important;
  clip-path: inset(50%) !important;
  height: 1px !important;
  margin: -1px !important;
  overflow: hidden !important;
  padding: 0 !important;
  position: absolute !important;
  width: 1px !important;
  min-width: auto !important;
  word-wrap: normal !important;
}

.choices .choices__inner input.choices__input:focus {
  outline: none !important;
  box-shadow: none !important;
  border: none !important;
}

.choices__placeholder {
  opacity: .5;
}

#wpforms-admin-form-embed-wizard .choices.is-open.is-flipped .choices__inner {
  border-radius: 4px 4px 0 0;
}

#wpforms-admin-form-embed-wizard .is-flipped .choices__list--dropdown {
  border-radius: inherit;
}

#wpforms-admin-form-embed-wizard .choices[data-type*="select-one"]:after {
  border: none;
  background: #ffffff url(data:image/svg+xml;charset=US-ASCII,%3Csvg%20width%3D%2220%22%20height%3D%2220%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M5%206l5%205%205-5%202%201-7%207-7-7%202-1z%22%20fill%3D%22%23777%22%2F%3E%3C%2Fsvg%3E) no-repeat center;
  background-size: 16px 16px;
  cursor: pointer;
  width: 16px;
  height: 16px;
  top: 13px;
  right: 8px;
  margin-top: 0;
}

#wpforms-admin-form-embed-wizard .choices[data-type*="select-one"].is-flipped:after {
  transform: rotate(180deg);
}

body.rtl .choices[data-type*="select-multiple"] .choices__inner {
  padding-right: 4px;
  padding-left: 24px;
}

body.rtl .choices__list--single {
  padding-right: 4px;
  padding-left: 16px;
}

body.rtl .choices__list--multiple .choices__item {
  margin-right: 0;
  margin-left: 3.75px;
}

body.rtl .choices__list--dropdown .choices__item {
  text-align: right;
}

body.rtl .choices__input {
  padding-right: 2px !important;
  padding-left: 0 !important;
}

body.rtl .choices[data-type*="select-multiple"] .choices__button, body.rtl .choices[data-type*="text"] .choices__button {
  margin-inline-end: 0;
  border-left: none;
}

@media (min-width: 640px) {
  body.rtl .choices__list--dropdown .choices__item--selectable {
    text-align: right;
    padding-left: 100px;
    padding-right: 10px;
  }
  body.rtl .choices__list--dropdown .choices__item--selectable:after {
    right: auto;
    left: 10px;
  }
}

.wpforms-admin-page .choices:after {
  content: "\f347";
  position: absolute;
  top: calc( 50% - 6px);
  inset-inline-end: 6px;
  font-family: dashicons, sans-serif;
  color: #50575e;
  border: none;
  width: 16px;
  height: 16px;
  line-height: 1;
  z-index: 2;
}

.wpforms-admin-page .choices.is-open:after {
  margin-top: -1px;
  transform: rotate(180deg);
}

.wpforms-admin-page .choices[data-type*="select-multiple"] .choices__inner {
  cursor: pointer;
}

.wpforms-admin-page .is-focused .choices__inner,
.wpforms-admin-page .is-open .choices__inner {
  border-color: #056aab;
  box-shadow: 0 0 0 1px #056aab;
  outline: none;
}

.wpforms-admin-page .is-flipped.is-open .choices__inner {
  border-color: #056aab;
  box-shadow: 0 0 0 1px #056aab;
  outline: none;
}

.wpforms-admin-page .is-open .choices__list--dropdown {
  border-color: #056aab;
  border-top-color: #72757b;
  border-bottom: 0;
  box-shadow: 0 1px 0 1px #056aab;
}

.wpforms-admin-page .is-open.is-flipped .choices__list--dropdown {
  border-top: 0;
  border-bottom: 1px solid #72757b;
  box-shadow: 0 -1px 0 1px #056aab;
}

.choices__inner {
  min-height: 36px;
  padding-top: 6px;
  line-height: 1;
}

div.wpforms-container.wpforms-edit-entry-container .wpforms-form .choices .choices__inner .choices__list--multiple .choices__item {
  line-height: 1;
}

.wpforms-admin-page:not(.wpforms_page_wpforms-entries) .choices[data-type*="select-multiple"][aria-expanded="false"] .choices__inner {
  max-height: 36px;
}

.wpforms-admin-page:not(.wpforms_page_wpforms-entries) .choices[data-type*="select-multiple"][aria-expanded="false"] .choices__inner .choices__list {
  overflow: hidden;
  display: block;
  max-height: 24px;
}

.wpforms-admin-page:not(.wpforms_page_wpforms-entries) .choices[data-type*="select-multiple"][aria-expanded="false"].choices__show-more:before {
  position: absolute;
  content: '\f11c';
  font-family: dashicons, sans-serif;
  top: 7px;
  height: 22px;
  line-height: 22px;
  inset-inline-end: 28px;
  text-align: center;
  font-size: 14px;
  color: #a7aaad;
  box-sizing: border-box;
  pointer-events: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.wpforms-admin-page:not(.wpforms_page_wpforms-entries) .choices[data-type*="select-multiple"] .choices__inner {
  padding-inline-end: 40px;
}

.wpforms-admin-page#wpforms-builder .choices[data-type*="select-multiple"][aria-expanded="false"] .choices__inner {
  max-height: 32px;
}

.wpforms-admin-page#wpforms-builder .choices[data-type*="select-multiple"][aria-expanded="false"].choices__show-more:before {
  top: 5px;
}

.wpforms-admin-page .wpforms-btn {
  border: 1px;
  border-style: solid;
  border-radius: 4px;
  cursor: pointer;
  display: inline-block;
  margin: 0;
  text-decoration: none;
  text-align: center;
  vertical-align: middle;
  white-space: nowrap;
  box-shadow: none;
  transition-property: all;
  transition-duration: 0.05s;
  transition-timing-function: ease-in-out;
}

.wpforms-admin-page .wpforms-btn.inactive {
  cursor: no-drop;
  pointer-events: none;
  box-shadow: none;
  opacity: 0.5;
}

.wpforms-admin-page .wpforms-btn-block {
  display: block;
  width: 100%;
}

.wpforms-admin-page .wpforms-btn-sm {
  font-size: 13px;
  font-weight: 500;
  padding: 4px 10px;
  min-height: 30px;
}

.wpforms-admin-page .wpforms-btn-md {
  font-size: 14px;
  font-weight: 600;
  padding: 7px 15px;
  min-height: 36px;
}

.wpforms-admin-page .wpforms-btn-lg {
  font-size: 16px;
  font-weight: 600;
  padding: 16px 28px;
}

.wpforms-admin-page .wpforms-btn-orange {
  background-color: #e27730;
  border-color: #e27730;
  color: #ffffff;
}

.wpforms-admin-page .wpforms-btn-orange:hover, .wpforms-admin-page .wpforms-btn-orange:active, .wpforms-admin-page .wpforms-btn-orange:focus {
  background-color: #cd6622;
  border-color: #cd6622;
  color: #ffffff;
}

.wpforms-admin-page .wpforms-btn-orange:focus {
  box-shadow: 0 0 0 2px #cd6622;
  border-color: #ffffff;
  outline: 0;
}

.wpforms-admin-page .wpforms-btn-red {
  background-color: #d63638;
  border-color: #d63638;
  color: #fff;
}

.wpforms-admin-page .wpforms-btn-red:hover, .wpforms-admin-page .wpforms-btn-red:focus {
  background-color: #b32d2e;
  border-color: #b32d2e;
  color: #fff;
}

.wpforms-admin-page .wpforms-btn-red:focus {
  box-shadow: 0 0 0 1px #fff, 0 0 0 3px #b32d2e;
  outline: 0;
}

.wpforms-admin-page .wpforms-btn-blue {
  background-color: #056aab;
  border-color: #056aab;
  color: #ffffff;
}

.wpforms-admin-page .wpforms-btn-blue:hover, .wpforms-admin-page .wpforms-btn-blue:focus {
  background-color: #04558a;
  border-color: #04558a;
  color: #ffffff;
}

.wpforms-admin-page .wpforms-btn-blue:focus {
  box-shadow: 0 0 0 2px #04558a;
  border-color: #ffffff;
  outline: 0;
}

.wpforms-admin-page .wpforms-btn-grey {
  background-color: #eee;
  border-color: #ccc;
  color: #666;
}

.wpforms-admin-page .wpforms-btn-grey:hover {
  background-color: #d7d7d7;
  border-color: #ccc;
  color: #444;
}

.wpforms-admin-page .wpforms-btn-bordered {
  background-color: transparent;
  border: 1px solid #8c8f94;
  color: #50575e;
}

.wpforms-admin-page .wpforms-btn-bordered:hover, .wpforms-admin-page .wpforms-btn-bordered:focus {
  background-color: #f0f0f1;
  border-color: #50575e;
  color: #2c3338;
}

.wpforms-admin-page .wpforms-btn-light-grey {
  background-color: #f6f7f7;
  color: #50575e;
  border-color: #8c8f94;
}

.wpforms-admin-page .wpforms-btn-light-grey:hover, .wpforms-admin-page .wpforms-btn-light-grey:focus {
  color: #2c3338;
  border-color: #50575e;
}

.wpforms-admin-page .wpforms-btn-light-grey:focus {
  background-color: #f0f0f1;
  box-shadow: 0 0 0 1px #50575e;
  outline: 0;
}

.wpforms-admin-page .wpforms-btn-trans-green {
  background-color: transparent;
  border: none;
  color: #2a9b39;
}

.wpforms-admin-page .wpforms-btn-trans-green:hover {
  background-color: #2a9b39;
  color: #fff;
}

.wpforms-admin-page .wpforms-btn-trans-green .underline {
  position: relative;
}

.wpforms-admin-page .wpforms-btn-trans-green .underline:after {
  content: " ";
  border-bottom: 1px dashed #2a9b39;
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 100%;
}

.wpforms-admin-page .wpforms-btn-trans-green .dashicons {
  height: 18px;
}

.wpforms-admin-page .wpforms-btn-green {
  background-color: #008a20;
  color: #ffffff;
  border-color: #008a20;
}

.wpforms-admin-page .wpforms-btn-green:hover, .wpforms-admin-page .wpforms-btn-green:focus {
  background-color: #00a32a;
  color: #ffffff;
}

body div.jconfirm *,
body div.jconfirm *::before,
body div.jconfirm *::after {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  justify-items: center;
  animation: none;
  background: #ffffff;
  border-radius: 6px;
  border-top-style: solid;
  border-top-width: 4px;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
  padding-top: 34px;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-title-c,
body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-content-pane,
body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-buttons {
  grid-column: 1 / -1;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box.jconfirm-type-default {
  border-top-width: 0;
  padding-top: 25px;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box.jconfirm-type-default .jconfirm-title-c {
  margin-bottom: 20px;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box.jconfirm-type-default .jconfirm-title-c .jconfirm-icon-c {
  font-size: 44px;
  margin-bottom: -6px;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box.jconfirm-type-default button.btn-confirm {
  background-color: #e27730;
  border-color: #e27730;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box.jconfirm-type-default button.btn-confirm:hover {
  background-color: #cd6622;
  border-color: #cd6622;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box.jconfirm-type-red {
  border-top-color: #d63638 !important;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box.jconfirm-type-red .jconfirm-title-c .jconfirm-icon-c {
  color: #d63638 !important;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box.jconfirm-type-red button.btn-confirm {
  background-color: #d63638;
  border-color: #d63638;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box.jconfirm-type-red button.btn-confirm:hover {
  background-color: #b32d2e;
  border-color: #b32d2e;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box.jconfirm-type-orange {
  border-top-color: #e27730 !important;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box.jconfirm-type-orange .jconfirm-title-c .jconfirm-icon-c {
  color: #e27730 !important;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box.jconfirm-type-orange button.btn-confirm {
  background-color: #e27730;
  border-color: #e27730;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box.jconfirm-type-orange button.btn-confirm:hover {
  background-color: #cd6622;
  border-color: #cd6622;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box.jconfirm-type-yellow {
  border-top-color: #ffb900 !important;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box.jconfirm-type-yellow .jconfirm-title-c .jconfirm-icon-c {
  color: #ffb900 !important;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box.jconfirm-type-yellow button.btn-confirm {
  background-color: #ffb900;
  border-color: #ffb900;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box.jconfirm-type-yellow button.btn-confirm:hover {
  background-color: #ffaa00;
  border-color: #ffaa00;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box.jconfirm-type-blue {
  border-top-color: #0399ed !important;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box.jconfirm-type-blue .jconfirm-title-c .jconfirm-icon-c {
  color: #0399ed !important;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box.jconfirm-type-blue button.btn-confirm {
  background-color: #0399ed;
  border-color: #0399ed;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box.jconfirm-type-blue button.btn-confirm:hover {
  background-color: #036aab;
  border-color: #036aab;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box.jconfirm-type-green {
  border-top-color: #00a32a !important;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box.jconfirm-type-green .jconfirm-title-c .jconfirm-icon-c {
  color: #00a32a !important;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box.jconfirm-type-green button.btn-confirm {
  background-color: #00a32a;
  border-color: #00a32a;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box.jconfirm-type-green button.btn-confirm:hover {
  background-color: #008a20;
  border-color: #008a20;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box.jconfirm-type-purple {
  border-top-color: #7a30e2 !important;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box.jconfirm-type-purple .jconfirm-title-c .jconfirm-icon-c {
  color: #7a30e2 !important;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box.jconfirm-type-purple button.btn-confirm {
  background-color: #7a30e2;
  border-color: #7a30e2;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box.jconfirm-type-purple button.btn-confirm:hover {
  background-color: #5c24a9;
  border-color: #5c24a9;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-closeIcon {
  color: transparent;
  font-family: FontAwesome;
  height: 14px;
  opacity: 1;
  right: 10px;
  top: 10px;
  width: 14px;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-closeIcon:after {
  color: #bbbbbb;
  content: "\f00d";
  font-size: 16px;
  left: 0;
  position: absolute;
  top: 0;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-closeIcon:hover:after {
  color: #777777 !important;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-title-c {
  margin: 0 0 20px 0;
  padding: 0;
  font-weight: 600;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-title-c .jconfirm-icon-c {
  font-size: 47px;
  margin: 0;
  -ms-transform: none !important;
  transform: none !important;
  -webkit-transition: none !important;
  transition: none !important;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-title-c .jconfirm-icon-c + .jconfirm-title {
  margin-top: 20px;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-title-c .jconfirm-title {
  color: #444444;
  display: block;
  line-height: 30px;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-content-pane {
  display: block;
  margin-bottom: 20px;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-content-pane .jconfirm-content {
  color: #444444;
  font-size: 16px;
  line-height: 24px;
  margin-bottom: 0;
  overflow: inherit;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-content-pane .jconfirm-content.lite-upgrade p {
  color: #777777;
  font-size: 18px;
  padding: 0 20px;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-content-pane .jconfirm-content p {
  font-size: inherit;
  line-height: inherit;
  margin: 0 0 16px;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-content-pane .jconfirm-content p:last-of-type {
  margin: 0;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-content-pane .jconfirm-content p.large {
  font-size: 18px;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-content-pane .jconfirm-content p.small {
  font-size: 14px;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-content-pane .jconfirm-content input[type=text],
body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-content-pane .jconfirm-content input[type=number],
body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-content-pane .jconfirm-content input[type=email],
body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-content-pane .jconfirm-content input[type=url],
body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-content-pane .jconfirm-content input[type=password],
body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-content-pane .jconfirm-content input[type=search],
body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-content-pane .jconfirm-content input[type=tel],
body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-content-pane .jconfirm-content textarea,
body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-content-pane .jconfirm-content select {
  margin: 10px 2px;
  width: calc(100% - 4px);
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .already-purchased {
  display: block;
  grid-row: 5;
  grid-column: 1 / -1;
  color: #bbbbbb;
  font-size: 14px;
  margin-top: 15px;
  text-decoration: underline;
  text-align: center;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .already-purchased:hover {
  color: #777777;
  text-decoration: underline;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .discount-note {
  grid-row: 4;
  grid-column: 1 / -1;
  margin: 25px 0 0 0;
  text-align: center;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .discount-note p {
  background-color: #fcf9e8;
  color: #777777;
  font-size: 16px;
  margin: 0 -30px;
  padding: 22px 52px 12px 52px;
  position: relative;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .discount-note p:after {
  top: -16px;
  background-color: #ffffff;
  border-radius: 50%;
  color: #00a32a;
  content: "\f058";
  display: inline-block;
  font: normal normal normal 14px FontAwesome;
  font-size: 26px;
  margin-right: -18px;
  padding: 5px 6px;
  position: absolute;
  right: 50%;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .discount-note span {
  color: #00a32a;
  font-weight: 700;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .discount-note a {
  color: #777777;
  display: block;
  margin-top: 12px;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .feature-video {
  margin: 30px 0 0 0;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .pro-feature-video {
  margin: 15px 0 10px 0;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box input[type=text]:not(.choices__input) {
  display: block;
  width: 99%;
  border: 1px solid #d6d6d6;
  padding: 10px !important;
  box-shadow: none;
  margin: 10px 1px 1px 1px !important;
  line-height: 1 !important;
  outline: 0;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box input[type=text]:not(.choices__input):focus {
  border-color: #007cba;
  box-shadow: 0 0 0 1px #007cba;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box #wpforms-edu-modal-license-key {
  margin-top: 20px;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-buttons {
  margin-top: -10px;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-buttons button {
  min-width: 83px;
  background: #f8f8f8;
  border: 1px solid #cccccc;
  border-radius: 4px;
  color: #777777;
  font-size: 16px;
  font-weight: 600;
  line-height: 20px;
  outline: none;
  padding: 11px 17px;
  text-transform: none;
  margin: 10px;
  transition-property: all;
  transition-duration: 0.15s;
  transition-timing-function: ease-out;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-buttons button:hover {
  background: #eeeeee;
  border-color: #cccccc;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-buttons button[disabled] {
  cursor: no-drop;
  pointer-events: none;
  opacity: .25;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-buttons button.btn-confirm {
  color: #ffffff;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-buttons button.hidden + button {
  margin-left: 0;
  margin-right: 0;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-buttons button.btn-block {
  display: block;
  margin: 0 0 10px 0 !important;
  text-align: center;
  width: 100%;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-buttons button.btn-normal-case {
  text-transform: none !important;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-buttons button i {
  margin: 0 10px 0 0;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .error {
  color: #d63638;
  display: none;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box.wpforms-providers-account-add-modal .jconfirm-content .description {
  font-size: 13px;
  line-height: 1.4;
  margin-top: 15px;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box #wpforms-embed-shortcode {
  margin: 20px 0;
  text-align: center;
  font-size: 24px;
  padding: 8px 5px;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box #wpforms-embed-shortcode:disabled {
  color: #333333;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box button[disabled] {
  cursor: no-drop;
}

body div.jconfirm.jconfirm-wpforms-education .jconfirm-content-pane {
  height: auto !important;
  min-height: fit-content;
}

.choices {
  font-size: 16px;
  text-align: start;
}

.choices input[type=text].choices__input:not(.wpforms-hidden) {
  display: inline-block !important;
}

body .jconfirm.has-video div.jconfirm-box-container .jconfirm-box {
  padding-bottom: 0;
  padding-top: 30px;
}

body .jconfirm.has-video div.jconfirm-box-container .already-purchased {
  display: block;
  grid-row: 4;
  grid-column: 1 / 2;
  margin-top: 0;
}

body .jconfirm.has-video div.jconfirm-box-container .already-purchased:hover {
  color: #777777;
}

body .jconfirm.has-video div.jconfirm-box-container .discount-note {
  grid-row: 5;
  margin: 20px 0 0;
}

body .jconfirm.has-video div.jconfirm-box-container .discount-note p {
  margin: 0 -30px;
  padding: 20px 52px;
  border-radius: 0 0 6px 6px;
}

body .jconfirm.has-video div.jconfirm-box-container .discount-note p:after {
  display: none;
}

body .jconfirm.has-video div.jconfirm-box-container .feature-video, body .jconfirm.has-video div.jconfirm-box-container .pro-feature-video {
  grid-row: 1 / span 4;
  grid-column-start: 2;
  margin-top: 0;
  margin-left: 15px;
}

body .jconfirm.has-video div.jconfirm-box-container .jconfirm-title-c,
body .jconfirm.has-video div.jconfirm-box-container .jconfirm-content-pane,
body .jconfirm.has-video div.jconfirm-box-container .jconfirm-buttons {
  grid-column: 1 / 2;
}

body .jconfirm.upgrade-modal .jconfirm-box-container div.jconfirm-box {
  padding-bottom: 30px;
}

body .jconfirm.upgrade-modal .jconfirm-box-container div.jconfirm-box .pro-feature-video {
  margin-bottom: 0;
}

body .jconfirm.upgrade-modal .jconfirm-box-container div.jconfirm-box .jconfirm-buttons {
  padding-bottom: 0;
}

@media screen and (max-width: 1023px) {
  body .jconfirm.has-video div.jconfirm-box {
    grid-template-columns: repeat(1, 1fr);
  }
  body .jconfirm.has-video .feature-video, body .jconfirm.has-video .pro-feature-video {
    display: none;
  }
}

.wpforms-admin-page .wpforms-alert {
  padding: 16px;
  margin-bottom: 18px;
  border: 1px solid transparent;
}

.wpforms-admin-page .wpforms-alert h4 {
  margin-top: 0;
  color: inherit;
}

.wpforms-admin-page .wpforms-alert p {
  margin: 0 0 15px 0;
}

.wpforms-admin-page .wpforms-alert p:last-of-type {
  margin: 0;
}

.wpforms-admin-page .wpforms-alert.wpforms-alert-nomargin {
  margin: 0;
}

.wpforms-admin-page .wpforms-alert.wpforms-alert-small {
  font-size: 12px;
}

.wpforms-admin-page .wpforms-alert.wpforms-alert-success {
  background-color: #dff0d8;
  border-color: #d6e9c6;
  color: #3c763d;
}

.wpforms-admin-page .wpforms-alert.wpforms-alert-info {
  background-color: #d9edf7;
  border-color: #bce8f1;
  color: #31708f;
}

.wpforms-admin-page .wpforms-alert.wpforms-alert-warning {
  background-color: #fcf8e3;
  border-color: #faebcc;
  color: #8a6d3b;
}

.wpforms-admin-page .wpforms-alert.wpforms-alert-danger {
  background-color: #f2dede;
  border-color: #ebccd1;
  color: #a94442;
}

.wpforms-badge {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
  text-transform: uppercase;
  font-weight: 700;
  text-align: center;
  line-height: 6px;
  user-select: none;
  transition-property: all;
  transition-duration: 0.15s;
  transition-timing-function: ease-out;
}

.wpforms-badge i {
  margin-right: 5px;
}

.wpforms-badge-lg {
  font-size: 10px;
  letter-spacing: 0.5px;
  padding: 8px 10px;
}

.wpforms-badge-sm {
  font-size: 8px;
  letter-spacing: 0.4px;
  padding: 6px 8px;
}

.wpforms-badge-inline {
  display: inline-block;
}

.wpforms-badge-corner {
  position: absolute;
  right: 0;
  top: 0;
}

.wpforms-badge-block {
  display: block;
  width: fit-content;
}

.wpforms-badge-rounded {
  border-radius: 3px;
}

.wpforms-badge-rounded-bl {
  border-radius: 0 0 0 3px;
}

.wpforms-badge-square {
  border-radius: 0;
}

.wpforms-badge-silver, .wpforms-badge-platinum {
  color: #999999;
  background-color: #ededed;
}

.wpforms-badge-titanium {
  color: #8c8f94;
  background-color: #e5e5e6;
}

.wpforms-badge-steel {
  color: #9ba4af;
  background-color: #e7ecf2;
}

.wpforms-badge-slate {
  color: #9ba4af;
  background-color: #dbe4ee;
}

.wpforms-badge-stone {
  color: #999999;
  background-color: #444444;
}

.wpforms-badge-orange {
  color: #e79055;
  background-color: #fdf2eb;
}

.wpforms-badge-blue {
  color: #30abf0;
  background-color: #e6f4fe;
}

.wpforms-badge-green {
  color: #30b450;
  background-color: #e5f6e9;
}

.wpforms-badge-red {
  color: #dd595b;
  background-color: #fae6e7;
}

.wpforms-badge-purple {
  color: #9b64e8;
  background-color: #faf5fe;
}

#wpcontent {
  padding-left: 0 !important;
  position: relative;
}

@media (max-width: 600px) {
  #wpcontent {
    padding-top: 46px;
  }
}

@media (max-width: 600px) {
  #wpbody {
    padding-top: 0;
  }
}

.wpforms-admin-page a {
  color: #056aab;
}

.wpforms-admin-page a:hover {
  color: #04558a;
}

.wpforms-admin-page .row-actions .trash a,
.wpforms-admin-page .row-actions .delete a {
  color: #d63638;
}

.wpforms-admin-page .row-actions .trash a:hover,
.wpforms-admin-page .row-actions .delete a:hover {
  color: #b32d2e;
}

.wpforms-admin-page .button {
  color: #056aab;
  border-color: #056aab;
}

.wpforms-admin-page .button[disabled] {
  color: #056aab !important;
  border-color: #056aab !important;
}

.wpforms-admin-page .button:hover {
  color: #04558a;
  border-color: #04558a;
}

.wpforms-admin-page .button:focus {
  color: #056aab;
  border-color: #056aab;
  box-shadow: 0 0 0 1px #056aab;
}

.wpforms-admin-page .button.button-primary {
  background-color: #056aab;
  border-color: #056aab;
  color: #ffffff;
}

.wpforms-admin-page .button.button-primary:hover, .wpforms-admin-page .button.button-primary:focus {
  background-color: #04558a;
  border-color: #04558a;
  color: #ffffff;
}

.wpforms-admin-page .button.button-primary:focus {
  border-color: #ffffff;
  box-shadow: 0 0 0 2px #04558a;
}

.wpforms-admin-page #screen-meta-links,
.wpforms-admin-page #screen-meta {
  display: none;
}

.wpforms-admin-page .video-container {
  position: relative;
  padding-bottom: 56.1%;
  height: 0;
  overflow: hidden;
}

.wpforms-admin-page .video-container iframe,
.wpforms-admin-page .video-container object,
.wpforms-admin-page .video-container embed {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.wpforms-admin-page .wpforms-file-upload input[type=file] {
  width: 0.1px;
  height: 0.1px;
  opacity: 0;
  overflow: hidden;
  position: absolute;
  z-index: -1;
}

.wpforms-admin-page .wpforms-file-upload label {
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;
  display: inline-block;
  padding: 0;
  outline: none;
}

.wpforms-admin-page .wpforms-file-upload input[type=file]:focus + label .fld,
.wpforms-admin-page .wpforms-file-upload input[type=file].has-focus + label .fld {
  border: 1px solid #2271b1;
  box-shadow: 0 0 0 1px #2271b1;
  outline: none;
}

.wpforms-admin-page .wpforms-file-upload .fld {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 3px;
  box-shadow: none;
  color: #333;
  display: inline-block;
  vertical-align: middle;
  padding: 7px 12px;
  margin: 0 10px 0 0;
  width: 400px;
  min-height: 35px;
}

.wpforms-admin-page .wpforms-file-upload .fld .placeholder {
  opacity: .5;
}

.wpforms-admin-page .wpforms-file-upload strong .fa {
  margin-inline-end: 8px;
}

.wpforms-admin-page .wpforms-hide {
  display: none;
}

.wpforms-admin-page .wpforms-hidden {
  display: none !important;
}

.wpforms-admin-page .wpforms-h1-placeholder {
  display: none;
}

.wpforms-admin-page .notice {
  display: none;
}

.wpforms-admin-page .lity {
  z-index: 999999999;
  padding: 20px;
}

.wpforms-admin-page .lity-close {
  margin: 10px;
}

.wpforms-admin-page .lity-content {
  max-width: 80vw;
  max-height: 80vh;
}

.wpforms-admin-page .lity-content img {
  max-height: 80vh !important;
  max-width: 80vw !important;
}

.wpforms-admin-page input.choices__input {
  line-height: normal;
  box-shadow: none;
}

.wpforms-admin-page input:focus, .wpforms-admin-page select:focus, .wpforms-admin-page textarea:focus {
  border: 1px solid #016AAB !important;
  box-shadow: 0 0 0 1px #016AAB !important;
  outline: none !important;
}

.wpforms-admin-page a:focus {
  box-shadow: 0 0 2px 1px #016AABCC;
}

.wpforms-admin-page #poststuff #post-body.columns-2 {
  margin-right: 320px;
}

.wpforms-admin-page #poststuff #post-body.columns-2 .postbox-container {
  margin-right: -320px;
  width: 300px;
}

.wpforms-admin-page .postbox {
  background: #ffffff;
  border: 1px solid #c3c4c7;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
  border-radius: 4px;
}

.wpforms-admin-page .postbox .hndle {
  cursor: default !important;
  border-color: #c3c4c7;
}

.wpforms-admin-page .postbox #major-publishing-actions,
.wpforms-admin-page .postbox .overlay {
  border-radius: 0 0 4px 4px;
}

.wpforms-admin-page .postbox #major-publishing-actions {
  border-top: 1px solid #c3c4c7;
}

.wpforms-admin-wrap {
  margin: 0;
}

.wpforms-admin-wrap .notice {
  margin-left: 20px !important;
  margin-right: 20px !important;
}

.wpforms-admin-wrap .notice.wpforms-notice-spam {
  margin-bottom: 10px;
}

.wpforms-tools-tab-action-scheduler .error,
.wpforms-tools-tab-action-scheduler .updated {
  margin-left: 20px !important;
  margin-right: 20px !important;
}

.wpforms-admin-content {
  padding-left: 20px;
  padding-right: 20px;
}

.wpforms-admin-content:before {
  content: " ";
  display: table;
}

.wpforms-admin-content:after {
  clear: both;
  content: " ";
  display: table;
}

.wpforms-admin-content hr {
  border: none;
  border-top: 1px solid #e4e4e4;
  margin: 20px 0;
  background: none;
}

.wpforms-clear:before {
  content: " ";
  display: table;
}

.wpforms-clear:after {
  clear: both;
  content: " ";
  display: table;
}

.wpforms-admin-columns > div[class*="-column-"] {
  float: left;
}

.wpforms-admin-columns .wpforms-admin-column-20 {
  width: 20%;
}

.wpforms-admin-columns .wpforms-admin-column-33 {
  width: 33.33333%;
}

.wpforms-admin-columns .wpforms-admin-column-40 {
  width: 40%;
}

.wpforms-admin-columns .wpforms-admin-column-50 {
  width: 50%;
}

.wpforms-admin-columns .wpforms-admin-column-60 {
  width: 60%;
}

.wpforms-admin-columns .wpforms-admin-column-80 {
  width: 80%;
}

.wpforms-admin-columns .wpforms-admin-column-last {
  float: right !important;
}

.wpforms-admin-columns:after {
  content: "";
  display: table;
  clear: both;
}

.lity-active,
.lity-active body.wpforms-admin-page {
  overflow: scroll;
}

@media screen and (max-width: 782px) {
  .wpforms-admin-wrap .notice {
    margin: 5px 10px 10px 10px !important;
  }
  .wpforms-tools-tab-action-scheduler .error,
  .wpforms-tools-tab-action-scheduler .updated {
    margin: 5px 10px 10px 10px !important;
  }
}

.flatpickr-calendar select.flatpickr-monthDropdown-months,
.flatpickr-calendar input.numInput {
  min-height: auto;
  padding: 0;
}

.flatpickr-calendar select.flatpickr-monthDropdown-months:focus, .flatpickr-calendar select.flatpickr-monthDropdown-months:hover, .flatpickr-calendar select.flatpickr-monthDropdown-months:active,
.flatpickr-calendar input.numInput:focus,
.flatpickr-calendar input.numInput:hover,
.flatpickr-calendar input.numInput:active {
  border: none !important;
  box-shadow: none !important;
  outline: none !important;
  background: transparent !important;
}

@media (max-width: 599px) {
  .flatpickr-calendar .flatpickr-current-month .numInputWrapper {
    width: calc(6ch - 14px);
  }
  .flatpickr-calendar .flatpickr-current-month .numInputWrapper span.arrowUp,
  .flatpickr-calendar .flatpickr-current-month .numInputWrapper span.arrowDown {
    display: none;
  }
}

.rtl.wpforms-admin-page #wpcontent {
  padding-right: 0 !important;
  padding-left: 0 !important;
}

.rtl.wpforms-admin-page #poststuff #post-body.columns-2 {
  margin-right: 0;
  margin-left: 320px;
}

.rtl.wpforms-admin-page #poststuff #post-body.columns-2 .postbox-container {
  margin-left: -320px;
  margin-right: unset;
  width: 300px;
}

#wpforms-header-temp {
  margin: 0;
  position: absolute;
  top: -1px;
  left: 20px;
  right: 0;
  z-index: 99;
}

@media (max-width: 599px) {
  #wpforms-header-temp {
    padding-top: 50px;
  }
}

#wpforms-header {
  border-top: 3px solid #e27730;
  padding: 22px 20px;
  box-sizing: border-box;
  height: 111px;
}

@media (max-width: 782px) {
  #wpforms-header {
    height: 101px;
  }
}

#wpforms-header img {
  display: block;
  margin: 0;
  max-width: 235px;
}

@media (max-width: 782px) {
  #wpforms-header img {
    max-width: 200px;
  }
}

@media screen and (min-width: 1024px) {
  #wpforms-notice-bar + #wpforms-header-temp {
    top: 34px;
  }
}

#wpforms-payments h1.page-title .wpforms-payments-overview-help {
  font-size: 14px;
  font-weight: 600;
  line-height: 18px;
  margin-inline-start: auto;
}

#wpforms-payments h1.page-title .wpforms-payments-overview-help a {
  align-items: center;
  color: #999999;
  display: flex;
  gap: 6px;
  text-decoration: none;
}

#wpforms-payments h1.page-title .wpforms-payments-overview-help a:is(:active, :focus, :hover) {
  box-shadow: none;
  color: #777777;
  outline: none;
}

#wpforms-payments h1.page-title .wpforms-payments-overview-help a i {
  font-size: 16px;
}

body.wpforms-admin-page #wpbody-content {
  padding-bottom: 200px;
}

@media (max-width: 782px) {
  body.wpforms-admin-page #wpbody-content {
    padding-bottom: 0 !important;
  }
}

body.wpforms-admin-page.wpforms_page_wpforms-entries #wpbody-content {
  padding-bottom: 185px;
}

body.wpforms-admin-page.wpforms_page_wpforms-entries #wpbody-content #wpforms-entries-single {
  margin-bottom: 10px;
}

body.wpforms-admin-page.wpforms_page_wpforms-entries #wpbody-content #wpforms_reports_widget_pro,
body.wpforms-admin-page.wpforms_page_wpforms-entries #wpbody-content .wpforms-entries-list-upgrade {
  margin-bottom: 50px;
}

body.wpforms-admin-page.wpforms_page_wpforms-templates #wpbody-content {
  padding-bottom: 215px;
}

body.wpforms-admin-page.wpforms_page_wpforms-settings #wpbody-content {
  padding-bottom: 150px;
}

body.wpforms-admin-page.wpforms_page_wpforms-settings #wpbody-content .settings-lite-cta {
  margin-bottom: 80px;
}

body.wpforms-admin-page.wpforms_page_wpforms-settings #wpbody-content .wpforms-admin-settings-integrations {
  margin-bottom: 80px;
}

body.wpforms-admin-page.wpforms_page_wpforms-settings #wpbody-content .wpforms-admin-settings-integrations .settings-lite-cta {
  margin-bottom: 0;
}

body.wpforms-admin-page.wpforms_page_wpforms-tools #wpbody-content {
  padding-bottom: 0;
}

body.wpforms-admin-page.wpforms_page_wpforms-tools #wpbody-content #wpforms-tools {
  padding-bottom: 230px;
}

@media (max-width: 782px) {
  body.wpforms-admin-page.wpforms_page_wpforms-tools #wpbody-content #wpforms-tools {
    padding-bottom: 20px !important;
  }
}

body.wpforms-admin-page.wpforms_page_wpforms-tools #wpbody-content #wpforms-tools.wpforms-tools-tab-action-scheduler, body.wpforms-admin-page.wpforms_page_wpforms-tools #wpbody-content #wpforms-tools.wpforms-tools-tab-logs {
  padding-bottom: 185px;
}

body.wpforms-admin-page.wpforms_page_wpforms-addons #wpbody-content, body.wpforms-admin-page.wpforms_page_wpforms-analytics #wpbody-content, body.wpforms-admin-page.wpforms_page_wpforms-smtp #wpbody-content, body.wpforms-admin-page.wpforms_page_wpforms-about #wpbody-content {
  padding-bottom: 210px;
}

body.wpforms-admin-page.wpforms_page_wpforms-addons #wpbody-content .wpforms-admin-about-section:last-child, body.wpforms-admin-page.wpforms_page_wpforms-analytics #wpbody-content .wpforms-admin-about-section:last-child, body.wpforms-admin-page.wpforms_page_wpforms-smtp #wpbody-content .wpforms-admin-about-section:last-child, body.wpforms-admin-page.wpforms_page_wpforms-about #wpbody-content .wpforms-admin-about-section:last-child {
  margin-bottom: 20px;
}

body.wpforms-admin-page.wpforms_page_wpforms-community #wpbody-content {
  padding-bottom: 190px;
}

body.wpforms-admin-page #wpfooter .wpforms-footer-promotion {
  text-align: center;
  font-weight: 400;
  font-size: 13px;
  line-height: normal;
  color: #646970;
  padding: 30px 0;
  margin-bottom: 20px;
}

body.wpforms-admin-page #wpfooter .wpforms-footer-promotion p {
  font-weight: 600;
}

body.wpforms-admin-page #wpfooter .wpforms-footer-promotion-links,
body.wpforms-admin-page #wpfooter .wpforms-footer-promotion-social {
  display: flex;
  justify-content: center;
  align-items: center;
}

body.wpforms-admin-page #wpfooter .wpforms-footer-promotion-links {
  margin: 10px 0;
  color: #646970;
}

body.wpforms-admin-page #wpfooter .wpforms-footer-promotion-links a {
  color: #056aab;
}

body.wpforms-admin-page #wpfooter .wpforms-footer-promotion-links a:hover {
  color: #04558a;
}

body.wpforms-admin-page #wpfooter .wpforms-footer-promotion-links span {
  color: #c3c4c7;
  padding: 0 7px;
}

body.wpforms-admin-page #wpfooter .wpforms-footer-promotion-social {
  gap: 10px;
  margin: 0;
}

body.wpforms-admin-page #wpfooter .wpforms-footer-promotion-social li {
  margin-bottom: 0;
}

body.wpforms-admin-page #wpfooter .wpforms-footer-promotion-social li path {
  color: #646970;
}

body.wpforms-admin-page #wpfooter .wpforms-footer-promotion-social li:hover path {
  fill: #50575e;
}

body.wpforms-admin-page #wpfooter .wpforms-footer-promotion-social a {
  display: block;
  height: 16px;
}

body.wpforms-admin-page #wpfooter #footer-left {
  color: #50575e;
  font-size: 13px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

body.wpforms-admin-page #wpfooter #footer-left strong {
  font-weight: 600;
}

.wpforms-admin-wrap .page-title {
  display: flex !important;
  /* Elementor compatibility. */
  align-items: center;
  gap: 20px;
  margin: 0 0 20px 0;
  padding: 15px 20px;
  color: #101517;
  background-color: #ffffff;
  font-size: 22px;
  font-weight: 400;
  line-height: 30px;
}

.wpforms-admin-wrap .page-title-action {
  transition-property: color, background-color, border-color, box-shadow;
  border-radius: 4px !important;
}

.wpforms-admin-wrap .page-title-action,
.wpforms-admin-wrap .page-title-action:active {
  position: initial;
  display: inline-flex !important;
  /* Elementor compatibility. */
  align-items: center;
  gap: 7px;
  margin: 0;
  padding: 6px 10px;
  font-size: 13px;
  font-weight: 600;
  line-height: normal;
}

.wpforms-admin-wrap .page-title-action-icon {
  width: auto;
  height: 12px;
  fill: #ffffff;
  opacity: 0.75;
}

.wpforms-payments-wrap .page-title {
  font-size: 22px;
  line-height: 30px;
}

.wpforms-payments-wrap.wpforms-payments-wrap-payments .page-title {
  align-items: center;
  display: flex !important;
  gap: 20px;
}

.wpforms-payments-wrap .wpforms-payments-overview-help {
  font-size: 14px;
  font-weight: 600;
  line-height: 18px;
  margin-inline-start: auto;
}

.wpforms-payments-wrap .wpforms-payments-overview-help a {
  align-items: center;
  color: #999999;
  display: flex;
  gap: 6px;
  text-decoration: none;
}

.wpforms-payments-wrap .wpforms-payments-overview-help a:is(:active, :focus, :hover) {
  box-shadow: none;
  color: #777777;
  outline: none;
}

.wpforms-payments-wrap .wpforms-payments-overview-help a i {
  font-size: 16px;
}

.wpforms-admin-tabs {
  display: flex;
  flex-wrap: wrap;
  gap: 0 30px;
  margin: 0 0 20px 0;
  padding: 0 20px;
  background-color: #ffffff;
  list-style: none;
  font-size: 14px;
  font-weight: 400;
}

@media (max-width: 767px) {
  .wpforms-admin-tabs {
    flex-wrap: nowrap;
    overflow-x: scroll;
    padding: 0 30px 0 20px;
    white-space: nowrap;
  }
  .wpforms-admin-tabs::after {
    content: "\f11c";
    font-family: dashicons;
    position: absolute;
    top: 0;
    inset-inline-end: 0;
    bottom: 0;
    background-color: #ffffff;
    width: 36px;
    height: 60px;
    box-shadow: 0 0 20px -5px #00000030;
    clip-path: inset(0 0 0 -20px);
    color: #a7aaad;
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    opacity: 0;
    transition-property: opacity;
    transition-duration: 0.15s;
    transition-timing-function: ease-out;
  }
  .wpforms-admin-tabs.wpforms-admin-tabs--scrollable::after {
    opacity: 1;
  }
}

.wpforms-admin-tabs li {
  margin: 0;
  padding: 0;
}

.wpforms-admin-tabs li a {
  display: block;
  padding: 15px 0 12px 0;
  line-height: 30px;
  border-bottom: 3px solid #ffffff;
  box-shadow: none;
  color: #2c3338;
  text-decoration: none;
}

.wpforms-admin-tabs li a:hover, .wpforms-admin-tabs li a:focus, .wpforms-admin-tabs li a.active:focus {
  color: #2c3338;
  border-color: #c3c4c7;
}

.wpforms-admin-tabs li a.active {
  border-color: #e27730;
}

.wpforms-admin-tabs li a:focus {
  box-shadow: none;
}

.rtl .wpforms-admin-wrap .page-title-action,
.rtl .wpforms-admin-wrap .page-title-action:active {
  flex-direction: row-reverse;
}

#wpforms-flyout {
  position: fixed;
  z-index: 99999;
  transition: all 0.2s ease-in-out;
  inset-inline-end: 40px;
  bottom: 40px;
  opacity: 1;
}

@media (max-width: 959px) {
  #wpforms-flyout {
    display: none;
  }
}

#wpforms-flyout .wpforms-flyout-head {
  display: block;
}

#wpforms-flyout .wpforms-flyout-head img {
  width: 54px;
  height: 54px;
  display: block;
  border-radius: 50%;
  border: 3px solid #E1772F;
  overflow: hidden;
  box-shadow: 0 3px 20px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease-in-out;
}

#wpforms-flyout .wpforms-flyout-head:hover img {
  box-shadow: 0 3px 30px rgba(0, 0, 0, 0.25);
}

#wpforms-flyout .wpforms-flyout-head .wpforms-flyout-label {
  opacity: 0;
  transform: translateY(-50%) scale(0);
  margin-inline-end: -50px;
}

#wpforms-flyout .wpforms-flyout-head:hover .wpforms-flyout-label {
  opacity: 1;
  transform: translateY(-50%) scale(1);
  margin-inline-end: 0;
}

#wpforms-flyout .wpforms-flyout-head:focus {
  box-shadow: none;
}

#wpforms-flyout .wpforms-flyout-head .wpforms-flyout-label {
  inset-inline-end: calc(100% + 15px);
}

#wpforms-flyout .wpforms-flyout-label {
  position: absolute;
  display: block;
  top: 50%;
  inset-inline-end: calc(100% + 25px);
  transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -webkit-transform: translateY(-50%);
  color: #fff;
  background: #5F5E5E 0 0 no-repeat padding-box;
  font-size: 12px;
  white-space: nowrap;
  padding: 5px 10px;
  height: auto !important;
  line-height: initial;
  transition: all 0.2s ease-out;
  border-radius: 3px;
  -moz-border-radius: 3px;
  -webkit-border-radius: 3px;
}

#wpforms-flyout .wpforms-flyout-item {
  position: absolute;
  inset-inline-start: 10px;
  width: 40px;
  height: 40px;
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s ease-in-out;
  transform: scale(0);
  border-radius: 50%;
  box-shadow: 0 3px 20px rgba(0, 0, 0, 0.2);
  background: #036AAB 0 0 no-repeat padding-box;
  text-align: center;
  vertical-align: middle;
}

#wpforms-flyout .wpforms-flyout-item i {
  color: #ffffff;
  font-size: 20px;
  line-height: 40px;
  vertical-align: middle;
}

#wpforms-flyout .wpforms-flyout-item.wpforms-flyout-item-0 {
  bottom: 75px;
}

#wpforms-flyout .wpforms-flyout-item.wpforms-flyout-item-1 {
  bottom: 130px;
}

#wpforms-flyout .wpforms-flyout-item.wpforms-flyout-item-2 {
  bottom: 185px;
}

#wpforms-flyout .wpforms-flyout-item.wpforms-flyout-item-3 {
  bottom: 240px;
}

#wpforms-flyout .wpforms-flyout-item.wpforms-flyout-item-4 {
  bottom: 295px;
}

#wpforms-flyout .wpforms-flyout-item:hover {
  box-shadow: 0 3px 30px rgba(0, 0, 0, 0.25);
  background: #0096F0 0 0 no-repeat padding-box;
}

#wpforms-flyout .wpforms-flyout-item:hover .wpforms-flyout-label {
  background: #444444 0 0 no-repeat padding-box;
}

#wpforms-flyout.opened .wpforms-flyout-item {
  opacity: 1;
  visibility: visible;
  transform: scale(1);
}

#wpforms-flyout.opened .wpforms-flyout-head .wpforms-flyout-label {
  display: none;
}

#wpforms-flyout.opened .wpforms-flyout-item-0 {
  transition: transform 0.2s 0ms, background-color 0.2s;
}

#wpforms-flyout.opened .wpforms-flyout-item-1 {
  transition: transform 0.2s 35ms, background-color 0.2s;
}

#wpforms-flyout.opened .wpforms-flyout-item-2 {
  transition: transform 0.2s 70ms, background-color 0.2s;
}

#wpforms-flyout.opened .wpforms-flyout-item-3 {
  transition: transform 0.2s 105ms, background-color 0.2s;
}

#wpforms-flyout.opened .wpforms-flyout-item-4 {
  transition: transform 0.2s 140ms, background-color 0.2s;
}

#wpforms-flyout.out {
  opacity: 0;
  visibility: hidden;
}

.wpforms-admin-empty-state-container {
  color: #50575e;
  font-style: normal;
  padding: 30px;
  text-align: center;
}

.wpforms-admin-empty-state-container .waving-hand-emoji {
  background-image: url(../images/empty-states/waving-hand-emoji.png);
  background-position: 0 0;
  background-repeat: no-repeat;
  background-size: 24px 24px;
  display: inline-block;
  padding-left: 34px;
}

.wpforms-admin-empty-state-container h2 {
  color: #1d2327;
  font-family: inherit;
  font-size: 24px;
  font-weight: 600;
  line-height: 29px;
  margin: 0 0 10px;
}

.wpforms-admin-empty-state-container h4 {
  color: #32373c;
  font-family: inherit;
  font-size: 16px;
  font-weight: 600;
  line-height: 1.5;
  margin-block: 0;
}

.wpforms-admin-empty-state-container img {
  max-width: 428px;
  width: 100%;
  margin: 30px auto;
}

.wpforms-admin-empty-state-container p {
  font-family: inherit;
  font-size: 16px;
  line-height: 24px;
  margin-block: 0;
  text-align: center;
}

.wpforms-admin-empty-state-container.wpforms-admin-no-forms p:first-of-type {
  font-weight: 600;
}

.wpforms-admin-empty-state-container.wpforms-admin-no-forms .wpforms-btn, .wpforms-admin-empty-state-container.wpforms-admin-no-forms .wpforms-btn:active {
  border: none;
  border-radius: 3px;
  font-family: inherit;
  font-weight: 600;
  font-size: 16px;
  line-height: 1.5;
  text-align: center;
  color: #ffffff;
  padding: 15px 30px;
  margin: 0;
  text-decoration: none;
}

.wpforms-admin-empty-state-container.wpforms-admin-no-forms .wpforms-btn:hover, .wpforms-admin-empty-state-container.wpforms-admin-no-forms .wpforms-btn:active:hover {
  text-decoration: none;
}

.wpforms-admin-empty-state-container.wpforms-admin-no-forms .wpforms-btn:focus, .wpforms-admin-empty-state-container.wpforms-admin-no-forms .wpforms-btn:active:focus {
  outline: none;
}

.wpforms-admin-empty-state-container.wpforms-admin-no-forms .wpforms-admin-no-forms-footer {
  margin-top: 30px;
  font-size: 14px;
  line-height: 16px;
}

.wpforms-admin-empty-state-container.wpforms-elementor-no-forms {
  font-family: 'Helvetica Neue', sans-serif;
  background: rgba(255, 255, 255, 0.5);
  border: 1px solid #71d7d7;
  box-sizing: border-box;
  padding: 20px;
}

.wpforms-admin-empty-state-container.wpforms-elementor-no-forms img {
  max-width: 240px;
  width: 100%;
  margin: 0 auto;
}

.wpforms-admin-empty-state-container.wpforms-elementor-no-forms p {
  font-family: inherit;
  font-weight: normal;
  font-size: 14px;
  line-height: 18px;
  text-align: center;
  color: #495157;
  max-width: 450px;
  margin: 20px auto 0;
}

.wpforms-admin-empty-state-container.wpforms-elementor-no-forms a {
  color: inherit;
  text-decoration: underline;
  position: relative;
}

.wpforms-admin-empty-state-container.wpforms-elementor-no-forms a:hover {
  text-decoration: none;
}

.wpforms-admin-empty-state-container.wpforms-elementor-no-forms .wpforms-btn {
  background: #525962;
  border: none;
  border-radius: 3px;
  font-family: inherit;
  font-weight: 600;
  font-size: 12px;
  line-height: 1.5;
  text-align: center;
  text-transform: uppercase;
  color: #ffffff;
  padding: 7px 17px;
  margin: 20px 0 0;
  text-decoration: none;
}

.wpforms-admin-empty-state-container.wpforms-elementor-no-forms .wpforms-btn:hover {
  text-decoration: none;
  background: #2b2c31;
}

.wpforms-admin-empty-state-container.wpforms-elementor-no-forms .wpforms-btn:focus {
  outline: none;
}

.wpforms-admin-empty-state-container.wpforms-elementor-no-forms .wpforms-admin-no-forms-footer {
  font-size: 12px;
  line-height: 1.5;
  color: #6d7882;
}

.wpforms-admin-empty-state-container.wpforms-admin-no-entries img {
  max-width: 413px;
  display: block;
}

.wpforms-admin-empty-state-container.wpforms-admin-no-payments {
  max-width: 640px;
  margin: 0 auto;
}

.wpforms-admin-empty-state-container.wpforms-admin-no-payments img {
  display: block;
}

.wpforms-admin-empty-state-container.wpforms-admin-no-payments .wpforms-btn-lg {
  border-radius: 4px;
  padding: 14px 20px;
}

.wpforms-admin-empty-state-container.wpforms-admin-no-payments a:not([class]) {
  color: #056aab;
  text-decoration: underline;
}

.wpforms-admin-empty-state-container.wpforms-admin-no-payments a:not([class]):hover {
  text-decoration: none;
}

.wpforms-admin-empty-state-container.wpforms-admin-no-payments .wpforms-admin-no-forms-footer {
  margin-top: 30px;
  font-size: 14px;
}

.wpforms-admin-empty-state-container.wpforms-admin-no-user-templates h2, .wpforms-admin-empty-state-container.wpforms-admin-no-user-templates h4 {
  font-weight: 500;
}

.wpforms-admin-empty-state-container.wpforms-admin-no-user-templates p {
  font-size: 14px;
  color: #777777;
}

.wpforms-admin-empty-state-container.wpforms-admin-no-user-templates p.wpforms-admin-no-forms-footer {
  color: #50575e;
}

.wpforms-admin-empty-state-container.wpforms-admin-no-user-templates img {
  max-width: 560px;
  border-radius: 4px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

#wpforms-reset-filter {
  clear: both;
  margin: 20px 0;
  padding: 10px;
  font-size: 15px;
  text-align: center;
  background: #ffffff;
  border: 1px solid #ccd0d4;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
}

#wpforms-reset-filter .reset {
  cursor: pointer;
  color: #a7aaad;
  margin-left: 3px;
}

#wpforms-reset-filter .reset:focus, #wpforms-reset-filter .reset:hover {
  color: #dc3232;
}

@media (max-width: 782px) {
  #wpforms-reset-filter {
    margin-bottom: 10px;
  }
}

.wpforms-toggle-control {
  display: flex;
  align-items: center;
  margin: 0 1px;
  line-height: 1;
  color: #50575e;
  font-weight: 500;
}

.wpforms-toggle-control input[type=checkbox] {
  position: absolute;
  overflow: hidden;
  height: 0;
  width: 0;
  border: none !important;
  box-shadow: none !important;
  margin: 0;
  min-width: 0;
}

.wpforms-toggle-control input[type=checkbox]:hover, .wpforms-toggle-control input[type=checkbox]:focus {
  border: none !important;
  box-shadow: none !important;
}

.wpforms-toggle-control input[type=checkbox]:hover + label.wpforms-toggle-control-icon {
  background-color: #646970;
}

.wpforms-toggle-control input[type=checkbox]:focus + label.wpforms-toggle-control-icon {
  background-color: #646970;
  box-shadow: 0 0 0 1px #ffffff, 0 0 0 2px #8c8f94;
}

.wpforms-toggle-control input[type=checkbox]:checked + label.wpforms-toggle-control-icon {
  background-color: #056aab;
}

.wpforms-toggle-control input[type=checkbox]:checked + label.wpforms-toggle-control-icon:after {
  left: calc( 100% - 2px);
  transform: translateX(-100%);
}

.wpforms-toggle-control input[type=checkbox]:checked:hover + label.wpforms-toggle-control-icon {
  background-color: #04558a;
}

.wpforms-toggle-control input[type=checkbox]:checked:focus + label.wpforms-toggle-control-icon {
  box-shadow: 0 0 0 1px #ffffff, 0 0 0 2px #04558a;
}

.wpforms-toggle-control input[type=checkbox]:disabled + label.wpforms-toggle-control-icon {
  opacity: 0.5;
}

.wpforms-toggle-control input[type=checkbox]:disabled:hover + label.wpforms-toggle-control-icon, .wpforms-toggle-control input[type=checkbox]:disabled:focus + label.wpforms-toggle-control-icon {
  box-shadow: none;
}

.wpforms-toggle-control span,
.wpforms-toggle-control label {
  display: inline-block;
  margin-bottom: 0;
}

.wpforms-toggle-control .wpforms-toggle-control-label {
  padding: 0 10px;
  max-width: calc( 100% - 65px);
}

.wpforms-toggle-control .wpforms-toggle-control-status {
  color: #50575e;
  font-size: 12px;
  line-height: 17px;
  padding: 0 10px;
}

.wpforms-toggle-control .wpforms-toggle-control-icon {
  background-color: #8c8f94;
  border-radius: 8.5px;
  cursor: pointer;
  display: inline-block;
  height: 17px;
  position: relative;
  text-indent: -9999px;
  width: 27px;
}

.wpforms-toggle-control .wpforms-toggle-control-icon:after {
  background: #ffffff;
  border-radius: 50%;
  content: "";
  height: 13px;
  left: 2px;
  position: absolute;
  top: 2px;
  width: 13px;
  transition: all 0.25s ease-out;
}

.wpforms-toggle-control:hover input:checked + label.wpforms-toggle-control-icon {
  background-color: #215d8f;
}

.wpforms-toggle-control:hover .wpforms-toggle-control-icon {
  background-color: #8c8f94;
}

.wpforms-toggle-control.wpforms-important .wpforms-toggle-control-icon {
  background-color: #d63638;
}

.wpforms-admin-settings .wpforms-setting-row .wpforms-toggle-desc {
  margin: 8px 0 0;
}

#wpforms-payments .wpforms-admin-content .wpforms-education-container-page .wpforms-setting-field,
#wpforms-settings .wpforms-admin-content .wpforms-education-container-page .wpforms-setting-field {
  margin: 0;
  max-width: none;
}

#wpforms-payments .wpforms-admin-content .wpforms-education-page .wpforms-setting-field,
#wpforms-settings .wpforms-admin-content .wpforms-education-page .wpforms-setting-field {
  margin: 0;
  max-width: 1000px;
}

#wpforms-payments .wpforms-admin-content .wpforms-education-page,
#wpforms-settings .wpforms-admin-content .wpforms-education-page {
  max-width: 1000px;
  margin-bottom: 30px;
  padding: 0;
}

#wpforms-payments .wpforms-admin-content .wpforms-education-page.wpforms-setting-row,
#wpforms-settings .wpforms-admin-content .wpforms-education-page.wpforms-setting-row {
  padding: 0;
}

#wpforms-payments .wpforms-admin-content .wpforms-education-page .fa,
#wpforms-settings .wpforms-admin-content .wpforms-education-page .fa {
  font-family: FontAwesome;
}

#wpforms-payments .wpforms-admin-content .wpforms-education-page-heading h4,
#wpforms-settings .wpforms-admin-content .wpforms-education-page-heading h4 {
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  gap: 10px;
}

#wpforms-payments .wpforms-admin-content .wpforms-education-page-heading p,
#wpforms-settings .wpforms-admin-content .wpforms-education-page-heading p {
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: #444444;
  margin-top: 0;
}

#wpforms-payments .wpforms-admin-content .wpforms-education-page-images,
#wpforms-settings .wpforms-admin-content .wpforms-education-page-images {
  display: flex;
  gap: 25px;
  margin: 25px 0;
}

#wpforms-payments .wpforms-admin-content .wpforms-education-page-images figure,
#wpforms-settings .wpforms-admin-content .wpforms-education-page-images figure {
  margin: 0;
}

#wpforms-payments .wpforms-admin-content .wpforms-education-page-images figcaption,
#wpforms-settings .wpforms-admin-content .wpforms-education-page-images figcaption {
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  text-align: center;
  color: #777777;
  margin-top: 10px;
}

#wpforms-payments .wpforms-admin-content .wpforms-education-page-images-image,
#wpforms-settings .wpforms-admin-content .wpforms-education-page-images-image {
  display: inline-block;
  position: relative;
  padding: 5px;
  background-color: #ffffff;
  box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

#wpforms-payments .wpforms-admin-content .wpforms-education-page-images-image img,
#wpforms-settings .wpforms-admin-content .wpforms-education-page-images-image img {
  max-width: 100%;
  display: block;
}

#wpforms-payments .wpforms-admin-content .wpforms-education-page-images-image .hover,
#wpforms-settings .wpforms-admin-content .wpforms-education-page-images-image .hover {
  position: absolute;
  opacity: 0;
  height: calc(100% - 10px);
  width: calc(100% - 10px);
  top: 0;
  left: 0;
  border: 5px solid #ffffff;
  background-color: rgba(0, 0, 0, 0.15);
  background-image: url("../images/zoom.svg");
  background-repeat: no-repeat;
  background-position: center;
  background-size: 50px;
  transition: all 0.3s;
  box-sizing: initial;
}

#wpforms-payments .wpforms-admin-content .wpforms-education-page-images-image:hover .hover,
#wpforms-settings .wpforms-admin-content .wpforms-education-page-images-image:hover .hover {
  opacity: 1;
  transition: all 0.3s;
}

#wpforms-payments .wpforms-admin-content .wpforms-education-page-caps,
#wpforms-settings .wpforms-admin-content .wpforms-education-page-caps {
  max-width: 986px;
  box-sizing: content-box;
  background: #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  border-radius: 6px;
  padding: 20px;
  overflow: auto;
}

#wpforms-payments .wpforms-admin-content .wpforms-education-page-caps p,
#wpforms-settings .wpforms-admin-content .wpforms-education-page-caps p {
  font-weight: 600;
  font-size: 16px;
  line-height: 16px;
  color: #32373c;
  margin-bottom: 20px;
  margin-top: 0;
}

#wpforms-payments .wpforms-admin-content .wpforms-education-page-caps ul,
#wpforms-settings .wpforms-admin-content .wpforms-education-page-caps ul {
  margin: 0;
  margin-bottom: -20px;
}

@media (min-width: 783px) {
  #wpforms-payments .wpforms-admin-content .wpforms-education-page-caps ul,
  #wpforms-settings .wpforms-admin-content .wpforms-education-page-caps ul {
    display: flex;
    flex-wrap: wrap;
  }
}

#wpforms-payments .wpforms-admin-content .wpforms-education-page-caps ul li,
#wpforms-settings .wpforms-admin-content .wpforms-education-page-caps ul li {
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: #50575e;
  margin-bottom: 20px;
}

@media (min-width: 783px) {
  #wpforms-payments .wpforms-admin-content .wpforms-education-page-caps ul li,
  #wpforms-settings .wpforms-admin-content .wpforms-education-page-caps ul li {
    flex: 0 0 33.3333%;
  }
}

#wpforms-payments .wpforms-admin-content .wpforms-education-page-caps ul li i,
#wpforms-settings .wpforms-admin-content .wpforms-education-page-caps ul li i {
  color: #008a20;
  margin-right: 10px;
}

#wpforms-payments .wpforms-admin-content .wpforms-education-page-button,
#wpforms-settings .wpforms-admin-content .wpforms-education-page-button {
  margin-top: 25px;
}

#wpforms-payments .wpforms-admin-content .wpforms-education-page .msg,
#wpforms-settings .wpforms-admin-content .wpforms-education-page .msg {
  background: #ffffff;
  line-height: 1.5em;
  font-size: 14px;
  color: #444444;
  margin: 20px 0 0 0;
  padding: 10px;
  border: 0;
}

#wpforms-payments .wpforms-admin-content .wpforms-education-page .msg.error,
#wpforms-settings .wpforms-admin-content .wpforms-education-page .msg.error {
  border-left: 4px solid #dc3232;
}

#wpforms-payments .wpforms-admin-content .wpforms-education-page .msg.info,
#wpforms-settings .wpforms-admin-content .wpforms-education-page .msg.info {
  border-left: 4px solid #22a0d0;
}

@media screen and (max-width: 1023px) {
  .wpforms-dyk {
    display: none !important;
  }
}

.wpforms-dyk td {
  background-color: #ffffff;
  border-left: 4px solid #056aab;
  border-top: 1px solid #c3c4c7;
  padding: 10px;
}

.wpforms-dyk .wpforms-dyk-fbox {
  align-items: center;
  align-content: stretch;
  justify-content: flex-start;
  display: flex;
  opacity: 1;
  transition: all .3s;
}

.wpforms-dyk .wpforms-dyk-fbox.out {
  opacity: 0;
  transform: scaleY(0);
}

.wpforms-dyk .wpforms-dyk-icon {
  width: 41px;
  height: 41px;
  margin-right: 10px;
}

.wpforms-dyk .wpforms-dyk-icon > svg,
.wpforms-dyk .wpforms-dyk-bulb {
  width: 25px;
  height: 25px;
  margin-right: 10px;
  border-radius: 50%;
  fill: #ffffff;
  background-color: #056aab;
  padding: 8px;
}

.wpforms-dyk .wpforms-dyk-message {
  font-size: 13px;
  font-weight: 400;
  color: #50575e;
}

.wpforms-dyk .wpforms-dyk-message strong {
  font-weight: 600;
  color: #2c3338;
}

.wpforms-dyk .wpforms-dyk-buttons {
  margin-left: auto;
  vertical-align: middle;
  min-width: 280px;
  text-align: right;
}

.wpforms-dyk .wpforms-dyk-buttons > a,
.wpforms-dyk .wpforms-dyk-buttons > button {
  vertical-align: middle;
  margin-left: 10px;
}

.wpforms-dyk .wpforms-dyk-buttons .wpforms-dismiss-button {
  border: none;
  padding: 0;
  background: 0 0;
  color: #a7aaad;
  cursor: pointer;
  margin-left: 6px;
}

.wpforms-dyk .wpforms-dyk-buttons .wpforms-dismiss-button:before {
  background: 0 0;
  content: "\f057";
  display: block;
  font: normal 16px/16px FontAwesome, sans-serif;
  speak: none;
  height: 16px;
  text-align: center;
  width: 16px;
  -webkit-font-smoothing: antialiased;
}

.wpforms-dyk .wpforms-dyk-buttons .wpforms-dismiss-button:hover {
  color: #dc3232;
}

.wpforms-dyk .wpforms-dyk-buttons .wpforms-dismiss-button:focus {
  outline: none;
}

.wpforms-dyk .wpforms-dyk-buttons .learn-more {
  text-decoration: underline;
}

.wpforms-dyk .wpforms-dyk-buttons .wpforms-btn-md {
  min-height: auto;
  font-size: 14px;
}

#wpforms-setting-row-email-heading .wpforms-setting-field {
  max-width: 100%;
}

.wpforms-smtp-education-notice {
  background: #ffffff;
  border-radius: 3px;
  margin: 30px 0 0;
  padding: 18px 20px 18px 78px;
  position: relative;
}

.wpforms-smtp-education-notice:before {
  content: '';
  display: block;
  width: 38px;
  height: 48px;
  position: absolute;
  left: 20px;
  top: 15px;
  background-image: url("../images/smtp/pattie.svg");
  background-size: 100%;
}

.wpforms-smtp-education-notice-title {
  font-weight: 600;
  font-size: 16px;
  line-height: 20px;
}

.wpforms-smtp-education-notice-description {
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #646970;
  margin-top: 5px;
}

.wpforms-smtp-education-notice-description a {
  color: #e27730;
  font-weight: 600;
}

.wpforms-smtp-education-notice-description a:hover {
  color: #cd6622;
}

.wpforms-smtp-education-notice-dismiss-button {
  position: absolute;
  right: 0;
  top: 0;
  padding: 5px;
  color: #a7aaad;
  cursor: pointer;
}

.wpforms-smtp-education-notice-dismiss-button:hover {
  color: #787c82;
}

.wpforms-loading-spinner {
  background-image: url("../images/spinner.svg");
  background-repeat: no-repeat;
  background-size: 15px 15px;
  background-position: center;
  display: block;
  height: 15px;
  margin: 0 10px 0 0;
  vertical-align: -2px;
  width: 15px;
  -webkit-animation: wpforms-spinner-rotation 0.8s linear infinite;
  -moz-animation: wpforms-spinner-rotation 0.8s linear infinite;
  -ms-animation: wpforms-spinner-rotation 0.8s linear infinite;
  -o-animation: wpforms-spinner-rotation 0.8s linear infinite;
  animation: wpforms-spinner-rotation 0.8s linear infinite;
}

.wpforms-loading-spinner.wpforms-loading-white {
  background-image: url("../images/spinner-white.svg");
}

.wpforms-loading-spinner.wpforms-loading-blue {
  background-image: url("../images/spinner-blue.svg");
  background-size: 14px 15px;
}

.wpforms-loading-spinner.wpforms-loading-md {
  background-size: 24px 24px;
  height: 24px;
  width: 24px;
}

.wpforms-loading-spinner.wpforms-loading-inline {
  display: inline-block;
  margin: auto;
}

@keyframes wpforms-spinner-rotation {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.wpforms-datepicker-popover-content .wpforms-datepicker-choices [type="radio"], .wpforms-datepicker-popover-content .wpforms-datepicker-calendar .form-control {
  border: none;
  clip: rect(1px, 1px, 1px, 1px);
  clip-path: inset(50%);
  height: 1px;
  margin: -1px;
  overflow: hidden;
  padding: 0;
  position: absolute;
  width: 1px;
  word-wrap: normal;
}

#wpforms-datepicker-popover-button {
  background: #ffffff;
  border-color: #8c8f94;
  color: #3c434a;
  display: flex;
  align-items: center;
  font-size: 14px;
  gap: 16px;
}

#wpforms-datepicker-popover-button:hover {
  color: #056aab;
}

#wpforms-datepicker-popover-button:focus-within, #wpforms-datepicker-popover-button:focus {
  color: #056aab;
  border-color: #056aab;
  box-shadow: 0 0 0 1px #056aab;
}

#wpforms-datepicker-popover-button::after {
  content: "";
  display: inline-block;
  height: 5px;
  width: 5px;
  border-style: solid;
  border-width: 1.5px;
  border-color: transparent #787c82 #787c82 transparent;
  transform: rotate(45deg);
  margin-top: -5px;
}

.wpforms-datepicker-popover {
  background: #ffffff;
  border-radius: 6px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
  display: none;
  margin-top: 9px;
  position: absolute;
  z-index: 30;
  right: 0;
  width: 416px;
  max-width: 80vw;
}

.wpforms-datepicker-popover-content {
  display: flex;
  flex-wrap: wrap;
}

.wpforms-datepicker-popover-content .wpforms-datepicker-choices {
  flex-grow: 1;
  padding: 20px;
  margin: 0;
  font-size: 14px;
  line-height: 14px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 15px;
}

@media (min-width: 601px) {
  .wpforms-datepicker-popover-content .wpforms-datepicker-choices {
    border-right: 1px solid #ccd0d4;
    margin-right: 4px;
  }
}

@media (max-width: 600px) {
  .wpforms-datepicker-popover-content .wpforms-datepicker-choices {
    border-bottom: 1px solid #ccd0d4;
  }
}

.wpforms-datepicker-popover-content .wpforms-datepicker-choices li {
  margin-bottom: 0;
}

.wpforms-datepicker-popover-content .wpforms-datepicker-choices label {
  display: block;
}

.wpforms-datepicker-popover-content .wpforms-datepicker-choices label:not(.is-selected):hover {
  color: #1d2327;
}

.wpforms-datepicker-popover-content .wpforms-datepicker-choices .is-selected {
  font-weight: 600;
  color: #e27730;
}

.wpforms-datepicker-popover-content .wpforms-datepicker-calendar {
  padding: 10px;
}

@media (min-width: 601px) {
  .wpforms-datepicker-popover-content .wpforms-datepicker-calendar {
    width: 245px;
  }
}

.wpforms-datepicker-popover-content .wpforms-datepicker-calendar .flatpickr-calendar {
  box-shadow: none;
  width: 100%;
}

.wpforms-datepicker-popover-content .wpforms-datepicker-calendar .flatpickr-calendar.inline {
  top: 0;
}

.wpforms-datepicker-popover-content .wpforms-datepicker-calendar .flatpickr-calendar * {
  box-sizing: border-box;
}

.wpforms-datepicker-popover-content .wpforms-datepicker-calendar .flatpickr-calendar .flatpickr-months {
  position: relative;
}

.wpforms-datepicker-popover-content .wpforms-datepicker-calendar .flatpickr-calendar .flatpickr-months .flatpickr-next-month,
.wpforms-datepicker-popover-content .wpforms-datepicker-calendar .flatpickr-calendar .flatpickr-months .flatpickr-prev-month {
  height: 24px;
  width: 24px;
  line-height: 17px;
  padding: 5px;
}

.wpforms-datepicker-popover-content .wpforms-datepicker-calendar .flatpickr-calendar .flatpickr-months .flatpickr-next-month svg,
.wpforms-datepicker-popover-content .wpforms-datepicker-calendar .flatpickr-calendar .flatpickr-months .flatpickr-prev-month svg {
  width: 12px;
  height: 12px;
  stroke: #2c3338;
  stroke-width: 1.5px;
}

.wpforms-datepicker-popover-content .wpforms-datepicker-calendar .flatpickr-calendar .flatpickr-month {
  height: 24px;
}

.wpforms-datepicker-popover-content .wpforms-datepicker-calendar .flatpickr-calendar .flatpickr-current-month {
  font-size: 14px;
  height: 24px;
  padding: 0;
}

.wpforms-datepicker-popover-content .wpforms-datepicker-calendar .flatpickr-calendar .flatpickr-current-month .cur-year,
.wpforms-datepicker-popover-content .wpforms-datepicker-calendar .flatpickr-calendar .flatpickr-current-month .flatpickr-monthDropdown-months {
  font-weight: 400;
  min-height: 24px;
  box-shadow: none;
}

@media (max-width: 782px) {
  #wpbody .wpforms-datepicker-popover-content .wpforms-datepicker-calendar .flatpickr-calendar .flatpickr-current-month .cur-year, #wpbody
  .wpforms-datepicker-popover-content .wpforms-datepicker-calendar .flatpickr-calendar .flatpickr-current-month .flatpickr-monthDropdown-months {
    font-size: inherit;
    height: auto;
  }
}

.wpforms-datepicker-popover-content .wpforms-datepicker-calendar .flatpickr-calendar .flatpickr-current-month .cur-year:hover,
.wpforms-datepicker-popover-content .wpforms-datepicker-calendar .flatpickr-calendar .flatpickr-current-month .flatpickr-monthDropdown-months:hover {
  background-color: #ffffff;
  color: #2c3338;
}

.wpforms-datepicker-popover-content .wpforms-datepicker-calendar .flatpickr-calendar .flatpickr-current-month .cur-year:focus,
.wpforms-datepicker-popover-content .wpforms-datepicker-calendar .flatpickr-calendar .flatpickr-current-month .flatpickr-monthDropdown-months:focus {
  border-width: 0 !important;
  box-shadow: none !important;
  color: inherit;
}

.wpforms-datepicker-popover-content .wpforms-datepicker-calendar .flatpickr-calendar .flatpickr-current-month .numInputWrapper span {
  right: -4px;
}

.wpforms-datepicker-popover-content .wpforms-datepicker-calendar .flatpickr-calendar .flatpickr-current-month .numInputWrapper:hover {
  background-color: #ffffff;
}

.wpforms-datepicker-popover-content .wpforms-datepicker-calendar .flatpickr-calendar .flatpickr-weekdays {
  height: 32px;
}

.wpforms-datepicker-popover-content .wpforms-datepicker-calendar .flatpickr-calendar .flatpickr-weekday {
  color: #787c82;
  font-weight: 600;
  font-size: 11px;
}

.wpforms-datepicker-popover-content .wpforms-datepicker-calendar .flatpickr-calendar .flatpickr-rContainer,
.wpforms-datepicker-popover-content .wpforms-datepicker-calendar .flatpickr-calendar .flatpickr-days,
.wpforms-datepicker-popover-content .wpforms-datepicker-calendar .flatpickr-calendar .dayContainer {
  width: 100%;
}

.wpforms-datepicker-popover-content .wpforms-datepicker-calendar .flatpickr-calendar .dayContainer {
  max-width: 100%;
  min-width: 100%;
}

.wpforms-datepicker-popover-content .wpforms-datepicker-calendar .flatpickr-calendar .today:is(:hover, :focus) {
  color: inherit;
}

.wpforms-datepicker-popover-content .wpforms-datepicker-calendar .flatpickr-calendar .flatpickr-day {
  display: flex;
  align-items: center;
  height: auto;
  line-height: 30px;
  margin: 0;
  aspect-ratio: 1;
  box-shadow: none;
  max-width: none;
  border-style: hidden;
}

.wpforms-datepicker-popover-content .wpforms-datepicker-calendar .flatpickr-calendar .flatpickr-day.inRange, .wpforms-datepicker-popover-content .wpforms-datepicker-calendar .flatpickr-calendar .flatpickr-day:focus, .wpforms-datepicker-popover-content .wpforms-datepicker-calendar .flatpickr-calendar .flatpickr-day:hover {
  background: #f0f0f1;
}

.wpforms-datepicker-popover-content .wpforms-datepicker-calendar .flatpickr-calendar .flatpickr-day.selected, .wpforms-datepicker-popover-content .wpforms-datepicker-calendar .flatpickr-calendar .flatpickr-day.startRange, .wpforms-datepicker-popover-content .wpforms-datepicker-calendar .flatpickr-calendar .flatpickr-day.endRange {
  background: #056aab;
  color: #ffffff;
}

.wpforms-datepicker-popover-content .wpforms-datepicker-calendar .flatpickr-calendar .flatpickr-day.startRange + .endRange {
  box-shadow: none;
}

.wpforms-datepicker-popover-content .wpforms-datepicker-calendar .flatpickr-calendar .flatpickr-disabled,
.wpforms-datepicker-popover-content .wpforms-datepicker-calendar .flatpickr-calendar .prevMonthDay,
.wpforms-datepicker-popover-content .wpforms-datepicker-calendar .flatpickr-calendar .nextMonthDay,
.wpforms-datepicker-popover-content .wpforms-datepicker-calendar .flatpickr-calendar .notAllowed {
  color: #c3c4c7;
}

.wpforms-datepicker-popover-content .wpforms-datepicker-action {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-end;
  gap: 10px;
  width: 100%;
  border-top: 1px solid #ccd0d4;
  padding: 15px 20px;
}

.wpforms-datepicker-popover-content .wpforms-datepicker-action button {
  box-shadow: none;
}

.wpforms-datepicker-popover-content .wpforms-datepicker-action .button-secondary {
  border-style: hidden;
  background-color: #ffffff;
  color: #056aab;
}

.wpforms-datepicker-popover-content .wpforms-datepicker-action .button-secondary:focus, .wpforms-datepicker-popover-content .wpforms-datepicker-action .button-secondary:hover {
  background-color: #ffffff;
  box-shadow: none;
  color: #04558a;
}

.rtl .wpforms-datepicker-popover {
  left: 0;
  right: auto;
}

.wpforms-card-image-overlay {
  aspect-ratio: 31/36;
  background-position: center;
  background-size: contain;
  border: 1px solid #c3c4c7;
  border-radius: 6px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  justify-content: center;
  order: -1;
  position: relative;
  transition: box-shadow .15s ease-in-out, border .15s ease-in-out;
  width: 100%;
  padding: 12px;
}

.wpforms-card-image-overlay:before {
  background-color: #ffffff;
  content: "";
  height: 100%;
  left: 0;
  opacity: 0;
  position: absolute;
  top: 0;
  width: 100%;
  border-radius: inherit;
  transition: opacity .15s ease-in-out;
}

.wpforms-card-image-overlay .wpforms-btn {
  opacity: 0;
  transition: all .05s ease-in-out, opacity .15s ease-in-out;
  width: 100%;
  z-index: 2;
}

.wpforms-card-image-group .wpforms-setting-field label {
  align-items: center;
  color: #50575e;
  display: flex;
  font-size: 14px;
  flex-wrap: wrap;
  gap: 12px 10px;
  justify-content: center;
  text-align: center;
}

.wpforms-card-image-group .wpforms-setting-field label:hover .wpforms-card-image-overlay {
  border: 2px solid #ffffff;
  box-shadow: 0 0 0 2px #50575e, 0 2px 4px 2px rgba(0, 0, 0, 0.07);
}

.wpforms-card-image-group .wpforms-setting-field label:hover .wpforms-card-image-overlay:before {
  opacity: .7;
}

.wpforms-card-image-group .wpforms-setting-field label:hover .wpforms-card-image-overlay .wpforms-btn {
  opacity: 1;
}

.wpforms-card-image-group .wpforms-setting-field input[type=radio] {
  display: none;
}

.wpforms-card-image-group .wpforms-setting-field input[type=radio]:checked + label {
  font-weight: 500;
  color: #2c3338;
}

.wpforms-card-image-group .wpforms-setting-field input[type=radio]:checked + label .wpforms-card-image-overlay {
  border: 2px solid #ffffff;
  box-shadow: 0 0 0 2px #e27730, 0 2px 4px 2px rgba(0, 0, 0, 0.07);
}

.wpforms-admin-single-navigation {
  display: flex;
  gap: 20px;
  align-items: center;
  color: #50575e;
  font-size: 14px;
  margin-inline-start: auto;
}

@media (max-width: 600px) {
  .wpforms-admin-single-navigation {
    display: none;
  }
}

.wpforms-admin-single-navigation .wpforms-admin-single-navigation-text {
  margin-right: -5px;
  color: #a7aaad;
}

.wpforms-admin-single-navigation .wpforms-admin-single-navigation-buttons {
  display: flex;
  align-items: center;
  border: 1px solid #8c8f94;
  border-radius: 4px;
  overflow: hidden;
}

.wpforms-admin-single-navigation .wpforms-admin-single-navigation-buttons .wpforms-admin-single-navigation-current {
  color: #32373c;
  flex: 1;
  min-height: 30px;
  min-width: 30px;
  padding-inline: 5px;
  border: 1px solid #8c8f94;
  border-top: none;
  border-bottom: none;
}

.wpforms-admin-single-navigation .wpforms-admin-single-navigation-buttons .wpforms-btn-grey {
  min-height: 30px;
  width: 30px;
  background-color: #f0f0f1;
  border: none;
  text-decoration: none;
}

.wpforms-admin-single-navigation .wpforms-admin-single-navigation-buttons .wpforms-btn-grey:hover, .wpforms-admin-single-navigation .wpforms-admin-single-navigation-buttons .wpforms-btn-grey:focus {
  background: #dcdcde;
}

.wpforms-admin-single-navigation .wpforms-admin-single-navigation-buttons .wpforms-btn-grey.inactive {
  background: rgba(240, 240, 241, 0.25);
  opacity: 1;
  pointer-events: none;
}

.wpforms-admin-single-navigation .wpforms-admin-single-navigation-buttons .wpforms-btn-grey.inactive .dashicons {
  opacity: 0.25;
}

.wpforms-admin-single-navigation .wpforms-admin-single-navigation-buttons .wpforms-btn-grey .dashicons {
  width: 16px;
  font-size: 16px;
  height: 16px;
  color: #50575e;
}

@media (max-width: 782px) {
  .wpforms-admin-single-navigation .wpforms-admin-single-navigation-buttons .wpforms-btn-grey {
    font-size: 13px;
  }
}

.wpforms-admin-single-navigation .wpforms-admin-single-navigation-buttons #wpforms-admin-single-navigation-prev-link,
.wpforms-admin-single-navigation .wpforms-admin-single-navigation-buttons #wpforms-admin-single-navigation-next-link,
.wpforms-admin-single-navigation .wpforms-admin-single-navigation-buttons .wpforms-admin-single-navigation-current {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}

.wpforms-admin-single-navigation .wpforms-admin-single-navigation-buttons .wpforms-admin-single-navigation-current {
  width: 100%;
  min-width: 30px;
  padding: 0 10px;
  text-decoration: none;
  background: #ffffff;
  font-size: 14px;
  color: #50575e;
  box-sizing: border-box;
  z-index: 1;
}

.wpforms-overview-chart {
  background-color: #ffffff;
  border: 1px solid #ccd0d4;
  border-radius: 4px;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  position: relative;
  overflow: hidden;
}

.wpforms-overview-chart .spinner {
  background-color: #ffffff;
  background-position: center calc(50% - 12px);
  background-repeat: no-repeat;
  bottom: 0;
  height: 320px;
  left: 0;
  margin: 0;
  opacity: 1;
  position: absolute;
  visibility: visible;
  width: 100%;
  z-index: 30;
}

.wpforms-overview-chart-notice {
  background: radial-gradient(50% 50% at 50% 50%, rgba(0, 0, 0, 0) 0, #ffffff 100%);
  bottom: 0;
  height: 320px;
  left: 0;
  position: absolute;
  width: 100%;
  z-index: 20;
}

.wpforms-overview-chart-notice-content {
  background: #ffffff;
  border-radius: 6px;
  box-sizing: border-box;
  box-shadow: 0 5px 25px rgba(0, 0, 0, 0.15);
  left: 50%;
  padding: 20px;
  position: absolute;
  text-align: center;
  top: 50%;
  transform: translate(-50%, -50%);
  width: Min(365px, 90%);
}

.wpforms-overview-chart-notice-content h2 {
  font-size: 20px;
  font-weight: 600;
  line-height: 1.3;
  margin-top: 0;
  margin-bottom: 10px;
}

.wpforms-overview-chart-notice-content p {
  color: #787c82;
  font-size: 14px;
  line-height: 18px;
  margin: 0;
}

.wpforms-overview-chart-heading {
  background-color: #f6f6f6;
  border-bottom: 1px solid #eeeeee;
  color: #3c434a;
  font-size: 14px;
  font-weight: 600;
  line-height: 17px;
  margin-block: 0;
  padding: 13px;
}

.wpforms-overview-chart-canvas {
  background-color: #ffffff;
  box-sizing: border-box;
  height: 324px;
  position: relative;
}

.wpforms-overview-chart-settings .wpforms-dash-widget-settings-button {
  background: #f6f7f7;
  border-color: #8c8f94;
  color: #50575e;
  padding: 5px;
  width: 32px;
}

.wpforms-overview-chart-settings .wpforms-dash-widget-settings-button:hover {
  color: #2c3338;
  border-color: #50575e;
  background: #f6f7f7;
}

.wpforms-overview-chart-settings .wpforms-dash-widget-settings-button:focus-within, .wpforms-overview-chart-settings .wpforms-dash-widget-settings-button:focus {
  color: #2c3338;
  border-color: #50575e;
  box-shadow: 0 0 0 1px #50575e;
}

.wpforms-overview-chart-settings .wpforms-dash-widget-settings-button .dashicons {
  font-size: 19px;
  line-height: 17px;
  height: 17px;
  width: 17px;
}

.wpforms-overview-chart-settings .wpforms-dash-widget-settings-container {
  position: relative;
}

.wpforms-overview-chart-settings .wpforms-dash-widget-settings-menu {
  background: #ffffff;
  border-radius: 6px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
  display: none;
  margin-top: 9px;
  width: 140px;
  position: absolute;
  z-index: 30;
  right: 0;
  padding: 10px;
}

.wpforms-overview-chart-settings .wpforms-dash-widget-settings-menu h4 {
  font-size: 11px;
  font-weight: 600;
  line-height: 13px;
  margin: 0 0 8px;
  text-transform: uppercase;
}

.wpforms-overview-chart-settings .wpforms-dash-widget-settings-menu [type="radio"] {
  margin-top: 0;
}

@media (max-width: 782px) {
  .wpforms-overview-chart-settings .wpforms-dash-widget-settings-menu [type="radio"] {
    height: 16px;
    width: 16px;
  }
  .wpforms-overview-chart-settings .wpforms-dash-widget-settings-menu [type="radio"]:checked::before {
    margin: 2.5px;
  }
}

.wpforms-overview-chart-settings .wpforms-dash-widget-settings-menu-wrap {
  border-bottom: 1px solid #dcdcde;
  margin-bottom: 10px;
  padding-bottom: 10px;
}

.wpforms-overview-chart-settings .wpforms-dash-widget-settings-menu-item:nth-child(n+2) {
  margin-top: 5px;
}

.rtl .wpforms-overview-chart-settings .wpforms-dash-widget-settings-button {
  margin-left: 5px;
  margin-right: 0;
}

.rtl .wpforms-overview-chart-settings .wpforms-dash-widget-settings-menu {
  left: 0;
  right: auto;
}

.wpforms-overview-top-bar {
  align-items: center;
  display: flex;
  gap: 30px;
  justify-content: space-between;
  margin-bottom: 20px;
}

@media (max-width: 782px) {
  .wpforms-overview-top-bar {
    flex-wrap: wrap;
  }
}

.wpforms-overview-top-bar .button {
  line-height: 17px;
  min-height: 30px;
}

.wpforms-overview-top-bar-heading {
  align-items: center;
  display: flex;
  gap: 8px;
}

.wpforms-overview-top-bar-heading h2 {
  color: #3c434a;
  font-size: 18px;
  font-weight: 600;
  margin: 0;
}

.wpforms-overview-top-bar-heading .dashicons {
  cursor: pointer;
  padding: 0;
}

.wpforms-overview-top-bar-filter-form {
  position: relative;
}

.wpforms-overview-top-bar-filters {
  display: flex;
  gap: 10px;
  margin-inline-start: auto;
}

.wpforms-overview-top-bar-filters .button {
  box-shadow: none;
}

.wpforms-overview-top-bar-filters .wpforms-toggle-control {
  align-items: center;
  color: #50575e;
  display: flex;
  gap: 10px;
  padding-inline-end: 10px;
  margin: 0;
}

.wpforms-overview-top-bar-filters .wpforms-toggle-control .wpforms-toggle-control-icon::after {
  transition: none;
}

.wpforms-overview-top-bar-filters .wpforms-toggle-control [type=checkbox]:checked + .wpforms-toggle-control-icon {
  background-color: #e27730;
}

.wpforms-admin-wrap p.search-box {
  display: inline-flex;
  flex-wrap: wrap;
  gap: 6px;
  height: auto;
  margin-bottom: 7px;
  position: relative;
}

.wpforms-admin-wrap p.search-box input,
.wpforms-admin-wrap p.search-box select {
  margin: 0;
}

@media (max-width: 600px) {
  .wpforms-admin-wrap p.search-box input,
  .wpforms-admin-wrap p.search-box select {
    min-width: 100%;
  }
}

.wpforms-admin-wrap p.search-box input[name="s"] {
  flex-grow: 1;
  width: 180px;
}

.wpforms-admin-wrap p.search-box [type="submit"] {
  font-size: 14px;
  line-height: 17px;
}

@media (max-width: 782px) {
  .wpforms-admin-wrap p.search-box {
    width: 100%;
  }
}

.wpforms-admin-wrap .tablenav {
  color: #444444;
  padding-top: 3px;
}

.wpforms-admin-wrap .tablenav .displaying-num {
  margin-inline-end: 6px;
}

.wpforms-admin-wrap .tablenav .paging-input {
  align-items: center;
  display: inline-flex;
  gap: 4px;
}

.wpforms-admin-wrap .tablenav .button {
  box-shadow: none;
}

.wpforms-admin-wrap .tablenav .total-pages,
.wpforms-admin-wrap .tablenav #table-paging {
  margin: 0;
}

.wpforms-admin-wrap .tablenav-pages .pagination-links {
  align-items: center;
  display: inline-flex;
  gap: 4px;
}

.wpforms-admin-wrap .tablenav-pages .current-page {
  border-radius: 3px;
  width: 38px;
  margin: 0;
}

.wpforms-admin-wrap .tablenav-pages .tablenav-paging-text {
  max-width: 80px;
  padding: 0 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.wpforms-admin-wrap.wpforms-entries-spam-empty .tablenav {
  display: none;
}

.wpforms-admin-wrap.wpforms-entries-spam-empty .subsubsub {
  margin-bottom: 18px;
}

.rtl .wpforms-admin-wrap .tablenav-pages .current-page {
  order: 1;
}

table.wpforms-table-list {
  border-color: #ccd0d4;
  border-radius: 4px;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
}

table.wpforms-table-list td {
  line-height: 16px;
  vertical-align: middle;
}

table.wpforms-table-list td.column-name {
  font-weight: 600;
  min-height: 25px;
}

table.wpforms-table-list td[data-colname]::before {
  box-sizing: border-box;
  padding-inline-end: 10px;
}

table.wpforms-table-list th {
  line-height: 17px;
  text-transform: capitalize;
}

table.wpforms-table-list tfoot th,
table.wpforms-table-list thead th {
  border-color: #ccd0d4;
  color: #32373c;
  padding: 9px 12px;
}

table.wpforms-table-list tfoot td.check-column,
table.wpforms-table-list thead td.check-column {
  border-color: #ccd0d4;
  padding-block: 10px;
  width: 1%;
}

table.wpforms-table-list tbody td {
  padding: 12px;
}

table.wpforms-table-list tbody th.check-column {
  padding-block: 14px;
  vertical-align: middle;
  width: 1%;
}

table.wpforms-table-list .check-column .label-covers-full-cell:hover + input[type="checkbox"] {
  box-shadow: none;
}

table.wpforms-table-list button {
  cursor: pointer;
}

table.wpforms-table-list .column-date span[title] {
  text-decoration: none;
}

table.wpforms-table-list.striped > tbody > :nth-child(odd) {
  background-color: #f6f6f6;
}

table.wpforms-table-list.striped > tbody > tr.is-selected {
  background-color: rgba(34, 113, 177, 0.12);
}

table.wpforms-table-list .sorted a,
table.wpforms-table-list .sortable a {
  display: flex;
  padding: 9px 17px 9px 12px;
}

table.wpforms-table-list .sorting-indicator {
  inset-inline-start: 15px;
  margin-inline-start: -10px;
  position: relative;
}

table.wpforms-table-list .toggle-row {
  height: 100%;
  top: 0;
}

table.wpforms-table-list .toggle-row::before {
  inset-inline-start: auto;
  inset-inline-end: 4px;
  top: calc(50% - 11px);
}

@media (max-width: 1024px) {
  table.wpforms-table-list.has-many-columns .check-column, table.wpforms-table-list.has-few-columns .check-column {
    vertical-align: top;
  }
  table.wpforms-table-list.has-many-columns th.column-primary ~ th, table.wpforms-table-list.has-few-columns th.column-primary ~ th {
    display: none;
  }
  table.wpforms-table-list.has-many-columns td.column-primary, table.wpforms-table-list.has-few-columns td.column-primary {
    display: block;
    position: relative;
    padding-inline-end: 50px;
  }
  table.wpforms-table-list.has-many-columns td.column-primary ~ td, table.wpforms-table-list.has-few-columns td.column-primary ~ td {
    display: none;
    padding: 3px 8px 3px 35%;
  }
  table.wpforms-table-list.has-many-columns td.column-primary .toggle-row, table.wpforms-table-list.has-few-columns td.column-primary .toggle-row {
    display: block;
  }
  table.wpforms-table-list.has-many-columns td.column-primary > *, table.wpforms-table-list.has-few-columns td.column-primary > * {
    vertical-align: sub;
  }
  table.wpforms-table-list.has-many-columns .is-expanded td:not(.column-primary):not(.hidden), table.wpforms-table-list.has-few-columns .is-expanded td:not(.column-primary):not(.hidden) {
    display: block;
    position: relative;
    text-align: right;
  }
  table.wpforms-table-list.has-many-columns .is-expanded td:not(.column-primary):nth-child(n+2), table.wpforms-table-list.has-few-columns .is-expanded td:not(.column-primary):nth-child(n+2) {
    margin-bottom: 10px;
  }
  table.wpforms-table-list.has-many-columns .is-expanded td:not(.column-primary)[data-colname]::before, table.wpforms-table-list.has-few-columns .is-expanded td:not(.column-primary)[data-colname]::before {
    content: attr(data-colname);
    display: block;
    inset-inline-start: 10px;
    overflow: hidden;
    position: absolute;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 32%;
    text-align: left;
  }
}

@media (max-width: 782px) {
  table.wpforms-table-list tr .check-column:is(th, td) {
    vertical-align: top;
  }
  table.wpforms-table-list tr .check-column:is(th, td) [type="checkbox"] {
    height: 16px;
    margin-bottom: 0;
    width: 16px;
  }
  table.wpforms-table-list tr .check-column:is(th, td) [type="checkbox"]::before {
    height: 20px;
    margin: -2px 0 0px -4px;
    width: 20px;
  }
  table.wpforms-table-list td.column-primary {
    display: block;
  }
  table.wpforms-table-list td.column-primary > * {
    vertical-align: sub;
  }
  table.wpforms-table-list .is-expanded td:not(.column-primary):nth-child(n+2) {
    margin-bottom: 10px;
  }
  table.wpforms-table-list .is-expanded td:not(.column-primary)::before {
    margin-inline-start: 2px;
  }
}

table.forms .check-column .label-covers-full-cell:hover + input[type="checkbox"],
table.action-scheduler .check-column .label-covers-full-cell:hover + input[type="checkbox"] {
  box-shadow: none;
}

.wpforms-tabs-wrapper {
  padding: 0 20px;
  margin-bottom: 20px;
}

.wpforms-tabs-wrapper .nav-tab-wrapper {
  padding-top: 0;
}

#wpforms-payments .nav-tab-wrapper .nav-tab {
  background: rgba(220, 220, 222, 0.5);
  border: 1px solid #c3c4c7;
  border-bottom: none;
  border-radius: 3px 3px 0 0;
  padding: 10px 12px;
  font-weight: 600;
  font-size: 13px;
  line-height: 16px;
  color: #2d2d2d;
}

#wpforms-payments .nav-tab-wrapper .nav-tab:hover, #wpforms-payments .nav-tab-wrapper .nav-tab:focus {
  background-color: #ffffff;
  box-shadow: none;
}

#wpforms-payments .nav-tab-wrapper .nav-tab.nav-tab-active {
  background: #f0f0f1;
  border-bottom: 1px solid #f0f0f1;
}

@media (max-width: 781px) {
  #wpforms-payments .nav-tab-wrapper {
    border-bottom: 1px solid #c3c4c7;
  }
  #wpforms-payments .nav-tab-wrapper .nav-tab {
    margin-bottom: -1px;
  }
}

#wpforms-admin-about *,
#wpforms-admin-about *::before,
#wpforms-admin-about *::after {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

#wpforms-admin-about .wpforms-admin-about-section {
  margin: 0 20px 20px;
  padding: 30px;
  background: #ffffff;
  border: 1px solid #dddddd;
  line-height: 2;
}

#wpforms-admin-about .wpforms-admin-about-section h1,
#wpforms-admin-about .wpforms-admin-about-section h2,
#wpforms-admin-about .wpforms-admin-about-section h3,
#wpforms-admin-about .wpforms-admin-about-section h4,
#wpforms-admin-about .wpforms-admin-about-section h5 {
  margin-top: 0;
  padding-top: 0;
  line-height: 1.6;
}

#wpforms-admin-about .wpforms-admin-about-section h2 {
  font-size: 24px;
}

#wpforms-admin-about .wpforms-admin-about-section h3 {
  font-size: 18px;
  margin-bottom: 30px;
  color: #23282c;
}

#wpforms-admin-about .wpforms-admin-about-section ul,
#wpforms-admin-about .wpforms-admin-about-section p {
  font-size: 16px;
}

#wpforms-admin-about .wpforms-admin-about-section p {
  margin-bottom: 20px;
}

#wpforms-admin-about .wpforms-admin-about-section p.bigger {
  font-size: 18px;
}

#wpforms-admin-about .wpforms-admin-about-section p.smaller {
  font-size: 14px;
}

#wpforms-admin-about .wpforms-admin-about-section p:last-child {
  margin-bottom: 0;
}

#wpforms-admin-about .wpforms-admin-about-section hr {
  margin: 30px 0;
}

#wpforms-admin-about .wpforms-admin-about-section figure {
  margin: 0;
}

#wpforms-admin-about .wpforms-admin-about-section figure img {
  width: 100%;
}

#wpforms-admin-about .wpforms-admin-about-section figure figcaption {
  font-size: 14px;
  color: #888888;
  margin-top: 5px;
  text-align: center;
  line-height: initial;
}

#wpforms-admin-about .wpforms-admin-about-section .wpforms-admin-column-40 {
  padding-left: 15px;
}

@media (max-width: 767px) {
  #wpforms-admin-about .wpforms-admin-about-section .wpforms-admin-column-40 {
    width: 100%;
    padding-left: 0;
    padding-top: 20px;
  }
}

#wpforms-admin-about .wpforms-admin-about-section .wpforms-admin-column-60 {
  padding-right: 15px;
}

@media (max-width: 767px) {
  #wpforms-admin-about .wpforms-admin-about-section .wpforms-admin-column-60 {
    width: 100%;
    padding-right: 0;
  }
}

#wpforms-admin-about .wpforms-admin-about-section ul.list-plain {
  margin-top: 0;
  margin-bottom: 0;
}

#wpforms-admin-about .wpforms-admin-about-section ul.list-plain li {
  margin-bottom: 0;
}

#wpforms-admin-about .wpforms-admin-about-section ul.list-features li .fa {
  color: #2a9b39;
  margin: 0 8px 0 0;
}

#wpforms-admin-about .wpforms-admin-about-section .fa-star {
  color: gold;
}

#wpforms-admin-about .wpforms-admin-about-section .no-margin {
  margin: 0 !important;
}

#wpforms-admin-about .wpforms-admin-about-section .no-padding {
  padding: 0 !important;
}

#wpforms-admin-about .wpforms-admin-about-section .centered {
  text-align: center !important;
}

#wpforms-admin-about .wpforms-admin-about-section-first-form {
  display: flex;
}

@media (max-width: 767px) {
  #wpforms-admin-about .wpforms-admin-about-section-first-form {
    display: block !important;
  }
}

#wpforms-admin-about .wpforms-admin-about-section-first-form .wpforms-admin-about-section-first-form-text {
  flex: 1;
  padding-right: 30px;
}

@media (max-width: 767px) {
  #wpforms-admin-about .wpforms-admin-about-section-first-form .wpforms-admin-about-section-first-form-text {
    flex: none;
  }
}

#wpforms-admin-about .wpforms-admin-about-section-first-form .wpforms-admin-about-section-first-form-video iframe {
  border: 1px solid #dddddd;
}

@media (max-width: 767px) {
  #wpforms-admin-about .wpforms-admin-about-section-first-form .wpforms-admin-about-section-first-form-video iframe {
    max-width: 100%;
  }
}

@media (max-width: 767px) {
  #wpforms-admin-about .wpforms-admin-about-section-first-form .wpforms-admin-about-section-first-form-video {
    padding-top: 20px;
  }
}

#wpforms-admin-about .wpforms-admin-about-section-hero {
  padding: 0;
}

#wpforms-admin-about .wpforms-admin-about-section-hero .wpforms-admin-about-section-hero-main,
#wpforms-admin-about .wpforms-admin-about-section-hero .wpforms-admin-about-section-hero-extra {
  padding: 30px;
}

@media (max-width: 767px) {
  #wpforms-admin-about .wpforms-admin-about-section-hero .wpforms-admin-about-section-hero-extra .wpforms-admin-column-50 {
    float: none;
    width: 100%;
  }
}

#wpforms-admin-about .wpforms-admin-about-section-hero .wpforms-admin-about-section-hero-main {
  border-bottom: 1px solid #dddddd;
}

#wpforms-admin-about .wpforms-admin-about-section-hero .wpforms-admin-about-section-hero-main.no-border {
  border-bottom: 0;
}

#wpforms-admin-about .wpforms-admin-about-section-hero .wpforms-admin-about-section-hero-main p {
  color: #666;
}

#wpforms-admin-about .wpforms-admin-about-section-hero h3.call-to-action {
  margin-bottom: -10px;
}

#wpforms-admin-about .wpforms-admin-about-section-hero span.price-20-off {
  color: #6ab255;
}

#wpforms-admin-about .wpforms-admin-about-section-squashed {
  margin-bottom: 0;
}

#wpforms-admin-about .wpforms-admin-about-section-squashed:not(:last-of-type) {
  border-bottom: 0;
}

#wpforms-admin-about .wpforms-admin-about-section-post h2 {
  margin-bottom: -10px;
}

#wpforms-admin-about .wpforms-admin-about-section-post h3 {
  margin-bottom: 15px;
}

#wpforms-admin-about .wpforms-admin-about-section-post p:last-of-type {
  margin-bottom: 30px;
}

#wpforms-admin-about .wpforms-admin-about-section-post .wpforms-admin-column-20 {
  padding-right: 20px;
  width: auto;
}

#wpforms-admin-about .wpforms-admin-about-section-post .wpforms-admin-column-20 img {
  width: 270px;
}

@media (max-width: 767px) {
  #wpforms-admin-about .wpforms-admin-about-section-post .wpforms-admin-column-20 {
    width: 20%;
  }
  #wpforms-admin-about .wpforms-admin-about-section-post .wpforms-admin-column-20 img {
    width: auto;
    max-width: 100%;
  }
}

#wpforms-admin-about .wpforms-admin-about-section-post .wpforms-admin-column-80 {
  padding-left: 20px;
  width: calc(100% - 20px - 270px);
}

@media (max-width: 767px) {
  #wpforms-admin-about .wpforms-admin-about-section-post .wpforms-admin-column-80 {
    width: 80%;
  }
}

#wpforms-admin-about .wpforms-admin-about-section-post .wpforms-admin-about-section-post-link {
  padding: 10px 15px;
  background-color: #df7739;
  color: #fff;
  border-radius: 3px;
  text-decoration: none;
  margin-top: 15px;
  font-size: 14px;
}

#wpforms-admin-about .wpforms-admin-about-section-post .wpforms-admin-about-section-post-link:hover, #wpforms-admin-about .wpforms-admin-about-section-post .wpforms-admin-about-section-post-link:focus {
  background-color: #b85a1b;
  color: #fff;
}

#wpforms-admin-about .wpforms-admin-about-section-post .wpforms-admin-about-section-post-link:focus {
  box-shadow: 0 0 0 1px #fff, 0 0 0 3px #b85a1b;
  outline: 0;
}

#wpforms-admin-about .wpforms-admin-about-section-post .wpforms-admin-about-section-post-link .fa {
  color: #edba9e;
  vertical-align: middle;
  margin-left: 8px;
}

#wpforms-admin-about .wpforms-admin-about-section-table {
  background-color: #fafafa;
  overflow-x: auto;
}

#wpforms-admin-about .wpforms-admin-about-section-table table {
  background-color: #ffffff;
  border-top: 1px solid #dddddd;
  border-collapse: collapse;
}

#wpforms-admin-about .wpforms-admin-about-section-table table tr td {
  border-bottom: 1px solid #dddddd;
  border-right: 1px solid #dddddd;
  padding: 30px;
  vertical-align: top;
}

@media (max-width: 767px) {
  #wpforms-admin-about .wpforms-admin-about-section-table table tr td {
    padding: 15px;
  }
}

#wpforms-admin-about .wpforms-admin-about-section-table table tr td:last-of-type {
  border-right: 0;
}

#wpforms-admin-about .wpforms-admin-about-section-table table tr:last-child td {
  border-bottom: none;
}

#wpforms-admin-about .wpforms-admin-about-section-table table p {
  background-repeat: no-repeat;
  background-size: 15px auto;
  background-position: 0 6px;
  margin: 0;
}

#wpforms-admin-about .wpforms-admin-about-section-table table p.features-full {
  padding-left: 30px;
  background-image: url(../images/about/icon-full.svg);
}

#wpforms-admin-about .wpforms-admin-about-section-table table p.features-none {
  padding-left: 30px;
  background-image: url(../images/about/icon-none.svg);
}

#wpforms-admin-about .wpforms-admin-about-section-table table p.features-partial {
  padding-left: 30px;
  background-position: -3px 0;
  background-size: 23px auto;
  background-image: url(../images/about/icon-partial.svg);
}

#wpforms-admin-about .wpforms-admin-about-section-table .wpforms-admin-about-section-hero-main {
  padding: 0;
}

#wpforms-admin-about .wpforms-admin-about-section-table .wpforms-admin-about-section-hero-main h3 {
  padding: 30px 30px 30px 60px;
}

#wpforms-admin-about .wpforms-admin-about-section-table .wpforms-admin-about-section-hero-main .wpforms-admin-column-33:first-child h3 {
  padding: 30px;
}

#wpforms-admin-about #wpforms-admin-addons {
  padding: 0 30px;
}

#wpforms-admin-about #wpforms-admin-addons .addon-container {
  padding: 0 10px;
}

#wpforms-admin-about #wpforms-admin-addons .addon-item .details {
  padding: 20px;
}

#wpforms-admin-about #wpforms-admin-addons .addon-item h5 {
  margin-bottom: 10px;
}

#wpforms-admin-about #wpforms-admin-addons .addon-item img {
  padding: 10px;
}

#wpforms-admin-about #wpforms-admin-addons .addon-item img[src*="-mi"] {
  padding: 13px;
}

#wpforms-admin-about #wpforms-admin-addons .addon-item .action-button .button.disabled, #wpforms-admin-about #wpforms-admin-addons .addon-item .action-button .button.loading {
  cursor: default;
}

.wpforms-addons-header {
  padding: 15px 20px !important;
  background: #ffffff;
  margin-bottom: 20px !important;
  display: flex !important;
  align-items: center;
  justify-content: space-between;
}

@media (max-width: 599px) {
  .wpforms-addons-header {
    flex-direction: column;
    align-items: flex-start;
  }
}

.wpforms-addons-header-title {
  font-size: 22px;
  font-weight: 400;
  color: #101517;
  display: flex;
  gap: 20px;
  align-items: center;
}

@media (max-width: 599px) {
  .wpforms-addons-header-title {
    margin-bottom: 20px;
  }
}

.wpforms-addons-header-search {
  position: relative;
}

@media (max-width: 599px) {
  .wpforms-addons-header-search {
    width: 100%;
  }
}

.wpforms-addons-header-search:before {
  font-family: FontAwesome, sans-serif;
  content: "\f002";
  color: #a7aaad;
  font-size: 16px;
  text-align: center;
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
}

.wpforms-addons-header-search #wpforms-addons-search {
  width: 250px;
  height: 36px;
  padding: 10px 10px 10px 30px;
  font-size: 14px;
  font-weight: 400;
}

.wpforms-addons-header-search #wpforms-addons-search::placeholder {
  color: #a7aaad;
}

@media (max-width: 599px) {
  .wpforms-addons-header-search #wpforms-addons-search {
    width: 100%;
  }
}

.wpforms-addons-list-section:first-child {
  margin-bottom: 20px;
}

.wpforms-addons-list-section-title {
  font-size: 16px;
  font-weight: 500;
  color: #101517;
  margin-bottom: 20px;
  margin-top: 0;
}

.wpforms-addons-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  grid-gap: 20px;
}

@media (max-width: 782px) {
  .wpforms-addons-list {
    grid-template-columns: 1fr;
  }
}

.wpforms-addons-list-item {
  margin: 0;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  overflow: hidden;
  align-items: flex-start;
  border-radius: 6px;
  border: 1px solid #c3c4c7;
  background: #ffffff;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.07);
}

.wpforms-addons-list-item-header {
  padding: 20px;
  display: flex;
  gap: 20px;
}

.wpforms-addons-list-item-header img {
  width: 80px;
  height: 80px;
  max-width: 80px !important;
  border: 1px solid rgba(195, 196, 199, 0.5);
  border-radius: 6px;
}

.wpforms-addons-list-item-header-meta-title {
  margin-bottom: 5px;
  display: flex;
  align-items: center;
  gap: 10px;
}

@media (max-width: 600px) {
  .wpforms-addons-list-item-header-meta-title {
    align-items: flex-start;
    flex-direction: column;
  }
}

.wpforms-addons-list-item-header-meta-title a {
  color: #2c3338;
  font-size: 16px;
  font-weight: 500;
  line-height: 22px;
  text-decoration-line: underline;
  text-underline-offset: 2px;
}

.wpforms-addons-list-item-header-meta-excerpt {
  color: #646970;
  font-size: 14px;
  font-weight: 400;
  line-height: 18px;
}

.wpforms-addons-list-item-error-msg {
  color: #b32d2e;
  font-weight: 500;
}

.wpforms-addons-list-item-footer {
  padding: 20px;
  display: flex;
  background: #f6f6f6;
  width: 100%;
  justify-content: space-between;
  box-sizing: border-box;
  align-items: center;
  min-height: 70px;
}

.wpforms-addons-list-item-footer-missing .wpforms-toggle-control {
  display: none;
}

.wpforms-addons-list-item-footer-active .wpforms-addons-list-item-footer-settings-link {
  display: block;
}

.wpforms-addons-list-item-footer-with-error .wpforms-addons-list-item-footer-actions {
  display: none;
}

.wpforms-addons-list-item-footer-settings-link {
  display: none;
}

.wpforms-addons-list-item-footer-error {
  width: 100%;
  color: #646970;
}

.wpforms-addons-list-item-footer-error p {
  margin: 0;
}

.wpforms-addons #wpforms-addons-list-section-all .wpforms-addons-list .wpforms-addons-list-item.has-badge {
  border: 2px solid #8c8f94;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.15);
}

.wpforms-addons .wpforms-notice.notice-info {
  padding: 20px;
  border: none;
  margin-bottom: 20px;
  border-radius: 6px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.07);
}

.wpforms-addons .wpforms-notice.notice-info strong {
  color: #1d2327;
  font-size: 20px;
  font-weight: 500;
  line-height: 18px;
}

.wpforms-addons .wpforms-notice.notice-info p {
  margin-top: 0;
  font-size: 15px;
  font-weight: 400;
  line-height: 20px;
  color: rgba(44, 51, 56, 0.8);
}

.wpforms-addons .wpforms-notice.notice-info p:last-child {
  margin-bottom: 0;
}

.wpforms-addons .wpforms-notice.notice-info p.notice-title {
  margin-bottom: 0;
  color: #1d2327;
}

.wpforms-addons .wpforms-notice.notice-info .notice-buttons {
  display: flex;
  gap: 10px;
}

@media (max-width: 782px) {
  .wpforms-addons .wpforms-notice {
    margin: 20px !important;
  }
}

.wpforms-addons .wpforms-notice .wpforms-btn {
  box-sizing: border-box;
}

.wpforms-addons #wpforms-addons-no-results {
  display: none;
  font-size: 14px;
}

.wpforms-addons .wpforms-addons-link {
  color: #a7aaad;
  text-align: center;
  font-size: 13px;
  font-weight: 400;
  line-height: 20px;
  text-decoration-line: underline;
}

.wpforms-addons .wpforms-addons-link:hover {
  color: #2c3338;
}

#wpforms-admin-addons *,
#wpforms-admin-addons *::before,
#wpforms-admin-addons *::after {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

#wpforms-admin-addons #wpforms-admin-addons-list .list {
  display: flex;
  flex-wrap: wrap;
  align-items: stretch;
  margin-left: -20px;
  margin-right: -20px;
}

#wpforms-admin-addons #wpforms-admin-addons-list .list .action-button button {
  background: none;
  border: 1px solid #ddd;
  border-radius: 3px;
  box-shadow: none;
  font-weight: 600;
  width: 140px;
  text-align: center;
  padding: 8px 5px;
}

#wpforms-admin-addons #wpforms-admin-addons-list .list .action-button button:hover, #wpforms-admin-addons #wpforms-admin-addons-list .list .action-button button:focus, #wpforms-admin-addons #wpforms-admin-addons-list .list .action-button button.loading {
  background-color: #e9e9e9;
}

#wpforms-admin-addons #wpforms-admin-addons-list .list .action-button button:focus {
  border-color: #2271b1;
  box-shadow: 0 0 0 1px #2271b1;
  outline: none;
}

#wpforms-admin-addons .addons-container {
  display: flex;
  flex-wrap: wrap;
  align-items: stretch;
  margin-left: -20px;
  margin-right: -20px;
}

#wpforms-admin-addons .unlock-msg {
  padding: 0;
  margin-top: -20px;
  clear: both;
}

#wpforms-admin-addons .unlock-msg h4 {
  margin: 1.5em 0 8px;
}

#wpforms-admin-addons .unlock-msg p {
  margin: 0 0 1.5em;
}

#wpforms-admin-addons #wpforms-admin-addons-search {
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 3px;
  box-shadow: none;
  color: #333;
  vertical-align: middle;
  padding: 2px 12px;
  margin: -2px 10px 0 0;
  width: 200px;
  min-height: 30px;
  margin-inline-start: auto;
}

#wpforms-admin-addons #wpforms-admin-addons-search:focus {
  border-color: #bbb;
}

#wpforms-admin-addons .addon-container {
  padding: 0 20px;
  width: 33.333333%;
  margin-bottom: 20px;
}

@media (max-width: 1249px) {
  #wpforms-admin-addons .addon-container {
    width: 50%;
  }
}

@media (max-width: 767px) {
  #wpforms-admin-addons .addon-container {
    width: 100%;
  }
}

#wpforms-admin-addons h4 {
  font-size: 17px;
  font-weight: 700;
}

#wpforms-admin-addons .addon-item {
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 6px;
  margin: 0;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  overflow: hidden;
}

#wpforms-admin-addons .addon-item img {
  border: 1px solid #eee;
  float: left;
  max-width: 75px;
}

#wpforms-admin-addons .addon-item h5 {
  margin: 0 0 0 100px;
  font-size: 16px;
}

#wpforms-admin-addons .addon-item h5 a {
  color: #444;
  display: inline-block;
  margin: 0 10px 10px 0;
}

#wpforms-admin-addons .addon-item h5 a:hover {
  color: #006799;
}

#wpforms-admin-addons .addon-item p {
  margin: 0 0 0 100px;
}

#wpforms-admin-addons .addon-item .details {
  padding: 30px 20px;
}

#wpforms-admin-addons .addon-item .actions {
  display: flex;
  align-items: center;
  background-color: #f7f7f7;
  border-top: 1px solid #ddd;
  padding: 20px;
  min-height: 75px;
  position: relative;
}

#wpforms-admin-addons .addon-item .actions .msg {
  background-color: #f7f7f7;
  border: none;
  position: absolute;
  text-align: center;
  font-weight: 600;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  margin: 0;
  z-index: 99;
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

#wpforms-admin-addons .addon-item .actions .msg.success {
  color: #008a20;
}

#wpforms-admin-addons .addon-item .actions .msg.error {
  color: red;
}

#wpforms-admin-addons .addon-item .actions .msg p {
  margin: 0;
}

#wpforms-admin-addons .addon-item .actions .msg a,
#wpforms-admin-addons .addon-item .actions .msg a:hover {
  color: inherit;
}

#wpforms-admin-addons .addon-item .status {
  flex-grow: 1;
}

#wpforms-admin-addons .addon-item .status .status-missing,
#wpforms-admin-addons .addon-item .status .status-go-to-url {
  color: #666;
}

#wpforms-admin-addons .addon-item .status .status-installed {
  color: red;
}

#wpforms-admin-addons .addon-item .status .status-active {
  color: #008a20;
}

#wpforms-admin-addons .addon-item .upgrade-button {
  text-align: center;
}

#wpforms-admin-addons .addon-item .upgrade-button a {
  font-weight: 600;
  width: 140px;
  text-align: center;
  padding: 8px 5px;
}

#wpforms-admin-addons .addon-item .action-button button {
  width: 140px;
}

#wpforms-admin-addons .addon-item .wpforms-addon-recommended {
  background-color: #00a32a;
  border-radius: 4px;
  color: #ffffff;
  font-size: 10px;
  font-weight: 700;
  line-height: 1;
  padding: 4px 5px;
  margin-bottom: 5px;
  display: inline-block;
  position: relative;
  top: -2px;
  text-transform: uppercase;
  cursor: default;
}

#wpforms-admin-addons .addon-item .wpforms-addon-recommended i {
  opacity: 0.8;
}

.addon-item .status {
  flex-grow: 1;
}

.addon-item .status .status-inactive {
  color: red;
}

.addon-item .status .status-active {
  color: #008a20;
}

.addon-item .upgrade-button {
  text-align: center;
}

.addon-item .upgrade-button a {
  font-weight: 600;
  width: 140px;
  text-align: center;
  padding: 8px 5px;
}

.addon-item .action-button button {
  cursor: pointer;
}

.addon-item .action-button button .fa {
  margin-right: 8px;
}

.addon-item .action-button button .fa.fa-spinner {
  margin-right: 0;
}

.addon-item .action-button button.status-active .fa {
  color: #008a20;
}

.addon-item .action-button button.status-inactive .fa {
  color: red;
}

.addon-item .action-button button.status-missing .fa {
  color: #999;
}

.addon-item .action-button button.disabled {
  cursor: default;
  pointer-events: none;
}

.addon-item .action-button button.loading {
  cursor: default;
}

.addon-item .action-button button.loading .fa {
  color: #666;
}

.addon-item .action-button a {
  text-decoration: none;
}

@media (min-width: 1250px) {
  #wpforms-admin-community .item {
    width: calc(33.3333333% - 13.3333333px);
  }
}

@media (max-width: 1249px) {
  #wpforms-admin-community .item {
    width: calc(50% - 10px);
  }
}

@media (max-width: 767px) {
  #wpforms-admin-community .item {
    width: 100%;
  }
}

#wpforms-admin-community *, #wpforms-admin-community *::before, #wpforms-admin-community *::after {
  box-sizing: border-box;
}

#wpforms-admin-community .items {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  padding: 0 20px 20px 20px;
}

#wpforms-admin-community .item {
  border: 1px solid #DDDDDD;
  background-color: #ffffff;
  position: relative;
}

#wpforms-admin-community .item-cover {
  width: 100%;
  height: 223px;
  position: relative;
  display: block;
}

#wpforms-admin-community .item-img {
  width: auto;
  height: auto;
  max-width: 100px;
  max-height: 100px;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  margin: auto;
}

#wpforms-admin-community .item-title {
  margin: 17px 20px 10px 20px;
  font-size: 16px;
}

#wpforms-admin-community .item-description {
  margin: 0 20px 98px 20px;
  font-size: 14px;
}

#wpforms-admin-community .item-footer {
  position: absolute;
  width: 100%;
  bottom: 0;
  padding: 20px;
  border-top: 1px solid #dddddd;
  background-color: #F7F7F7;
}

.wpforms-constant-contact-wrap {
  font-size: 16px;
  line-height: 1.5;
  color: #444444;
}

.wpforms-constant-contact-wrap .wpforms-admin-content {
  max-width: 1000px;
}

.wpforms-constant-contact-wrap .notice {
  display: none !important;
}

.wpforms-constant-contact-wrap h2 {
  font-size: 20px;
  font-weight: 700;
  line-height: 1.2;
  margin: 10px 0 20px;
}

.wpforms-constant-contact-wrap p {
  font-size: inherit;
  line-height: inherit;
  margin: 25px 0;
}

.wpforms-constant-contact-wrap ul,
.wpforms-constant-contact-wrap ol {
  margin-left: 20px;
}

.wpforms-constant-contact-wrap ul li,
.wpforms-constant-contact-wrap ol li {
  margin-bottom: 25px;
}

.wpforms-constant-contact-wrap ul {
  list-style-type: disc;
}

.wpforms-constant-contact-wrap hr {
  margin: 25px 0;
}

.wpforms-constant-contact-wrap a {
  color: #1856ed;
  transition: 0.3s ease;
}

.wpforms-constant-contact-wrap a:hover, .wpforms-constant-contact-wrap a:focus {
  color: #0f3592;
}

.wpforms-constant-contact-wrap .logo-link {
  display: block;
  float: right;
  width: auto;
  margin-left: 50px;
  border-radius: 6px;
  overflow: hidden;
  margin-bottom: 25px;
  max-width: 100%;
}

.wpforms-constant-contact-wrap .logo-link .logo {
  display: block;
  max-width: 100%;
}

.wpforms-constant-contact-wrap .button {
  background-color: #1856ed;
  border: 0;
  border-radius: 4px;
  color: #ffffff;
  font-size: 16px;
  line-height: 1.25;
  font-weight: 600;
  padding: 14px 30px;
  text-align: center;
  transition: 0.3s ease;
  height: auto;
}

.wpforms-constant-contact-wrap .button:hover, .wpforms-constant-contact-wrap .button:focus {
  background-color: #0f3592;
  color: #ffffff;
}

.wpforms-constant-contact-wrap .dashicons {
  color: #19be19;
  font-size: 26px;
  position: relative;
  top: -1px;
}

.wpforms-constant-contact-wrap .bold-marker::marker {
  font-weight: bold;
}

.wpforms-constant-contact-wrap .steps {
  display: flex;
  flex-wrap: wrap;
  margin-left: -15px;
  margin-right: -15px;
  margin-bottom: -25px;
  color: #777777;
  font-size: 14px;
  line-height: 1.14285714;
  text-align: center;
}

.wpforms-constant-contact-wrap .step {
  box-sizing: border-box;
  width: 50%;
  padding: 0 15px;
  margin: 0 0 25px;
}

.wpforms-constant-contact-wrap .step-image-wrapper {
  position: relative;
  border: 5px solid #ffffff;
  border-radius: 4px;
  overflow: hidden;
}

.wpforms-constant-contact-wrap .step-image-wrapper .hover {
  position: absolute;
  display: block;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0;
  background-color: rgba(0, 0, 0, 0.15);
  background-image: url(../images/zoom.svg);
  background-repeat: no-repeat;
  background-position: center;
  background-size: 50px;
  transition: all 0.3s;
}

.wpforms-constant-contact-wrap .step-image-wrapper .hover:hover {
  opacity: 1;
}

.wpforms-constant-contact-wrap .step img {
  max-width: 100%;
  height: auto;
  display: block;
  box-sizing: border-box;
}

.wpforms-constant-contact-wrap .step figcaption {
  margin-top: 10px;
}

@media only screen and (max-width: 767px) {
  .wpforms-constant-contact-wrap .button {
    font-size: 13px;
    padding: 11px 15px;
  }
  .wpforms-constant-contact-wrap .logo-link {
    float: none;
    margin-left: 0;
    margin-right: 0;
  }
  .wpforms-constant-contact-wrap .logo-link .logo {
    margin: 0 auto;
  }
  .wpforms-constant-contact-wrap .steps .step {
    width: 100%;
  }
}

#wpforms-entries-list .form-details {
  border-bottom: 1px solid #ddd;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding-bottom: 15px;
  margin-bottom: 10px;
}

#wpforms-entries-list .form-details .form-details-sub {
  color: #999;
  font-size: 10px;
  text-transform: uppercase;
  width: 100%;
}

#wpforms-entries-list .form-details .form-details-title {
  display: flex;
  font-size: 18px;
  font-weight: 400;
  margin-block: 0;
  margin-inline: 0 auto;
}

#wpforms-entries-list .form-details .form-details-title span {
  color: #646970;
  margin-inline-start: 8px;
}

#wpforms-entries-list .form-details .form-selector {
  position: relative;
}

#wpforms-entries-list .form-details .form-selector .toggle {
  margin-inline-start: 5px;
  border-radius: 50%;
  color: #a7aaad;
  font-size: 16px;
  box-shadow: none;
}

#wpforms-entries-list .form-details .form-selector .toggle:hover, #wpforms-entries-list .form-details .form-selector .toggle.active {
  background-color: #e3e3e3;
  outline: none;
}

#wpforms-entries-list .form-details .form-selector .toggle:before {
  vertical-align: middle;
}

#wpforms-entries-list .form-details .form-selector .form-list {
  display: none;
  background: #fff;
  border-radius: 3px;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.1);
  position: absolute;
  top: 30px;
  left: -103px;
  width: 230px;
  padding: 10px 0;
  z-index: 9991;
}

#wpforms-entries-list .form-details .form-selector .form-list:before {
  content: " ";
  position: absolute;
  top: -10px;
  left: 110px;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 0 8px 10px 8px;
  border-color: transparent transparent #fff transparent;
}

#wpforms-entries-list .form-details .form-selector ul {
  max-height: 196px;
  overflow-x: hidden;
  padding: 0;
  margin: 0;
}

#wpforms-entries-list .form-details .form-selector ul li {
  list-style: none;
  margin: 0;
  font-size: 13px;
}

#wpforms-entries-list .form-details .form-selector ul li a {
  display: block;
  text-decoration: none;
  padding: 5px 15px;
  color: #444;
}

#wpforms-entries-list .form-details .form-selector ul li a:hover {
  background-color: #f7f7f7;
}

#wpforms-entries-list .form-details .form-details-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

#wpforms-entries-list .form-details .form-details-actions a {
  text-decoration: none;
}

#wpforms-entries-list .form-details .form-details-actions a .dashicons {
  width: 1em;
  height: 1em;
  font-size: 16px;
}

#wpforms-entries-list .search-box .wpforms-form-search-box-field {
  max-width: 180px;
  vertical-align: top;
  margin: 0 5px 0 0;
}

#wpforms-entries-list .search-box .wpforms-form-search-box-comparison {
  max-width: 95px;
  vertical-align: top;
  margin: 0 5px 0 0;
}

#wpforms-entries-list .search-box .wpforms-form-search-box-term {
  min-height: 30px;
  line-height: normal;
  margin: 0 5px 0 0;
}

#wpforms-entries-list .wpforms-filter-date .wpforms-filter-date-selector {
  min-height: 30px;
  background-color: #fff;
  vertical-align: middle;
  max-width: 210px;
  line-height: normal;
}

#wpforms-entries-list .wpforms-filter-date button {
  margin: 1px 8px 0 0;
  vertical-align: top;
}

#wpforms-entries-list .tablenav.bottom .actions input {
  margin: 0;
}

#wpforms-entries-list .tablenav .actions {
  padding: 0 10px 0 0;
}

#wpforms-entries-list .wp-list-table .new-entries-notification td {
  padding: 0;
  text-align: center;
}

#wpforms-entries-list .wp-list-table .new-entries-notification td a {
  display: none;
  padding: 10px;
  background-color: #d9edf7;
}

#wpforms-entries-list .wp-list-table .wpforms-no-entries-found {
  position: sticky;
  left: 10px;
  width: 200px;
}

#wpforms-entries-list .wp-list-table tbody td {
  padding: 9px 10px 8px 10px;
}

#wpforms-entries-list .wp-list-table .manage-column {
  min-width: 120px;
  max-width: 20vw;
}

#wpforms-entries-list .wp-list-table .manage-column.check-column {
  width: 35px;
  min-width: 35px;
  max-width: 35px;
}

#wpforms-entries-list .wp-list-table .manage-column.column-graph {
  text-align: center;
}

#wpforms-entries-list .wp-list-table .column-indicators {
  width: 50px;
  min-width: 50px;
  line-height: 14px;
  text-align: center;
  white-space: nowrap;
  padding: 9px 8px 8px 4px;
}

#wpforms-entries-list .wp-list-table .column-indicators .dashicons {
  width: 14px;
  height: 14px;
  font-size: 14px;
}

#wpforms-entries-list .wp-list-table .indicator-star {
  box-shadow: none;
  color: #c3c4c7;
  display: inline-block;
  line-height: 14px;
  margin-inline-end: 10px;
  vertical-align: middle;
}

#wpforms-entries-list .wp-list-table .indicator-star:hover, #wpforms-entries-list .wp-list-table .indicator-star.unstar {
  color: #ffb900;
}

#wpforms-entries-list .wp-list-table .indicator-read {
  border-radius: 14px;
  box-shadow: inset 0 0 0 2.5px #a2c5dc;
  display: inline-block;
  height: 14px;
  line-height: 14px;
  transition: box-shadow 0.1s ease-in;
  vertical-align: middle;
  width: 14px;
}

#wpforms-entries-list .wp-list-table .indicator-read:hover, #wpforms-entries-list .wp-list-table .indicator-read.unread {
  box-shadow: inset 0 0 0 1.5px #c3c4c7;
}

#wpforms-entries-list .wp-list-table .column-entry_id {
  min-width: 110px;
}

#wpforms-entries-list .wp-list-table .column-date {
  min-width: 210px;
  text-wrap: balance;
}

#wpforms-entries-list .wp-list-table td.column-date span {
  display: inline-block;
}

#wpforms-entries-list .wp-list-table .column-type {
  width: 120px;
}

#wpforms-entries-list .wp-list-table .column-notes_count {
  min-width: 125px;
}

#wpforms-entries-list .wp-list-table .column-payment {
  width: 100px;
  min-width: 100px;
}

#wpforms-entries-list .wp-list-table .column-payment > span {
  color: #a7aaad;
}

#wpforms-entries-list .wp-list-table .column-payment [class*="payment-status-"] {
  align-items: center;
  display: grid;
  gap: 10px;
  grid-template-columns: 14px 1fr;
  white-space: nowrap;
}

#wpforms-entries-list .wp-list-table .column-payment [class*="payment-status-"]:before {
  background-image: url(../images/payments/status/icon-n-a.svg);
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  content: '';
  display: inline-block;
  height: 14px;
  width: 14px;
}

#wpforms-entries-list .wp-list-table .column-payment .payment-status-completed::before {
  background-image: url(../images/payments/status/icon-completed.svg);
}

#wpforms-entries-list .wp-list-table .column-payment .payment-status-pending::before {
  background-image: url(../images/payments/status/icon-pending.svg);
}

#wpforms-entries-list .wp-list-table .column-payment .payment-status-failed::before {
  background-image: url(../images/payments/status/icon-failed.svg);
}

#wpforms-entries-list .wp-list-table .column-payment .payment-status-refunded::before {
  background-image: url(../images/payments/status/icon-refunded.svg);
}

#wpforms-entries-list .wp-list-table .column-payment .payment-status-partrefund::before {
  background-image: url(../images/payments/status/icon-partrefund.svg);
}

#wpforms-entries-list .wp-list-table .column-payment .payment-status-cancelled::before {
  background-image: url(../images/payments/status/icon-cancelled.svg);
}

#wpforms-entries-list .wp-list-table .column-payment .payment-status-active::before {
  background-image: url(../images/payments/status/icon-active.svg);
}

#wpforms-entries-list .wp-list-table .column-payment .payment-status-processed::before {
  background-image: url(../images/payments/status/icon-processed.svg);
}

#wpforms-entries-list .wp-list-table .column-payment .payment-status-not-synced::before {
  background-image: url(../images/payments/status/icon-not-synced.svg);
}

#wpforms-entries-list .wp-list-table .column-actions {
  width: 130px;
  min-width: 130px;
  position: relative;
  white-space: nowrap;
}

#wpforms-entries-list .wp-list-table.wpforms-entries-table-spam .column-actions {
  width: 155px;
}

#wpforms-entries-list .wp-list-table .sep {
  color: #ddd;
}

#wpforms-entries-list .wp-list-table .delete:hover,
#wpforms-entries-list .wp-list-table .trash:hover {
  color: red;
}

#wpforms-entries-list #wpforms-list-table-ext-edit-columns-cog {
  top: 9px;
}

@media (max-width: 782px) {
  #wpforms-entries-list .wpforms-entries-overview-table .wp-list-table tbody td.column-primary {
    padding-right: 50px;
    line-height: 25px;
  }
  #wpforms-entries-list .wpforms-entries-overview-table .wp-list-table tbody td.column-primary > a {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: initial;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
  }
}

#wpforms-entries-list .wpforms-dash-widget-chart-block {
  padding: 12px 12px 0 8px;
  background-color: #ffffff;
  border: 1px solid #e5e5e5;
  margin-top: 20px;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
}

#wpforms-entries-list .wpforms-dash-widget-chart-block .wpforms-error-no-data-chart {
  border: none;
}

#wpforms-entries-list #wpforms-dash-widget-chart-title {
  display: inline-block;
  margin-left: 0;
  font-size: 18px;
}

#wpforms-entries-list .wpforms-dash-widget-block:first-child {
  margin-top: 0;
}

#wpforms-entries-list .wpforms-dash-widget-reset-chart {
  color: #d83638;
  opacity: 1;
  vertical-align: text-bottom;
  margin: 0 0 -1px 0;
}

#wpforms-entries-list .wpforms-dash-widget-reset-chart:hover {
  color: #ad2b2c;
}

#wpforms-entries-list .wpforms-dash-widget-reset-chart .dashicons {
  font-size: 20px;
  height: 1em;
  width: 1em;
}

#wpforms-entries-list #wpforms-dash-widget-timespan {
  margin: 5px -12px 0 0;
}

#wpforms-entries-list #wpforms-dash-widget-forms-list-block {
  overflow: visible;
}

#wpforms-entries-list #wpforms-dash-widget-forms-list-table {
  border: 1px solid #e5e5e5;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
}

#wpforms-entries-list #wpforms-dash-widget-forms-list-table .wpforms-dash-widget-forms-list-columns td {
  padding: 12px 20px;
  border-bottom: 1px solid #e1e1e1;
}

#wpforms-entries-list #wpforms-dash-widget-forms-list-table tbody {
  border-top: 0;
}

#wpforms-entries-list #wpforms-dash-widget-forms-list-table tr {
  border-bottom: 0;
}

#wpforms-entries-list #wpforms-dash-widget-forms-list-table tr:first-child {
  border-top: 0;
}

#wpforms-entries-list #wpforms-dash-widget-forms-list-table tr:first-child td {
  border-top: 0;
}

#wpforms-entries-list #wpforms-dash-widget-forms-list-table tr:nth-child(even) td {
  background-color: #f9f9f9;
}

#wpforms-entries-list #wpforms-dash-widget-forms-list-table td {
  text-align: left;
  padding: 20px 20px;
  font-size: 14px;
  border: none;
  color: #555;
  background-color: #fff;
}

#wpforms-entries-list #wpforms-dash-widget-forms-list-table tr > td:nth-child(2) {
  width: 180px;
}

#wpforms-entries-list #wpforms-dash-widget-forms-list-table tr > td:nth-child(3) {
  width: 60px;
  text-align: center;
}

#wpforms-entries-list #wpforms-dash-widget-forms-list-table tr > td:nth-child(4) {
  width: 90px;
  text-align: center;
}

#wpforms-entries-list #wpforms-dash-widget-forms-list-table tr > td:nth-child(5) {
  width: 60px;
  text-align: center;
  padding-top: 0;
  padding-bottom: 0;
}

#wpforms-entries-list #wpforms-dash-widget-forms-list-table .wpforms-dash-widget-form-active td:first-child {
  border-left: 3px solid #0073aa;
  padding-left: 17px;
}

#wpforms-entries-list #wpforms-dash-widget-forms-list-table .wpforms-dash-widget-form-active .wpforms-dash-widget-single-chart-btn {
  display: none !important;
}

#wpforms-entries-list #wpforms-dash-widget-forms-list-table .wpforms-dash-widget-form-active .wpforms-dash-widget-reset-chart {
  display: inline-block !important;
  border: none;
  background: none;
  margin: 0;
}

#wpforms-entries-list #wpforms-dash-widget-forms-list-table .wpforms-dash-widget-forms-list-columns {
  background-color: #ffffff;
  border-bottom: 1px solid #ddd;
}

#wpforms-entries-list #wpforms-dash-widget-forms-list-table a {
  text-decoration: none;
}

#wpforms-entries-list #wpforms-dash-widget-forms-list-table a:hover {
  text-decoration: none;
}

#wpforms-entries-list #wpforms-dash-widget-forms-list-table .wpforms-dash-widget-form-title a {
  font-weight: 600;
}

@media screen and (max-width: 1024px) {
  #wpforms-entries-list .column-indicators,
  #wpforms-entries-list .wp-list-table.entries td.column-indicators {
    display: none !important;
  }
}

@media screen and (max-width: 782px) {
  .wpforms_page_wpforms-entries .wpforms-admin-content {
    padding-left: 10px;
    padding-right: 10px;
  }
  #wpforms-entries-list .wp-list-table {
    margin: 0;
    width: 100%;
    table-layout: auto;
  }
  #wpforms-entries-list .wp-list-table tr:not(.inline-edit-row):not(.no-items) .indicators {
    display: none;
  }
  #wpforms-entries-list .wp-list-table tr:not(.inline-edit-row):not(.no-items) .indicators + td:not(.column-primary) {
    margin-top: 0;
  }
  #wpforms-entries-list .wp-list-table th.check-column, #wpforms-entries-list .wp-list-table td.check-column {
    height: 45px;
  }
  #wpforms-entries-list .wp-list-table th.column-actions, #wpforms-entries-list .wp-list-table td.column-actions {
    width: auto;
  }
  #wpforms-entries-list .wp-list-table .column-date {
    text-wrap: normal;
  }
  #wpforms-entries-list .wp-list-table.entries #the-list tr.no-items:last-child {
    position: relative;
  }
  #wpforms-entries-list .wp-list-table.entries #the-list tr.no-items:last-child td {
    border-top: 1px solid #ccd0d4;
    border-bottom: 1px solid #ccd0d4 !important;
    position: absolute;
    min-height: 80px;
    width: 100%;
    box-sizing: border-box;
    margin-top: -40px;
    padding: 30px;
    background-color: #ffffff;
    font-size: 16px;
  }
  #wpforms-entries-list .form-details-actions {
    margin-top: 15px;
  }
  #wpforms-entries-list .form-details-actions a {
    margin-bottom: 10px !important;
    font-size: 16px;
  }
  #wpforms-entries-list p.search-box {
    margin: 0;
    width: auto;
  }
  #wpforms-entries-list p.search-box select, #wpforms-entries-list p.search-box input, #wpforms-entries-list p.search-box button {
    max-width: 24% !important;
    min-height: 40px !important;
    margin-bottom: 5px !important;
  }
}

#wpforms-entries-table .tablenav .actions select {
  margin-right: 5px;
}

#wpforms-entries-table .tablenav .actions #doaction {
  margin-right: 0;
}

#wpforms-entries-table .wpforms-filter-date .wpforms-filter-date-selector {
  float: left;
  margin-right: 5px;
}

#wpforms-entries-table .wpforms-filter-date button {
  margin: 0;
}

#wpforms-entries-table .search-box .wpforms-form-search-box-field,
#wpforms-entries-table .search-box .wpforms-form-search-box-comparison,
#wpforms-entries-table .search-box .wpforms-form-search-box-term,
#wpforms-entries-table .search-box button {
  float: left;
}

@media (max-width: 782px) {
  #wpforms-entries-table .search-box {
    position: relative;
    bottom: auto;
    width: 100%;
    height: auto;
    clear: both;
  }
  #wpforms-entries-table .search-box .wpforms-form-search-box-term,
  #wpforms-entries-table .wpforms-filter-date .wpforms-filter-date-selector {
    padding: 3px 10px;
    min-height: 40px;
  }
  #wpforms-entries-table .tablenav.top {
    margin-bottom: 10px;
  }
  #wpforms-entries-table .tablenav .actions,
  #wpforms-entries-table .tablenav .wpforms-filter-date {
    display: block;
    margin-bottom: 10px;
  }
}

@media screen and (max-width: 400px) {
  #wpforms-entries-table .tablenav.bottom .bulkactions {
    width: 100%;
  }
}

#wpforms-entries-table .wpforms-table-container {
  border: 1px solid #ccd0d4;
  border-radius: 4px;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
  width: 100%;
  overflow-x: auto;
  overflow-y: hidden;
  box-sizing: border-box;
}

#wpforms-entries-table .wpforms-table-container .wpforms-table-list {
  border: none;
  box-shadow: none;
  border-radius: inherit;
  position: relative;
}

@media (max-width: 782px) {
  #wpforms-entries-table .wpforms-table-container table.wp-list-table td.column-primary {
    height: auto !important;
    padding-top: 13px;
    padding-right: 50px;
  }
  #wpforms-entries-table .wpforms-table-container table.wp-list-table td.column-primary .toggle-row:before {
    top: 0;
  }
  #wpforms-entries-table .wpforms-table-container table.wp-list-table td.column-primary + td {
    margin-top: 50px;
  }
  #wpforms-entries-table .wpforms-table-container table.wp-list-table td.column-primary > div {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: initial;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
  }
}

body.rtl #wpforms-entries-list .wp-list-table div[data-field-type="phone"] {
  direction: ltr;
  unicode-bidi: embed;
  text-align: right;
}

body.rtl #wpforms-entries-list .wp-list-table.has-many-columns .is-expanded td:not(.column-primary)[data-colname]::before, body.rtl #wpforms-entries-list .wp-list-table.has-few-columns .is-expanded td:not(.column-primary)[data-colname]::before {
  text-align: right;
}

body.rtl #wpforms-entries-list .wp-list-table.has-many-columns .is-expanded td:not(.column-primary):not(.hidden), body.rtl #wpforms-entries-list .wp-list-table.has-few-columns .is-expanded td:not(.column-primary):not(.hidden) {
  text-align: left;
}

#wpforms-overview #wpforms-reset-filter {
  margin-top: 0;
}

#wpforms-overview .search-box input {
  max-width: 165px;
}

#wpforms-overview .search-box .button {
  margin-left: 1px;
  font-size: 14px;
  line-height: 2;
}

#wpforms-overview .subsubsub {
  margin: 10px 0 -4px 0;
}

#wpforms-overview .tablenav.top {
  margin-top: 0;
  padding-top: 8px;
}

#wpforms-overview .tablenav > div,
#wpforms-overview .tablenav > button {
  margin-bottom: 10px;
}

#wpforms-overview .wpforms-table-container {
  border: none;
}

#wpforms-overview .wp-list-table {
  table-layout: auto;
  width: 100%;
  max-width: 100%;
  border: 1px solid #ccd0d4;
}

#wpforms-overview .wp-list-table.striped tbody > :nth-child(odd) {
  background-color: #ffffff;
}

#wpforms-overview .wp-list-table.striped tbody > :nth-child(even) {
  background-color: #f6f6f6;
}

#wpforms-overview .wp-list-table .manage-column {
  position: relative;
}

#wpforms-overview .wp-list-table .manage-column > a {
  padding-right: 0;
}

#wpforms-overview .wp-list-table .column-name {
  width: auto;
}

#wpforms-overview .wp-list-table .column-name > span > strong,
#wpforms-overview .wp-list-table .column-name > a > strong {
  font-weight: 600;
}

#wpforms-overview .wp-list-table tbody .column-name > a {
  max-height: 120px;
  overflow: hidden;
}

#wpforms-overview .wp-list-table tbody .column-name .post-state {
  font-weight: 500;
}

#wpforms-overview .wp-list-table .column-id {
  width: 65px;
}

#wpforms-overview .wp-list-table .column-tags {
  width: 300px;
}

#wpforms-overview .wp-list-table .column-tags:before {
  content: '';
  display: block;
  width: 300px;
}

#wpforms-overview .wp-list-table .column-author {
  width: 130px;
}

#wpforms-overview .wp-list-table .column-shortcode {
  width: 150px;
}

#wpforms-overview .wp-list-table .column-created {
  width: 220px;
}

#wpforms-overview .wp-list-table .column-locations {
  width: 55px;
}

#wpforms-overview .wp-list-table .column-cog {
  z-index: revert;
}

#wpforms-overview .wp-list-table .column-entries {
  width: 80px;
}

#wpforms-overview .wp-list-table .column-entries .wpforms-lite-connect-entries-count a {
  align-items: center;
  display: inline-flex;
  position: relative;
  gap: 6px;
}

#wpforms-overview .wp-list-table .column-entries .wpforms-lite-connect-entries-count a svg {
  color: #8c8f94;
  width: 16px;
  height: 12px;
  transition-property: color;
  transition-duration: 0.15s;
  transition-timing-function: ease-out;
}

#wpforms-overview .wp-list-table .column-entries .wpforms-lite-connect-entries-count a path {
  fill: currentColor;
}

#wpforms-overview .wp-list-table .column-entries .wpforms-lite-connect-entries-count a:hover {
  text-decoration: underline;
}

#wpforms-overview .wp-list-table .column-entries .wpforms-lite-connect-entries-count a:hover svg {
  color: #50575e;
}

#wpforms-overview .wp-list-table .column-entries .wpforms-lite-connect-entries-count a[data-title]::after {
  background-color: rgba(30, 30, 30, 0.9);
  border-radius: 2px;
  bottom: 100%;
  box-sizing: border-box;
  content: attr(data-title);
  color: #fff;
  font-size: 11px;
  left: calc( 50% - 88.5px);
  letter-spacing: -.3px;
  line-height: 16px;
  opacity: 0;
  padding: 5px 8px;
  position: absolute;
  text-align: left;
  visibility: hidden;
  /* hide by default */
  width: 177px;
  transition-property: opacity;
  transition-duration: 0.4s;
  transition-timing-function: ease-out;
}

#wpforms-overview .wp-list-table .column-entries .wpforms-lite-connect-entries-count a[data-title]:hover::after {
  opacity: 1;
  visibility: visible;
}

#wpforms-overview .wp-list-table span.wpforms-locations-column-title {
  display: none;
}

#wpforms-overview .wp-list-table span.wpforms-locations-column-icon {
  display: block;
  width: 12px;
  height: 16px;
  background-image: url("../images/file-code.svg");
}

#wpforms-overview .wp-list-table .locations .locations-list {
  display: none;
}

#wpforms-overview .wp-list-table .wpforms-locations-pane td {
  padding: 8px 11px 11px;
}

#wpforms-overview .wp-list-table .wpforms-locations-pane td:before {
  content: '';
  display: block;
  width: 100%;
  height: 1px;
  background: #dcdcde;
  transform: translateY(-8px);
}

#wpforms-overview .wp-list-table .wpforms-locations-pane .wpforms-locations-pane-title {
  display: block;
  margin-bottom: 0.3em;
  text-transform: uppercase;
  color: #50575e;
  font-size: 12px;
  font-weight: 600;
}

#wpforms-overview .wp-list-table .wpforms-locations-pane .wpforms-locations-list-item {
  display: block;
  margin-bottom: 0.3em;
}

#wpforms-overview .wp-list-table .wpforms-locations-pane a.wpforms-locations-link {
  color: #a7aaad;
  font-style: italic;
  font-weight: normal;
  margin-left: 2px;
}

#wpforms-overview .wp-list-table .wpforms-locations-pane a.wpforms-locations-link i {
  margin-left: 5px;
}

#wpforms-overview .wp-list-table .wpforms-locations-pane a.wpforms-locations-link:hover {
  color: #50575e;
}

#wpforms-overview .wp-list-table .wpforms-locations-pane .wpforms-locations-close {
  margin-top: 0.4em;
}

#wpforms-overview .choices:not(.is-focused) .choices__inner,
#wpforms-overview .choices:not(.is-open) .choices__inner {
  border-color: #8c8f94;
}

#wpforms-overview .choices__inner {
  min-height: 30px;
  padding: 1px !important;
  padding-inline-end: 45px !important;
  align-items: center;
}

#wpforms-overview .choices__inner .choices__item {
  margin: 2px;
}

#wpforms-overview .choices__inner .choices__input {
  margin: 0 !important;
  background-image: none !important;
  cursor: text;
  line-height: 26px;
}

#wpforms-overview .choices__inner .choices__button {
  flex-shrink: 0;
}

#wpforms-overview .choices__inner .choices__arrow {
  position: absolute;
  z-index: 2;
  width: 24px;
  height: 24px;
  inset-inline-end: 1px;
  top: calc( 50% - 12px);
  background: #ffffff;
}

#wpforms-overview .choices__list--dropdown .choices__item {
  padding: 10px 12px;
}

#wpforms-overview .wpforms-tags-filter {
  display: inline-flex;
  flex-wrap: nowrap;
  min-width: 300px;
  margin-right: 12px;
}

#wpforms-overview .wpforms-tags-filter .choices {
  margin: 0;
  flex-grow: 10;
  max-width: 25vw;
}

#wpforms-overview .wpforms-tags-filter .choices:after {
  z-index: 9;
}

#wpforms-overview .wpforms-tags-filter .choices[aria-expanded="true"] .choices__inner {
  max-height: max-content;
  position: relative;
  z-index: 9;
}

#wpforms-overview .wpforms-tags-filter .choices[aria-expanded="true"] .choices__inner .choices__list {
  white-space: initial;
}

#wpforms-overview .wpforms-tags-filter .choices[data-type*="select-multiple"][aria-expanded="false"].choices__show-more:before {
  top: 4px !important;
  inset-inline-end: 28px !important;
}

#wpforms-overview .wpforms-tags-filter .choices[data-type*="select-multiple"][aria-expanded="false"] .choices__inner {
  height: 30px;
}

#wpforms-overview .wpforms-tags-filter .choices__inner {
  max-height: 30px;
  padding: 2px 24px 2px 2px;
}

#wpforms-overview .wpforms-tags-filter .choices__inner .choices__list {
  height: auto !important;
}

#wpforms-overview .wpforms-tags-filter .choices__inner .choices__input {
  margin-left: 4px !important;
}

#wpforms-overview .wpforms-tags-filter select {
  width: calc( 100% - 50px);
  visibility: hidden;
}

#wpforms-overview .wpforms-tags-filter input::placeholder {
  color: #32373c;
}

#wpforms-overview .wpforms-tags-filter .button {
  margin-bottom: 0;
  margin-inline-start: 6px;
  height: 30px;
}

#wpforms-overview .wpforms-column-tags-links .wpforms-column-tags-edit {
  visibility: hidden;
  width: 300px;
}

#wpforms-overview .wpforms-column-tags-links .wpforms-column-tags-links-list {
  margin-bottom: 2px;
}

#wpforms-overview .wpforms-column-tags-form {
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  justify-content: space-between;
}

#wpforms-overview .wpforms-column-tags-form .choices {
  margin: 0;
  flex-grow: 10;
  width: 250px;
}

#wpforms-overview .wpforms-column-tags-form .choices.choices__show-more:before {
  top: 4px !important;
  right: 28px !important;
}

#wpforms-overview .wpforms-column-tags-form .choices[aria-expanded="false"] .choices__inner {
  max-height: 30px !important;
}

#wpforms-overview .wpforms-column-tags-form i.dashicons {
  margin-left: 8px;
  width: 16px;
  height: 16px;
  font-size: 15px;
  cursor: pointer;
}

#wpforms-overview .wpforms-column-tags-form i.dashicons.wpforms-column-tags-edit-cancel {
  color: #787c82;
}

#wpforms-overview .wpforms-column-tags-form i.dashicons.wpforms-column-tags-edit-cancel:hover {
  color: #d63638;
}

#wpforms-overview .wpforms-column-tags-form i.dashicons.wpforms-column-tags-edit-save {
  margin-left: 6px;
  color: #00a32a;
}

#wpforms-overview .wpforms-column-tags-form i.dashicons.wpforms-column-tags-edit-save:hover {
  color: #007017;
}

#wpforms-overview .wpforms-column-tags-form i.wpforms-spinner {
  margin: -2px 0 0 6px;
  width: 16px;
  min-width: 16px;
  height: 16px;
  background-size: 14px 14px;
  background-position: center;
  visibility: visible;
}

#wpforms-overview .wpforms-bulk-edit-tags {
  background: #ffffff;
}

#wpforms-overview .wpforms-bulk-edit-tags .wpforms-fbox {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
}

@media (max-width: 600px) {
  #wpforms-overview .wpforms-bulk-edit-tags .wpforms-fbox {
    flex-wrap: wrap;
  }
}

#wpforms-overview .wpforms-bulk-edit-tags .wpforms-edit-tags {
  margin-left: 10px;
  width: 310px;
}

@media (max-width: 600px) {
  #wpforms-overview .wpforms-bulk-edit-tags .wpforms-edit-tags {
    width: 100%;
    margin: 0;
  }
}

#wpforms-overview .wpforms-bulk-edit-tags .wpforms-edit-forms {
  width: calc( 90% - 720px);
}

@media (max-width: 600px) {
  #wpforms-overview .wpforms-bulk-edit-tags .wpforms-edit-forms {
    width: 100% !important;
    margin-bottom: 10px;
  }
}

#wpforms-overview .wpforms-bulk-edit-tags .wpforms-edit-forms .is-open .choices__list--dropdown {
  display: none;
}

#wpforms-overview .wpforms-bulk-edit-tags .wpforms-edit-forms .is-open .choices__inner {
  border-radius: 4px;
}

#wpforms-overview .wpforms-bulk-edit-tags .wpforms-edit-forms .choices__inner {
  cursor: default;
  min-height: 140px;
  max-height: 200px;
  align-items: start;
  overflow-y: auto;
  padding: 5px 1px;
}

#wpforms-overview .wpforms-bulk-edit-tags .wpforms-edit-forms .choices[data-type*="select-multiple"]:after {
  display: none;
}

#wpforms-overview .wpforms-bulk-edit-tags .wpforms-edit-forms .choices__input {
  display: none !important;
}

#wpforms-overview .wpforms-bulk-edit-tags .wpforms-edit-forms .choices__list--multiple {
  display: block !important;
}

#wpforms-overview .wpforms-bulk-edit-tags .wpforms-edit-forms .choices__list--multiple .choices__item {
  display: flex;
  flex-direction: row-reverse;
  width: fit-content;
  cursor: default;
  padding: 3px 7px;
  background: none;
  color: #50575e;
  border: none;
  word-break: break-word;
}

#wpforms-overview .wpforms-bulk-edit-tags .wpforms-edit-forms .choices__list--multiple .choices__item button {
  float: left;
  margin: 0 7px 0 0;
  background: none;
  opacity: 1;
}

#wpforms-overview .wpforms-bulk-edit-tags .wpforms-edit-forms .choices__list--multiple .choices__item button:hover:before {
  color: #d63638;
}

#wpforms-overview .wpforms-bulk-edit-tags .wpforms-edit-forms .choices__list--multiple .choices__item button:before {
  font-family: dashicons, sans-serif;
  content: "\f153";
  width: 13px;
  height: 13px;
  font-size: 14px;
  color: #787c82;
  text-indent: 0;
  position: absolute;
  left: 0;
  top: 0;
}

#wpforms-overview .wpforms-bulk-edit-tags.wpforms-row-form td {
  padding: 10px;
}

#wpforms-overview .wpforms-bulk-edit-tags.wpforms-row-buttons td {
  padding: 10px;
}

#wpforms-overview .wpforms-bulk-edit-tags .wpforms-bulk-edit-tags-save {
  float: right;
}

#wpforms-overview .wpforms-bulk-edit-tags .wpforms-bulk-edit-tags-save i {
  margin: 0 4px 0 0;
}

#wpforms-overview .wpforms-bulk-edit-tags.wpforms-row-message td {
  padding: 0;
}

#wpforms-overview .wpforms-bulk-edit-tags.wpforms-row-message .wpforms-message {
  padding: 20px;
  text-align: center;
  border-top: 1px solid #ccd0d4;
  border-bottom: 1px solid #ccd0d4;
}

#wpforms-overview .wpforms-manage-tags {
  margin-right: 6px;
}

@media (max-width: 430px) {
  #wpforms-overview #doaction2 {
    margin-right: 35px;
  }
}

body.toplevel_page_wpforms-overview [id^="__lpform_input"] {
  display: none !important;
}

body.wpforms-manage-tags-modal {
  overflow: hidden;
  height: 100vh;
}

body.wpforms-manage-tags-modal .jconfirm .jconfirm-cell {
  overflow-y: auto;
  max-height: 100vh;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
}

body.wpforms-manage-tags-modal .jconfirm .jconfirm-box-container .jconfirm-box {
  width: calc( 100% - 40px) !important;
  max-width: 550px;
}

body.wpforms-manage-tags-modal .jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-content-pane {
  height: auto !important;
  min-height: fit-content;
  max-height: fit-content !important;
}

body.wpforms-manage-tags-modal .wpforms-manage-tags-items {
  margin-top: 25px;
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  overflow-wrap: anywhere;
}

body.wpforms-manage-tags-modal .wpforms-manage-tags-items input {
  display: none;
}

body.wpforms-manage-tags-modal .wpforms-manage-tags-items input + label {
  background: #f8f8f8 url("../images/trash.svg") no-repeat right 10px center;
  background-size: 12px 14px;
  border: 1px solid #f8f8f8;
  border-radius: 3px;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  padding: 7px 30px 7px 10px;
  margin: 0 10px 10px 0;
  transition-property: all;
  transition-duration: 0.15s;
  transition-timing-function: ease-out;
}

body.wpforms-manage-tags-modal .wpforms-manage-tags-items input + label:hover {
  border-color: #a7aaad;
}

body.wpforms-manage-tags-modal .wpforms-manage-tags-items input + label span {
  color: #777777;
}

body.wpforms-manage-tags-modal .wpforms-manage-tags-items input:checked + label {
  background: #ffffff url("../images/trash-red.svg") no-repeat right 10px center;
  background-size: 12px 14px;
  border-color: #d63638;
}

body.wpforms-manage-tags-modal .wpforms-manage-tags-notice {
  background: #FCF9E8;
  border-radius: 3px;
  font-size: 14px;
  line-height: 22px;
  text-align: center;
  color: #444444;
  padding: 7px;
  margin: 20px 0 10px 0;
}

tr:hover .wpforms-column-tags-links .wpforms-column-tags-edit,
.mobile .wpforms-column-tags-links .wpforms-column-tags-edit {
  visibility: visible !important;
}

.branch-4-9 #wpforms-overview .search-box input {
  vertical-align: top;
  margin: 0;
  height: 28px;
}

.branch-4-9 #wpforms-overview .search-box .button {
  font-size: 13px;
}

.branch-5-2 #wpforms-overview .actions {
  padding-top: 0;
}

.branch-5-2 #wpforms-overview .delete-all {
  margin-top: 1px;
}

.branch-5-2 #wpforms-overview .choices:not(.is-focused) .choices__inner,
.branch-5-2 #wpforms-overview .choices:not(.is-open) .choices__inner {
  border-color: #dddddd;
}

.branch-5-2 #wpforms-overview .wpforms-tags-filter .choices__inner {
  max-height: 28px;
  min-height: 28px;
}

.branch-5-2 #wpforms-overview .wpforms-tags-filter .choices__list--multiple .choices__item {
  padding: 3px 6px;
}

@media screen and (max-width: 1280px) {
  #wpforms-overview .wpforms-tags-filter {
    min-width: 200px;
  }
  #wpforms-overview .wp-list-table tr th:nth-child(5) ~ th:not(.column-entries):not(.column-created):not(.column-cog) {
    display: none;
  }
  #wpforms-overview .wp-list-table tr td:nth-child(5) ~ td:not(.column-entries):not(.column-created):not(.column-cog) {
    display: none;
  }
}

@media screen and (max-width: 960px) {
  #wpforms-overview .choices__list--dropdown .choices__item--selectable.is-highlighted:after {
    display: none !important;
  }
  #wpforms-overview .wp-list-table tr:not(.wpforms-bulk-edit-tags) th, #wpforms-overview .wp-list-table tr:not(.wpforms-bulk-edit-tags) td {
    display: none;
  }
  #wpforms-overview .wp-list-table tr:not(.wpforms-bulk-edit-tags) th.check-column, #wpforms-overview .wp-list-table tr:not(.wpforms-bulk-edit-tags) th.column-name, #wpforms-overview .wp-list-table tr:not(.wpforms-bulk-edit-tags) th.column-created, #wpforms-overview .wp-list-table tr:not(.wpforms-bulk-edit-tags) th.column-entries, #wpforms-overview .wp-list-table tr:not(.wpforms-bulk-edit-tags) td.check-column, #wpforms-overview .wp-list-table tr:not(.wpforms-bulk-edit-tags) td.column-name, #wpforms-overview .wp-list-table tr:not(.wpforms-bulk-edit-tags) td.column-created, #wpforms-overview .wp-list-table tr:not(.wpforms-bulk-edit-tags) td.column-entries {
    display: table-cell;
  }
}

@media screen and (max-width: 782px) {
  .toplevel_page_wpforms-overview .wpforms-admin-content {
    padding-left: 10px;
    padding-right: 10px;
  }
  #wpforms-overview {
    margin-bottom: 24px;
  }
  #wpforms-overview .wp-list-table tr:not(.inline-edit-row):not(.no-items) td.column-name > a {
    display: block;
    max-height: 40px;
    overflow: hidden;
  }
  #wpforms-overview .wp-list-table tr:not(.inline-edit-row):not(.no-items) td.column-name .row-actions {
    overflow: hidden;
    height: 30px;
    gap: 0;
  }
  #wpforms-overview .wp-list-table tr:not(.inline-edit-row):not(.no-items) td.column-name .row-actions a {
    padding: 4px;
  }
  #wpforms-overview .wp-list-table tr:not(.inline-edit-row):not(.no-items).is-expanded td:not(.hidden) {
    overflow: visible !important;
  }
  #wpforms-overview .wp-list-table tr:not(.inline-edit-row):not(.no-items).is-expanded td:not(.check-column):not(.column-primary):first-of-type {
    margin-top: 0 !important;
  }
  #wpforms-overview .wp-list-table tr:not(.inline-edit-row):not(.no-items).is-expanded td.column-name .row-actions {
    overflow: visible;
  }
  #wpforms-overview .wp-list-table tr:not(.inline-edit-row):not(.no-items) td.column-created, #wpforms-overview .wp-list-table tr:not(.inline-edit-row):not(.no-items) td.column-entries, #wpforms-overview .wp-list-table tr:not(.inline-edit-row):not(.no-items) th.column-created, #wpforms-overview .wp-list-table tr:not(.inline-edit-row):not(.no-items) th.column-entries {
    display: none;
  }
  #wpforms-overview .wp-list-table tr.wpforms-locations-pane {
    display: none;
  }
  #wpforms-overview .wp-list-table tr.is-expanded ~ tr.wpforms-locations-pane {
    display: table-row;
  }
  #wpforms-overview .search-box {
    width: calc( 100% - 20px);
  }
  #wpforms-overview .search-box #wpforms-overview-search-term {
    width: 100%;
    height: auto;
    max-width: 100%;
    margin-bottom: 10px;
  }
  .branch-4-9 #wpforms-overview .search-box .button {
    margin-left: 0;
    font-size: 14px;
    line-height: normal;
  }
  #wpforms-overview .tablenav.top {
    display: none;
  }
  #wpforms-overview .tablenav.bottom .bulkactions {
    margin-bottom: 10px;
  }
  #wpforms-overview .tablenav.bottom .delete-all {
    margin-left: 1px;
    margin-right: 60px;
  }
  #wpforms-overview .tablenav.bottom .displaying-num {
    top: 16px;
  }
  #wpforms-overview .tablenav-pages.one-page {
    margin-bottom: 0;
    float: none;
  }
  #wpforms-overview .tablenav-pages.one-page .displaying-num {
    top: 12px;
  }
  #wpforms-overview .subsubsub {
    margin: 0 0 10px 0;
  }
  #wpforms-overview .wpforms-tags-filter {
    vertical-align: top;
  }
  #wpforms-overview .wpforms-tags-filter .choices {
    max-width: 40vw;
    min-width: 40vw;
  }
  #wpforms-overview .wpforms-tags-filter .choices__inner {
    min-height: 40px;
    max-height: 40px;
  }
  #wpforms-overview .wpforms-manage-tags {
    vertical-align: top;
    margin-right: 80px;
  }
  .branch-5-2 #wpforms-overview .wpforms-tags-filter .choices__inner {
    min-height: 32px;
    max-height: 32px;
  }
  .branch-5-2 #wpforms-overview .bulkactions select,
  .branch-5-2 #wpforms-overview .bulkactions input {
    height: 32px;
    margin-top: 0;
    margin-bottom: 0;
  }
  .branch-5-2 #wpforms-overview .search-box .button {
    line-height: normal;
  }
}

@media screen and (max-width: 600px) {
  .tablenav.bottom .displaying-num {
    position: absolute;
  }
  #wpforms-overview .wpforms-tags-filter {
    margin-right: 3px;
  }
  #wpforms-overview .wpforms-tags-filter .choices {
    min-width: 52vw;
  }
  #wpforms-overview .wpforms-manage-tags {
    margin-right: 0;
  }
}

#wpforms-payments.wpforms-payments-wrap-payment a {
  text-decoration: none;
}

#wpforms-payments.wpforms-payments-wrap-payment .page-title {
  padding: 14px 20px;
}

#wpforms-payments.wpforms-payments-wrap-payment .hndle {
  color: #32373c;
}

#wpforms-payments.wpforms-payments-wrap-payment .wpforms-single-payment-tooltip {
  background-image: url(../images/payments/single/info-circle.svg);
  background-size: contain;
  height: 14px;
  margin-top: 2px;
  width: 14px;
}

#wpforms-payments.wpforms-payments-wrap-payment .wpforms-single-payment-tooltip-content {
  display: none;
}

#wpforms-payments.wpforms-payments-wrap-payment .wpforms-payment-actions {
  align-items: center;
  background: #f6f6f6;
  border-top: 1px solid #c3c4c7;
  display: flex;
  padding: 10px 12px;
  min-height: 30px;
}

#wpforms-payments.wpforms-payments-wrap-payment .wpforms-payment-actions .status {
  display: inline-flex;
  align-items: center;
  gap: 5px;
  width: 50%;
  font-size: 14px;
  line-height: 17px;
  color: #50575e;
  font-weight: 400;
  padding-left: 3px;
}

#wpforms-payments.wpforms-payments-wrap-payment .wpforms-payment-actions .status .processed {
  color: #008a20;
}

#wpforms-payments.wpforms-payments-wrap-payment .wpforms-payment-actions .status .completed {
  color: #008a20;
}

#wpforms-payments.wpforms-payments-wrap-payment .wpforms-payment-actions .status .active {
  color: #008a20;
}

#wpforms-payments.wpforms-payments-wrap-payment .wpforms-payment-actions .status .cancelled {
  color: #50575e;
}

#wpforms-payments.wpforms-payments-wrap-payment .wpforms-payment-actions .status .not-synced {
  color: #50575e;
}

#wpforms-payments.wpforms-payments-wrap-payment .wpforms-payment-actions .status .failed {
  color: #d63638;
}

#wpforms-payments.wpforms-payments-wrap-payment .wpforms-payment-actions .status .pending {
  color: #bd8600;
}

#wpforms-payments.wpforms-payments-wrap-payment .wpforms-payment-actions .status .refunded {
  color: #50575e;
}

#wpforms-payments.wpforms-payments-wrap-payment .wpforms-payment-actions .wpforms-payment-action-status-value {
  font-weight: 600;
  text-transform: capitalize;
}

#wpforms-payments.wpforms-payments-wrap-payment .wpforms-payment-actions .wpforms-payment-action-status-value.partrefund span {
  white-space: nowrap;
}

#wpforms-payments.wpforms-payments-wrap-payment .wpforms-payment-actions .actions {
  align-items: center;
  justify-content: end;
  display: flex;
  width: 50%;
}

#wpforms-payments.wpforms-payments-wrap-payment .wpforms-payment-actions .actions .link {
  margin-right: 10px;
  font-weight: 400;
  font-size: 13px;
  line-height: 16px;
  text-decoration: none;
}

#wpforms-payments.wpforms-payments-wrap-payment .wpforms-payment-actions .actions .link:focus {
  outline: none;
  box-shadow: none;
}

#wpforms-payments.wpforms-payments-wrap-payment .wpforms-payment-actions .actions .button {
  box-shadow: none;
}

#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-info h2.hndle,
#wpforms-payments.wpforms-payments-wrap-payment #wpforms-subscription-details h2.hndle,
#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-entry-fields h2.hndle,
#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-advanced-info h2.hndle {
  flex-wrap: wrap;
  gap: 5px;
  justify-content: flex-start;
  padding: 12px 15px;
}

#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-info h2.hndle span:first-child,
#wpforms-payments.wpforms-payments-wrap-payment #wpforms-subscription-details h2.hndle span:first-child,
#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-entry-fields h2.hndle span:first-child,
#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-advanced-info h2.hndle span:first-child {
  font-size: 16px;
  font-weight: 600;
  line-height: 20px;
}

#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-info h2.hndle span:nth-child(n+2)::before,
#wpforms-payments.wpforms-payments-wrap-payment #wpforms-subscription-details h2.hndle span:nth-child(n+2)::before,
#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-entry-fields h2.hndle span:nth-child(n+2)::before,
#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-advanced-info h2.hndle span:nth-child(n+2)::before {
  content: "/";
  color: #a7aaad;
  margin-inline-end: 5px;
}

#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-info h2.hndle .dashicons,
#wpforms-payments.wpforms-payments-wrap-payment #wpforms-subscription-details h2.hndle .dashicons,
#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-entry-fields h2.hndle .dashicons,
#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-advanced-info h2.hndle .dashicons {
  font-size: 16px;
  height: 16px;
  width: 16px;
  margin: 4px 4px 0 0;
  color: #e27730;
}

#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-info h2.hndle .wpforms-payment-entry-id,
#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-info h2.hndle .wpforms-payment-id,
#wpforms-payments.wpforms-payments-wrap-payment #wpforms-subscription-details h2.hndle .wpforms-payment-entry-id,
#wpforms-payments.wpforms-payments-wrap-payment #wpforms-subscription-details h2.hndle .wpforms-payment-id,
#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-entry-fields h2.hndle .wpforms-payment-entry-id,
#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-entry-fields h2.hndle .wpforms-payment-id,
#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-advanced-info h2.hndle .wpforms-payment-entry-id,
#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-advanced-info h2.hndle .wpforms-payment-id {
  color: #50575e;
  font-size: 14px;
  font-weight: 400;
  line-height: 17px;
}

#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-info .inside,
#wpforms-payments.wpforms-payments-wrap-payment #wpforms-subscription-details .inside,
#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-entry-fields .inside,
#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-advanced-info .inside {
  padding: 0;
  margin: 0;
}

#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-info .inside p,
#wpforms-payments.wpforms-payments-wrap-payment #wpforms-subscription-details .inside p,
#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-entry-fields .inside p,
#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-advanced-info .inside p {
  padding: 0;
  margin: 0;
}

#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-info .inside .wpforms-payments-details-list,
#wpforms-payments.wpforms-payments-wrap-payment #wpforms-subscription-details .inside .wpforms-payments-details-list {
  background-color: #dcdcde;
  display: flex;
  flex-wrap: wrap;
  gap: 1px;
  margin: 0;
}

#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-info .inside .wpforms-payments-details-list .wpforms-payments-details-stat-card,
#wpforms-payments.wpforms-payments-wrap-payment #wpforms-subscription-details .inside .wpforms-payments-details-list .wpforms-payments-details-stat-card {
  flex-grow: 1;
  margin: 0;
  width: 24%;
}

#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-info .inside .wpforms-payments-details-list .wpforms-payments-details-stat-card .is-amount .statcard-value,
#wpforms-payments.wpforms-payments-wrap-payment #wpforms-subscription-details .inside .wpforms-payments-details-list .wpforms-payments-details-stat-card .is-amount .statcard-value {
  word-break: break-all;
}

@media (max-width: 1367px) and (min-width: 601px) {
  #wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-info .inside .wpforms-payments-details-list .wpforms-payments-details-stat-card,
  #wpforms-payments.wpforms-payments-wrap-payment #wpforms-subscription-details .inside .wpforms-payments-details-list .wpforms-payments-details-stat-card {
    width: 49%;
  }
}

@media (max-width: 600px) {
  #wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-info .inside .wpforms-payments-details-list .wpforms-payments-details-stat-card,
  #wpforms-payments.wpforms-payments-wrap-payment #wpforms-subscription-details .inside .wpforms-payments-details-list .wpforms-payments-details-stat-card {
    width: 100%;
  }
}

#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-info .inside .wpforms-payments-details-list .wpforms-payments-details-stat-card button,
#wpforms-payments.wpforms-payments-wrap-payment #wpforms-subscription-details .inside .wpforms-payments-details-list .wpforms-payments-details-stat-card button {
  align-items: center;
  background-color: #ffffff;
  border-width: 0;
  box-shadow: none;
  column-gap: 15px;
  display: grid;
  justify-items: start;
  grid-template-columns: 48px 1fr;
  grid-template-rows: repeat(2, auto);
  grid-template-areas: "image label" "image value";
  height: 100%;
  outline: 0;
  padding: 15px;
  text-align: left;
  width: 100%;
}

#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-info .inside .wpforms-payments-details-list .wpforms-payments-details-stat-card button::before,
#wpforms-payments.wpforms-payments-wrap-payment #wpforms-subscription-details .inside .wpforms-payments-details-list .wpforms-payments-details-stat-card button::before {
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  content: "";
  grid-area: image;
  height: 48px;
  width: 48px;
}

#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-info .inside .wpforms-payments-details-list .wpforms-payments-details-stat-card .coupon::before,
#wpforms-payments.wpforms-payments-wrap-payment #wpforms-subscription-details .inside .wpforms-payments-details-list .wpforms-payments-details-stat-card .coupon::before {
  background-image: url(../images/payments/single/icon-coupon.svg);
}

#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-info .inside .wpforms-payments-details-list .wpforms-payments-details-stat-card .cycle::before,
#wpforms-payments.wpforms-payments-wrap-payment #wpforms-subscription-details .inside .wpforms-payments-details-list .wpforms-payments-details-stat-card .cycle::before {
  background-image: url(../images/payments/single/icon-cycle.svg);
}

#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-info .inside .wpforms-payments-details-list .wpforms-payments-details-stat-card .date::before,
#wpforms-payments.wpforms-payments-wrap-payment #wpforms-subscription-details .inside .wpforms-payments-details-list .wpforms-payments-details-stat-card .date::before {
  background-image: url(../images/payments/single/icon-date.svg);
}

#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-info .inside .wpforms-payments-details-list .wpforms-payments-details-stat-card .lifetime-total::before,
#wpforms-payments.wpforms-payments-wrap-payment #wpforms-subscription-details .inside .wpforms-payments-details-list .wpforms-payments-details-stat-card .lifetime-total::before {
  background-image: url(../images/payments/single/icon-lifetime-total.svg);
}

#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-info .inside .wpforms-payments-details-list .wpforms-payments-details-stat-card .total::before,
#wpforms-payments.wpforms-payments-wrap-payment #wpforms-subscription-details .inside .wpforms-payments-details-list .wpforms-payments-details-stat-card .total::before {
  background-image: url(../images/payments/single/icon-total.svg);
}

#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-info .inside .wpforms-payments-details-list .wpforms-payments-details-stat-card .method::before,
#wpforms-payments.wpforms-payments-wrap-payment #wpforms-subscription-details .inside .wpforms-payments-details-list .wpforms-payments-details-stat-card .method::before {
  background-image: url(../images/payments/single/icon-method.svg);
}

#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-info .inside .wpforms-payments-details-list .wpforms-payments-details-stat-card .one-time::before,
#wpforms-payments.wpforms-payments-wrap-payment #wpforms-subscription-details .inside .wpforms-payments-details-list .wpforms-payments-details-stat-card .one-time::before {
  background-image: url(../images/payments/single/icon-one-time.svg);
}

#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-info .inside .wpforms-payments-details-list .wpforms-payments-details-stat-card .subscription::before,
#wpforms-payments.wpforms-payments-wrap-payment #wpforms-subscription-details .inside .wpforms-payments-details-list .wpforms-payments-details-stat-card .subscription::before {
  background-image: url(../images/payments/single/icon-subscription.svg);
}

#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-info .inside .wpforms-payments-details-list .wpforms-payments-details-stat-card .stat-card-label,
#wpforms-payments.wpforms-payments-wrap-payment #wpforms-subscription-details .inside .wpforms-payments-details-list .wpforms-payments-details-stat-card .stat-card-label {
  color: #787c82;
  font-size: 14px;
  grid-area: label;
  line-height: 17px;
}

#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-info .inside .wpforms-payments-details-list .wpforms-payments-details-stat-card .stat-card-value,
#wpforms-payments.wpforms-payments-wrap-payment #wpforms-subscription-details .inside .wpforms-payments-details-list .wpforms-payments-details-stat-card .stat-card-value {
  display: flex;
  align-items: center;
  gap: 5px;
  color: #50575e;
  font-weight: 600;
  font-size: 20px;
  grid-area: value;
  line-height: 24px;
}

#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-info .inside .wpforms-payments-details-list .wpforms-payments-details-stat-card .stat-card-value span,
#wpforms-payments.wpforms-payments-wrap-payment #wpforms-subscription-details .inside .wpforms-payments-details-list .wpforms-payments-details-stat-card .stat-card-value span {
  word-break: break-word;
}

@media (max-width: 600px) {
  #wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-info .inside .wpforms-payments-details-list .wpforms-payments-details-stat-card .stat-card-value,
  #wpforms-payments.wpforms-payments-wrap-payment #wpforms-subscription-details .inside .wpforms-payments-details-list .wpforms-payments-details-stat-card .stat-card-value {
    font-size: 18px;
  }
}

#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-advanced-info .inside .wpforms-payment-advanced-item {
  display: flex;
  flex-direction: row;
  background: #f6f6f6;
  padding: 8px 12px 8px 15px;
}

@media (max-width: 781px) {
  #wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-advanced-info .inside .wpforms-payment-advanced-item {
    flex-direction: column;
  }
}

#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-advanced-info .inside .wpforms-payment-advanced-item:nth-child(2n) {
  background: #ffffff;
}

#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-advanced-info .inside .wpforms-payment-advanced-item:last-child {
  border-radius: 4px;
}

#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-advanced-info .inside .wpforms-payment-advanced-item .wpforms-payment-advanced-item-label {
  font-weight: 600;
  width: 20%;
}

@media (max-width: 781px) {
  #wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-advanced-info .inside .wpforms-payment-advanced-item .wpforms-payment-advanced-item-label {
    width: 100%;
  }
}

#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-advanced-info .inside .wpforms-payment-advanced-item .wpforms-payment-advanced-item-value {
  width: 80%;
}

#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-entry-fields .inside {
  line-height: 16px;
}

#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-entry-fields .inside .no-fields {
  padding: 12px;
  margin: 0;
}

#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-entry-fields .inside .wpforms-payment-entry-field-name {
  background: #f6f6f6;
  color: #32373c;
  font-weight: 600;
  line-height: inherit;
  padding: 10px 15px;
}

#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-entry-fields .inside .wpforms-payment-entry-field-value {
  background: #fff;
  color: #50575e;
  padding: 10px 15px;
}

#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-entry-fields .inside .wpforms-payment-entry-field-value:last-child {
  border-radius: 4px;
}

#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-entry-fields .inside .empty .wpforms-payment-entry-field-value {
  font-style: italic;
  color: #999;
}

#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-entry-fields .inside .wpforms-field-file-upload p.file-entry {
  padding-left: 12px;
}

#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-entry-fields .inside .wpforms-field-file-upload .wpforms-help-tooltip {
  line-height: 16px;
  font-size: 14px;
  margin-left: 10px;
  color: #a6a6a6;
}

#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-entry-fields .inside .wpforms-field-file-upload .wpforms-help-tooltip:hover {
  color: #444;
}

#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-entry-fields .inside .wpforms-field-file-upload .file-icon {
  padding-right: 10px;
}

#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-entry-fields .inside .wpforms-field-file-upload .file-icon img {
  vertical-align: middle;
}

#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-entry-fields .inside .wpforms-field-file-upload .button-link-delete {
  text-decoration: none;
}

#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-entry-fields .inside .wpforms-field-file-upload .wpforms-trash-icon {
  width: 16px;
  height: 16px;
  font-size: 16px;
  color: #a00;
  margin-left: 8px;
}

#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-entry-fields .inside .wpforms-field-file-upload .wpforms-trash-icon:hover {
  color: #dc3232;
}

#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-entry-fields .inside .wpforms-field-file-upload a {
  cursor: pointer;
}

#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-entry-fields .inside .wpforms-field-file-upload a.disabled {
  opacity: 0.5;
}

#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-entry-fields .inside .wpforms-payment-entry-repeater-block, #wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-entry-fields .inside .wpforms-payment-entry-repeater-block > .wpforms-payment-entry-field-name,
#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-entry-fields .inside .wpforms-payment-entry-layout-block,
#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-entry-fields .inside .wpforms-payment-entry-layout-block > .wpforms-payment-entry-field-name {
  border-bottom: 1px solid #dcdcde;
}

#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-entry-fields .inside .wpforms-payment-entry-repeater-block .wpforms-payment-entry-repeater-row,
#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-entry-fields .inside .wpforms-payment-entry-repeater-block .wpforms-payment-entry-layout-row,
#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-entry-fields .inside .wpforms-payment-entry-layout-block .wpforms-payment-entry-repeater-row,
#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-entry-fields .inside .wpforms-payment-entry-layout-block .wpforms-payment-entry-layout-row {
  display: flex;
  flex-wrap: nowrap;
  justify-content: space-between;
}

#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-entry-fields .inside .wpforms-payment-entry-repeater-block .wpforms-payment-entry-repeater-row .wpforms-payment-entry-column,
#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-entry-fields .inside .wpforms-payment-entry-repeater-block .wpforms-payment-entry-layout-row .wpforms-payment-entry-column,
#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-entry-fields .inside .wpforms-payment-entry-layout-block .wpforms-payment-entry-repeater-row .wpforms-payment-entry-column,
#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-entry-fields .inside .wpforms-payment-entry-layout-block .wpforms-payment-entry-layout-row .wpforms-payment-entry-column {
  width: var(--field-layout-column-width, auto);
}

#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-entry-fields .inside .wpforms-payment-entry-repeater-block .wpforms-payment-entry-repeater-row .wpforms-payment-entry-field .wpforms-payment-entry-field-name,
#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-entry-fields .inside .wpforms-payment-entry-repeater-block .wpforms-payment-entry-layout-row .wpforms-payment-entry-field .wpforms-payment-entry-field-name,
#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-entry-fields .inside .wpforms-payment-entry-layout-block .wpforms-payment-entry-repeater-row .wpforms-payment-entry-field .wpforms-payment-entry-field-name,
#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-entry-fields .inside .wpforms-payment-entry-layout-block .wpforms-payment-entry-layout-row .wpforms-payment-entry-field .wpforms-payment-entry-field-name {
  display: none;
}

#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-entry-fields .inside .wpforms-payment-entry-repeater-block .wpforms-payment-entry-repeater-row .wpforms-payment-entry-field:first-child .wpforms-payment-entry-field-name,
#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-entry-fields .inside .wpforms-payment-entry-repeater-block .wpforms-payment-entry-layout-row .wpforms-payment-entry-field:first-child .wpforms-payment-entry-field-name,
#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-entry-fields .inside .wpforms-payment-entry-layout-block .wpforms-payment-entry-repeater-row .wpforms-payment-entry-field:first-child .wpforms-payment-entry-field-name,
#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-entry-fields .inside .wpforms-payment-entry-layout-block .wpforms-payment-entry-layout-row .wpforms-payment-entry-field:first-child .wpforms-payment-entry-field-name {
  display: block;
}

#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-entry-fields .wpforms-empty-field-toggle {
  float: right;
  text-decoration: none;
  padding: 2px 0 0;
  font-size: 12px;
}

#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-entry-fields .wpforms-field-richtext .wpforms-payment-entry-field-value {
  max-width: 100%;
  overflow: auto;
}

#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-entry-fields .wpforms-field-richtext .wpforms-payment-entry-field-value-richtext {
  width: calc(100% + 24px);
  height: 0;
  margin: -8px -12px -11px -12px;
}

#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-entry-fields .wpforms-field-richtext img {
  max-width: 100%;
}

#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-details .inside,
#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-actions .inside {
  margin: 0;
  padding: 0;
}

#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-details .button-delete {
  color: #d63638;
  border-color: #d63638;
}

#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-details .button-delete:hover {
  text-decoration: none;
}

#wpforms-payments.wpforms-payments-wrap-payment .wpforms-payment-details-meta .dashicons,
#wpforms-payments.wpforms-payments-wrap-payment .wpforms-payment-actions-meta .dashicons {
  color: #8c8f94;
  font-size: 22px;
  height: 22px;
  inset-inline-start: 12px;
  position: absolute;
  text-decoration: none;
  top: 5px;
  width: 22px;
}

#wpforms-payments.wpforms-payments-wrap-payment .wpforms-payment-details-meta .dashicons.dashicons-marker,
#wpforms-payments.wpforms-payments-wrap-payment .wpforms-payment-actions-meta .dashicons.dashicons-marker {
  color: #ffb900;
}

#wpforms-payments.wpforms-payments-wrap-payment .wpforms-payment-details-meta {
  padding: 5px 0;
}

#wpforms-payments.wpforms-payments-wrap-payment .wpforms-payment-details-meta p {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  margin: 0;
  padding: 6px 12px 6.5px 42px;
  position: relative;
}

#wpforms-payments.wpforms-payments-wrap-payment .wpforms-payment-details-meta strong {
  font-weight: 600;
}

#wpforms-payments.wpforms-payments-wrap-payment .wpforms-payment-details-meta .dashicons-wpforms {
  background-color: #8c8f94;
  -webkit-mask-image: url(../images/brand.svg);
  mask-image: url(../images/brand.svg);
}

#wpforms-payments.wpforms-payments-wrap-payment .wpforms-payment-actions-meta {
  padding: 0;
}

#wpforms-payments.wpforms-payments-wrap-payment .wpforms-payment-actions-meta p {
  border-bottom: 1px solid #eee;
  padding: 10px;
}

#wpforms-payments.wpforms-payments-wrap-payment .wpforms-payment-actions-meta p:last-of-type {
  border-bottom: 0;
}

#wpforms-payments.wpforms-payments-wrap-payment .wpforms-payment-actions-meta strong {
  font-weight: 600;
}

#wpforms-payments.wpforms-payments-wrap-payment .wpforms-payment-actions-meta .dashicons {
  margin-right: 3px;
}

#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-logs .inside {
  margin-top: 12px;
  padding-bottom: 0;
}

#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-logs .inside .wpforms-payment-no-logs {
  display: flex;
  flex-direction: column;
  line-height: 18px;
  padding-bottom: 12px;
}

#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-logs .inside .wpforms-payment-log-item {
  display: flex;
  flex-direction: column;
  background: #f6f6f6;
  border-radius: 3px;
  padding: 10px 12px;
  margin-bottom: 12px;
}

#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-logs .inside .wpforms-payment-log-item .wpforms-payment-log-item-value {
  color: #50575e;
  font-size: 13px;
  line-height: 18px;
  word-wrap: break-word;
}

#wpforms-payments.wpforms-payments-wrap-payment #wpforms-payment-logs .inside .wpforms-payment-log-item .wpforms-payment-log-item-date {
  color: #a7aaad;
  font-size: 12px;
  line-height: 18px;
}

#wpforms-payments.wpforms-payments-wrap-payment .wpforms-payment-single-education-notice {
  background: #fef8f5;
  border-radius: 3px;
  border: 1px solid rgba(226, 119, 48, 0.5);
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-start;
  padding: 15px;
  gap: 5px;
}

#wpforms-payments.wpforms-payments-wrap-payment .wpforms-payment-single-education-notice-title {
  font-weight: 600;
  font-size: 15px;
  line-height: 18px;
}

#wpforms-payments.wpforms-payments-wrap-payment .wpforms-payment-single-education-notice-description {
  align-items: center;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #32373c;
}

#wpforms-payments.wpforms-payments-wrap-payment .wpforms-payment-single-education-notice-description a {
  color: #e27730;
  font-weight: 600;
  text-decoration: none;
}

#wpforms-payments.wpforms-payments-wrap-payment .wpforms-payment-single-education-notice-description a:hover {
  color: #cd6622;
}

#wpforms-payments.wpforms-payments-wrap-payment .wpforms-payment-single-education-notice .wpforms-payment-single-education-notice-dismiss-button {
  z-index: 2;
  position: absolute;
  right: 0;
  top: 0;
  padding: 11px;
  color: #efb58f;
  cursor: pointer;
}

#wpforms-payments.wpforms-payments-wrap-payment .wpforms-payment-single-education-notice .wpforms-payment-single-education-notice-dismiss-button:hover {
  color: #e27730;
}

#wpforms-payments.wpforms-payments-wrap-payment .wpforms-payment-single-education-notice .wpforms-payment-single-education-notice-dismiss-button .dashicons {
  width: 16px;
  height: 16px;
  font-size: 16px;
}

#post-body-content .postbox {
  overflow: hidden;
}

#post-body-content h2.hndle {
  flex-wrap: wrap;
  gap: 5px;
  justify-content: flex-start;
  padding: 12px 15px;
}

#post-body-content h2.hndle span:first-child {
  font-size: 16px;
  font-weight: 500;
  line-height: 20px;
}

#post-body-content h2.hndle span:nth-child(n+2)::before {
  content: "/";
  color: #a7aaad;
  margin-inline-end: 5px;
}

#post-body-content h2.hndle .dashicons {
  font-size: 16px;
  height: 16px;
  width: 16px;
  margin: 4px 4px 0 0;
  color: #e27730;
}

#post-body-content h2.hndle .wpforms-payment-entry-id,
#post-body-content h2.hndle .wpforms-payment-id {
  color: #50575e;
  font-size: 14px;
  font-weight: 400;
  line-height: 17px;
}

#post-body-content .inside {
  padding: 0;
  margin: 0;
}

#post-body-content .inside p {
  padding: 0;
  margin: 0;
}

.wpforms-subscription-payment-history {
  border-collapse: collapse;
  line-height: 18px;
  text-transform: capitalize;
  width: 100%;
}

.wpforms-subscription-payment-history th,
.wpforms-subscription-payment-history td {
  padding: 10px;
}

@media (min-width: 782px) {
  .wpforms-subscription-payment-history th:first-of-type,
  .wpforms-subscription-payment-history td:first-of-type {
    padding-inline-start: 15px;
  }
  .wpforms-subscription-payment-history th:last-of-type,
  .wpforms-subscription-payment-history td:last-of-type {
    padding-inline-end: 15px;
  }
}

@media (max-width: 781px) {
  .wpforms-subscription-payment-history th,
  .wpforms-subscription-payment-history td {
    padding: 5px 15px;
  }
  .wpforms-subscription-payment-history th:first-of-type,
  .wpforms-subscription-payment-history td:first-of-type {
    padding-block-start: 15px;
  }
  .wpforms-subscription-payment-history th:last-of-type,
  .wpforms-subscription-payment-history td:last-of-type {
    padding-block-end: 15px;
  }
}

.wpforms-subscription-payment-history th {
  color: #32373c;
  font-weight: 600;
  text-align: start;
}

.wpforms-subscription-payment-history thead {
  text-align: left;
  background: #f6f6f6;
}

@media (max-width: 781px) {
  .wpforms-subscription-payment-history thead {
    display: none;
  }
}

.wpforms-subscription-payment-history tbody {
  color: #50575e;
}

.wpforms-subscription-payment-history tbody tr.current {
  background: #e6f0f7;
  font-weight: 500;
}

.wpforms-subscription-payment-history tbody tr:nth-child(even):not(.current) {
  background: #f6f6f6;
}

.wpforms-subscription-payment-history tbody td:first-of-type a {
  color: #056aab;
  display: inline-flex;
}

.wpforms-subscription-payment-history tbody td:first-of-type a::before {
  content: "#";
}

@media (max-width: 781px) {
  .wpforms-subscription-payment-history tbody td {
    display: block;
  }
  .wpforms-subscription-payment-history tbody td::before {
    content: attr(data-title) ": ";
    font-weight: 500;
  }
}

.rtl #wpforms-payments.wpforms-payments-wrap-payment .actions .link {
  margin-right: 0;
  margin-left: 10px;
}

.rtl #wpforms-payments.wpforms-payments-wrap-payment .wpforms-payment-details-meta p {
  padding: 6px 42px 6.5px 12px;
}

.rtl #wpforms-payments.wpforms-payments-wrap-payment .wpforms-payment-single-education-notice .wpforms-payment-single-education-notice-dismiss-button {
  left: 0;
  right: auto;
}

/**
 * "Payment Entries" overview page inside the admin, which lists all payment records.
 * This page is accessible via "WPForms" → "Payments".
 *
 * @since 1.8.2
 */
.wpforms-payments-overview-stats {
  border: 1px solid #c3c4c7;
  border-radius: 4px;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  overflow: hidden;
}

.wpforms-payments-overview-stats .wpforms-overview-chart {
  box-shadow: none;
  border-width: 0;
  border-radius: 0;
  margin-bottom: 0;
}

.wpforms-payments-overview-stats .wpforms-overview-chart-canvas {
  height: 329px;
}

.wpforms-payments-overview-reports.doing-ajax {
  pointer-events: none;
}

.wpforms-payments-overview-reports-legend {
  background-color: #dcdcde;
  display: flex;
  flex-wrap: wrap;
  gap: 1px;
  margin: 0;
  padding-top: 1px;
}

.wpforms-payments-overview-reports-statcard {
  flex-grow: 1;
  margin: 0;
  width: 32%;
}

@media (max-width: 1280px) and (min-width: 769px) {
  .wpforms-payments-overview-reports-statcard {
    width: 49%;
  }
}

@media (max-width: 768px) {
  .wpforms-payments-overview-reports-statcard {
    width: 100%;
  }
}

@media (min-width: 1368px) {
  .wpforms-payments-overview-reports-statcard:nth-child(1):nth-last-child(4) {
    width: 24%;
  }
}

@media (max-width: 1367px) and (min-width: 1281px) {
  .wpforms-payments-overview-reports-statcard:nth-child(1):nth-last-child(4) {
    width: 49%;
  }
}

@media (min-width: 1368px) {
  .wpforms-payments-overview-reports-statcard:nth-child(2):nth-last-child(3) {
    width: 24%;
  }
}

@media (max-width: 1367px) and (min-width: 1281px) {
  .wpforms-payments-overview-reports-statcard:nth-child(2):nth-last-child(3) {
    width: 49%;
  }
}

@media (min-width: 1368px) {
  .wpforms-payments-overview-reports-statcard:nth-child(3):nth-last-child(2) {
    width: 24%;
  }
}

@media (max-width: 1367px) and (min-width: 1281px) {
  .wpforms-payments-overview-reports-statcard:nth-child(3):nth-last-child(2) {
    width: 49%;
  }
}

@media (min-width: 1368px) {
  .wpforms-payments-overview-reports-statcard:nth-child(4):nth-last-child(1) {
    width: 24%;
  }
}

@media (max-width: 1367px) and (min-width: 1281px) {
  .wpforms-payments-overview-reports-statcard:nth-child(4):nth-last-child(1) {
    width: 49%;
  }
}

.wpforms-payments-overview-reports-statcard button {
  align-content: center;
  background-color: #fafafa;
  border-width: 0;
  box-shadow: none;
  column-gap: 15px;
  display: grid;
  grid-template-columns: 48px 1fr auto;
  grid-template-rows: repeat(2, auto);
  grid-template-areas: "image label delta" "image value delta";
  height: 100%;
  outline: 0;
  padding: 21px 20px;
  text-align: start;
  width: 100%;
}

.wpforms-payments-overview-reports-statcard button:not(.disabled) {
  cursor: pointer;
}

.wpforms-payments-overview-reports-statcard button:not(.disabled).is-selected, .wpforms-payments-overview-reports-statcard button:not(.disabled):hover {
  background-color: #ffffff;
}

.wpforms-payments-overview-reports-statcard button::before {
  align-self: center;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  content: "";
  grid-area: image;
  height: 48px;
  width: 48px;
}

.wpforms-payments-overview-reports-statcard .total-payments::before {
  background-image: url(../images/payments/icon-total-payments.svg);
}

.is-ready .wpforms-payments-overview-reports-statcard .total-payments.is-selected {
  border-color: #056aab;
  box-shadow: inset 0 3px 0 #056aab;
}

.wpforms-payments-overview-reports-statcard .total-sales::before {
  background-image: url(../images/payments/icon-total-sales.svg);
}

.is-ready .wpforms-payments-overview-reports-statcard .total-sales.is-selected {
  border-color: #389547;
  box-shadow: inset 0 3px 0 #389547;
}

.wpforms-payments-overview-reports-statcard .total-refunded::before {
  background-image: url(../images/payments/icon-total-refunded.svg);
}

.is-ready .wpforms-payments-overview-reports-statcard .total-refunded.is-selected {
  border-color: #50575e;
  box-shadow: inset 0 3px 0 #50575e;
}

.wpforms-payments-overview-reports-statcard .total-subscription::before {
  background-image: url(../images/payments/icon-total-subscription.svg);
}

.is-ready .wpforms-payments-overview-reports-statcard .total-subscription.is-selected {
  border-color: #e27730;
  box-shadow: inset 0 3px 0 #e27730;
}

.wpforms-payments-overview-reports-statcard .total-renewal-subscription::before {
  background-image: url(../images/payments/icon-total-subscription.svg);
}

.is-ready .wpforms-payments-overview-reports-statcard .total-renewal-subscription.is-selected {
  border-color: #e27730;
  box-shadow: inset 0 3px 0 #e27730;
}

.wpforms-payments-overview-reports-statcard .total-coupons::before {
  background-image: url(../images/payments/icon-total-coupons.svg);
}

.is-ready .wpforms-payments-overview-reports-statcard .total-coupons.is-selected {
  border-color: #e27730;
  box-shadow: inset 0 3px 0 #e27730;
}

.wpforms-payments-overview-reports-statcard .statcard-label {
  color: #787c82;
  font-size: 14px;
  grid-area: label;
  line-height: 17px;
}

.wpforms-payments-overview-reports-statcard .statcard-value {
  color: #50575e;
  font-weight: 600;
  font-size: 22px;
  grid-area: value;
  line-height: 27px;
}

.wpforms-payments-overview-reports-statcard .statcard-value span {
  color: #a7aaad;
  font-weight: 400;
}

@media (max-width: 600px) {
  .wpforms-payments-overview-reports-statcard .statcard-value {
    font-size: 18px;
  }
}

.wpforms-payments-overview-reports-statcard .statcard-delta {
  align-self: center;
  background-color: #eaeaeb;
  border-radius: 2px;
  color: #50575e;
  font-weight: 600;
  font-size: 11px;
  grid-area: delta;
  line-height: 13px;
}

.wpforms-payments-overview-reports-statcard .statcard-delta.is-calculated {
  padding: 5px 7px;
}

.wpforms-payments-overview-reports-statcard .statcard-delta.is-calculated::after {
  content: "%";
}

.wpforms-payments-overview-reports-statcard .statcard-delta:is(.is-upward, .is-downward)::before {
  background-image: url(../images/payments/chevron.svg);
  background-repeat: no-repeat;
  background-size: contain;
  content: "";
  display: inline-block;
  height: 11px;
  margin-inline-end: 5px;
  vertical-align: middle;
  width: 11px;
}

.wpforms-payments-overview-reports-statcard .statcard-delta.is-downward::before {
  transform: rotate(180deg);
}

.wpforms-payments-overview-reports-statcard .is-amount .statcard-value {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.wpforms-payments-overview-reports-statcard .upsell .statcard-delta {
  background-image: url(../images/payments/star.svg);
  background-repeat: no-repeat;
  background-size: contain;
  width: 25px;
  height: 25px;
}

.wpforms-payments-wrap-payments ul.subsubsub {
  margin: 0;
  line-height: 30px;
}

.wpforms-payments-wrap-payments #wpforms-reset-filter {
  border-color: #c3c4c7;
  border-radius: 4px;
  margin-top: 0;
}

.wpforms-payments-wrap-payments #wpforms-reset-filter .reset {
  text-decoration: none;
  margin-inline: 3px;
}

@media (max-width: 1279px) and (min-width: 783px) {
  .wpforms-payments-wrap-payments .search-box {
    justify-content: flex-start;
    margin-block: 10px;
    width: 100%;
  }
  .wpforms-payments-wrap-payments .search-box #wpforms-payments-search-input {
    flex-grow: unset;
  }
}

@media (max-width: 782px) {
  .wpforms-payments-wrap-payments .search-box {
    margin-top: 10px;
  }
}

.wpforms-payments-wrap-payments .tablenav .actions .button,
.wpforms-payments-wrap-payments .tablenav .wpforms-tablenav-filters .button {
  font-size: 14px;
  line-height: 1.67;
}

@media (max-width: 1279px) and (min-width: 783px) {
  .wpforms-payments-wrap-payments .tablenav {
    display: flex;
    flex-wrap: wrap;
    gap: 3px;
    height: auto;
  }
}

@media (max-width: 1279px) and (min-width: 783px) {
  .wpforms-payments-wrap-payments .tablenav-pages {
    margin-left: auto;
  }
}

@media (max-width: 782px) {
  .wpforms-payments-wrap-payments .tablenav-pages {
    margin-top: 10px;
  }
}

.wpforms-payments-wrap-payments .wpforms-tablenav-filters {
  display: inline-flex;
  flex-wrap: wrap;
  float: left;
  gap: 6px;
  margin: 0 0 9px;
}

@media (max-width: 1279px) and (min-width: 783px) {
  .wpforms-payments-wrap-payments .wpforms-tablenav-filters {
    order: -1;
    width: 100%;
  }
}

@media (max-width: 782px) {
  .wpforms-payments-wrap-payments .wpforms-tablenav-filters {
    width: 100%;
  }
  .wpforms-payments-wrap-payments .wpforms-tablenav-filters .button {
    margin-bottom: 0;
  }
}

@media (max-width: 600px) {
  .wpforms-payments-wrap-payments .wpforms-tablenav-filters > * {
    width: 100%;
  }
}

.wpforms-table-list-payments .payment-placeholder-text-none {
  color: #a7aaad;
}

.wpforms-table-list-payments td.column-title {
  word-break: break-word;
}

.wpforms-table-list-payments td.column-status {
  padding: 10px 11px;
  width: 1%;
}

.wpforms-table-list-payments td.column-status .wpforms-help-tooltip {
  background-image: url(../images/payments/icon-info.svg);
  background-repeat: no-repeat;
  background-size: contain;
  width: 11px;
  height: 11px;
}

@media (max-width: 1100px) and (min-width: 1025px) {
  .wpforms-table-list-payments.has-many-columns td.column-form {
    max-width: 7ch;
  }
}

@media (max-width: 1024px) and (min-width: 783px) {
  .wpforms-table-list-payments.has-many-columns .column-form {
    max-width: 100%;
  }
}

@media (min-width: 783px) {
  .wpforms-table-list-payments tbody tr {
    height: 44px;
  }
  .wpforms-table-list-payments .column-form {
    max-width: 240px;
  }
}

.wpforms-payment-status {
  align-items: center;
  border-radius: 2px;
  display: inline-flex;
  font-size: 12px;
  font-weight: 600;
  gap: 4px;
  line-height: 15px;
  padding: 4px 8px;
  white-space: nowrap;
}

.wpforms-payment-status.status-active {
  background-color: #dbf2df;
  color: #008a20;
}

.wpforms-payment-status.status-cancelled {
  background-color: #ededee;
  color: #50575e;
}

.wpforms-payment-status.status-completed {
  background-color: #dbf2df;
  color: #008a20;
}

.wpforms-payment-status.status-failed {
  background-color: #f7d6d6;
  color: #d63638;
}

.wpforms-payment-status.status-pending {
  background-color: #faf2d5;
  color: #bd8600;
}

.wpforms-payment-status.status-processed {
  background-color: #ededee;
  color: #50575e;
}

.wpforms-payment-status.status-not-synced {
  background-color: #ededee;
  color: #50575e;
}

.wpforms-payment-status.status-refunded {
  background-color: #ededee;
  color: #50575e;
}

.wpforms-payment-status.status-partrefund {
  background-color: #ededee;
  color: #50575e;
}

.column-subscription .wpforms-subscription-status, .column-subscription .payment-placeholder-text-none {
  white-space: nowrap;
}

.column-subscription .wpforms-subscription-status::before, .column-subscription .payment-placeholder-text-none::before {
  align-self: center;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  display: inline-block;
  content: "";
  height: 14px;
  width: 14px;
  margin-inline-end: 10px;
  position: relative;
  top: 3px;
  background-image: url(../images/payments/status/icon-n-a.svg);
}

.column-subscription .wpforms-subscription-status.status-active::before, .column-subscription .payment-placeholder-text-none.status-active::before {
  background-image: url(../images/payments/status/icon-active.svg);
}

.column-subscription .wpforms-subscription-status.status-cancelled::before, .column-subscription .payment-placeholder-text-none.status-cancelled::before {
  background-image: url(../images/payments/status/icon-cancelled.svg);
}

.column-subscription .wpforms-subscription-status.status-completed::before, .column-subscription .payment-placeholder-text-none.status-completed::before {
  background-image: url(../images/payments/status/icon-completed.svg);
}

.column-subscription .wpforms-subscription-status.status-failed::before, .column-subscription .payment-placeholder-text-none.status-failed::before {
  background-image: url(../images/payments/status/icon-failed.svg);
}

.column-subscription .wpforms-subscription-status.status-pending::before, .column-subscription .payment-placeholder-text-none.status-pending::before {
  background-image: url(../images/payments/status/icon-pending.svg);
}

.column-subscription .wpforms-subscription-status.status-processed::before, .column-subscription .payment-placeholder-text-none.status-processed::before {
  background-image: url(../images/payments/status/icon-processed.svg);
}

.column-subscription .wpforms-subscription-status.status-not-synced::before, .column-subscription .payment-placeholder-text-none.status-not-synced::before {
  background-image: url(../images/payments/status/icon-not-synced.svg);
}

.column-subscription .wpforms-subscription-status.status-refunded::before, .column-subscription .payment-placeholder-text-none.status-refunded::before {
  background-image: url(../images/payments/status/icon-refunded.svg);
}

.column-subscription .wpforms-subscription-status.status-partrefund::before, .column-subscription .payment-placeholder-text-none.status-partrefund::before {
  background-image: url(../images/payments/status/icon-partrefund.svg);
}

.wpforms-payments-viewing-test-mode {
  background-color: #fef5f0;
  border: 1px solid rgba(226, 119, 48, 0.5);
  border-radius: 4px;
  box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.05);
  color: #e27730;
  display: flex;
  font-weight: 600;
  gap: 6px;
  justify-content: center;
  margin-bottom: 20px;
  padding: 10px;
}

.wpforms-payments-viewing-test-mode::before {
  background-image: url(../images/payments/icon-exclamation.svg);
  background-size: contain;
  content: '';
  height: 16px;
  margin-top: 1px;
  width: 16px;
}

.wpforms-payments-viewing-test-mode p {
  margin-block: 0;
  line-height: 17px;
}

@media (max-width: 782px) {
  body.wpforms_page_wpforms-payments #wpbody-content {
    padding-bottom: 0;
  }
}

body.rtl .wpforms-tablenav-filters {
  float: right;
}

#wpforms-admin-analytics {
  width: 700px;
  margin: 0 auto;
}

#wpforms-admin-analytics *,
#wpforms-admin-analytics *::before,
#wpforms-admin-analytics *::after {
  box-sizing: border-box;
}

#wpforms-admin-analytics section {
  margin: 50px 0;
  text-align: left;
  clear: both;
}

#wpforms-admin-analytics p {
  font-size: 15px;
}

#wpforms-admin-analytics .top {
  text-align: center;
}

#wpforms-admin-analytics .top img {
  margin-bottom: 38px;
}

#wpforms-admin-analytics .top h1 {
  font-size: 26px;
  font-weight: 600;
  margin-bottom: 0;
  padding: 0;
}

#wpforms-admin-analytics .top p {
  font-size: 17px;
  color: #777777;
  margin-top: .5em;
}

#wpforms-admin-analytics .top .updated {
  display: none;
}

#wpforms-admin-analytics .screenshot > * {
  vertical-align: middle;
}

#wpforms-admin-analytics .screenshot .cont {
  display: inline-block;
  position: relative;
  width: 315px;
  padding: 5px;
  background-color: #ffffff;
  box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

#wpforms-admin-analytics .screenshot .cont img {
  max-width: 100%;
}

#wpforms-admin-analytics .screenshot .cont .hover {
  position: absolute;
  opacity: 0;
  height: 100%;
  width: 100%;
  top: 0;
  left: 0;
  border: 5px solid #ffffff;
  background-color: rgba(0, 0, 0, 0.15);
  background-image: url("../images/zoom.svg");
  background-repeat: no-repeat;
  background-position: center;
  background-size: 50px;
  transition: all 0.3s;
}

#wpforms-admin-analytics .screenshot .cont:hover .hover {
  opacity: 1;
  transition: all 0.3s;
}

#wpforms-admin-analytics .screenshot ul {
  display: inline-block;
  margin-left: 30px;
  list-style-type: none;
  max-width: calc(100% - 350px);
}

#wpforms-admin-analytics .screenshot li {
  margin: 16px 0;
  padding: 0 0 0 24px;
  font-size: 15px;
  background-image: url("../images/analytics/arrow-right.svg");
  background-position: left center;
  background-repeat: no-repeat;
  background-size: 14px;
  color: #777777;
}

#wpforms-admin-analytics .step {
  background-color: #f9f9f9;
  box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e5e5;
  margin: 0 0 25px 0;
}

#wpforms-admin-analytics .step > * {
  vertical-align: middle;
}

#wpforms-admin-analytics .step .num {
  display: inline-block;
  position: relative;
  width: 100px;
  height: 50px;
  text-align: center;
}

#wpforms-admin-analytics .step .loader {
  margin-top: -54px;
  transition: all .3s;
  opacity: 1;
}

#wpforms-admin-analytics .step div {
  display: inline-block;
  width: calc(100% - 104px);
  background-color: #ffffff;
  padding: 30px;
  border-left: 1px solid #eeeeee;
}

#wpforms-admin-analytics .step h2 {
  font-size: 24px;
  line-height: 1.2;
  margin-top: -5px;
  margin-bottom: -5px;
}

#wpforms-admin-analytics .step p {
  font-size: 16px;
  color: #777777;
}

#wpforms-admin-analytics .step .error {
  font-size: 14px;
  color: #b97474;
  margin-bottom: 0;
}

#wpforms-admin-analytics .step .button {
  font-weight: 600;
  padding: 10px 12px;
  min-width: 180px;
  height: auto;
  line-height: 1.2;
  text-align: center;
  font-size: 15px;
  transition: all .3s;
}

#wpforms-admin-analytics .step .button.grey {
  background: #f6f6f6;
  border: 1px solid #dddddd;
  box-shadow: none;
  text-shadow: none;
  color: #9fa5aa;
}

#wpforms-admin-analytics .step .button.disabled {
  cursor: default;
}

#wpforms-admin-analytics .step .link {
  text-decoration: none;
}

#wpforms-admin-analytics .step .hidden {
  opacity: 0;
  transition: all .3s;
}

#wpforms-admin-analytics .grey {
  opacity: 0.5;
}

@media (max-width: 767px) {
  #wpforms-admin-analytics {
    width: auto;
    margin: 0 20px;
  }
  #wpforms-admin-analytics .screenshot .cont {
    width: auto;
  }
  #wpforms-admin-analytics .screenshot ul {
    margin: 20px 0 0 15px;
    max-width: 100%;
  }
  #wpforms-admin-analytics .button {
    white-space: normal;
  }
}

@media (max-width: 540px) {
  #wpforms-admin-analytics .step .num {
    display: block;
    width: 100%;
    height: auto;
    padding: 20px 30px;
    text-align: left;
  }
  #wpforms-admin-analytics .step .loader {
    margin-left: 0;
  }
  #wpforms-admin-analytics .step div {
    display: block;
    width: 100%;
    border-left: none;
  }
}

.wpforms-admin-plugin-landing .notice {
  display: none !important;
}

.wpforms-admin-plugin-landing .loader {
  margin: 0 auto;
  position: relative;
  text-indent: -9999em;
  border-top: 4px solid #969696;
  border-right: 4px solid #969696;
  border-bottom: 4px solid #969696;
  border-left: 4px solid #404040;
  transform: translateZ(0);
  animation: load8 1.1s infinite linear;
  background-color: transparent;
}

.wpforms-admin-plugin-landing .loader, .wpforms-admin-plugin-landing .loader:after {
  display: block;
  border-radius: 50%;
  width: 50px;
  height: 50px;
}

@-webkit-keyframes load8 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes load8 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

#wpforms-admin-smtp {
  width: 700px;
  margin: 0 auto;
}

#wpforms-admin-smtp *,
#wpforms-admin-smtp *::before,
#wpforms-admin-smtp *::after {
  box-sizing: border-box;
}

#wpforms-admin-smtp section {
  margin: 50px 0;
  text-align: left;
  clear: both;
}

#wpforms-admin-smtp p {
  font-size: 15px;
}

#wpforms-admin-smtp .top {
  text-align: center;
}

#wpforms-admin-smtp .top img {
  margin-bottom: 38px;
}

#wpforms-admin-smtp .top h1 {
  font-size: 26px;
  font-weight: 600;
  margin-bottom: 0;
  padding: 0;
}

#wpforms-admin-smtp .top p {
  font-size: 17px;
  color: #777777;
  margin-top: .5em;
}

#wpforms-admin-smtp .top .updated {
  display: none;
}

#wpforms-admin-smtp .screenshot > * {
  vertical-align: middle;
}

#wpforms-admin-smtp .screenshot .cont {
  display: inline-block;
  position: relative;
  width: 315px;
  padding: 5px;
  background-color: #ffffff;
  box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

#wpforms-admin-smtp .screenshot .cont img {
  max-width: 100%;
  display: block;
}

#wpforms-admin-smtp .screenshot .cont .hover {
  position: absolute;
  opacity: 0;
  height: 100%;
  width: 100%;
  top: 0;
  left: 0;
  border: 5px solid #ffffff;
  background-color: rgba(0, 0, 0, 0.15);
  background-image: url("../images/zoom.svg");
  background-repeat: no-repeat;
  background-position: center;
  background-size: 50px;
  transition: all 0.3s;
}

#wpforms-admin-smtp .screenshot .cont:hover .hover {
  opacity: 1;
  transition: all 0.3s;
}

#wpforms-admin-smtp .screenshot ul {
  display: inline-block;
  margin: 0 0 0 30px;
  list-style-type: none;
  max-width: calc(100% - 350px);
}

#wpforms-admin-smtp .screenshot li {
  margin: 16px 0;
  padding: 0 0 0 24px;
  font-size: 15px;
  background-image: url("../images/smtp/arrow-right.svg");
  background-position: left 3px;
  background-repeat: no-repeat;
  background-size: 14px;
  color: #777777;
}

#wpforms-admin-smtp .step {
  background-color: #f9f9f9;
  box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e5e5;
  margin: 0 0 25px 0;
}

#wpforms-admin-smtp .step > * {
  vertical-align: middle;
}

#wpforms-admin-smtp .step .num {
  display: inline-block;
  position: relative;
  width: 100px;
  height: 50px;
  text-align: center;
}

#wpforms-admin-smtp .step .loader {
  margin-top: -54px;
  transition: all .3s;
  opacity: 1;
}

#wpforms-admin-smtp .step div {
  display: inline-block;
  width: calc(100% - 104px);
  background-color: #ffffff;
  padding: 30px;
  border-left: 1px solid #eeeeee;
}

#wpforms-admin-smtp .step h2 {
  font-size: 24px;
  line-height: 1.2;
  margin-top: -5px;
  margin-bottom: -5px;
}

#wpforms-admin-smtp .step p {
  font-size: 16px;
  color: #777777;
}

#wpforms-admin-smtp .step .error {
  font-size: 14px;
  color: #b97474;
  margin-bottom: 0;
}

#wpforms-admin-smtp .step .link {
  text-decoration: none;
}

#wpforms-admin-smtp .step .button {
  font-weight: 600;
  box-shadow: none;
  padding: 10px 12px;
  min-width: 180px;
  height: auto;
  line-height: 1.2;
  text-align: center;
  font-size: 15px;
  transition: all .3s;
}

#wpforms-admin-smtp .step .button.grey {
  background: #f6f6f6;
  border: 1px solid #dddddd;
  text-shadow: none;
  color: #9fa5aa;
}

#wpforms-admin-smtp .step .button.disabled {
  cursor: default;
}

#wpforms-admin-smtp .step .hidden {
  opacity: 0;
  transition: all .3s;
}

#wpforms-admin-smtp .grey {
  opacity: 0.5;
}

@media (max-width: 767px) {
  #wpforms-admin-smtp {
    width: auto;
    margin: 0 20px;
  }
  #wpforms-admin-smtp .screenshot .cont {
    width: auto;
  }
  #wpforms-admin-smtp .screenshot ul {
    margin: 20px 0 0 15px;
    max-width: 100%;
  }
  #wpforms-admin-smtp .button {
    white-space: normal;
  }
}

@media (max-width: 540px) {
  #wpforms-admin-smtp .step .num {
    display: block;
    width: 100%;
    height: auto;
    padding: 20px 30px;
    text-align: left;
  }
  #wpforms-admin-smtp .step .loader {
    margin-left: 0;
  }
  #wpforms-admin-smtp .step div {
    display: block;
    width: 100%;
    border-left: none;
  }
}

.wpforms-success-icon,
.wpforms-warning-icon,
.wpforms-error-icon {
  display: inline-block;
  vertical-align: middle;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  height: 17px;
  width: 17px;
  margin-right: 10px;
  margin-top: -3px;
}

.wpforms-success-icon {
  background-image: url(../images/check-circle.svg);
}

.wpforms-warning-icon {
  background-image: url(../images/exclamation-triangle-orange.svg);
  height: 15px;
}

.wpforms-error-icon {
  background-image: url(../images/exclamation-circle.svg);
}

#wpforms-settings .wpforms-specific-note-wrap {
  background-color: #fcf9e8;
  border-radius: 6px;
  padding: 15px;
  box-shadow: 0 0 0 1px #c3c4c7, 0 2px 4px 0 rgba(0, 0, 0, 0.07);
  margin-right: 60px;
}

#wpforms-settings .wpforms-specific-note-wrap .wpforms-specific-note-lightbulb {
  float: left;
  width: 14px;
  padding-top: 1px;
}

#wpforms-settings .wpforms-specific-note-wrap .wpforms-specific-note-lightbulb svg path {
  fill: #f0c33c;
}

#wpforms-settings .wpforms-specific-note-wrap .wpforms-specific-note-content {
  margin-inline-start: 25px;
  color: #2c3338;
  font-size: 15px;
  line-height: 22px;
}

#wpforms-settings .wpforms-specific-note-wrap .wpforms-specific-note-content p {
  margin-top: 0;
}

#wpforms-settings .wpforms-specific-note-wrap .wpforms-specific-note-content > p {
  margin-bottom: 1em;
}

#wpforms-settings .wpforms-specific-note-wrap .wpforms-specific-note-content > p:last-child {
  margin-bottom: 0;
}

#wpforms-settings .wpforms-specific-note-wrap .wpforms-specific-note-content .wpforms-strong {
  font-weight: 500;
}

#wpforms-settings .wpforms-admin-settings-form .wpforms-btn[type="submit"] {
  position: relative;
  z-index: 2;
}

#wpforms-settings .wpforms-toggle-control-status {
  text-transform: uppercase;
}

#wpforms-settings .wpforms-learn-more {
  display: inline-block;
}

#wpforms-settings .wpforms-learn-more:after {
  content: '';
  display: inline-block;
  width: 14px;
  height: 15px;
  background-image: url(../images/dashicons-arrow-right-alt.svg);
  background-size: 13px 13px;
  background-position: center;
  background-repeat: no-repeat;
  margin-left: 4px;
  position: relative;
  top: 3px;
}

@media (max-width: 463px) {
  #wpforms-settings #wpforms-settings-connect-btn {
    margin-top: 10px;
  }
}

.rtl #wpforms-settings .wpforms-specific-note-wrap .wpforms-specific-note-lightbulb {
  float: right;
}

.rtl .wpforms-admin-settings .wpforms-setting-row.wpforms-setting-row-radio .wpforms-setting-field .wpforms-settings-field-radio-wrapper {
  margin-right: 0;
  margin-left: 20px;
}

.rtl .wpforms-admin-settings .wpforms-setting-row.wpforms-setting-row-radio .wpforms-setting-field .wpforms-settings-field-radio-wrapper input[type=radio] {
  margin: 1px 0 0 10px;
}

.wpforms-admin-settings *,
.wpforms-admin-settings *::before,
.wpforms-admin-settings *::after {
  box-sizing: border-box;
}

.wpforms-admin-settings .wpforms-setting-row {
  padding: 0 0 30px 0;
  font-size: 14px;
  line-height: 1.3;
}

.wpforms-admin-settings .wpforms-setting-row input[type=password]:disabled {
  background: #ffffff;
  opacity: 0.5;
  border: 1px solid #8c8f94;
}

.wpforms-admin-settings .wpforms-setting-row:first-of-type {
  padding-top: 5px !important;
}

.wpforms-admin-settings .wpforms-setting-row:last-of-type {
  padding-bottom: 30px;
}

@media (max-width: 781px) {
  .wpforms-admin-settings .wpforms-setting-row {
    padding: 0 0 15px 0;
  }
}

.wpforms-admin-settings .wpforms-setting-row.section-heading {
  padding: 30px 0;
  border-top: 1px solid #dcdcde;
}

@media (max-width: 781px) {
  .wpforms-admin-settings .wpforms-setting-row.section-heading {
    padding: 25px 0 15px 0;
  }
}

.wpforms-admin-settings .wpforms-setting-row.section-heading.specific-note {
  border-top: none;
  padding: 0 0 30px 0;
}

.wpforms-admin-settings .wpforms-setting-row.section-heading:first-of-type {
  border: none;
}

.wpforms-admin-settings .wpforms-setting-row.section-heading.no-desc h4 {
  margin: 0;
}

.wpforms-admin-settings .wpforms-setting-row.section-heading .wpforms-setting-field {
  margin: 0;
  max-width: 1000px;
}

.wpforms-admin-settings .wpforms-setting-row.section-heading .wpforms-setting-field p {
  line-height: 20px;
}

.wpforms-admin-settings .wpforms-setting-row.tools textarea {
  margin-top: 22px;
}

.wpforms-admin-settings .wpforms-setting-row.tools .wpforms-toggle-control-status {
  text-transform: uppercase;
}

.wpforms-admin-settings .wpforms-setting-row.tools .choices {
  margin-bottom: 0;
}

.wpforms-admin-settings .wpforms-setting-row.wpforms-setting-row-checkbox input[type=checkbox] {
  float: left;
  margin: 1px 0 0 0;
}

.wpforms-admin-settings .wpforms-setting-row.wpforms-setting-row-checkbox input[type=checkbox]:disabled {
  background: #FFFFFF;
  opacity: 0.5;
  border: 1px solid #8C8F94;
  border-radius: 3px;
  cursor: not-allowed;
}

.wpforms-admin-settings .wpforms-setting-row.wpforms-setting-row-checkbox .desc,
.wpforms-admin-settings .wpforms-setting-row.wpforms-setting-row-checkbox .disabled-desc {
  margin: 0 0 0 30px;
}

.wpforms-admin-settings .wpforms-setting-row.wpforms-setting-row-checkbox .disabled-desc {
  margin-top: 15px;
}

.wpforms-admin-settings .wpforms-setting-row.wpforms-setting-row-license {
  line-height: 17px;
}

.wpforms-admin-settings .wpforms-setting-row.wpforms-setting-row-license strong {
  color: #3c4349;
  font-weight: 600;
}

.wpforms-admin-settings .wpforms-setting-row.wpforms-setting-row-license .wpforms-setting-license-wrapper {
  display: inline-block;
  position: relative;
}

@media (max-width: 447px) {
  .wpforms-admin-settings .wpforms-setting-row.wpforms-setting-row-license .wpforms-setting-license-wrapper {
    width: 100%;
  }
}

.wpforms-admin-settings .wpforms-setting-row.wpforms-setting-row-license .wpforms-setting-license-wrapper i::after {
  background-repeat: no-repeat;
  background-size: contain;
  background-position: center;
  content: '';
  height: 15.5px;
  position: absolute;
  pointer-events: none;
  right: 10px;
  top: calc(50% - 7.75px);
  width: 15.5px;
}

.wpforms-admin-settings .wpforms-setting-row.wpforms-setting-row-license .wpforms-setting-license-wrapper input:not([value=""]) {
  padding-right: 35px;
}

.wpforms-admin-settings .wpforms-setting-row.wpforms-setting-row-license .wpforms-setting-license-wrapper input:not([value=""]).wpforms-setting-license-is-valid + i::after {
  background-image: url(../images/check-circle.svg);
}

.wpforms-admin-settings .wpforms-setting-row.wpforms-setting-row-license .wpforms-setting-license-wrapper input:not([value=""]).wpforms-setting-license-is-invalid {
  border-color: #d63638;
  color: #d63638;
}

.wpforms-admin-settings .wpforms-setting-row.wpforms-setting-row-license .wpforms-setting-license-wrapper input:not([value=""]).wpforms-setting-license-is-invalid + i::after {
  background-image: url(../images/exclamation-circle.svg);
}

.wpforms-admin-settings .wpforms-setting-row.wpforms-setting-row-license .wpforms-setting-license-wrapper.wpforms-setting-license-block-ui {
  pointer-events: none;
}

.wpforms-admin-settings .wpforms-setting-row.wpforms-setting-row-license .wpforms-setting-license-wrapper.wpforms-setting-license-block-ui input {
  background-color: rgba(255, 255, 255, 0.5);
}

.wpforms-admin-settings .wpforms-setting-row.wpforms-setting-row-license .wpforms-setting-license-wrapper.wpforms-setting-license-block-ui input[disabled] {
  color: rgba(51, 51, 51, 0.5);
}

.wpforms-admin-settings .wpforms-setting-row.wpforms-setting-row-license .wpforms-setting-license-wrapper.wpforms-setting-license-block-ui input:not([value=""]):not([disabled]) {
  color: rgba(214, 54, 56, 0.5);
}

.wpforms-admin-settings .wpforms-setting-row.wpforms-setting-row-license .wpforms-setting-field {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  align-items: flex-start;
}

.wpforms-admin-settings .wpforms-setting-row.wpforms-setting-row-license .wpforms-setting-field p {
  line-height: 20px;
  width: 100%;
}

.wpforms-admin-settings .wpforms-setting-row.wpforms-setting-row-license .wpforms-setting-field p:nth-child(n+2) {
  margin-top: 0;
}

.wpforms-admin-settings .wpforms-setting-row.wpforms-setting-row-license .wpforms-setting-field hr {
  margin: 10px 0;
  width: 100%;
}

.wpforms-admin-settings .wpforms-setting-row.wpforms-setting-row-license #wpforms-setting-license-key {
  margin-right: 0;
}

.wpforms-admin-settings .wpforms-setting-row.wpforms-setting-row-license #wpforms-setting-license-key[disabled] {
  pointer-events: none;
}

.wpforms-admin-settings .wpforms-setting-row.wpforms-setting-row-license p.type {
  color: #646970;
}

.wpforms-admin-settings .wpforms-setting-row.wpforms-setting-row-license p.type strong {
  text-transform: capitalize;
  color: #50575e;
}

.wpforms-admin-settings .wpforms-setting-row.wpforms-setting-row-radio .wpforms-setting-field input[type=radio] {
  margin: 1px 10px 0 0;
}

.wpforms-admin-settings .wpforms-setting-row.wpforms-setting-row-radio .wpforms-setting-field input[type=radio]:focus {
  border-color: #8c8f94 !important;
}

.wpforms-admin-settings .wpforms-setting-row.wpforms-setting-row-radio .wpforms-setting-field input[type=radio]:focus:checked {
  border-color: #056aab !important;
}

.wpforms-admin-settings .wpforms-setting-row.wpforms-setting-row-radio .wpforms-setting-field input[type=radio]:checked {
  border-color: #056aab;
}

.wpforms-admin-settings .wpforms-setting-row.wpforms-setting-row-radio .wpforms-setting-field input[type=radio]:disabled {
  opacity: 0.5;
  border: 1px solid #8c8f94;
}

.wpforms-admin-settings .wpforms-setting-row.wpforms-setting-row-radio .wpforms-setting-field label {
  display: inline-block;
  text-align: center;
}

@media (max-width: 781px) {
  .wpforms-admin-settings .wpforms-setting-row.wpforms-setting-row-radio .wpforms-setting-field label {
    margin-top: 5px;
  }
}

.wpforms-admin-settings .wpforms-setting-row.wpforms-setting-row-radio .wpforms-setting-field .wpforms-settings-field-radio-wrapper {
  align-items: flex-start;
  display: inline-flex;
  margin-right: 20px;
}

.wpforms-admin-settings .wpforms-setting-row.wpforms-setting-row-radio .wpforms-setting-field .wpforms-settings-field-radio-wrapper:hover input[type=radio] {
  border: 1px solid #056aab;
}

.wpforms-admin-settings .wpforms-setting-row.wpforms-setting-row-radio .wpforms-setting-field .wpforms-settings-field-radio-wrapper:hover input[type=radio]:checked {
  border-color: #056aab;
  box-shadow: 0 0 0 1px #056aab;
}

.wpforms-admin-settings .wpforms-setting-row.wpforms-setting-row-radio .wpforms-setting-field .wpforms-settings-field-radio-wrapper:hover input[type=radio]:disabled {
  box-shadow: none;
}

.wpforms-admin-settings .wpforms-setting-row.wpforms-setting-row-radio .wpforms-setting-field .wpforms-settings-field-radio-wrapper input[type=radio] {
  flex-shrink: 0;
}

@media (max-width: 781px) {
  .wpforms-admin-settings .wpforms-setting-row.wpforms-setting-row-radio .wpforms-setting-field {
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    gap: 5px;
  }
}

.wpforms-admin-settings .wpforms-setting-row.wpforms-setting-row-radio#wpforms-setting-row-geolocation-field-provider .wpforms-setting-field, .wpforms-admin-settings .wpforms-setting-row.wpforms-setting-row-radio#wpforms-setting-row-captcha-provider .wpforms-setting-field {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

@media (max-width: 781px) {
  .wpforms-admin-settings .wpforms-setting-row.wpforms-setting-row-radio#wpforms-setting-row-geolocation-field-provider .wpforms-setting-field, .wpforms-admin-settings .wpforms-setting-row.wpforms-setting-row-radio#wpforms-setting-row-captcha-provider .wpforms-setting-field {
    gap: 15px;
    flex-direction: row;
  }
}

.wpforms-admin-settings .wpforms-setting-row.wpforms-setting-row-radio#wpforms-setting-row-geolocation-field-provider .wpforms-setting-field .wpforms-settings-field-radio-wrapper, .wpforms-admin-settings .wpforms-setting-row.wpforms-setting-row-radio#wpforms-setting-row-captcha-provider .wpforms-setting-field .wpforms-settings-field-radio-wrapper {
  flex: 0 0 166px;
  margin-right: 0;
}

@media (max-width: 781px) {
  .wpforms-admin-settings .wpforms-setting-row.wpforms-setting-row-radio#wpforms-setting-row-geolocation-field-provider .wpforms-setting-field label, .wpforms-admin-settings .wpforms-setting-row.wpforms-setting-row-radio#wpforms-setting-row-captcha-provider .wpforms-setting-field label {
    margin-top: 0;
  }
}

.wpforms-admin-settings .wpforms-setting-row.wpforms-setting-row-radio#wpforms-setting-row-geolocation-field-provider .wpforms-setting-field .desc, .wpforms-admin-settings .wpforms-setting-row.wpforms-setting-row-radio#wpforms-setting-row-captcha-provider .wpforms-setting-field .desc {
  flex: 0 0 100%;
  margin-top: 10px;
}

@media (max-width: 781px) {
  .wpforms-admin-settings .wpforms-setting-row.wpforms-setting-row-radio#wpforms-setting-row-geolocation-field-provider .wpforms-setting-field .desc, .wpforms-admin-settings .wpforms-setting-row.wpforms-setting-row-radio#wpforms-setting-row-captcha-provider .wpforms-setting-field .desc {
    margin-top: 5px;
  }
}

.wpforms-admin-settings .wpforms-setting-row#wpforms-setting-row-render-engine .wpforms-setting-field label:before {
  content: " ";
  display: block;
  width: 155px;
  height: 180px;
  margin: 0 0 12px 0;
  border: 1px solid #c3c4c7;
  border-radius: 6px;
}

.wpforms-admin-settings .wpforms-setting-row#wpforms-setting-row-render-engine .wpforms-setting-field input[type=radio] {
  display: none;
}

.wpforms-admin-settings .wpforms-setting-row#wpforms-setting-row-render-engine .wpforms-setting-field input[type=radio]:checked + label:before {
  box-shadow: 0 0 0 2px #e27730;
  border: 2px solid #ffffff;
}

.wpforms-admin-settings .wpforms-setting-row#wpforms-setting-row-render-engine .wpforms-setting-field .option-classic:before {
  background: url(../images/settings-email-plaintext.png) center center;
}

.wpforms-admin-settings .wpforms-setting-row#wpforms-setting-row-render-engine .wpforms-setting-field .option-modern:before {
  background: url(../images/settings-email-html.png) center center;
}

.wpforms-admin-settings .wpforms-setting-row#wpforms-setting-row-captcha-provider .wpforms-setting-field {
  margin-left: 0;
}

.wpforms-admin-settings .wpforms-setting-row#wpforms-setting-row-captcha-provider .wpforms-setting-field input[type=radio] {
  display: none;
}

.wpforms-admin-settings .wpforms-setting-row#wpforms-setting-row-captcha-provider .wpforms-setting-field input[type=radio]:checked + label {
  box-shadow: 0 0 0 2px #e27730, 0 2px 4px 0 rgba(0, 0, 0, 0.15);
}

.wpforms-admin-settings .wpforms-setting-row#wpforms-setting-row-captcha-provider .wpforms-setting-field label {
  width: 166px;
  height: 180px;
  padding: 30px;
  text-align: center;
  color: #2c3338;
  font-size: 16px;
  font-weight: 600;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 0 0 1px #c3c4c7;
  transition: box-shadow 0.05s;
}

.wpforms-admin-settings .wpforms-setting-row#wpforms-setting-row-captcha-provider .wpforms-setting-field label:hover, .wpforms-admin-settings .wpforms-setting-row#wpforms-setting-row-captcha-provider .wpforms-setting-field label:focus {
  box-shadow: 0 0 0 2px #50575e;
}

.wpforms-admin-settings .wpforms-setting-row#wpforms-setting-row-captcha-provider .wpforms-setting-field label:before {
  content: "";
  display: block;
  width: 100%;
  height: 80px;
  margin: 0 0 20px 0;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: auto 80px;
}

.wpforms-admin-settings .wpforms-setting-row#wpforms-setting-row-captcha-provider .wpforms-setting-field .option-hcaptcha:before {
  background-image: url(../images/settings-captcha-hcaptcha.svg);
}

.wpforms-admin-settings .wpforms-setting-row#wpforms-setting-row-captcha-provider .wpforms-setting-field .option-recaptcha:before {
  background-image: url(../images/settings-captcha-recaptcha.svg);
}

.wpforms-admin-settings .wpforms-setting-row#wpforms-setting-row-captcha-provider .wpforms-setting-field .option-turnstile:before {
  background-image: url(../images/settings-captcha-cloudflare.svg);
}

.wpforms-admin-settings .wpforms-setting-row#wpforms-setting-row-captcha-provider .wpforms-setting-field .option-none:before {
  background-image: url(../images/settings-captcha-none.svg);
}

.wpforms-admin-settings .wpforms-setting-row#wpforms-setting-row-captcha-provider .wpforms-setting-field .desc {
  margin-top: 30px;
}

@media (max-width: 781px) {
  .wpforms-admin-settings .wpforms-setting-row#wpforms-setting-row-captcha-provider .wpforms-setting-field {
    gap: 30px 0;
    flex-direction: row;
  }
  .wpforms-admin-settings .wpforms-setting-row#wpforms-setting-row-captcha-provider .wpforms-setting-field label {
    margin-top: 0;
  }
}

.wpforms-admin-settings .wpforms-setting-row#wpforms-setting-row-stripe-webhooks-endpoint-set .wpforms-stripe-webhook-endpoint-url a.wpforms-copy-to-clipboard, .wpforms-admin-settings .wpforms-setting-row#wpforms-setting-row-stripe-webhooks-endpoint-set .wpforms-square-webhook-endpoint-url a.wpforms-copy-to-clipboard, .wpforms-admin-settings .wpforms-setting-row#wpforms-setting-row-square-webhooks-endpoint-set .wpforms-stripe-webhook-endpoint-url a.wpforms-copy-to-clipboard, .wpforms-admin-settings .wpforms-setting-row#wpforms-setting-row-square-webhooks-endpoint-set .wpforms-square-webhook-endpoint-url a.wpforms-copy-to-clipboard {
  border: 1px solid #8c8f94;
  color: #50575e;
  height: 35px;
  width: 36px;
  display: inline-flex;
  align-content: center;
  flex-wrap: wrap;
  text-align: center;
}

.wpforms-admin-settings .wpforms-setting-row#wpforms-setting-row-stripe-webhooks-endpoint-set .wpforms-stripe-webhook-endpoint-url a.wpforms-copy-to-clipboard span, .wpforms-admin-settings .wpforms-setting-row#wpforms-setting-row-stripe-webhooks-endpoint-set .wpforms-square-webhook-endpoint-url a.wpforms-copy-to-clipboard span, .wpforms-admin-settings .wpforms-setting-row#wpforms-setting-row-square-webhooks-endpoint-set .wpforms-stripe-webhook-endpoint-url a.wpforms-copy-to-clipboard span, .wpforms-admin-settings .wpforms-setting-row#wpforms-setting-row-square-webhooks-endpoint-set .wpforms-square-webhook-endpoint-url a.wpforms-copy-to-clipboard span {
  text-align: center;
  font-size: 17px;
  margin: 3px 0 0 -1px;
}

.wpforms-admin-settings .wpforms-setting-row#wpforms-setting-row-stripe-webhooks-endpoint-set .wpforms-stripe-webhook-endpoint-url a.wpforms-copy-to-clipboard span.dashicons-yes-alt, .wpforms-admin-settings .wpforms-setting-row#wpforms-setting-row-stripe-webhooks-endpoint-set .wpforms-square-webhook-endpoint-url a.wpforms-copy-to-clipboard span.dashicons-yes-alt, .wpforms-admin-settings .wpforms-setting-row#wpforms-setting-row-square-webhooks-endpoint-set .wpforms-stripe-webhook-endpoint-url a.wpforms-copy-to-clipboard span.dashicons-yes-alt, .wpforms-admin-settings .wpforms-setting-row#wpforms-setting-row-square-webhooks-endpoint-set .wpforms-square-webhook-endpoint-url a.wpforms-copy-to-clipboard span.dashicons-yes-alt {
  color: #008a20;
}

.wpforms-admin-settings .wpforms-setting-row#wpforms-setting-row-stripe-webhooks-endpoint-set .wpforms-stripe-webhook-endpoint-url input[type=text]:disabled, .wpforms-admin-settings .wpforms-setting-row#wpforms-setting-row-stripe-webhooks-endpoint-set .wpforms-square-webhook-endpoint-url input[type=text]:disabled, .wpforms-admin-settings .wpforms-setting-row#wpforms-setting-row-square-webhooks-endpoint-set .wpforms-stripe-webhook-endpoint-url input[type=text]:disabled, .wpforms-admin-settings .wpforms-setting-row#wpforms-setting-row-square-webhooks-endpoint-set .wpforms-square-webhook-endpoint-url input[type=text]:disabled {
  cursor: copy;
  background: #ffffff;
  opacity: 0.5;
  border: 1px solid #8c8f94;
}

.wpforms-admin-settings .wpforms-setting-row#wpforms-setting-row-stripe-webhooks-endpoint-set .wpforms-stripe-webhook-endpoint-url p, .wpforms-admin-settings .wpforms-setting-row#wpforms-setting-row-stripe-webhooks-endpoint-set .wpforms-square-webhook-endpoint-url p, .wpforms-admin-settings .wpforms-setting-row#wpforms-setting-row-square-webhooks-endpoint-set .wpforms-stripe-webhook-endpoint-url p, .wpforms-admin-settings .wpforms-setting-row#wpforms-setting-row-square-webhooks-endpoint-set .wpforms-square-webhook-endpoint-url p {
  margin: 10px 0 10px 0;
}

.wpforms-admin-settings .wpforms-setting-row#wpforms-setting-row-stripe-webhooks-endpoint-set .wpforms-stripe-webhook-endpoint-url p:first-of-type, .wpforms-admin-settings .wpforms-setting-row#wpforms-setting-row-stripe-webhooks-endpoint-set .wpforms-square-webhook-endpoint-url p:first-of-type, .wpforms-admin-settings .wpforms-setting-row#wpforms-setting-row-square-webhooks-endpoint-set .wpforms-stripe-webhook-endpoint-url p:first-of-type, .wpforms-admin-settings .wpforms-setting-row#wpforms-setting-row-square-webhooks-endpoint-set .wpforms-square-webhook-endpoint-url p:first-of-type {
  margin-top: 0;
}

.wpforms-admin-settings .wpforms-setting-row#wpforms-setting-row-recaptcha-type .wpforms-setting-field {
  padding-top: 10px;
}

.wpforms-admin-settings .wpforms-setting-row .wpforms-captcha-preview .wpforms-captcha-turnstile iframe {
  display: block !important;
  position: relative !important;
  visibility: inherit !important;
}

.wpforms-admin-settings .wpforms-setting-row .wpforms-captcha-container iframe {
  margin: 0 0 8px;
}

.wpforms-admin-settings .wpforms-setting-row .wpforms-captcha-preview .desc,
.wpforms-admin-settings .wpforms-setting-row .wpforms-captcha-placeholder .desc {
  margin: 0;
}

.wpforms-admin-settings .wpforms-setting-row.wpforms-setting-row-image .wpforms-setting-field img {
  display: block;
  margin: 0 0 20px 0;
  width: Min(400px, 100%);
}

.wpforms-admin-settings .wpforms-setting-row .wpforms-setting-field .wpforms-color-picker {
  padding-inline-start: 35px;
  padding-inline-end: 8px;
  color: #50575e;
  letter-spacing: -.5px;
  font-size: 13px;
  margin: 0;
}

.wpforms-admin-settings .wpforms-setting-row .wpforms-setting-field .minicolors-input-swatch {
  top: 9px;
  inset-inline-start: 10px;
  height: 18px;
  width: 18px;
  border-radius: 2px;
  overflow: hidden;
  border-width: 0;
  background: #ffffff;
}

.wpforms-admin-settings .wpforms-setting-row .wpforms-setting-field .minicolors-swatch-color {
  box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.15);
}

.wpforms-admin-settings .wpforms-setting-row.wpforms-setting-row-columns {
  display: flex;
  flex-wrap: wrap;
}

.wpforms-admin-settings .wpforms-setting-row.wpforms-setting-row-columns .wpforms-setting-label {
  float: none;
  align-self: stretch;
  flex: 0 0 200px;
  color: #2c3338;
}

@media (max-width: 767px) {
  .wpforms-admin-settings .wpforms-setting-row.wpforms-setting-row-columns .wpforms-setting-label {
    flex: 0 0 100%;
  }
}

.wpforms-admin-settings .wpforms-setting-row.wpforms-setting-row-columns .wpforms-setting-field {
  margin: 0;
  flex: 1;
}

.wpforms-admin-settings .wpforms-setting-row.wpforms-setting-row-columns .wpforms-setting-columns {
  margin: 0;
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.wpforms-admin-settings .wpforms-setting-row.wpforms-setting-row-columns .wpforms-setting-column {
  flex-basis: 400px;
}

@media (max-width: 767px) {
  .wpforms-admin-settings .wpforms-setting-row.wpforms-setting-row-columns .wpforms-setting-column {
    flex-basis: 100%;
  }
}

.wpforms-admin-settings .wpforms-setting-row.wpforms-setting-row-columns .wpforms-setting-column > * {
  width: 100%;
}

.wpforms-admin-settings .wpforms-setting-row.submit, .wpforms-admin-settings .wpforms-setting-row.upgrade_to_pro {
  margin: 0;
  padding: 25px 0;
  border-bottom: none;
}

.wpforms-admin-settings .wpforms-setting-row h5 {
  margin: 0 0 20px 0;
  font-size: 16px;
  font-weight: 600;
}

.wpforms-admin-settings .wpforms-setting-row h4 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  line-height: normal;
  color: #1d2327;
}

.wpforms-admin-settings .wpforms-setting-row h3 {
  margin: 0 0 20px 0;
  font-size: 24px;
  font-weight: 600;
}

.wpforms-admin-settings .wpforms-setting-row p {
  margin: 10px 0 0;
  font-size: 14px;
  line-height: 20px;
  color: #2c3338;
}

.wpforms-admin-settings .wpforms-setting-row p.desc {
  color: #646970;
  line-height: 20px;
}

.wpforms-admin-settings .wpforms-setting-row p.desc.wpforms-captcha-preview-desc {
  margin: 8px 0 0 0;
}

.wpforms-admin-settings .wpforms-setting-row p.discount-note {
  font-style: italic;
  color: #646970;
}

.wpforms-admin-settings .wpforms-setting-row p.discount-note strong {
  color: green;
}

.wpforms-admin-settings .wpforms-setting-row span.wpforms-settings-warning {
  display: block;
  color: #d63638;
  font-weight: 600;
  line-height: 20px;
  margin-top: 5px;
}

.wpforms-admin-settings .wpforms-setting-row input[type=text],
.wpforms-admin-settings .wpforms-setting-row input[type=password],
.wpforms-admin-settings .wpforms-setting-row input[type=number],
.wpforms-admin-settings .wpforms-setting-row select {
  background-color: #ffffff;
  border: 1px solid #8c8f94;
  border-radius: 4px;
  box-shadow: none;
  color: #2c3338;
  display: inline-block;
  vertical-align: middle;
  padding: 7px 12px;
  margin: 0 10px 0 0;
  width: 400px;
  min-height: 36px;
  line-height: 1.3;
}

.wpforms-admin-settings .wpforms-setting-row input[type=text]:focus,
.wpforms-admin-settings .wpforms-setting-row input[type=password]:focus,
.wpforms-admin-settings .wpforms-setting-row input[type=number]:focus,
.wpforms-admin-settings .wpforms-setting-row select:focus {
  border-color: #056aab !important;
}

@media (max-width: 959px) {
  .wpforms-admin-settings .wpforms-setting-row input[type=text],
  .wpforms-admin-settings .wpforms-setting-row input[type=password],
  .wpforms-admin-settings .wpforms-setting-row input[type=number],
  .wpforms-admin-settings .wpforms-setting-row select {
    width: 300px;
  }
}

@media (max-width: 447px) {
  .wpforms-admin-settings .wpforms-setting-row input[type=text],
  .wpforms-admin-settings .wpforms-setting-row input[type=password],
  .wpforms-admin-settings .wpforms-setting-row input[type=number],
  .wpforms-admin-settings .wpforms-setting-row select {
    max-width: 300px;
    width: 100%;
  }
}

.wpforms-admin-settings .wpforms-setting-row select {
  padding: 7px 24px 7px 10px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.wpforms-admin-settings .wpforms-setting-row input[type=number] {
  width: 100px;
}

.wpforms-admin-settings .wpforms-setting-row button {
  margin-inline-end: 10px;
}

.wpforms-admin-settings .wpforms-setting-row button.choices__button {
  margin-right: 0;
}

.wpforms-admin-settings .wpforms-setting-row .choicesjs-select-wrap {
  display: block;
  max-width: 400px;
  color: #333;
}

.wpforms-admin-settings .wpforms-setting-row .choicesjs-select-wrap .choicesjs-select[multiple] option {
  padding: 1.5px 0 0 0;
}

.wpforms-admin-settings .wpforms-setting-row .choicesjs-select-wrap select {
  max-width: 400px;
}

@media (max-width: 959px) {
  .wpforms-admin-settings .wpforms-setting-row .choicesjs-select-wrap {
    max-width: 100%;
    width: 100%;
  }
  .wpforms-admin-settings .wpforms-setting-row .choicesjs-select-wrap select {
    max-width: 100%;
  }
}

.wpforms-admin-settings .wpforms-setting-row .progress-bar {
  background: #fff;
  border: 1px solid #e27730;
  height: 30px;
  width: 100%;
  position: relative;
  border-radius: 3px;
  margin: 0 0 16px 0;
}

.wpforms-admin-settings .wpforms-setting-row .progress-bar.complete {
  border: 1px solid #008a20;
}

.wpforms-admin-settings .wpforms-setting-row .progress-bar.complete .bar {
  background-color: #008a20;
}

.wpforms-admin-settings .wpforms-setting-row .progress-bar.complete .bar:after {
  content: none;
  display: none;
}

.wpforms-admin-settings .wpforms-setting-row .progress-bar .bar {
  display: block;
  background: #e27730;
  height: 100%;
  left: 0;
  position: absolute;
  -webkit-transition: width 0.5s ease-in-out;
  -moz-transition: width 0.5s ease-in-out;
  -o-transition: width 0.5s ease-in-out;
  transition: width 0.5s ease-in-out;
}

@-webkit-keyframes progress {
  to {
    background-position: 60px 0;
  }
}

@-moz-keyframes progress {
  to {
    background-position: 60px 0;
  }
}

@keyframes progress {
  to {
    background-position: 60px 0;
  }
}

.wpforms-admin-settings .wpforms-setting-row .progress-bar .bar:after {
  content: '';
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  -webkit-animation: progress 1s linear infinite;
  -moz-animation: progress 1s linear infinite;
  animation: progress 1s linear infinite;
  background-repeat: repeat-x;
  background-size: 60px 60px;
  background-image: -webkit-linear-gradient(-45deg, transparent 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(-45deg, transparent 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
}

.wpforms-admin-settings .wpforms-setting-label {
  display: block;
  float: left;
  width: 200px;
  padding: 0 10px 0 0;
}

@media (max-width: 781px) {
  .wpforms-admin-settings .wpforms-setting-label {
    float: none;
    width: 100%;
    padding-bottom: 15px;
  }
}

.wpforms-admin-settings .wpforms-setting-label label {
  color: #2c3338;
  display: flex;
  font-weight: 600;
  gap: 10px;
  line-height: 20px;
  margin-top: 8px;
}

.wpforms-admin-settings .wpforms-setting-row-toggle .wpforms-toggle-control {
  height: 25px;
  padding-top: 8px;
}

.wpforms-admin-settings .wpforms-settings-row-system-information {
  padding-bottom: 30px;
  border-bottom: 1px solid #dddddd;
  margin-bottom: 30px;
}

.wpforms-admin-settings .wpforms-settings-row-test-ssl,
.wpforms-admin-settings .wpforms-settings-row-recreate-tables {
  padding-bottom: 0 !important;
  margin: 30px 0;
}

.wpforms-admin-settings .wpforms-settings-row-test-ssl .notice,
.wpforms-admin-settings .wpforms-settings-row-recreate-tables .notice {
  display: block;
  margin: 20px 0 0 0;
  padding: 12px 15px 12px 20px;
}

.wpforms-admin-settings .wpforms-settings-row-test-ssl .pre-error,
.wpforms-admin-settings .wpforms-settings-row-recreate-tables .pre-error {
  margin: 20px 0 0 0;
}

.wpforms-admin-settings .wpforms-settings-row-test-ssl #wpforms-ssl-verify,
.wpforms-admin-settings .wpforms-settings-row-test-ssl #wpforms-recreate-tables,
.wpforms-admin-settings .wpforms-settings-row-recreate-tables #wpforms-ssl-verify,
.wpforms-admin-settings .wpforms-settings-row-recreate-tables #wpforms-recreate-tables {
  margin-top: 20px;
}

.wpforms-admin-settings .wpforms-setting-field {
  display: block;
  margin: 0 0 0 200px;
  max-width: 820px;
  line-height: 20px;
}

@media (max-width: 781px) {
  .wpforms-admin-settings .wpforms-setting-field {
    margin: 0;
    clear: both;
  }
}

.wpforms-admin-settings #wpforms-settings-providers {
  margin-bottom: 30px;
}

.wpforms-admin-settings p.submit {
  margin: 0;
  padding: 30px 0 20px 0;
  border-top: 1px solid #e4e4e4;
}

.wpforms-admin-settings .wpforms-notice {
  display: block;
  margin: 20px 0 -15px;
  margin-left: 0 !important;
  margin-right: 0 !important;
  background: #fff;
  border: 1px solid #c3c4c7;
  border-left-width: 4px;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
  padding: 1px 12px;
}

.wpforms-admin-settings .wpforms-notice.wpforms-error {
  border-left-color: #dc3232;
}

.wpforms-admin-settings .wpforms-notice.notice-success {
  border-left-color: #00a32a;
}

.wpforms-admin-settings .wpforms-notice p {
  margin: 0.5em 0 !important;
  padding: 2px;
  color: #3c434a;
}

.wpforms-admin-settings .info-area,
.wpforms-admin-settings .pre-error {
  display: block;
  width: 100%;
  max-width: 1000px;
  height: 450px;
  padding: 20px;
  border-radius: 4px;
  border: 1px solid #8c8f94;
  box-shadow: none;
  background: #ffffff;
  font-family: Menlo, Monaco, monospace;
  font-size: 12px;
  color: #2c3338;
  white-space: pre;
  overflow: auto;
}

.wpforms-admin-settings .info-area {
  margin: 20px 0;
}

.wpforms-admin-settings .pre-error {
  height: auto;
  max-height: 250px;
  margin-bottom: 20px;
}

.wpforms-admin-settings .checkbox-multiselect-columns {
  max-width: 600px;
  position: relative;
}

.wpforms-admin-settings .checkbox-multiselect-columns:after {
  content: ".";
  display: block;
  height: 0;
  line-height: 0;
  font-size: 0;
  clear: both;
  min-height: 0;
  visibility: hidden;
}

.wpforms-admin-settings .checkbox-multiselect-columns:before {
  content: "\f0ec";
  color: #999;
  font: normal normal normal 20px/1 FontAwesome, sans-serif;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  position: absolute;
  top: 130px;
  left: 50%;
  margin: 0 0 0 -10px;
}

.wpforms-admin-settings .checkbox-multiselect-columns .header {
  font-weight: 600;
  font-size: 13px;
  margin: 0;
  padding: 0 0 5px 0;
  text-align: center;
}

.wpforms-admin-settings .checkbox-multiselect-columns .first-column,
.wpforms-admin-settings .checkbox-multiselect-columns .second-column {
  width: 45%;
  float: left;
}

.wpforms-admin-settings .checkbox-multiselect-columns .second-column {
  float: right;
}

.wpforms-admin-settings .checkbox-multiselect-columns .second-column ul li {
  padding: 10px;
}

.wpforms-admin-settings .checkbox-multiselect-columns ul {
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 3px;
  position: relative;
  height: 250px;
  padding: 0;
  overflow-y: auto;
  margin: 0;
  list-style-type: none;
}

.wpforms-admin-settings .checkbox-multiselect-columns ul li {
  border-bottom: 1px #eee solid;
  margin: 0;
  font-size: 14px;
}

.wpforms-admin-settings .checkbox-multiselect-columns ul li label {
  display: block;
  padding: 10px 10px 10px 32px;
  position: relative;
  vertical-align: baseline;
}

.wpforms-admin-settings .checkbox-multiselect-columns ul li label:hover {
  background-color: #e27730;
  color: #fff;
}

.wpforms-admin-settings .checkbox-multiselect-columns ul li label:before {
  content: "\f0c8";
  color: #ddd;
  font: normal normal normal 16px/1 FontAwesome, sans-serif;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  position: absolute;
  top: 11px;
  left: 10px;
}

.wpforms-admin-settings .checkbox-multiselect-columns ul li label.checked {
  opacity: 0.6;
}

.wpforms-admin-settings .checkbox-multiselect-columns ul li label.checked:before {
  content: "\f14a";
  color: #008a20;
}

.wpforms-admin-settings .checkbox-multiselect-columns ul li input {
  display: none;
}

.wpforms-admin-settings .checkbox-multiselect-columns .all {
  color: #999;
  display: inline-block;
  font-size: 13px;
  margin: 10px 0 0;
}

.wpforms-admin-settings .wpforms-file-upload label {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.wpforms-admin-settings .wpforms-file-upload label .fld {
  margin-right: 0;
  border: 1px solid #8c8f94;
  line-height: 20px;
}

@media (max-width: 781px) {
  .wpforms-admin-settings .wpforms-file-upload label .fld {
    max-width: 300px;
  }
}

.wpforms-admin-settings #wpforms-importer-forms .wpforms-setting-row {
  padding: 0 !important;
}

.wpforms-admin-settings #wpforms-importer-forms .wpforms-setting-row p:first-child {
  margin: 0 0 30px 0;
}

.wpforms-admin-settings #wpforms-importer-forms .wpforms-setting-row .checkbox-multiselect-columns {
  margin-bottom: 30px;
}

.wpforms-admin-settings #wpforms-importer-forms .wpforms-setting-row .checkbox-multiselect-columns label.checked:before {
  color: #056aab;
}

.wpforms-admin-settings #wpforms-importer-analyze {
  display: none;
}

.wpforms-admin-settings #wpforms-importer-analyze .process-count,
.wpforms-admin-settings #wpforms-importer-analyze p {
  font-size: 14px;
}

.wpforms-admin-settings #wpforms-importer-analyze .upgrade {
  display: none;
  font-size: 14px;
}

.wpforms-admin-settings #wpforms-importer-analyze .upgrade h5 {
  font-size: 18px;
  font-weight: 600;
  padding: 0;
  margin: 30px 0 20px 0;
}

.wpforms-admin-settings #wpforms-importer-analyze .upgrade hr {
  border: none;
  border-bottom: 1px solid #e4e4e4;
  margin: 40px 0;
}

.wpforms-admin-settings #wpforms-importer-analyze .upgrade .wpforms-btn {
  margin-right: 10px;
}

.wpforms-admin-settings #wpforms-importer-analyze .upgrade ul li {
  list-style-type: disc;
  margin-left: 14px;
}

.wpforms-admin-settings #wpforms-importer-analyze .upgrade ul li.form {
  list-style-type: none;
  margin-left: 0;
  font-weight: 700;
}

.wpforms-admin-settings #wpforms-tools-entries-export h5 {
  clear: both;
  margin: 20px 0 10px 0;
}

.wpforms-admin-settings #wpforms-tools-entries-export label {
  clear: both;
  float: left;
  margin: 3px 0 3px 0;
}

.wpforms-admin-settings #wpforms-tools-entries-export .wpforms-toggle-all {
  margin-bottom: 10px;
}

.wpforms-admin-settings #wpforms-tools-entries-export .choicesjs-select-wrap {
  position: relative;
}

.wpforms-admin-settings #wpforms-tools-entries-export .choicesjs-select-wrap .choices {
  margin-bottom: 0;
}

.wpforms-admin-settings #wpforms-tools-entries-export #wpforms-tools-entries-export-selectform-spinner {
  position: absolute;
  top: 6px;
  right: -30px;
}

.wpforms-admin-settings #wpforms-tools-entries-export .wpforms-tools-export-date-selector-container {
  position: relative;
  max-width: 400px;
  width: 100%;
}

.wpforms-admin-settings #wpforms-tools-entries-export .wpforms-tools-export-date-selector-container .wpforms-date-selector {
  cursor: pointer;
}

.wpforms-admin-settings #wpforms-tools-entries-export .wpforms-tools-export-date-selector-container .wpforms-clear-datetime-field {
  position: absolute;
  right: 11px;
  bottom: 10.5px;
  padding: 0;
  border: none;
  background-color: transparent;
  margin: 0;
  color: #a7aaad;
  cursor: pointer;
}

.wpforms-admin-settings #wpforms-tools-entries-export .wpforms-tools-export-date-selector-container .wpforms-clear-datetime-field:hover {
  color: #d63638;
}

.wpforms-admin-settings #wpforms-tools-entries-export .wpforms-tools-export-date-selector-container .wpforms-clear-datetime-field i {
  font-size: 16px;
}

.wpforms-admin-settings #wpforms-tools-entries-export #wpforms-tools-entries-export-options-search .wpforms-search-box-field {
  width: 200px;
}

.wpforms-admin-settings #wpforms-tools-entries-export #wpforms-tools-entries-export-options-search .wpforms-search-box-comparison {
  width: 140px;
}

.wpforms-admin-settings #wpforms-tools-entries-export #wpforms-tools-entries-export-options-search .wpforms-search-box-term {
  width: 300px;
}

@media (max-width: 782px) {
  .wpforms-admin-settings #wpforms-tools-entries-export #wpforms-tools-entries-export-options-search .wpforms-search-box-field,
  .wpforms-admin-settings #wpforms-tools-entries-export #wpforms-tools-entries-export-options-search .wpforms-search-box-comparison,
  .wpforms-admin-settings #wpforms-tools-entries-export #wpforms-tools-entries-export-options-search .wpforms-search-box-term {
    display: block;
    width: 300px;
    margin-bottom: 10px;
  }
}

.wpforms-admin-settings #wpforms-tools-entries-export section {
  margin-top: 20px;
}

.wpforms-admin-settings #wpforms-tools-entries-export iframe {
  width: 0;
  height: 0;
  visibility: hidden;
}

.wpforms-admin-settings #wpforms-tools-entries-export .wpforms-btn-text {
  opacity: 1;
  transition: all .5s;
}

.wpforms-admin-settings #wpforms-tools-entries-export .wpforms-btn-spinner {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  transition: all .5s;
}

.wpforms-admin-settings #wpforms-tools-entries-export .wpforms-btn-spinner-on {
  cursor: default;
  opacity: 0.75;
  position: relative;
}

.wpforms-admin-settings #wpforms-tools-entries-export .wpforms-btn-spinner-on .wpforms-btn-text {
  opacity: 0 !important;
  transition: all .5s;
}

.wpforms-admin-settings #wpforms-tools-entries-export .wpforms-btn-spinner-on .wpforms-btn-spinner {
  opacity: 1 !important;
  transition: all .5s;
}

.wpforms-admin-settings #wpforms-tools-entries-export .wpforms-btn-spinner-on:hover {
  background-color: #e27730;
  border-color: #e27730;
}

.wpforms-admin-settings #wpforms-tools-entries-export .wpforms-tools-entries-export-notice-warning {
  background: #ffffff;
  border-style: solid;
  border-color: #f0c33c;
  border-width: 0 0 0 4px;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
  line-height: 1.5em;
  margin: 10px 0 0 0;
  padding: 10px;
}

.wpforms-admin-settings #wpforms-importer-process {
  display: none;
}

.wpforms-admin-settings #wpforms-importer-process .process-count,
.wpforms-admin-settings #wpforms-importer-process .process-completed {
  font-size: 14px;
  margin-top: 0;
}

.wpforms-admin-settings #wpforms-importer-process .process-completed {
  display: none;
}

.wpforms-admin-settings #wpforms-importer-process .status {
  margin: 20px 0 30px;
  background-color: #fff;
  border-radius: 3px;
  border: 1px solid #ddd;
  max-height: 800px;
  overflow-y: scroll;
  display: none;
}

.wpforms-admin-settings #wpforms-importer-process .status .item {
  border-bottom: 1px solid #ddd;
  padding: 20px;
}

.wpforms-admin-settings #wpforms-importer-process .status .item:last-of-type {
  border: none;
}

.wpforms-admin-settings #wpforms-importer-process .status .item p {
  font-size: 13px;
  margin: 12px 0 0;
}

.wpforms-admin-settings #wpforms-importer-process .status .item ul {
  font-size: 13px;
  margin: 12px 0 0 16px;
}

.wpforms-admin-settings #wpforms-importer-process .status .item ul li {
  list-style: disc;
}

.wpforms-admin-settings #wpforms-importer-process .status .item .status-icon {
  display: inline-block;
  margin: 0 10px 0 0;
}

.wpforms-admin-settings #wpforms-importer-process .status .item .status-icon.fa-info-circle {
  color: #00a0d2;
}

.wpforms-admin-settings #wpforms-importer-process .status .item .status-icon.fa-check {
  color: #46b450;
}

.wpforms-admin-settings #wpforms-importer-process .status .item .status-icon.fa-exclamation-triangle {
  color: #ffb900;
}

.wpforms-admin-settings #wpforms-importer-process .status .item .status-icon.fa-times, .wpforms-admin-settings #wpforms-importer-process .status .item .status-icon.fa-exclamation-circle {
  color: #dc3232;
}

.wpforms-admin-settings #wpforms-importer-process .status .item .name {
  font-size: 14px;
  float: left;
}

.wpforms-admin-settings #wpforms-importer-process .status .item .actions {
  font-size: 14px;
  float: right;
}

.wpforms-admin-settings #wpforms-importer-process .status .item .actions .sep {
  color: #ddd;
}

.wpforms-admin-settings .settings-lite-cta {
  background-color: #ffffff;
  border: 1px solid #dcdcde;
  padding: 25px 20px;
  margin: 10px 0 0 0;
  position: relative;
}

.wpforms-admin-settings .settings-lite-cta .dismiss {
  position: absolute;
  top: 10px;
  right: 10px;
  color: #a7aaad;
  font-size: 16px;
}

.wpforms-admin-settings .settings-lite-cta .dismiss:hover {
  color: #d63638;
}

.wpforms-admin-settings .settings-lite-cta h5 {
  margin: 0 0 16px;
  font-size: 18px;
  font-weight: 700;
  color: #1d2327;
}

.wpforms-admin-settings .settings-lite-cta h6 {
  font-weight: 700;
  font-size: 14px;
  margin: 0 0 16px;
}

.wpforms-admin-settings .settings-lite-cta p {
  font-size: 14px;
  margin: 0 0 16px;
  color: #2c3338;
}

.wpforms-admin-settings .settings-lite-cta p:last-of-type {
  margin: 0;
}

.wpforms-admin-settings .settings-lite-cta p a {
  color: #e27730;
}

.wpforms-admin-settings .settings-lite-cta p a:hover {
  color: #b85a1b;
}

.wpforms-admin-settings .settings-lite-cta ul {
  margin: 0;
  padding: 0;
  width: 50%;
  float: left;
}

@media (max-width: 600px) {
  .wpforms-admin-settings .settings-lite-cta ul {
    width: 100%;
    float: none;
  }
}

.wpforms-admin-settings .settings-lite-cta ul li {
  margin: 0;
  padding: 0 0 2px 16px;
  color: #555;
  font-size: 14px;
  position: relative;
}

.wpforms-admin-settings .settings-lite-cta ul li:before {
  content: '+';
  position: absolute;
  top: -1px;
  left: 0;
}

.wpforms-admin-settings .settings-lite-cta .list {
  margin: 0 0 16px 0;
  overflow: auto;
  max-width: 900px;
}

.wpforms-admin-settings .settings-lite-cta .green {
  color: #218900;
  font-weight: 700;
}

.wpforms-admin-settings .settings-lite-cta .fa-star {
  color: #ff982d;
}

.wpforms-admin-settings .tablenav .tablenav-pages a,
.wpforms-admin-settings .tablenav-pages-navspan {
  min-width: 28px;
  height: auto;
}

.rtl .wpforms-admin-settings .wpforms-setting-field {
  margin: 0 200px 0 0;
}

@media (max-width: 781px) {
  .rtl .wpforms-admin-settings .wpforms-setting-field {
    margin: 0;
    clear: both;
  }
}

.rtl .wpforms-admin-settings .wpforms-setting-label {
  float: right;
}

.rtl .wpforms-admin-settings #wpforms-tools-entries-export label {
  float: right;
}

.wpforms-admin-wrap .wpforms-admin-settings .notice {
  margin-left: 0 !important;
  margin-right: 0 !important;
}

#wpforms-tools.wpforms-tools-tab-import .wpforms-setting-row p:last-of-type, #wpforms-tools.wpforms-tools-tab-export .wpforms-setting-row p:last-of-type {
  margin-bottom: 20px;
}

#wpforms-tools.wpforms-tools-tab-import .wpforms-setting-row #wpforms-import,
#wpforms-tools.wpforms-tools-tab-import .wpforms-setting-row #wpforms-import-other,
#wpforms-tools.wpforms-tools-tab-import .wpforms-setting-row #wpforms-export-form,
#wpforms-tools.wpforms-tools-tab-import .wpforms-setting-row #wpforms-export-template, #wpforms-tools.wpforms-tools-tab-export .wpforms-setting-row #wpforms-import,
#wpforms-tools.wpforms-tools-tab-export .wpforms-setting-row #wpforms-import-other,
#wpforms-tools.wpforms-tools-tab-export .wpforms-setting-row #wpforms-export-form,
#wpforms-tools.wpforms-tools-tab-export .wpforms-setting-row #wpforms-export-template {
  margin-top: 20px;
}

#wpforms-tools.wpforms-tools-tab-import .wpforms-setting-row:last-of-type, #wpforms-tools.wpforms-tools-tab-export .wpforms-setting-row:last-of-type {
  padding-bottom: 0;
}

#wpforms-tools.wpforms-tools-tab-import .wpforms-settings-row-divider, #wpforms-tools.wpforms-tools-tab-export .wpforms-settings-row-divider {
  padding-bottom: 30px;
  border-bottom: 1px solid #dddddd;
  margin-bottom: 30px;
}

#wpforms-tools.wpforms-tools-tab-import .wpforms-btn[aria-disabled="true"], #wpforms-tools.wpforms-tools-tab-export .wpforms-btn[aria-disabled="true"] {
  opacity: .5;
  pointer-events: none;
}

#wpforms-tools.wpforms-tools-tab-action-scheduler .wpforms-admin-content h1 {
  margin: 0;
  padding: 5px 0 0 0;
  font-size: 18px;
  font-weight: 600;
  line-height: normal;
  color: #101517;
}

#wpforms-tools.wpforms-tools-tab-action-scheduler .wpforms-admin-content p {
  margin: 10px 0 0 0;
  font-size: 14px;
  font-weight: 400;
  font-style: normal;
  line-height: 20px;
  color: #2c3338;
}

#wpforms-tools.wpforms-tools-tab-action-scheduler .wpforms-admin-content .search-box {
  margin: 0 0 11px 0;
}

#wpforms-tools.wpforms-tools-tab-action-scheduler .wpforms-admin-content .tablenav.top {
  padding: 0;
  margin: 11px 0;
}

#wpforms-tools.wpforms-tools-tab-action-scheduler .wpforms-admin-content .tablenav.top .tablenav-pages {
  margin: 0;
}

#wpforms-tools.wpforms-tools-tab-action-scheduler .wpforms-admin-content .wrap,
#wpforms-tools.wpforms-tools-tab-action-scheduler .wpforms-admin-content .subsubsub {
  margin: 0;
}

@media screen and (max-width: 782px) {
  #wpforms-tools.wpforms-tools-tab-action-scheduler .search-box {
    position: relative;
    bottom: auto;
    width: 100%;
    height: auto;
    clear: both;
  }
}

#wpforms-tools .wp-list-table {
  border-radius: 4px;
  border-color: #ccd0d4;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
}

#wpforms-tools .wp-list-table.striped tbody > :nth-child(odd) {
  background-color: #f6f6f6;
}

#wpforms-tools .wp-list-table .column-primary a strong {
  font-weight: 600;
}

@media (max-width: 781px) {
  #wpforms-tools.wpforms-tools-tab-logs table.wp-list-table td.column-primary.hidden, #wpforms-tools.wpforms-tools-tab-action-scheduler table.wp-list-table td.column-primary.hidden {
    display: block;
  }
  #wpforms-tools.wpforms-tools-tab-logs table.wp-list-table th.column-primary.hidden, #wpforms-tools.wpforms-tools-tab-action-scheduler table.wp-list-table th.column-primary.hidden {
    display: table-cell;
  }
}

@media (max-width: 781px) {
  #wpforms-tools.wpforms-tools-tab-logs .tablenav .tablenav-pages a,
  #wpforms-tools.wpforms-tools-tab-logs .tablenav-pages-navspan, #wpforms-tools.wpforms-tools-tab-action-scheduler .tablenav .tablenav-pages a,
  #wpforms-tools.wpforms-tools-tab-action-scheduler .tablenav-pages-navspan {
    min-width: 44px;
    padding: 12px 8px;
    font-size: 18px;
    line-height: 1;
  }
}

.wpforms-admin-settings-access .choices[aria-expanded="false"] .choices__inner {
  max-height: 36px;
}

#wpforms-settings-providers .wpforms-settings-provider {
  border-bottom: 1px solid #ddd;
  padding: 30px 0;
}

#wpforms-settings-providers .wpforms-settings-provider.focus-out {
  opacity: 0.4;
}

#wpforms-settings-providers .wpforms-settings-provider-header {
  cursor: pointer;
  max-width: 1000px;
}

#wpforms-settings-providers .wpforms-settings-provider-logo {
  float: left;
  position: relative;
}

#wpforms-settings-providers .wpforms-settings-provider-logo .fa {
  position: absolute;
  top: 50%;
  margin: -9px 0 0 0;
  left: 10px;
  color: #666;
  font-size: 18px;
}

#wpforms-settings-providers .wpforms-settings-provider-logo img {
  background: #fff;
  border: 1px solid #ddd;
  max-width: 90px;
  display: block;
  margin: 0 0 0 40px;
}

#wpforms-settings-providers .wpforms-settings-provider-info {
  margin: 0 0 0 160px;
  position: relative;
  padding: 20px 0 0;
}

#wpforms-settings-providers .wpforms-settings-provider-info h3 {
  margin: 0 0 10px 0;
  font-size: 20px;
  font-weight: 600;
}

#wpforms-settings-providers .wpforms-settings-provider-info p {
  margin: 0;
  font-size: 14px;
}

#wpforms-settings-providers .wpforms-settings-provider-info .connected-indicator {
  color: #83c11f;
  width: 135px;
  height: 36px;
  border: 1px solid #83c11f;
  border-radius: 4px;
  font-size: 16px;
  position: absolute;
  top: 28px;
  right: 0;
  gap: 10px;
  align-items: center;
  justify-content: center;
  display: none;
}

#wpforms-settings-providers .connected .wpforms-settings-provider-info .connected-indicator {
  display: flex;
}

#wpforms-settings-providers .wpforms-settings-provider-accounts {
  margin: 0 0 0 160px;
  display: none;
  max-width: 840px;
}

#wpforms-settings-providers .focus-in .wpforms-settings-provider-accounts {
  display: block;
}

#wpforms-settings-providers .wpforms-settings-provider-accounts-list ul {
  padding: 0;
  margin: 0;
}

#wpforms-settings-providers .wpforms-settings-provider-accounts-list ul li {
  border-bottom: 1px solid #ddd;
  margin: 0;
  padding: 10px 0;
  font-size: 14px;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}

#wpforms-settings-providers .wpforms-settings-provider-accounts-list ul li .wpforms-alert {
  flex-basis: 100%;
}

#wpforms-settings-providers .wpforms-settings-provider-accounts-list ul li .wpforms-alert .wpforms-alert-buttons-constant-contact-v3 .wpforms-constant-contact-v3-auth i {
  padding-inline-end: 5px;
}

#wpforms-settings-providers .wpforms-settings-provider-accounts-list ul li:first-of-type {
  border-top: 1px solid #ddd;
}

#wpforms-settings-providers .wpforms-settings-provider-accounts-list ul li:last-of-type {
  margin-bottom: 16px;
}

#wpforms-settings-providers .wpforms-settings-provider-accounts-list ul span.label,
#wpforms-settings-providers .wpforms-settings-provider-accounts-list ul span.date,
#wpforms-settings-providers .wpforms-settings-provider-accounts-list ul span.remove {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  white-space: initial;
  overflow: hidden;
  text-overflow: ellipsis;
}

#wpforms-settings-providers .wpforms-settings-provider-accounts-list ul span.label {
  flex: 40%;
  padding-inline-end: 10px;
}

#wpforms-settings-providers .wpforms-settings-provider-accounts-list ul span.date {
  color: #999;
  flex: 40%;
}

#wpforms-settings-providers .wpforms-settings-provider-accounts-list ul span.remove {
  flex: 20%;
  padding-inline-start: 10px;
  text-align: right;
}

#wpforms-settings-providers .wpforms-settings-provider-accounts-connect {
  display: none;
  padding: 20px;
  background-color: #ebf3fb;
  border: 1px solid #2271b1;
}

#wpforms-settings-providers .wpforms-settings-provider-accounts-connect input[type=text] {
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 3px;
  box-shadow: none;
  color: #333;
  display: block;
  padding: 7px 12px;
  margin: 5px 0;
  width: 400px;
  min-height: 35px;
  line-height: 1.3;
}

#wpforms-settings-providers .focus-in .wpforms-settings-provider-accounts-connect {
  display: block;
}

#wpforms-settings-providers .wpforms-settings-provider-accounts-connect .fa {
  display: none;
}

#wpforms-settings-providers .wpforms-settings-provider-accounts-connect p,
#wpforms-settings-providers .wpforms-settings-provider-accounts-connect-fields,
#wpforms-settings-providers .wpforms-settings-provider-accounts p {
  margin: 0 0 15px 0;
  font-size: 14px;
}

#wpforms-settings-providers .wpforms-settings-provider-accounts-connect p .wpforms-btn i,
#wpforms-settings-providers .wpforms-settings-provider-accounts-connect-fields .wpforms-btn i,
#wpforms-settings-providers .wpforms-settings-provider-accounts p .wpforms-btn i {
  padding-inline-end: 5px;
}

#wpforms-settings-providers .wpforms-settings-provider-accounts-connect form .wpforms-settings-provider-accounts-connect-general-description {
  margin-bottom: 10px;
  font-weight: 600;
}

@media (max-width: 640px) {
  #wpforms-settings-providers .wpforms-settings-provider-info h3 {
    margin-inline-end: 36px;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    white-space: initial;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 24px;
    margin-block-end: 6px;
  }
  #wpforms-settings-providers .wpforms-settings-provider-accounts {
    margin-left: 40px;
    margin-top: 15px;
  }
  #wpforms-settings-providers .wpforms-settings-provider-accounts-list ul .wpforms-clear:before,
  #wpforms-settings-providers .wpforms-settings-provider-accounts-list ul .wpforms-clear:after {
    content: none;
  }
  #wpforms-settings-providers .wpforms-settings-provider-accounts-list ul span.label,
  #wpforms-settings-providers .wpforms-settings-provider-accounts-list ul span.date,
  #wpforms-settings-providers .wpforms-settings-provider-accounts-list ul span.remove {
    width: auto;
  }
  #wpforms-settings-providers .wpforms-settings-provider-accounts-list ul span.label {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  #wpforms-settings-providers .wpforms-settings-provider-accounts-connect input[type=text] {
    width: 100%;
    max-width: 400px;
  }
  #wpforms-settings-providers .connected .wpforms-settings-provider-info .connected-indicator {
    width: 26px;
    height: 26px;
    top: 16px;
  }
  #wpforms-settings-providers .connected .wpforms-settings-provider-info .connected-indicator span {
    display: none;
  }
}

.rtl #wpforms-settings-providers .wpforms-settings-provider-logo {
  float: right;
}

.rtl #wpforms-settings-providers .wpforms-settings-provider-logo .fa {
  right: 10px;
  left: auto;
}

.rtl #wpforms-settings-providers .wpforms-settings-provider-logo img {
  margin: 0 40px 0 0;
}

.rtl #wpforms-settings-providers .wpforms-settings-provider-info {
  margin: 0 160px 0 0;
}

.rtl #wpforms-settings-providers .wpforms-settings-provider-info .connected-indicator {
  right: auto;
  left: 0;
}

.rtl #wpforms-settings-providers .wpforms-settings-provider-accounts {
  margin: 0 160px 0 0;
}

.rtl #wpforms-settings-providers .wpforms-settings-provider-accounts-list ul span.label {
  float: right;
}

.rtl #wpforms-settings-providers .wpforms-settings-provider-accounts-list ul span.date {
  float: right;
}

.rtl #wpforms-settings-providers .wpforms-settings-provider-accounts-list ul span.remove {
  float: left;
}

.wpforms-card-image-group .wpforms-setting-field {
  display: grid;
  gap: 20px;
  grid-template-columns: repeat(auto-fit, 155px);
  max-width: 100%;
}

.option-default .wpforms-card-image-overlay {
  background-image: url(../images/email/template-classic.svg);
}

.option-classic .wpforms-card-image-overlay {
  background-image: url(../images/email/template-classic.svg);
}

.option-compact .wpforms-card-image-overlay {
  background-image: url(../images/email/template-compact.svg);
}

.option-modern .wpforms-card-image-overlay {
  background-image: url(../images/email/template-modern.svg);
}

.option-elegant .wpforms-card-image-overlay {
  background-image: url(../images/email/template-elegant.svg);
}

.option-tech .wpforms-card-image-overlay {
  background-image: url(../images/email/template-tech.svg);
}

.option-none .wpforms-card-image-overlay {
  background-image: url(../images/email/template-plaintext.svg);
}

.wpforms-admin-settings-email {
  /* Force to hide the controls for dark and light appearances. */
}

.wpforms-admin-settings-email .wpforms-email-header-image .wpforms-setting-field {
  display: grid;
  gap: 10px;
  justify-content: start;
  justify-items: start;
}

@media (min-width: 601px) {
  .wpforms-admin-settings-email .wpforms-email-header-image .wpforms-setting-field :where(.desc, img) {
    grid-column: 1/4 span;
  }
}

.wpforms-admin-settings-email .wpforms-email-header-image .wpforms-setting-field .desc {
  margin: 0;
}

.wpforms-admin-settings-email .wpforms-email-header-image .wpforms-setting-field .wpforms-btn {
  white-space: normal;
}

.wpforms-admin-settings-email .wpforms-email-header-image.wpforms-setting-row .wpforms-setting-field img {
  margin-bottom: 10px;
  width: auto;
}

.wpforms-admin-settings-email .wpforms-email-header-image input[type="text"] {
  margin: 0;
}

@media (min-width: 601px) {
  .wpforms-admin-settings-email .wpforms-email-header-image input[type="text"] {
    padding-inline-end: 0;
    width: 242px;
  }
}

@media (max-width: 600px) {
  .wpforms-admin-settings-email .wpforms-email-header-image input[type="text"] {
    max-width: 300px;
  }
}

.wpforms-admin-settings-email .wpforms-email-header-image .choices {
  margin-bottom: 0;
  width: 100%;
}

@media (min-width: 601px) {
  .wpforms-admin-settings-email .wpforms-email-header-image .choices {
    grid-column: 1;
    grid-row: 2;
  }
}

@media (min-width: 961px) {
  .wpforms-admin-settings-email .wpforms-email-header-image .choices {
    min-width: 200px;
  }
}

@media (max-width: 600px) {
  .wpforms-admin-settings-email .wpforms-email-header-image .choices {
    max-width: 300px;
  }
}

.wpforms-admin-settings-email .wpforms-email-header-image .wpforms-setting-remove-image {
  background-color: #f6f7f7;
  color: #d63638;
  margin: 0;
}

.wpforms-admin-settings-email .wpforms-email-header-image .wpforms-setting-remove-image:hover {
  background-color: #d63638;
  border-color: #d63638;
  color: #ffffff;
}

.wpforms-admin-settings-email .wpforms-email-header-image .wpforms-setting-remove-image:is(:focus-visible, :focus) {
  box-shadow: 0 0 0 1px #b32d2e;
  outline: 0;
}

.wpforms-admin-settings-email .wpforms-email-header-image .choices,
.wpforms-admin-settings-email .wpforms-email-header-image .wpforms-setting-field input,
.wpforms-admin-settings-email .wpforms-email-header-image .wpforms-setting-remove-image,
.wpforms-admin-settings-email .wpforms-email-header-image [name="email-header-image-size"] {
  display: none;
}

.wpforms-admin-settings-email .wpforms-email-header-image.has-external-image-url input[value]:not([value=""]) {
  display: block;
}

.wpforms-admin-settings-email .wpforms-email-header-image img + input + .choices {
  display: block;
}

.wpforms-admin-settings-email .wpforms-email-header-image img + input + .choices + .wpforms-setting-remove-image {
  display: block;
}

.wpforms-admin-settings-email .wpforms-email-header-image img + input + .choices + .wpforms-setting-remove-image + .wpforms-setting-upload-image {
  display: none;
}

.wpforms-admin-settings-email .wpforms-email-header-image img + input + .wpforms-setting-remove-image {
  display: block;
}

.wpforms-admin-settings-email .wpforms-email-header-image img + input + .wpforms-setting-remove-image + .wpforms-setting-upload-image {
  display: none;
}

@media (max-width: 1280px) and (min-width: 601px) {
  .wpforms-admin-settings-email .wpforms-email-header-image.has-external-image-url .wpforms-setting-remove-image {
    grid-column: 1/4 span;
    grid-row: 3;
  }
}

.wpforms-admin-settings-email .wpforms-email-header-image:not([class*="has-image-size"]) img {
  height: 0;
}

.wpforms-admin-settings-email .wpforms-email-header-image.has-image-size-small img {
  max-height: 120px;
  max-width: Min(280px, 100%);
}

.wpforms-admin-settings-email .wpforms-email-header-image.has-image-size-small img[style*="background-color"] {
  padding: 20px;
  border-radius: 4px;
}

.wpforms-admin-settings-email .wpforms-email-header-image.has-image-size-medium img {
  max-height: 180px;
  max-width: Min(410px, 100%);
}

.wpforms-admin-settings-email .wpforms-email-header-image.has-image-size-medium img[style*="background-color"] {
  padding: 30px;
  border-radius: 4px;
}

.wpforms-admin-settings-email .wpforms-email-header-image.has-image-size-large img {
  max-height: 240px;
  max-width: Min(580px, 100%);
}

.wpforms-admin-settings-email .wpforms-email-header-image.has-image-size-large img[style*="background-color"] {
  padding: 40px;
  border-radius: 4px;
}

.wpforms-admin-settings-email .wpforms-setting-row [class^="notice-"] {
  background-color: #ffffff;
  border: 1px solid #c3c4c7;
  border-left-width: 4px;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
  padding: 11px 15px;
}

.wpforms-admin-settings-email .wpforms-setting-row [class^="notice-"] p {
  margin: 0;
  line-height: 18px;
}

.wpforms-admin-settings-email .wpforms-setting-row .notice-info {
  border-left-color: #056aab;
}

.wpforms-admin-settings-email .wpforms-setting-row .notice-warning {
  border-left-color: #dba617;
}

.wpforms-admin-settings-email .email-appearance-mode-toggle .wpforms-setting-field {
  padding-top: 8px;
}

.wpforms-admin-settings-email .wpforms-setting-row-color_scheme.legacy-template .notice-warning, .wpforms-admin-settings-email .wpforms-setting-row-color_scheme.education-modal .notice-warning {
  display: none;
}

.wpforms-admin-settings-email .wpforms-setting-row-color_scheme .notice-warning {
  margin-top: 20px;
  max-width: 820px;
}

@media (min-width: 783px) {
  .wpforms-admin-settings-email .wpforms-setting-row-color_scheme .notice-warning {
    margin-left: 200px;
  }
}

.wpforms-admin-settings-email .wpforms-setting-row-color_scheme .wpforms-setting-field {
  display: grid;
  max-width: 400px;
  row-gap: 10px;
}

.wpforms-admin-settings-email .wpforms-setting-row-color_scheme .wpforms-setting-field label {
  color: #646970;
}

@media (min-width: 601px) {
  .wpforms-admin-settings-email .wpforms-setting-row-color_scheme .wpforms-setting-field label {
    grid-row: 2;
  }
}

@media (max-width: 600px) {
  .wpforms-admin-settings-email .wpforms-setting-row-color_scheme .wpforms-setting-field label:not(:last-child) {
    padding-bottom: 10px;
  }
}

@media (min-width: 601px) {
  .wpforms-admin-settings-email .wpforms-setting-row-color_scheme .wpforms-setting-field {
    grid-template-columns: repeat(4, 1fr);
  }
  .wpforms-admin-settings-email .wpforms-setting-row-color_scheme .wpforms-setting-field .wpforms-color-picker {
    border-color: transparent;
    width: 100%;
  }
  .wpforms-admin-settings-email .wpforms-setting-row-color_scheme .wpforms-setting-field .minicolors {
    border: 1px solid #8c8f94;
  }
  .wpforms-admin-settings-email .wpforms-setting-row-color_scheme .wpforms-setting-field .minicolors:first-of-type {
    border-end-start-radius: 3px;
    border-start-start-radius: 3px;
  }
  .wpforms-admin-settings-email .wpforms-setting-row-color_scheme .wpforms-setting-field .minicolors:last-of-type {
    border-end-end-radius: 3px;
    border-start-end-radius: 3px;
  }
  .wpforms-admin-settings-email .wpforms-setting-row-color_scheme .wpforms-setting-field .minicolors:not(:first-of-type) {
    border-inline-start-width: 0;
  }
  .wpforms-admin-settings-email .wpforms-setting-row-color_scheme .wpforms-setting-field .minicolors:not(:first-of-type) .wpforms-color-picker {
    border-end-start-radius: 0;
    border-start-start-radius: 0;
  }
  .wpforms-admin-settings-email .wpforms-setting-row-color_scheme .wpforms-setting-field .minicolors:not(:last-of-type) .wpforms-color-picker {
    border-end-end-radius: 0;
    border-start-end-radius: 0;
  }
}

.wpforms-admin-settings-email .wpforms-setting-row.legacy-template,
.wpforms-admin-settings-email .wpforms-setting-row.education-modal {
  width: max-content;
}

.wpforms-admin-settings-email .wpforms-setting-row.legacy-template .minicolors,
.wpforms-admin-settings-email .wpforms-setting-row.legacy-template .choicesjs-select-wrap,
.wpforms-admin-settings-email .wpforms-setting-row.legacy-template.email-appearance-mode-toggle .wpforms-settings-field-radio-wrapper,
.wpforms-admin-settings-email .wpforms-setting-row.education-modal .minicolors,
.wpforms-admin-settings-email .wpforms-setting-row.education-modal .choicesjs-select-wrap,
.wpforms-admin-settings-email .wpforms-setting-row.education-modal.email-appearance-mode-toggle .wpforms-settings-field-radio-wrapper {
  opacity: .5;
  pointer-events: none;
}

.wpforms-admin-settings-email .wpforms-setting-row.legacy-template label {
  pointer-events: none;
}

.wpforms-admin-settings-email .email-light-mode.wpforms-hide, .wpforms-admin-settings-email .email-dark-mode.wpforms-hide {
  display: none !important;
}

.wpforms-admin-settings-payments .wpforms-admin-settings-form #wpforms-setting-row-currency .choices.is-open.is-flipped .choices__list--dropdown .choices__list {
  max-height: 200px;
}

.wpforms-admin-settings-payments .wpforms-setting-row:last-of-type {
  border-bottom: none;
}

.wpforms-admin-settings-payments .wpforms-setting-row#wpforms-setting-row-stripe-webhooks-communication .wpforms-setting-field, .wpforms-admin-settings-payments .wpforms-setting-row#wpforms-setting-row-stripe-api-version .wpforms-setting-field, .wpforms-admin-settings-payments .wpforms-setting-row#wpforms-setting-row-authorize_net-connection-status-live .wpforms-setting-field, .wpforms-admin-settings-payments .wpforms-setting-row#wpforms-setting-row-authorize_net-connection-status-test .wpforms-setting-field {
  margin-top: 8px;
}

.wpforms-admin-settings-payments .wpforms-setting-row#wpforms-setting-row-square-connection-status-sandbox .wpforms-square-connected, .wpforms-admin-settings-payments .wpforms-setting-row#wpforms-setting-row-square-connection-status-production .wpforms-square-connected {
  margin-top: 8px;
}

.wpforms-admin-settings-payments .wpforms-setting-row#wpforms-setting-row-square-connection-status-sandbox .wpforms-square-connected .wpforms-notice.notice-error, .wpforms-admin-settings-payments .wpforms-setting-row#wpforms-setting-row-square-connection-status-production .wpforms-square-connected .wpforms-notice.notice-error {
  border-left-color: #d63638;
  margin: 10px 0;
}

.wpforms-admin-settings-payments .wpforms-setting-row#wpforms-setting-row-square-location-id-sandbox, .wpforms-admin-settings-payments .wpforms-setting-row#wpforms-setting-row-square-location-id-production {
  padding-bottom: 0;
}

.wpforms-admin-settings-payments .wpforms-setting-row#wpforms-setting-row-square-location-id-sandbox.location-error .choices__inner, .wpforms-admin-settings-payments .wpforms-setting-row#wpforms-setting-row-square-location-id-production.location-error .choices__inner {
  border-color: #d63638;
  box-shadow: 0 0 0 1px #d63638;
}

.wpforms-admin-settings-payments .wpforms-setting-row#wpforms-setting-row-square-location-id-sandbox.location-error .choices__list--dropdown, .wpforms-admin-settings-payments .wpforms-setting-row#wpforms-setting-row-square-location-id-production.location-error .choices__list--dropdown {
  border-color: #d63638;
  box-shadow: 0 1px 0 1px #d63638;
}

.wpforms-admin-settings-payments .wpforms-setting-row#wpforms-setting-row-square-location-status-sandbox .wpforms-notice.notice-error, .wpforms-admin-settings-payments .wpforms-setting-row#wpforms-setting-row-square-location-status-production .wpforms-notice.notice-error {
  border-left-color: #d63638;
}

.wpforms-admin-settings-payments .wpforms-setting-row#wpforms-setting-row-square-webhooks-enabled {
  padding-bottom: 10px;
}

.wpforms-admin-settings-payments .wpforms-setting-row#wpforms-setting-row-square-webhooks-connect-status-sandbox, .wpforms-admin-settings-payments .wpforms-setting-row#wpforms-setting-row-square-webhooks-connect-status-production {
  padding-bottom: 0;
}

.wpforms-admin-settings-payments .wpforms-setting-row#wpforms-setting-row-square-webhooks-connect-status-sandbox .wpforms-notice.notice-error, .wpforms-admin-settings-payments .wpforms-setting-row#wpforms-setting-row-square-webhooks-connect-status-production .wpforms-notice.notice-error {
  margin: 0 0 10px 0;
  border-left-color: #d63638;
}

.wpforms-admin-settings-payments .wpforms-setting-row#wpforms-setting-row-square-webhooks-communication-status {
  padding-bottom: 0;
}

.wpforms-admin-settings-payments .wpforms-setting-row#wpforms-setting-row-square-webhooks-communication-status .wpforms-notice.notice-warning {
  margin: -20px 0 20px 0;
  border-left-color: #036aab;
}

.wpforms-admin-settings-payments .wpforms-setting-row#wpforms-setting-row-paypal-commerce-connection-status-live p:not(.desc), .wpforms-admin-settings-payments .wpforms-setting-row#wpforms-setting-row-paypal-commerce-connection-status-sandbox p:not(.desc) {
  margin-top: 0;
}

.wpforms-admin-settings-payments .wpforms-setting-row#wpforms-setting-row-paypal-commerce-connection-status-live .wpforms-paypal-commerce-connected, .wpforms-admin-settings-payments .wpforms-setting-row#wpforms-setting-row-paypal-commerce-connection-status-sandbox .wpforms-paypal-commerce-connected {
  display: block;
  margin-top: 8px;
}

.wpforms-admin-settings-payments .wpforms-setting-row#wpforms-setting-row-paypal-commerce-connection-status-live .wpforms-paypal-commerce-connected p, .wpforms-admin-settings-payments .wpforms-setting-row#wpforms-setting-row-paypal-commerce-connection-status-sandbox .wpforms-paypal-commerce-connected p {
  margin-top: 10px;
}

.wpforms-admin-settings-payments p.submit {
  border-top: 1px solid #e4e4e4;
}

.wpforms-admin-settings-payments .wpforms-status {
  display: flex;
  align-items: center;
  gap: 10px;
}

.wpforms-admin-settings-payments .wpforms-status:before {
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  content: "";
  height: 17px;
  width: 17px;
}

.wpforms-admin-settings-payments .wpforms-status.wpforms-connected:before {
  background-image: url(../images/check-circle.svg);
}

.wpforms-admin-settings-payments .wpforms-status.wpforms-disconnected:before {
  background-image: url(../images/exclamation-circle.svg);
}

.wpforms-admin-settings-payments .wpforms-status.wpforms-warning:before {
  background-image: url(../images/exclamation-triangle.svg);
}

.wpforms-admin-settings-payments .wpforms-setting-row-toggle[id$="-sandbox-mode"] input[type=checkbox]:checked + label.wpforms-toggle-control-icon,
.wpforms-admin-settings-payments .wpforms-setting-row-toggle[id$="-test-mode"] input[type=checkbox]:checked + label.wpforms-toggle-control-icon {
  background-color: #d63638;
}

.wpforms-admin-settings-payments .wpforms-setting-row-toggle[id$="-sandbox-mode"] input[type=checkbox]:checked + label.wpforms-toggle-control-icon:hover,
.wpforms-admin-settings-payments .wpforms-setting-row-toggle[id$="-test-mode"] input[type=checkbox]:checked + label.wpforms-toggle-control-icon:hover {
  background-color: #b32d2e;
}

.wpforms-admin-settings-payments .wpforms-setting-row-toggle[id$="-sandbox-mode"] input[type=checkbox]:checked:focus + label.wpforms-toggle-control-icon,
.wpforms-admin-settings-payments .wpforms-setting-row-toggle[id$="-test-mode"] input[type=checkbox]:checked:focus + label.wpforms-toggle-control-icon {
  box-shadow: 0 0 0 1px #ffffff, 0 0 0 2px #d63638;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .error.wpforms-square-webhooks-connect-error {
  margin-top: 10px !important;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-content-pane .jconfirm-content input[type=text]#wpforms-square-personal-access-token {
  margin-top: 20px !important;
}

#wpforms-welcome {
  border-top: 3px solid #e27730;
  color: #555;
  padding-top: 110px;
}

@media (max-width: 767px) {
  #wpforms-welcome {
    padding-top: 64px;
  }
}

#wpforms-welcome *,
#wpforms-welcome *::before,
#wpforms-welcome *::after {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

#wpforms-welcome .container {
  margin: 0 auto;
  max-width: 720px;
  padding: 0;
}

#wpforms-welcome .wpforms-btn {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

#wpforms-welcome .block {
  padding: 40px;
}

@media (max-width: 767px) {
  #wpforms-welcome .block {
    padding: 20px;
  }
}

#wpforms-welcome img {
  max-width: 100%;
  height: auto;
}

#wpforms-welcome h1 {
  color: #222;
  font-size: 24px;
  text-align: center;
  margin: 0 0 16px 0;
}

#wpforms-welcome h5 {
  color: #222;
  font-size: 16px;
  margin: 0 0 8px 0;
}

#wpforms-welcome h6 {
  font-size: 16px;
  font-weight: 400;
  line-height: 1.6;
  text-align: center;
  margin: 0;
}

#wpforms-welcome p {
  font-size: 14px;
  margin: 0 0 20px 0;
}

#wpforms-welcome .button-wrap {
  max-width: 590px;
  margin: 0 auto 0 auto;
}

#wpforms-welcome .button-wrap .left {
  float: left;
  width: 50%;
  padding-right: 20px;
}

@media (max-width: 767px) {
  #wpforms-welcome .button-wrap .left {
    float: none;
    width: 100%;
    padding: 0;
    margin-bottom: 20px;
  }
}

#wpforms-welcome .button-wrap .right {
  float: right;
  width: 50%;
  padding-left: 20px;
}

@media (max-width: 767px) {
  #wpforms-welcome .button-wrap .right {
    float: none;
    width: 100%;
    padding: 0;
  }
}

#wpforms-welcome .intro {
  background-color: #fff;
  border: 2px solid #e1e1e1;
  border-radius: 2px;
  margin-bottom: 30px;
  position: relative;
  padding-top: 40px;
}

#wpforms-welcome .intro .sullie {
  background-color: #fff;
  border: 2px solid #e1e1e1;
  border-radius: 50%;
  height: 110px;
  width: 110px;
  padding: 18px 14px 0 14px;
  position: absolute;
  top: -58px;
  left: 50%;
  margin-left: -55px;
}

#wpforms-welcome .intro .video-thumbnail {
  display: block;
  margin: 0 auto;
}

#wpforms-welcome .intro .button-wrap {
  margin-top: 25px;
}

#wpforms-welcome .features {
  background-color: #fff;
  border: 2px solid #e1e1e1;
  border-bottom: 0;
  border-radius: 2px 2px 0 0;
  position: relative;
  padding-top: 20px;
  padding-bottom: 20px;
}

#wpforms-welcome .features .feature-list {
  margin-top: 60px;
}

#wpforms-welcome .features .feature-block {
  float: left;
  width: 50%;
  padding-bottom: 35px;
  overflow: auto;
}

@media (max-width: 767px) {
  #wpforms-welcome .features .feature-block {
    float: none;
    width: 100%;
  }
}

#wpforms-welcome .features .feature-block.first {
  padding-right: 20px;
  clear: both;
}

@media (max-width: 767px) {
  #wpforms-welcome .features .feature-block.first {
    padding-right: 0;
  }
}

#wpforms-welcome .features .feature-block.last {
  padding-left: 20px;
}

@media (max-width: 767px) {
  #wpforms-welcome .features .feature-block.last {
    padding-left: 0;
  }
}

#wpforms-welcome .features .feature-block img {
  float: left;
  max-width: 46px;
}

#wpforms-welcome .features .feature-block h5 {
  margin-left: 68px;
}

#wpforms-welcome .features .feature-block p {
  margin: 0;
  margin-left: 68px;
}

#wpforms-welcome .features .button-wrap {
  margin-top: 25px;
  text-align: center;
}

#wpforms-welcome .upgrade-cta {
  background-color: #000;
  border: 2px solid #e1e1e1;
  border-top: 0;
  border-bottom: 0;
  color: #fff;
}

#wpforms-welcome .upgrade-cta h2 {
  color: #fff;
  font-size: 20px;
  margin: 0 0 30px 0;
}

#wpforms-welcome .upgrade-cta ul {
  display: -ms-flex;
  display: -webkit-flex;
  display: flex;
  -webkit-flex-wrap: wrap;
  flex-wrap: wrap;
  font-size: 15px;
  margin: 0;
  padding: 0;
}

#wpforms-welcome .upgrade-cta ul li {
  display: flex;
  width: 50%;
  margin: 0 0 8px 0;
  padding: 0;
  gap: 5px;
}

#wpforms-welcome .upgrade-cta ul li .dashicons {
  color: #008a20;
}

#wpforms-welcome .upgrade-cta .left {
  float: left;
  width: 66.666666%;
  padding-right: 20px;
}

@media (max-width: 767px) {
  #wpforms-welcome .upgrade-cta .left {
    float: none;
    width: 100%;
    padding-right: 0;
    margin-bottom: 20px;
  }
}

#wpforms-welcome .upgrade-cta .right {
  float: right;
  width: 33.333333%;
  padding: 20px 0 0 20px;
  text-align: center;
}

@media (max-width: 767px) {
  #wpforms-welcome .upgrade-cta .right {
    float: none;
    width: 100%;
    padding-left: 0;
  }
}

#wpforms-welcome .upgrade-cta .right h2 {
  text-align: center;
  margin: 0;
}

#wpforms-welcome .upgrade-cta .right h2 span {
  display: inline-block;
  border-bottom: 1px solid #555;
  padding: 0 15px 12px;
}

#wpforms-welcome .upgrade-cta .right .price {
  padding: 26px 0;
}

#wpforms-welcome .upgrade-cta .right .price .amount {
  font-size: 48px;
  font-weight: 600;
  position: relative;
  display: inline-block;
}

#wpforms-welcome .upgrade-cta .right .price .amount:before {
  content: '$';
  position: absolute;
  top: -8px;
  left: -16px;
  font-size: 18px;
}

#wpforms-welcome .upgrade-cta .right .price .term {
  font-size: 12px;
  display: inline-block;
}

#wpforms-welcome .testimonials {
  background-color: #fff;
  border: 2px solid #e1e1e1;
  border-top: 0;
  padding: 20px 0;
}

#wpforms-welcome .testimonials .testimonial-block {
  margin: 50px 0 0 0;
}

#wpforms-welcome .testimonials .testimonial-block img {
  border-radius: 50%;
  float: left;
  max-width: 100px;
  box-shadow: 0 0 18px rgba(0, 0, 0, 0.2);
}

@media (max-width: 767px) {
  #wpforms-welcome .testimonials .testimonial-block img {
    width: 65px;
  }
}

#wpforms-welcome .testimonials .testimonial-block p {
  font-size: 14px;
  margin: 0 0 12px 140px;
}

@media (max-width: 767px) {
  #wpforms-welcome .testimonials .testimonial-block p {
    margin-left: 100px;
  }
}

#wpforms-welcome .testimonials .testimonial-block p:last-of-type {
  margin-bottom: 0;
}

#wpforms-welcome .footer {
  background-color: #f1f1f1;
  border: 2px solid #e1e1e1;
  border-top: 0;
  border-radius: 0 0 2px 2px;
}

#wpforms-welcome.pro .features {
  border: 2px solid #e1e1e1;
  margin-bottom: 30px;
}

#wpforms-welcome.pro .upgrade,
#wpforms-welcome.pro .footer {
  display: none;
}

#wpforms-welcome.pro .testimonials {
  border: 2px solid #e1e1e1;
}

.dashboard_page_wpforms-getting-started .video-container {
  border: 2px solid #e1e1e1;
}

.dashboard_page_wpforms-getting-started #wpfooter,
.dashboard_page_wpforms-getting-started div.notice {
  display: none !important;
}

.wpforms-wpcode {
  display: grid;
  grid-template-columns: 1fr;
}

.wpforms-wpcode .wpforms-wpcode-container {
  position: relative;
}

.wpforms-wpcode .wpforms-wpcode-blur {
  filter: blur(6px);
  pointer-events: none;
  -webkit-user-select: none;
  user-select: none;
  width: 100%;
}

@media (max-width: 782px) and (min-width: 651px) {
  .wpforms-wpcode .wpforms-wpcode-blur #wpforms-wpcode-snippets-list .list .wpforms-wpcode-snippet {
    display: none;
  }
  .wpforms-wpcode .wpforms-wpcode-blur #wpforms-wpcode-snippets-list .list .wpforms-wpcode-snippet:nth-child(-n+4) {
    display: flex;
  }
}

@media (max-width: 650px) {
  .wpforms-wpcode .wpforms-wpcode-blur #wpforms-wpcode-snippets-list .list .wpforms-wpcode-snippet {
    display: none;
  }
  .wpforms-wpcode .wpforms-wpcode-blur #wpforms-wpcode-snippets-list .list .wpforms-wpcode-snippet:nth-child(-n+2) {
    display: flex;
  }
}

.wpforms-wpcode .wpforms-wpcode-popup {
  z-index: 100;
  justify-self: center;
  align-self: start;
  margin: 80px 0 0 0;
  height: fit-content;
  width: 90%;
  max-width: 730px;
  display: flex;
  padding: 40px;
  flex-direction: column;
  align-items: center;
  border-radius: 6px;
  background: #ffffff;
  box-shadow: 0 5px 60px 0 rgba(0, 0, 0, 0.2);
}

.wpforms-wpcode .wpforms-wpcode-popup .wpforms-wpcode-popup-title {
  color: #1d2327;
  text-align: center;
  width: 100%;
  font-size: 22px;
  font-weight: 500;
  line-height: 24px;
  margin-bottom: 15px;
}

.wpforms-wpcode .wpforms-wpcode-popup .wpforms-wpcode-popup-description {
  text-align: center;
  width: 100%;
  color: #50575e;
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
  margin-bottom: 30px;
}

.wpforms-wpcode .wpforms-wpcode-popup .wpforms-wpcode-popup-button {
  margin-bottom: 15px;
}

.wpforms-wpcode .wpforms-wpcode-popup .wpforms-wpcode-popup-link {
  color: #646970;
  text-align: center;
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
  text-decoration-line: underline;
}

.wpforms-wpcode .wpforms-wpcode-popup .wpforms-wpcode-popup-link:hover {
  color: #2c3338;
}

.wpforms-wpcode .wpforms-wpcode-blur,
.wpforms-wpcode .wpforms-wpcode-popup {
  grid-row: 1;
  grid-column: 1;
}

.wpforms-wpcode .wpforms-wpcode-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  padding-bottom: 30px;
  margin-bottom: 30px;
  border-bottom: 1px solid #dcdcde;
}

@media (max-width: 599px) {
  .wpforms-wpcode .wpforms-wpcode-header {
    flex-direction: column;
    align-items: flex-start;
  }
}

.wpforms-wpcode .wpforms-wpcode-header .wpforms-wpcode-header-search {
  position: relative;
  margin-left: 30px;
}

.wpforms-wpcode .wpforms-wpcode-header .wpforms-wpcode-header-search:before {
  font: normal normal normal 14px/1 FontAwesome, sans-serif;
  content: "\f002";
  color: #a7aaad;
  padding: 12px;
  font-size: 16px;
  left: 0;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}

@media (max-width: 599px) {
  .wpforms-wpcode .wpforms-wpcode-header .wpforms-wpcode-header-search {
    width: 100%;
    margin-top: 20px;
    margin-left: 0;
  }
}

.wpforms-wpcode .wpforms-wpcode-header #wpforms-wpcode-snippet-search {
  width: 250px;
  height: 36px;
  padding-left: 30px;
}

@media (max-width: 599px) {
  .wpforms-wpcode .wpforms-wpcode-header #wpforms-wpcode-snippet-search {
    width: 100%;
  }
}

.wpforms-wpcode #wpforms-wpcode-snippets-list .list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  grid-gap: 20px;
}

@media (max-width: 599px) {
  .wpforms-wpcode #wpforms-wpcode-snippets-list .list {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }
}

.wpforms-wpcode #wpforms-wpcode-snippets-list .list .wpforms-wpcode-snippet {
  margin: 0;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  overflow: hidden;
  align-items: flex-start;
  border-radius: 6px;
  border: 1px solid #c3c4c7;
  background: #ffffff;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.07);
}

.wpforms-wpcode #wpforms-wpcode-snippets-list .list .wpforms-wpcode-snippet .wpforms-wpcode-snippet-header {
  width: 100%;
  padding: 20px;
}

.wpforms-wpcode #wpforms-wpcode-snippets-list .list .wpforms-wpcode-snippet .wpforms-wpcode-snippet-title {
  margin: 0;
  padding-bottom: 5px;
  color: #2c3338;
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: 22px;
  overflow: hidden;
  position: relative;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.wpforms-wpcode #wpforms-wpcode-snippets-list .list .wpforms-wpcode-snippet .wpforms-wpcode-snippet-note {
  color: #646970;
  font-size: 14px;
  font-weight: 400;
  line-height: 18px;
}

.wpforms-wpcode #wpforms-wpcode-snippets-list .list .wpforms-wpcode-snippet .wpforms-wpcode-snippet-footer {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f6f6f6;
  padding: 20px;
}

.wpforms-wpcode #wpforms-wpcode-snippets-list .list .wpforms-wpcode-snippet .wpforms-wpcode-snippet-badge {
  color: #008a20;
  font-size: 10px;
  font-weight: 700;
  line-height: 10px;
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

.wpforms-wpcode #wpforms-wpcode-snippets-list .list .wpforms-wpcode-snippet .wpforms-wpcode-snippet-badge.wpforms-wpcode-installing-in-progress {
  color: #a7aaad;
}

.wpforms-wpcode #wpforms-wpcode-snippets-list .list .wpforms-wpcode-snippet .wpforms-wpcode-snippet-button {
  text-align: center;
}

.wpforms-wpcode #wpforms-wpcode-no-results {
  display: none;
  font-size: 14px;
}
