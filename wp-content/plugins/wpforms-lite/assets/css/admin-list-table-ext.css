.wpforms-list-table-ext-page .wp-list-table {
  border: none;
  border-radius: 4px;
  margin: 0;
  box-shadow: none;
}

.wpforms-list-table-ext-page .wp-list-table th, .wpforms-list-table-ext-page .wp-list-table td {
  box-sizing: border-box;
}

.wpforms-list-table-ext-page .wp-list-table .wpforms-table-column-dragged-out {
  color: transparent;
  background-color: #f0f0f1;
}

.wpforms-list-table-ext-page .wp-list-table .wpforms-table-column-dragged-out > * {
  opacity: 0 !important;
}

.wpforms-list-table-ext-page .wp-list-table .wpforms-table-column-dragged-out > #wpforms-list-table-ext-edit-columns-cog {
  background-color: transparent;
  opacity: 1 !important;
}

.wpforms-list-table-ext-page .wp-list-table .wpforms-table-column-drag-placeholder {
  display: none;
}

.wpforms-list-table-ext-page .wp-list-table .wpforms-table-column-drag-placeholder-prev {
  box-shadow: inset -2px 0 0 0 #8c8f94;
}

.wpforms-list-table-ext-page .wp-list-table thead .wpforms-table-cell-sticky, .wpforms-list-table-ext-page .wp-list-table tbody#the-list .wpforms-table-cell-sticky, .wpforms-list-table-ext-page .wp-list-table tfoot .wpforms-table-cell-sticky {
  position: sticky !important;
  z-index: 2;
  background-color: #ffffff;
}

.wpforms-list-table-ext-page .wp-list-table thead .wpforms-table-cell-sticky.left, .wpforms-list-table-ext-page .wp-list-table tbody#the-list .wpforms-table-cell-sticky.left, .wpforms-list-table-ext-page .wp-list-table tfoot .wpforms-table-cell-sticky.left {
  left: 0;
}

.wpforms-list-table-ext-page .wp-list-table thead .wpforms-table-cell-sticky.left.shadow, .wpforms-list-table-ext-page .wp-list-table tbody#the-list .wpforms-table-cell-sticky.left.shadow, .wpforms-list-table-ext-page .wp-list-table tfoot .wpforms-table-cell-sticky.left.shadow {
  clip-path: polygon(0 0, 150% 0, 150% calc( 100% + 1px), 0 calc( 100% + 1px));
  box-shadow: 2px 0 6px 0 rgba(0, 0, 0, 0.12);
}

.wpforms-list-table-ext-page .wp-list-table thead .wpforms-table-cell-sticky.right, .wpforms-list-table-ext-page .wp-list-table tbody#the-list .wpforms-table-cell-sticky.right, .wpforms-list-table-ext-page .wp-list-table tfoot .wpforms-table-cell-sticky.right {
  right: 0;
}

.wpforms-list-table-ext-page .wp-list-table thead .wpforms-table-cell-sticky.right.shadow, .wpforms-list-table-ext-page .wp-list-table tbody#the-list .wpforms-table-cell-sticky.right.shadow, .wpforms-list-table-ext-page .wp-list-table tfoot .wpforms-table-cell-sticky.right.shadow {
  clip-path: polygon(-50% 0, 100% 0, 100% calc( 100% + 1px), -50% calc( 100% + 1px));
  box-shadow: -2px 0 6px 0 rgba(0, 0, 0, 0.12);
}

.wpforms-list-table-ext-page .wp-list-table thead .wpforms-table-cell-sticky.column-indicators, .wpforms-list-table-ext-page .wp-list-table tbody#the-list .wpforms-table-cell-sticky.column-indicators, .wpforms-list-table-ext-page .wp-list-table tfoot .wpforms-table-cell-sticky.column-indicators {
  left: 35px;
}

.wpforms-list-table-ext-page .wp-list-table.striped.forms tbody#the-list > :nth-child(even) .wpforms-table-cell-sticky,
.wpforms-list-table-ext-page .wp-list-table.striped:not(.forms) tbody#the-list > :nth-child(odd) .wpforms-table-cell-sticky {
  background-color: #f6f6f6;
}

.wpforms-list-table-ext-page .wp-list-table thead th, .wpforms-list-table-ext-page .wp-list-table tfoot th {
  font-size: 14px;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  vertical-align: middle;
  padding-right: 28px;
}

.wpforms-list-table-ext-page .wp-list-table thead th:not(.wpforms-table-cell-sticky):not(.wpforms-table-column-not-draggable):not(.column-cog):hover, .wpforms-list-table-ext-page .wp-list-table tfoot th:not(.wpforms-table-cell-sticky):not(.wpforms-table-column-not-draggable):not(.column-cog):hover {
  cursor: pointer;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='10' height='13' fill='none'%3E%3Cpath fill='%23A7AAAD' d='M4.375 3.031a.9.9 0 0 1-.875.875H1.75a.881.881 0 0 1-.875-.875v-1.75c0-.492.383-.875.875-.875H3.5c.465 0 .875.383.875.875v1.75Zm0 4.375a.9.9 0 0 1-.875.875H1.75a.881.881 0 0 1-.875-.875v-1.75c0-.492.383-.875.875-.875H3.5c.465 0 .875.383.875.875v1.75Zm0 4.375a.9.9 0 0 1-.875.875H1.75a.881.881 0 0 1-.875-.875v-1.75c0-.492.383-.875.875-.875H3.5c.465 0 .875.383.875.875v1.75Zm5.25-8.75a.9.9 0 0 1-.875.875H7a.881.881 0 0 1-.875-.875v-1.75c0-.492.383-.875.875-.875h1.75c.465 0 .875.383.875.875v1.75Zm0 4.375a.9.9 0 0 1-.875.875H7a.881.881 0 0 1-.875-.875v-1.75c0-.492.383-.875.875-.875h1.75c.465 0 .875.383.875.875v1.75Zm0 4.375a.9.9 0 0 1-.875.875H7a.881.881 0 0 1-.875-.875v-1.75c0-.492.383-.875.875-.875h1.75c.465 0 .875.383.875.875v1.75Z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 8px top 12px;
  overflow: hidden;
}

.wpforms-list-table-ext-page .wp-list-table thead th a, .wpforms-list-table-ext-page .wp-list-table tfoot th a {
  float: none;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 0 10px;
  line-height: 36px;
}

.wpforms-list-table-ext-page .wp-list-table thead th a .sorting-indicators, .wpforms-list-table-ext-page .wp-list-table tfoot th a .sorting-indicators {
  float: none;
}

.wpforms-list-table-ext-page .wp-list-table thead th a .screen-reader-text, .wpforms-list-table-ext-page .wp-list-table tfoot th a .screen-reader-text {
  position: initial;
}

.wpforms-list-table-ext-page .wp-list-table thead th a:focus, .wpforms-list-table-ext-page .wp-list-table tfoot th a:focus {
  box-shadow: none;
}

.wpforms-list-table-ext-page .wp-list-table thead .check-column input:hover + label,
.wpforms-list-table-ext-page .wp-list-table thead .check-column label:hover, .wpforms-list-table-ext-page .wp-list-table tfoot .check-column input:hover + label,
.wpforms-list-table-ext-page .wp-list-table tfoot .check-column label:hover {
  background-color: #ffffff;
}

.wpforms-list-table-ext-page .wp-list-table thead tr th:last-child {
  padding-right: 32px;
}

.wpforms-list-table-ext-page .wp-list-table .check-column {
  width: 35px;
  min-width: 35px;
  max-width: 35px;
  padding-block: 10px 0;
  padding-inline: 3px 8px;
  vertical-align: top;
}

.wpforms-list-table-ext-page .wp-list-table .check-column.manage-column {
  padding-block: 0 1px;
  padding-inline: 3px 8px;
  vertical-align: middle;
}

.wpforms-list-table-ext-page .wp-list-table .check-column.manage-column input {
  vertical-align: text-bottom;
}

.wpforms-list-table-ext-page .wp-list-table .column-cog {
  min-width: 28px;
  width: 28px;
  padding: 0 !important;
}

.wpforms-list-table-ext-page .wp-list-table thead .column-cog {
  border-top-right-radius: 4px;
}

.wpforms-list-table-ext-page .wp-list-table tfoot .column-cog {
  border-bottom-right-radius: 4px;
}

.wpforms-list-table-ext-page .wpforms-table-container {
  width: auto;
  clear: both;
  background-color: #ffffff;
  border-radius: 4px;
  border: 1px solid #ccd0d4;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
  margin: 10px 0 0 0;
}

.wpforms-list-table-ext-page .wpforms-table-scroll {
  width: 100%;
  overflow-x: auto;
  padding-bottom: 0;
}

.wpforms-list-table-ext-page .wpforms-table-scroll.wpforms-scrollbar::-webkit-scrollbar {
  background: transparent;
  width: 14px;
  height: 14px;
}

.wpforms-list-table-ext-page .wpforms-table-scroll.wpforms-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.wpforms-list-table-ext-page .wpforms-table-scroll.wpforms-scrollbar::-webkit-scrollbar-thumb {
  box-shadow: inset 0 0 4px 4px rgba(0, 0, 0, 0.05);
  background: transparent;
  border-radius: 14px;
  border: solid 4px transparent;
}

.wpforms-list-table-ext-page .wpforms-table-scroll.wpforms-scrollbar::-webkit-resizer, .wpforms-list-table-ext-page .wpforms-table-scroll.wpforms-scrollbar::-webkit-scrollbar-button, .wpforms-list-table-ext-page .wpforms-table-scroll.wpforms-scrollbar::-webkit-scrollbar-corner {
  display: none;
}

@-moz-document url-prefix() {
  .wpforms-list-table-ext-page .wpforms-table-scroll.wpforms-scrollbar {
    scrollbar-color: rgba(0, 0, 0, 0.05) transparent;
    scrollbar-gutter: initial !important;
    scrollbar-width: thin;
  }
}

.wpforms-list-table-ext-page .wpforms-table-scroll.wpforms-scrollbar:hover::-webkit-scrollbar {
  background: transparent;
  width: 14px;
  height: 14px;
}

.wpforms-list-table-ext-page .wpforms-table-scroll.wpforms-scrollbar:hover::-webkit-scrollbar-track {
  background: transparent;
}

.wpforms-list-table-ext-page .wpforms-table-scroll.wpforms-scrollbar:hover::-webkit-scrollbar-thumb {
  box-shadow: inset 0 0 4px 4px rgba(0, 0, 0, 0.3);
  background: transparent;
  border-radius: 14px;
  border: solid 4px transparent;
}

.wpforms-list-table-ext-page .wpforms-table-scroll.wpforms-scrollbar:hover::-webkit-resizer, .wpforms-list-table-ext-page .wpforms-table-scroll.wpforms-scrollbar:hover::-webkit-scrollbar-button, .wpforms-list-table-ext-page .wpforms-table-scroll.wpforms-scrollbar:hover::-webkit-scrollbar-corner {
  display: none;
}

@-moz-document url-prefix() {
  .wpforms-list-table-ext-page .wpforms-table-scroll.wpforms-scrollbar:hover {
    scrollbar-color: rgba(0, 0, 0, 0.3) transparent;
    scrollbar-gutter: initial !important;
    scrollbar-width: thin;
  }
}

.wpforms-list-table-ext-page .wpforms-table-column-drag-helper {
  box-sizing: border-box;
  background-color: #ffffff;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='10' height='13' fill='none'%3E%3Cpath fill='%23056AAB' d='M4.375 3.031a.9.9 0 0 1-.875.875H1.75a.881.881 0 0 1-.875-.875v-1.75c0-.492.383-.875.875-.875H3.5c.465 0 .875.383.875.875v1.75Zm0 4.375a.9.9 0 0 1-.875.875H1.75a.881.881 0 0 1-.875-.875v-1.75c0-.492.383-.875.875-.875H3.5c.465 0 .875.383.875.875v1.75Zm0 4.375a.9.9 0 0 1-.875.875H1.75a.881.881 0 0 1-.875-.875v-1.75c0-.492.383-.875.875-.875H3.5c.465 0 .875.383.875.875v1.75Zm5.25-8.75a.9.9 0 0 1-.875.875H7a.881.881 0 0 1-.875-.875v-1.75c0-.492.383-.875.875-.875h1.75c.465 0 .875.383.875.875v1.75Zm0 4.375a.9.9 0 0 1-.875.875H7a.881.881 0 0 1-.875-.875v-1.75c0-.492.383-.875.875-.875h1.75c.465 0 .875.383.875.875v1.75Zm0 4.375a.9.9 0 0 1-.875.875H7a.881.881 0 0 1-.875-.875v-1.75c0-.492.383-.875.875-.875h1.75c.465 0 .875.383.875.875v1.75Z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 10px center;
  border: none;
  box-shadow: 0 8px 12px 0 #00000026;
  min-height: 36px;
  padding: 10px 28px 10px 10px;
  display: block;
  opacity: 0.9 !important;
  font-weight: normal;
  font-size: 14px;
  text-align: initial !important;
  cursor: grab !important;
  appearance: unset;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.wpforms-list-table-ext-page .wpforms-table-column-drag-helper * {
  cursor: grab !important;
}

.wpforms-list-table-ext-page .wpforms-table-column-drag-helper a {
  padding: 0 !important;
}

.wpforms-list-table-ext-page .wpforms-table-column-drag-helper #wpforms-list-table-ext-edit-columns-cog {
  display: none;
}

.wpforms-list-table-ext-page .wpforms-table-column-drag-helper .sorting-indicators {
  display: none;
}

.wpforms-list-table-ext-page .wpforms-table-column-drag-helper .wpforms-locations-column-title {
  display: none;
}

.wpforms-list-table-ext-page .wpforms-table-column-drag-helper .wpforms-locations-column-icon {
  display: block;
  width: 12px;
  height: 16px;
  opacity: 0.7;
  background-image: url("../images/file-code.svg");
}

#wpforms-list-table-ext-edit-columns-select-container {
  position: absolute;
  right: 28px;
}

#wpforms-list-table-ext-edit-columns-select-container::after {
  content: '';
  display: block;
  clear: both;
}

#wpforms-list-table-ext-edit-columns-select-container .wpforms-multiselect-checkbox-wrapper {
  width: 240px;
}

#wpforms-list-table-ext-edit-columns-select-container .wpforms-multiselect-checkbox-wrapper .wpforms-multiselect-checkbox-form-outline {
  display: none;
}

#wpforms-list-table-ext-edit-columns-select-container .wpforms-multiselect-checkbox-wrapper .wpforms-multiselect-checkbox-list {
  position: relative;
  max-width: 240px;
  margin-top: 0;
}

#wpforms-list-table-ext-edit-columns-select-container .wpforms-multiselect-checkbox-wrapper .wpforms-multiselect-checkbox-list.open.open-up {
  bottom: unset;
}

#wpforms-list-table-ext-edit-columns-select-container .wpforms-multiselect-checkbox-wrapper .wpforms-multiselect-checkbox-list .wpforms-multiselect-checkbox-search {
  margin: 15px;
  color: #50575e;
  width: calc( 100% - 30px);
  display: block;
}

#wpforms-list-table-ext-edit-columns-select-container .wpforms-multiselect-checkbox-wrapper .wpforms-multiselect-checkbox-list .wpforms-multiselect-checkbox-search::placeholder {
  color: #a7aaad;
}

#wpforms-list-table-ext-edit-columns-select-container .wpforms-multiselect-checkbox-wrapper .wpforms-multiselect-checkbox-list .wpforms-multiselect-checkbox-items {
  padding-inline: 0;
  border-bottom: 1px solid #dcdcde;
  border-top: 1px solid #dcdcde;
  margin-bottom: 10px;
  margin-top: 3px;
  padding-bottom: 10px;
  padding-top: 0;
}

#wpforms-list-table-ext-edit-columns-select-container .wpforms-multiselect-checkbox-wrapper .wpforms-multiselect-checkbox-list .wpforms-multiselect-checkbox-items::-webkit-scrollbar {
  background: transparent;
  width: 14px;
  height: 14px;
}

#wpforms-list-table-ext-edit-columns-select-container .wpforms-multiselect-checkbox-wrapper .wpforms-multiselect-checkbox-list .wpforms-multiselect-checkbox-items::-webkit-scrollbar-track {
  background: transparent;
}

#wpforms-list-table-ext-edit-columns-select-container .wpforms-multiselect-checkbox-wrapper .wpforms-multiselect-checkbox-list .wpforms-multiselect-checkbox-items::-webkit-scrollbar-thumb {
  box-shadow: inset 0 0 4px 4px rgba(0, 0, 0, 0.05);
  background: transparent;
  border-radius: 14px;
  border: solid 4px transparent;
}

#wpforms-list-table-ext-edit-columns-select-container .wpforms-multiselect-checkbox-wrapper .wpforms-multiselect-checkbox-list .wpforms-multiselect-checkbox-items::-webkit-resizer, #wpforms-list-table-ext-edit-columns-select-container .wpforms-multiselect-checkbox-wrapper .wpforms-multiselect-checkbox-list .wpforms-multiselect-checkbox-items::-webkit-scrollbar-button, #wpforms-list-table-ext-edit-columns-select-container .wpforms-multiselect-checkbox-wrapper .wpforms-multiselect-checkbox-list .wpforms-multiselect-checkbox-items::-webkit-scrollbar-corner {
  display: none;
}

@-moz-document url-prefix() {
  #wpforms-list-table-ext-edit-columns-select-container .wpforms-multiselect-checkbox-wrapper .wpforms-multiselect-checkbox-list .wpforms-multiselect-checkbox-items {
    scrollbar-color: rgba(0, 0, 0, 0.05) transparent;
    scrollbar-gutter: initial !important;
    scrollbar-width: thin;
  }
}

#wpforms-list-table-ext-edit-columns-select-container .wpforms-multiselect-checkbox-wrapper .wpforms-multiselect-checkbox-list .wpforms-multiselect-checkbox-items.wpforms-multiselect-checkbox-items-no-search {
  border-top: none;
  margin-top: 0;
}

#wpforms-list-table-ext-edit-columns-select-container .wpforms-multiselect-checkbox-wrapper .wpforms-multiselect-checkbox-list .wpforms-multiselect-checkbox-items:hover::-webkit-scrollbar {
  background: transparent;
  width: 14px;
  height: 14px;
}

#wpforms-list-table-ext-edit-columns-select-container .wpforms-multiselect-checkbox-wrapper .wpforms-multiselect-checkbox-list .wpforms-multiselect-checkbox-items:hover::-webkit-scrollbar-track {
  background: transparent;
}

#wpforms-list-table-ext-edit-columns-select-container .wpforms-multiselect-checkbox-wrapper .wpforms-multiselect-checkbox-list .wpforms-multiselect-checkbox-items:hover::-webkit-scrollbar-thumb {
  box-shadow: inset 0 0 4px 4px rgba(0, 0, 0, 0.3);
  background: transparent;
  border-radius: 14px;
  border: solid 4px transparent;
}

#wpforms-list-table-ext-edit-columns-select-container .wpforms-multiselect-checkbox-wrapper .wpforms-multiselect-checkbox-list .wpforms-multiselect-checkbox-items:hover::-webkit-resizer, #wpforms-list-table-ext-edit-columns-select-container .wpforms-multiselect-checkbox-wrapper .wpforms-multiselect-checkbox-list .wpforms-multiselect-checkbox-items:hover::-webkit-scrollbar-button, #wpforms-list-table-ext-edit-columns-select-container .wpforms-multiselect-checkbox-wrapper .wpforms-multiselect-checkbox-list .wpforms-multiselect-checkbox-items:hover::-webkit-scrollbar-corner {
  display: none;
}

@-moz-document url-prefix() {
  #wpforms-list-table-ext-edit-columns-select-container .wpforms-multiselect-checkbox-wrapper .wpforms-multiselect-checkbox-list .wpforms-multiselect-checkbox-items:hover {
    scrollbar-color: rgba(0, 0, 0, 0.3) transparent;
    scrollbar-gutter: initial !important;
    scrollbar-width: thin;
  }
}

#wpforms-list-table-ext-edit-columns-select-container .wpforms-multiselect-checkbox-wrapper .wpforms-multiselect-checkbox-list .wpforms-multiselect-checkbox-items label {
  margin: 0 15px;
}

#wpforms-list-table-ext-edit-columns-select-container .wpforms-multiselect-checkbox-wrapper .wpforms-multiselect-checkbox-list .wpforms-multiselect-checkbox-items label:first-child {
  padding-top: 15px;
}

#wpforms-list-table-ext-edit-columns-select-container .wpforms-multiselect-checkbox-wrapper .wpforms-multiselect-checkbox-list .wpforms-multiselect-checkbox-items label span {
  width: 180px;
  display: block;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

#wpforms-list-table-ext-edit-columns-select-container .wpforms-multiselect-checkbox-wrapper .wpforms-multiselect-checkbox-list .wpforms-multiselect-checkbox-items label input[type='checkbox']:hover {
  border-color: #2271b1;
}

#wpforms-list-table-ext-edit-columns-select-container .wpforms-multiselect-checkbox-wrapper .wpforms-multiselect-checkbox-list .wpforms-multiselect-checkbox-items label input[type='checkbox']:hover:checked {
  background-color: #04558a;
}

#wpforms-list-table-ext-edit-columns-select-container .wpforms-multiselect-checkbox-wrapper .wpforms-multiselect-checkbox-list .wpforms-multiselect-checkbox-items label input[type='checkbox']:focus {
  border: 2px solid #2271b1 !important;
  box-shadow: none !important;
}

#wpforms-list-table-ext-edit-columns-select-container .wpforms-multiselect-checkbox-wrapper .wpforms-multiselect-checkbox-list .wpforms-multiselect-checkbox-items label input[type='checkbox']:focus:checked {
  background-color: #04558a !important;
  border: 1px solid white !important;
  box-shadow: 0 0 0 1px #04558a !important;
}

#wpforms-list-table-ext-edit-columns-select-container .wpforms-multiselect-checkbox-wrapper .wpforms-multiselect-checkbox-list .wpforms-multiselect-checkbox-items .wpforms-multiselect-checkbox-no-results {
  margin: 0 15px 5px 15px;
  color: #646970;
  font-size: 14px;
  line-height: 16.7px;
}

#wpforms-list-table-ext-edit-columns-select-container .wpforms-multiselect-checkbox-wrapper .wpforms-multiselect-checkbox-list .wpforms-multiselect-checkbox-items .wpforms-multiselect-checkbox-optgroup {
  color: #8C8F94;
  font-weight: 500;
  font-size: 12px;
  line-height: 19px;
  padding-left: 15px;
}

#wpforms-list-table-ext-edit-columns-select-container .wpforms-multiselect-checkbox-wrapper .wpforms-multiselect-checkbox-list .wpforms-multiselect-checkbox-items .wpforms-multiselect-checkbox-optgroup:last-of-type {
  border-top: 1px solid #dcdcde;
  padding-top: 10px;
}

#wpforms-list-table-ext-edit-columns-select-container .wpforms-multiselect-checkbox-wrapper .wpforms-multiselect-checkbox-list #wpforms-list-table-ext-edit-columns-select-submit {
  margin: 5px 15px 10px 15px;
  height: 31px;
}

#wpforms-list-table-ext-edit-columns-cog {
  outline: none;
  box-shadow: none;
  font-size: 19px;
  color: #8b8f95;
  position: absolute;
  inset-inline-end: 12px;
  background-color: #ffffff;
  top: 8px;
  right: 10px;
  padding: 0;
}

#wpforms-list-table-ext-edit-columns-cog.active {
  color: #046bab;
}

#wpforms-list-table-ext-edit-columns-cog:hover:not(.active) {
  color: #646970;
}

.wpforms-no-scroll {
  overflow: hidden !important;
}

body.wpforms-loading, body.wpforms-loading * {
  cursor: wait !important;
}

body.wpforms-loading:before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: transparent;
  z-index: 9999999;
}

@media screen and (max-width: 782px) {
  .wpforms-list-table-ext-page .wp-list-table tr:not(.inline-edit-row):not(.no-items):not(.wpforms-bulk-edit-tags) {
    position: relative;
  }
  .wpforms-list-table-ext-page .wp-list-table tr:not(.inline-edit-row):not(.no-items):not(.wpforms-bulk-edit-tags) td:not(.check-column):not(.column-primary) {
    padding-left: 35% !important;
  }
  .wpforms-list-table-ext-page .wp-list-table tr:not(.inline-edit-row):not(.no-items):not(.wpforms-bulk-edit-tags) td:not(.check-column):not(.column-primary):before {
    content: attr(data-colname) !important;
  }
  .wpforms-list-table-ext-page .wp-list-table tr:not(.inline-edit-row):not(.no-items):not(.wpforms-bulk-edit-tags) th.column-primary {
    width: calc( 100% - 44px) !important;
    padding-inline: 10px;
  }
  .wpforms-list-table-ext-page .wp-list-table tr:not(.inline-edit-row):not(.no-items):not(.wpforms-bulk-edit-tags) td.column-primary {
    height: 60px;
    position: relative;
    padding-top: 12px;
    top: 0;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .wpforms-list-table-ext-page .wp-list-table tr:not(.inline-edit-row):not(.no-items):not(.wpforms-bulk-edit-tags) td.column-primary > a strong {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: initial;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
  .wpforms-list-table-ext-page .wp-list-table tr:not(.inline-edit-row):not(.no-items):not(.wpforms-bulk-edit-tags).is-expanded td:not(.check-column):not(.column-primary):first-of-type {
    margin-top: 60px;
  }
  .wpforms-list-table-ext-page .wp-list-table tr:not(.inline-edit-row):not(.no-items):not(.wpforms-bulk-edit-tags).is-expanded td:not(.check-column):not(.column-primary):last-of-type {
    margin-bottom: 20px;
  }
  .wpforms-list-table-ext-page .wp-list-table tr:not(.inline-edit-row):not(.no-items):not(.wpforms-bulk-edit-tags).is-expanded td:not(:first-of-type).column-primary {
    position: absolute;
    top: 0;
    left: 35px;
    width: calc(100% - 35px) !important;
  }
  @supports (font: -apple-system-body) and (-webkit-appearance: none) and (-webkit-hyphens: none) {
    .wpforms-list-table-ext-page .wp-list-table tr:not(.inline-edit-row):not(.no-items):not(.wpforms-bulk-edit-tags).is-expanded td:not(:first-of-type).column-primary {
      top: auto;
      left: 46px;
      width: calc(100% - 57px) !important;
    }
  }
  .wpforms-list-table-ext-page .wp-list-table tr:not(.inline-edit-row):not(.no-items):not(.wpforms-bulk-edit-tags).is-expanded td:not(.column-primary):first-of-type {
    padding-top: 90px;
  }
  .wpforms-list-table-ext-page .wp-list-table tr:not(.inline-edit-row):not(.no-items):not(.wpforms-bulk-edit-tags).is-expanded td.column-primary {
    height: 80px !important;
  }
  .wpforms-list-table-ext-page .wp-list-table tr:not(.inline-edit-row):not(.no-items):not(.wpforms-bulk-edit-tags) .row-actions {
    padding-left: 4px;
  }
  .wpforms-list-table-ext-page .wp-list-table tr:not(.wpforms-bulk-edit-tags) th.check-column, .wpforms-list-table-ext-page .wp-list-table tr:not(.wpforms-bulk-edit-tags) td.check-column {
    height: 65px;
  }
  .wpforms-list-table-ext-page .wp-list-table tr:not(.wpforms-bulk-edit-tags) th.check-column.column-cb, .wpforms-list-table-ext-page .wp-list-table tr:not(.wpforms-bulk-edit-tags) td.check-column.column-cb {
    padding-top: 0;
    vertical-align: middle;
  }
  .wpforms-list-table-ext-page .wp-list-table tr:not(.wpforms-bulk-edit-tags) th.check-column.column-cb input, .wpforms-list-table-ext-page .wp-list-table tr:not(.wpforms-bulk-edit-tags) td.check-column.column-cb input {
    margin: 0;
    margin-inline: 8px;
  }
  .wpforms-list-table-ext-page .wp-list-table tr:not(.wpforms-bulk-edit-tags) th:not(.check-column):not(.column-primary), .wpforms-list-table-ext-page .wp-list-table tr:not(.wpforms-bulk-edit-tags) td:not(.check-column):not(.column-primary) {
    display: none;
  }
}

.rtl .wpforms-list-table-ext-page .wpforms-table-column-drag-helper {
  background-position: left 10px center;
}

.rtl .wpforms-list-table-ext-page .wp-list-table thead .wpforms-table-cell-sticky.left, .rtl .wpforms-list-table-ext-page .wp-list-table tbody#the-list .wpforms-table-cell-sticky.left, .rtl .wpforms-list-table-ext-page .wp-list-table tfoot .wpforms-table-cell-sticky.left {
  right: 0;
  left: auto;
}

.rtl .wpforms-list-table-ext-page .wp-list-table thead .wpforms-table-cell-sticky.left.shadow, .rtl .wpforms-list-table-ext-page .wp-list-table tbody#the-list .wpforms-table-cell-sticky.left.shadow, .rtl .wpforms-list-table-ext-page .wp-list-table tfoot .wpforms-table-cell-sticky.left.shadow {
  clip-path: polygon(-50% 0, 100% 0, 100% calc( 100% + 1px), -50% calc( 100% + 1px));
  box-shadow: -2px 0 6px 0 rgba(0, 0, 0, 0.12);
}

.rtl .wpforms-list-table-ext-page .wp-list-table thead .wpforms-table-cell-sticky.right, .rtl .wpforms-list-table-ext-page .wp-list-table tbody#the-list .wpforms-table-cell-sticky.right, .rtl .wpforms-list-table-ext-page .wp-list-table tfoot .wpforms-table-cell-sticky.right {
  right: auto;
  left: 0;
}

.rtl .wpforms-list-table-ext-page .wp-list-table thead .wpforms-table-cell-sticky.right.shadow, .rtl .wpforms-list-table-ext-page .wp-list-table tbody#the-list .wpforms-table-cell-sticky.right.shadow, .rtl .wpforms-list-table-ext-page .wp-list-table tfoot .wpforms-table-cell-sticky.right.shadow {
  clip-path: polygon(0 0, 150% 0, 150% calc( 100% + 1px), 0 calc( 100% + 1px));
  box-shadow: 2px 0 6px 0 rgba(0, 0, 0, 0.12);
}

.rtl .wpforms-list-table-ext-page .wp-list-table thead .wpforms-table-cell-sticky.column-indicators, .rtl .wpforms-list-table-ext-page .wp-list-table tbody#the-list .wpforms-table-cell-sticky.column-indicators, .rtl .wpforms-list-table-ext-page .wp-list-table tfoot .wpforms-table-cell-sticky.column-indicators {
  right: 35px;
  left: auto;
}

.rtl .wpforms-list-table-ext-page .wp-list-table thead th, .rtl .wpforms-list-table-ext-page .wp-list-table tfoot th {
  padding-right: unset;
  padding-left: 28px;
}

.rtl .wpforms-list-table-ext-page .wp-list-table thead th:not(.wpforms-table-cell-sticky):not(.wpforms-table-column-not-draggable):not(.column-cog):hover, .rtl .wpforms-list-table-ext-page .wp-list-table tfoot th:not(.wpforms-table-cell-sticky):not(.wpforms-table-column-not-draggable):not(.column-cog):hover {
  background-position: left 8px top 12px;
}

.rtl #wpforms-list-table-ext-edit-columns-cog {
  inset-inline-start: 12px;
  inset-inline-end: unset;
  right: unset;
  left: 10px;
}

.rtl #wpforms-list-table-ext-edit-columns-select-container {
  right: unset;
  left: 28px;
}

.rtl #wpforms-list-table-ext-edit-columns-select-container .wpforms-multiselect-checkbox-optgroup {
  padding-left: unset;
  padding-right: 15px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
}
