#wpforms-notifications {
  background: #ffffff 0 0 no-repeat padding-box;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.05);
  border-radius: 6px;
  opacity: 1;
  min-height: 48px;
  margin: 0 0 20px 0;
}

#wpforms-notifications * {
  box-sizing: border-box;
}

#wpforms-notifications .wpforms-notifications-header {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  border-bottom: 1px solid #dcdcde;
}

#wpforms-notifications .wpforms-notifications-header .wpforms-notifications-bell {
  position: relative;
  width: 16px;
  height: 20px;
  top: 3px;
  margin-inline-end: 10px;
}

#wpforms-notifications .wpforms-notifications-header .wpforms-notifications-bell svg {
  fill: #a7aaad;
}

#wpforms-notifications .wpforms-notifications-header .wpforms-notifications-circle {
  position: absolute;
  width: 11px;
  height: 11px;
  border-radius: 50%;
  top: -4px;
  right: -1px;
  border: 2px solid #ffffff;
  background-color: #d63638;
}

#wpforms-notifications .wpforms-notifications-header .wpforms-notifications-title {
  font-size: 14px;
  font-weight: 600;
  font-style: normal;
  line-height: 1;
  color: #2c3338;
}

#wpforms-notifications .wpforms-notifications-body {
  position: relative;
}

#wpforms-notifications .wpforms-notifications-messages {
  padding-block: 15px;
  padding-inline: 15px 100px;
}

#wpforms-notifications .wpforms-notifications-messages .wpforms-notifications-message {
  display: none;
}

#wpforms-notifications .wpforms-notifications-messages .wpforms-notifications-message.current {
  display: block;
}

#wpforms-notifications .wpforms-notifications-messages .wpforms-notifications-title {
  color: #2c3338;
  font-size: 17px;
  font-weight: 600;
  line-height: 25px;
  margin: 0;
}

#wpforms-notifications .wpforms-notifications-messages .wpforms-notifications-content {
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
  margin: 5px 0 15px 0;
  color: #50575e;
}

#wpforms-notifications .wpforms-notifications-messages .wpforms-notifications-content p {
  font-size: inherit;
  line-height: inherit;
  margin: 0;
}

#wpforms-notifications .wpforms-notifications-messages .wpforms-notifications-content p + p {
  margin-top: 10px;
}

#wpforms-notifications .wpforms-notifications-messages .wpforms-notifications-buttons {
  margin-block: 0;
  margin-inline: 0 80px;
}

#wpforms-notifications .wpforms-notifications-messages .wpforms-notifications-buttons a {
  margin-block: 0;
  margin-inline: 0 10px;
  min-height: unset;
}

#wpforms-notifications .wpforms-notifications-messages .wpforms-notifications-buttons .button-secondary {
  background-color: #f6f7f7;
  border-color: #056aab;
  color: #056aab;
}

#wpforms-notifications .wpforms-notifications-messages .wpforms-notifications-buttons .button-secondary:hover, #wpforms-notifications .wpforms-notifications-messages .wpforms-notifications-buttons .button-secondary:active, #wpforms-notifications .wpforms-notifications-messages .wpforms-notifications-buttons .button-secondary:focus {
  background-color: #f0f0f1;
  border-color: #04558a;
  color: #04558a;
}

#wpforms-notifications .wpforms-notifications-messages .wpforms-notifications-buttons .button-secondary:focus {
  box-shadow: 0 0 0 1px #04558a;
}

#wpforms-notifications .wpforms-notifications-badge {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  gap: 5px;
  padding: 6px 8px;
  margin-left: 10px;
  border-radius: 3px;
  background-color: #f6f6f6;
  color: #50575e;
  font-size: 11px;
  font-weight: 700;
  line-height: 1;
  text-decoration: none;
  text-transform: uppercase;
}

#wpforms-notifications .wpforms-notifications-badge svg {
  width: 15px;
  height: 13px;
}

#wpforms-notifications .wpforms-notifications-badge:focus, #wpforms-notifications .wpforms-notifications-badge:hover {
  background-color: #f0f0f1;
  box-shadow: none;
}

#wpforms-notifications .dismiss {
  position: absolute;
  top: 15px;
  inset-inline-end: 15px;
  width: 14px;
  height: 14px;
  fill: #a7aaad;
  cursor: pointer;
}

#wpforms-notifications .dismiss:hover {
  fill: #d63638;
}

#wpforms-notifications .navigation {
  position: absolute;
  bottom: 15px;
  inset-inline-end: 15px;
  width: 64px;
  height: 30px;
}

#wpforms-notifications .navigation a {
  display: block;
  width: 30px;
  height: 30px;
  border: 1px solid #7e8993;
  border-radius: 3px;
  font-size: 16px;
  line-height: 1.625;
  text-align: center;
  cursor: pointer;
  background-color: #ffffff;
  color: #41454a;
}

#wpforms-notifications .navigation a:hover {
  background-color: #f1f1f1;
}

#wpforms-notifications .navigation .prev {
  float: left;
}

#wpforms-notifications .navigation .next {
  float: right;
}

#wpforms-notifications .navigation .disabled {
  border-color: #dddddd;
  color: #a0a5aa;
  cursor: default;
}

#wpforms-notifications .navigation .disabled:hover {
  background-color: #ffffff;
}

.lity-iframe .lity-content {
  margin: 0 auto;
}

@media screen and (max-width: 768px) {
  #wpforms-notifications .wpforms-notifications-messages {
    padding-block: 15px 10px;
    padding-inline: 16px 50px;
  }
  #wpforms-notifications .wpforms-notifications-messages .wpforms-notifications-message .wpforms-notifications-title {
    line-height: 22px;
    margin-block: 0 -2px;
    margin-inline: 0 30px;
    min-height: 24px;
  }
  #wpforms-notifications .wpforms-notifications-messages .wpforms-notifications-message .wpforms-notifications-content {
    font-size: 16px;
    line-height: 22px;
  }
  #wpforms-notifications .wpforms-notifications-messages .wpforms-notifications-message .wpforms-notifications-buttons {
    margin: 0;
    padding-inline-end: 40px;
  }
  #wpforms-notifications .wpforms-notifications-messages .wpforms-notifications-message .wpforms-notifications-buttons a.button {
    margin-bottom: 10px;
  }
  #wpforms-notifications .navigation {
    bottom: 20px;
    right: 20px;
  }
}

.rtl #wpforms-notifications .navigation .prev {
  float: right;
}

.rtl #wpforms-notifications .navigation .next {
  float: left;
}
