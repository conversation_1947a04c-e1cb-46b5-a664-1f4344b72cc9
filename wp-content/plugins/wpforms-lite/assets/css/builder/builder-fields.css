.wpforms-panel-fields {
  width: 100vw;
  height: 100vh;
}

.wpforms-panel-fields .wpforms-field-row:before {
  content: "";
  display: table;
}

.wpforms-panel-fields .wpforms-field-row:after {
  clear: both;
  content: "";
  display: table;
}

.wpforms-panel-fields #wpforms-hidden {
  display: none;
}

.wpforms-panel-fields .wpforms-field-dragging {
  background-color: #f1f1f1;
}

.wpforms-panel-fields .wpforms-field-drag-out, .wpforms-panel-fields .wpforms-field-drag-over {
  background: #036aab;
  border: none;
  border-radius: 4px;
  color: #ffffff;
  display: block;
  overflow: hidden;
  padding: 12px 14px;
  text-align: left;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 172px;
  max-width: 172px;
  z-index: 10000;
  box-shadow: 0 8px 12px 0 rgba(0, 0, 0, 0.25);
}

.wpforms-panel-fields .wpforms-field-drag-out i, .wpforms-panel-fields .wpforms-field-drag-over i {
  color: rgba(255, 255, 255, 0.65);
  margin: 0 5px 0 0;
}

.wpforms-panel-fields .wpforms-field-drag-placeholder {
  border: 1px dashed #036aab;
  background-color: #EBF3FC80;
  border-radius: 6px;
  margin: 0 0 5px 0;
  width: 100%;
  height: 60px;
}

.wpforms-panel-fields .wpforms-field-drag-placeholder.wpforms-field-drag-not-allowed {
  border-color: #b32d2e;
  background-color: #fcf0f1;
}

.wpforms-panel-fields .wpforms-field-drag-not-allowed {
  cursor: not-allowed !important;
}

.wpforms-panel-fields .wpforms-field-drag-pending {
  border: 1px dashed #036aab;
  background-color: #EBF3FC80;
  border-radius: 6px;
  margin: 0 0 5px 0;
  text-align: center;
  width: 100%;
  max-width: 100%;
  color: rgba(0, 0, 0, 0.7);
  box-shadow: none;
}

.wpforms-panel-fields .wpforms-field-drag-pending i {
  font-size: 18px;
  line-height: 24px;
  color: rgba(0, 0, 0, 0.7);
}

.wpforms-panel-fields .wpforms-field-drag-pending .wpforms-loading-spinner {
  margin-left: 15px;
  width: 18px;
  height: 18px;
  vertical-align: -3px;
  background-size: 18px 18px;
}

#wpforms-panel-fields .wpforms-panel-sidebar {
  position: absolute;
  top: calc( 124px + var( --wpforms-admin-bar-height ));
  bottom: 0;
  inset-inline-start: 95px;
  overflow: hidden;
  transition-property: top, width, inset-inline-start;
  transition-duration: 0.25s, 0.15s, 0.15s;
  transition-timing-function: ease-out, ease-out, ease-in-out;
}

#wpforms-panel-fields .wpforms-panel-sidebar .no-gap {
  margin-bottom: 0;
}

#wpforms-panel-fields .wpforms-panel-sidebar .sub-label {
  color: #86919e;
  font-size: 12px;
  line-height: 14px;
  margin: 8px 0 0 1px;
}

#wpforms-panel-fields .wpforms-panel-sidebar .wpforms-field-options-columns {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: space-between;
  margin-bottom: 20px;
}

#wpforms-panel-fields .wpforms-panel-sidebar .wpforms-field-options-columns + .wpforms-field-options-columns {
  margin-top: -10px;
}

#wpforms-panel-fields .wpforms-panel-sidebar .wpforms-field-options-columns:last-child {
  margin-bottom: 0;
}

#wpforms-panel-fields .wpforms-panel-sidebar .wpforms-field-options-columns > .wpforms-field-options-column:first-child {
  margin-left: 1px;
}

#wpforms-panel-fields .wpforms-panel-sidebar .wpforms-field-options-columns > .wpforms-field-options-column {
  flex-grow: 1;
}

#wpforms-panel-fields .wpforms-panel-sidebar .wpforms-field-options-columns.wpforms-field-options-columns-2 > .wpforms-field-options-column {
  max-width: calc( 100%/2 - 6px);
}

#wpforms-panel-fields .wpforms-panel-sidebar .wpforms-field-options-columns.wpforms-field-options-columns-3 > .wpforms-field-options-column {
  max-width: calc( 100%/3 - 9px);
}

#wpforms-panel-fields .wpforms-panel-sidebar .wpforms-field-options-columns.wpforms-field-options-columns-4 > .wpforms-field-options-column {
  max-width: calc( 100%/4 - 10px);
}

#wpforms-panel-fields .wpforms-panel-sidebar .wpforms-field-options-columns.wpforms-field-options-columns-5 > .wpforms-field-options-column {
  max-width: calc( 100%/5 - 10px);
}

#wpforms-panel-fields .wpforms-panel-sidebar .wpforms-field-options-columns.wpforms-field-options-columns-6 > .wpforms-field-options-column {
  max-width: calc( 100%/6 - 10px);
}

#wpforms-panel-fields .wpforms-panel-sidebar .wpforms-field-options-columns.wpforms-field-options-columns-7 > .wpforms-field-options-column {
  max-width: calc( 100%/7 - 11px);
  text-align: center;
}

#wpforms-panel-fields .wpforms-panel-sidebar .wpforms-field-options-columns.checkboxes-row > label {
  margin-top: 0;
  max-width: 24px;
}

#wpforms-panel-fields .wpforms-panel-sidebar .wpforms-field-options-columns.checkboxes-row > label input {
  margin: 1px 0 5px 0;
}

#wpforms-panel-fields .wpforms-panel-sidebar .wpforms-field-options-columns select {
  display: block;
}

#wpforms-field-options:before {
  background-color: #ebf3fc;
  border-bottom: 1px solid #ced7e0;
  content: '';
  display: block;
  height: 40px;
  inset-inline-start: 115px;
  position: fixed;
  top: calc( 125px + var( --wpforms-admin-bar-height ));
  width: 360px;
  z-index: 10;
  transition-property: top, width, inset-inline-start;
  transition-duration: 0.25s, 0.15s, 0.15s;
  transition-timing-function: ease-out, ease-out, ease-in-out;
}

.wpforms-panel-sidebar-closed #wpforms-field-options:before {
  inset-inline-start: calc( 115px - 400px);
}

.wpforms-panel-fields .no-fields-preview {
  align-content: center;
  min-height: calc( 100vh - 260px - var( --wpforms-admin-bar-height ));
  padding-bottom: 40px;
  padding-top: 40px;
  position: relative;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.wpforms-panel-fields .no-fields-preview::before {
  background-image: url("../../images/empty-states/no-fields.svg");
  background-position: center center;
  background-repeat: no-repeat;
  background-size: 371px 260px;
  content: "";
  display: block;
  height: 260px;
  width: 100%;
}

.wpforms-panel-fields .no-fields-preview::after {
  background-image: url("../../images/builder/default-arrow.svg");
  background-repeat: no-repeat;
  background-size: 97px 83px;
  content: "";
  height: 83px;
  inset-inline-start: 15px;
  position: absolute;
  top: 10px;
  width: 97px;
}

.rtl .wpforms-panel-fields .no-fields-preview::after {
  transform: scale(-1, 1);
}

.wpforms-panel-fields .no-fields-preview h4 {
  color: #444444;
  font-size: 24px;
  font-weight: 600;
  line-height: 29px;
  margin: 30px 0 10px;
}

.wpforms-panel-fields .no-fields-preview p {
  color: #777777;
  font-size: 16px;
  font-weight: 400;
  line-height: 19px;
  margin: 0;
}

.wpforms-panel-fields .wpforms-title-desc {
  border-bottom: 1px solid #dddddd;
  margin: 0 0 15px 0;
}

.wpforms-panel-fields .wpforms-title-desc:hover {
  cursor: pointer;
}

.wpforms-panel-fields .wpforms-title-desc .wpforms-form-name {
  font-size: 28px;
  font-weight: 600;
  line-height: 28px;
  margin: 0;
  padding: 0;
}

.wpforms-panel-fields .wpforms-title-desc .wpforms-form-desc {
  color: #777777;
  display: block;
  font-size: 14px;
  line-height: 18px;
  margin: 15px 0 15px 0;
}

.wpforms-panel-fields .wpforms-title-desc .wpforms-title-desc-inner {
  padding-bottom: 15px;
  padding-inline-end: 200px;
}

.wpforms-panel-fields .wpforms-field-wrap {
  margin: 0px -15px;
  padding-top: 5px;
  padding-bottom: 19px;
}

.wpforms-panel-fields .wpforms-field {
  border: 1px dashed transparent;
  border-radius: 6px;
  font-size: 14px;
  margin: 0 0 5px 0;
  padding: 15px;
  position: relative;
  transition-property: border-color;
  transition-duration: 0.25s;
  transition-timing-function: ease-in;
}

.wpforms-panel-fields .wpforms-field:not(.wpforms-field-child-hovered):hover {
  border: 1px dashed #cccccc;
  cursor: pointer;
}

.wpforms-panel-fields .wpforms-field.active {
  background-color: #f8f8f8;
  border: 1px dashed #cccccc;
}

.wpforms-panel-fields .wpforms-field .label-title {
  cursor: pointer;
  display: block;
  font-size: 16px;
  font-weight: 600;
  line-height: 20px;
  margin: 0 0 15px 0;
  padding-inline-end: 60px;
  text-align: start;
  word-break: break-word;
}

.wpforms-panel-fields .wpforms-field .label-title .required {
  color: #d63638;
  display: none;
}

.wpforms-panel-fields .wpforms-field .label-title .hidden_text,
.wpforms-panel-fields .wpforms-field .label-title .empty_text {
  display: none;
}

.wpforms-panel-fields .wpforms-field .label-title .text {
  display: inline-block;
  margin-inline-end: 15px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.wpforms-panel-fields .wpforms-field .label-title .text:has(~ .required ~ .wpforms-badge) {
  max-width: calc( 100% - 75px);
}

.wpforms-panel-fields .wpforms-field .label-title .grey .wpforms-badge {
  margin-inline-start: 15px;
}

.wpforms-panel-fields .wpforms-field .label-title .wpforms-badge {
  vertical-align: top;
  margin-top: -1px;
}

.wpforms-panel-fields .wpforms-field.label_empty > .label-title {
  color: #444444;
}

.wpforms-panel-fields .wpforms-field.label_empty > .label-title .empty_text {
  display: inline;
  color: #ffb900;
  line-height: 1;
  padding-right: 5px;
}

.wpforms-panel-fields .wpforms-field.label_empty > .label-title .hidden_text {
  display: none !important;
}

.wpforms-panel-fields .wpforms-field.label_hide.label_empty > .label-title .text {
  color: #444444;
}

.wpforms-panel-fields .wpforms-field.label_hide > .label-title {
  color: #777777;
}

.wpforms-panel-fields .wpforms-field.label_hide > .label-title .hidden_text {
  display: inline;
  color: #777777;
  padding-inline-end: 5px;
  vertical-align: top;
}

.wpforms-panel-fields .wpforms-field.sublabel_hide .wpforms-sub-label {
  display: none;
}

.wpforms-panel-fields .wpforms-field.required .label-title .required {
  display: inline-block;
  font-weight: 300;
  margin-inline-start: 5px;
  margin-inline-end: 15px;
  vertical-align: top;
}

.wpforms-panel-fields .wpforms-field.required .label-title .text {
  margin-inline-end: 0;
}

.wpforms-panel-fields .wpforms-field.required .label-title .text:has(~ .required ~ .wpforms-badge) {
  max-width: calc( 100% - 90px);
}

.wpforms-panel-fields .wpforms-field .wpforms-sub-label {
  color: #999999;
  display: block;
  font-size: 14px;
  line-height: 17px;
  margin-top: 5px;
  margin-inline-start: 1px;
  word-break: break-word;
}

.wpforms-panel-fields .wpforms-field input[readonly], .wpforms-panel-fields .wpforms-field input[disabled],
.wpforms-panel-fields .wpforms-field textarea[readonly],
.wpforms-panel-fields .wpforms-field textarea[disabled],
.wpforms-panel-fields .wpforms-field select[readonly],
.wpforms-panel-fields .wpforms-field select[disabled] {
  background-color: #ffffff;
  border: 1px solid #cccccc;
  box-shadow: none;
  color: #999999;
  cursor: pointer;
  opacity: 1;
}

.wpforms-panel-fields .wpforms-field input[type=text],
.wpforms-panel-fields .wpforms-field input[type=range],
.wpforms-panel-fields .wpforms-field input[type=email],
.wpforms-panel-fields .wpforms-field input[type=url],
.wpforms-panel-fields .wpforms-field input[type=password],
.wpforms-panel-fields .wpforms-field input[type=file],
.wpforms-panel-fields .wpforms-field select,
.wpforms-panel-fields .wpforms-field textarea {
  background-color: #ffffff;
  border: 1px solid #cccccc;
  border-radius: 4px;
  box-shadow: none;
  display: block;
  font-size: 16px;
  height: 40px;
  max-width: none;
  padding: 6px 10px;
  width: 60%;
  min-width: 250px;
}

.wpforms-panel-fields .wpforms-field textarea {
  height: 110px;
  width: 100%;
  min-width: 100%;
}

.wpforms-panel-fields .wpforms-field textarea[readonly], .wpforms-panel-fields .wpforms-field textarea[disabled] {
  resize: none;
}

.wpforms-panel-fields .wpforms-field input[type=checkbox],
.wpforms-panel-fields .wpforms-field input[type=radio] {
  margin-right: 10px;
}

.wpforms-panel-fields .wpforms-field select[multiple] {
  height: auto;
}

.wpforms-panel-fields .wpforms-field select {
  padding-inline-end: 24px;
  overflow: hidden !important;
  text-overflow: ellipsis;
}

.wpforms-panel-fields .wpforms-field select > option {
  color: inherit;
  opacity: 1;
}

.wpforms-panel-fields .wpforms-field.size-small input[type=text],
.wpforms-panel-fields .wpforms-field.size-small input[type=range],
.wpforms-panel-fields .wpforms-field.size-small input[type=email],
.wpforms-panel-fields .wpforms-field.size-small input[type=url],
.wpforms-panel-fields .wpforms-field.size-small input[type=password],
.wpforms-panel-fields .wpforms-field.size-small select {
  width: 25%;
}

.wpforms-panel-fields .wpforms-field.size-small textarea {
  height: 60px;
}

.wpforms-panel-fields .wpforms-field.size-large input[type=text],
.wpforms-panel-fields .wpforms-field.size-large input[type=range],
.wpforms-panel-fields .wpforms-field.size-large input[type=email],
.wpforms-panel-fields .wpforms-field.size-large input[type=url],
.wpforms-panel-fields .wpforms-field.size-large input[type=password],
.wpforms-panel-fields .wpforms-field.size-large select {
  width: 100%;
}

.wpforms-panel-fields .wpforms-field.size-large textarea {
  height: 300px;
}

.wpforms-panel-fields .wpforms-field .wpforms-field-row {
  margin: 0 0 10px 0;
  position: relative;
}

.wpforms-panel-fields .wpforms-field .wpforms-field-row:last-of-type {
  margin: 0;
}

.wpforms-panel-fields .wpforms-field .wpforms-field-duplicate,
.wpforms-panel-fields .wpforms-field .wpforms-field-delete {
  background-color: transparent;
  color: #777777;
  font-size: 16px;
  height: 20px;
  opacity: 0;
  position: absolute;
  inset-inline-end: 40px;
  text-align: center;
  top: 15px;
  width: 20px;
  z-index: 10;
  transition-property: all;
  transition-duration: 0.25s;
  transition-timing-function: ease-in;
}

.wpforms-panel-fields .wpforms-field .wpforms-field-duplicate:hover i,
.wpforms-panel-fields .wpforms-field .wpforms-field-delete:hover i {
  color: #444444;
}

.wpforms-panel-fields .wpforms-field .wpforms-field-delete {
  color: #d63638;
  inset-inline-end: 15px;
  font-size: 18px;
}

.wpforms-panel-fields .wpforms-field .wpforms-field-delete:hover i {
  color: #b32d2e;
}

.wpforms-panel-fields .wpforms-field:not(.wpforms-field-child-hovered):hover > .wpforms-field-duplicate,
.wpforms-panel-fields .wpforms-field:not(.wpforms-field-child-hovered):hover > .wpforms-field-delete, .wpforms-panel-fields .wpforms-field:not(.wpforms-field-child-hovered).active > .wpforms-field-duplicate,
.wpforms-panel-fields .wpforms-field:not(.wpforms-field-child-hovered).active > .wpforms-field-delete {
  opacity: 1;
}

.wpforms-panel-fields .wpforms-field.wpforms-field-pagebreak .wpforms-field-duplicate {
  display: none;
}

.wpforms-panel-fields .wpforms-field .wpforms-field-helper {
  background: #eeeeee;
  border-inline-start: 1px dashed #cccccc;
  border-end-end-radius: 6px;
  border-start-start-radius: 6px;
  border-top: 1px dashed #cccccc;
  bottom: 0;
  color: #999999;
  font-size: 11px;
  line-height: 14px;
  font-weight: 500;
  opacity: 0;
  padding: 9px;
  position: absolute;
  inset-inline-end: 0;
  z-index: 10;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  transition-property: all;
  transition-duration: 0.25s;
  transition-timing-function: ease-in;
}

.wpforms-panel-fields .wpforms-field .wpforms-field-helper-edit {
  margin-inline-end: 5px;
}

.wpforms-panel-fields .wpforms-field .wpforms-field-helper-drag {
  position: relative;
  padding-inline-start: 5px;
  margin-inline-end: 5px;
}

.wpforms-panel-fields .wpforms-field .wpforms-field-helper-drag:before {
  content: '';
  position: absolute;
  width: 1px;
  height: calc( 100% - 6px);
  background: #a6a6a6;
  inset-inline-start: 0;
  top: 3px;
}

.wpforms-panel-fields .wpforms-field .wpforms-field-helper-hide {
  color: #a6a6a6;
  margin-inline-start: 5px;
  font-size: 14px;
  width: 14px;
  height: 14px;
  text-align: center;
}

.wpforms-panel-fields .wpforms-field .wpforms-field-helper-hide:hover {
  color: #777777;
}

.wpforms-panel-fields .wpforms-field-not-draggable .wpforms-field-helper-drag {
  display: none;
}

.wpforms-panel-fields .wpforms-field:not(.wpforms-field-child-hovered):hover > .wpforms-field-helper {
  opacity: 1;
}

.wpforms-panel-fields .wpforms-field.wpforms-field-radio .wpforms-alert-dynamic, .wpforms-panel-fields .wpforms-field.wpforms-field-checkbox .wpforms-alert-dynamic, .wpforms-panel-fields .wpforms-field.wpforms-field-payment-multiple .wpforms-alert-dynamic, .wpforms-panel-fields .wpforms-field.wpforms-field-payment-checkbox .wpforms-alert-dynamic {
  margin: 15px 0 0 0;
}

.wpforms-panel-fields .wpforms-field.wpforms-field-stick .wpforms-field-delete,
.wpforms-panel-fields .wpforms-field.wpforms-field-stick .wpforms-field-helper {
  display: none !important;
}

.wpforms-panel-fields .wpforms-field .description {
  clear: both;
  color: #777777;
  font-size: 14px;
  margin: 5px 0 0 0;
  word-break: break-word;
}

.wpforms-panel-fields .wpforms-field .description:empty {
  margin: 0;
}

.wpforms-panel-fields .wpforms-field .description.disclaimer {
  background-color: #ffffff;
  border: 1px solid #cccccc;
  font-size: 12px;
  height: 125px;
  margin-top: 15px;
  overflow-y: scroll;
  padding: 10px 10px 0 10px;
}

.wpforms-panel-fields .wpforms-field .description.disclaimer p {
  font-size: inherit;
  line-height: 17px;
  margin: 0 0 15px 0;
}

.wpforms-panel-fields .wpforms-field .wpforms-field-hide-remaining {
  display: none !important;
}

.wpforms-panel-fields .wpforms-field .wpforms-field-hide-remaining ~ * {
  display: none !important;
}

.wpforms-panel-fields .ui-sortable-disabled .wpforms-field .wpforms-field-duplicate,
.wpforms-panel-fields .ui-sortable-disabled .wpforms-field .wpforms-field-delete {
  cursor: no-drop;
}

.wpforms-panel-fields .wpforms-field-submit {
  margin-bottom: 0;
}

.wpforms-panel-fields .wpforms-field-submit input[type=submit] {
  background: #999999;
  border: none;
  border-radius: 4px;
  color: #ffffff;
  cursor: pointer;
  font-size: 17px;
  font-weight: 600;
  line-height: 21px;
  padding: 10px 15px;
}

.wpforms-panel-fields .wpforms-field-submit input[type=submit]:focus {
  border: none;
  box-shadow: none;
}

.wpforms-panel-fields .wpforms-field select.quantity-input {
  float: inline-start;
  margin-inline-start: 15px;
  width: 70px;
  min-width: 70px;
}

.wpforms-panel-fields .wpforms-field-payment-single .price,
.wpforms-panel-fields .wpforms-field-payment-single .wpforms-currency-symbol,
.wpforms-panel-fields .wpforms-field-payment-checkbox .price,
.wpforms-panel-fields .wpforms-field-payment-checkbox .wpforms-currency-symbol,
.wpforms-panel-fields .wpforms-field-payment-multiple .price,
.wpforms-panel-fields .wpforms-field-payment-multiple .wpforms-currency-symbol {
  white-space: nowrap;
}

#wpforms-panel-fields .wpforms-tabs {
  background: #e0e8f0;
  border-bottom: 1px solid #ced7e0;
  display: flex;
  flex-wrap: nowrap;
  inset-inline-start: 95px;
  margin: 0;
  padding: 0;
  position: fixed;
  top: calc( 76px + var( --wpforms-admin-bar-height ));
  width: 400px;
  z-index: 11;
  transition-property: top, width, inset-inline-start;
  transition-duration: 0.25s, 0.15s, 0.15s;
  transition-timing-function: ease-out;
}

#wpforms-panel-fields .wpforms-tabs .active {
  background: #ebf3fc;
}

#wpforms-panel-fields .wpforms-tabs li {
  margin: 0;
  width: 50%;
}

#wpforms-panel-fields .wpforms-tabs li:last-of-type {
  border-inline-start: 1px solid #ced7e0;
}

#wpforms-panel-fields .wpforms-tabs a {
  color: #444444;
  display: block;
  font-size: 16px;
  font-weight: 400;
  line-height: 20px;
  overflow: hidden;
  padding: 14px;
  text-align: center;
  text-decoration: none;
  text-overflow: ellipsis;
  white-space: nowrap;
}

#wpforms-panel-fields .wpforms-tabs a:hover {
  color: #777777;
  text-decoration: none;
}

#wpforms-panel-fields .wpforms-tabs a:focus {
  -webkit-box-shadow: none;
  box-shadow: none;
}

#wpforms-panel-fields .wpforms-tabs a i {
  color: #86919e;
  font-size: 16px;
  margin-inline-end: 10px;
}

#wpforms-panel-fields .wpforms-tabs a.active {
  pointer-events: none;
}

#wpforms-panel-fields .wpforms-tabs a.active, #wpforms-panel-fields .wpforms-tabs a.active:hover {
  color: #444444;
  font-weight: 600;
  text-decoration: none;
}

#wpforms-panel-fields .wpforms-tab-content {
  display: none;
  margin: 40px 0 0 0;
  position: relative;
  height: calc( 100% - 40px);
  overflow-x: hidden;
  overflow-y: auto;
  scroll-behavior: smooth;
}

#wpforms-panel-fields .wpforms-tab-content.wpforms-add-fields {
  display: block;
  margin-top: 0;
  height: 100%;
  scroll-behavior: smooth;
  padding-block-start: 20px;
  padding-block-end: 20px;
  padding-inline-start: 20px;
  padding-inline-end: 20px;
}

#wpforms-panel-fields .wpforms-tab-content.wpforms-hide-options-groups {
  margin-top: 0;
  height: 100%;
}

#wpforms-panel-fields .wpforms-tab-content.wpforms-hide-options-groups:before {
  display: none !important;
}

#wpforms-panel-fields .wpforms-tab-content.wpforms-hide-options-groups .wpforms-field-option-group-toggle {
  display: none !important;
}

.wpforms-add-fields-group {
  border-bottom: 1px solid #ced7e0;
}

.wpforms-add-fields-group:first-of-type {
  margin-top: -20px;
}

.wpforms-add-fields-group:last-of-type {
  margin-bottom: 0;
  padding-bottom: 0;
}

.wpforms-add-fields-group a {
  color: #444444;
  display: block;
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  outline: 0;
  overflow: hidden;
  padding: 20px 0;
  text-decoration: none;
}

.wpforms-add-fields-group a:hover {
  text-decoration: none;
}

.wpforms-add-fields-group a:active, .wpforms-add-fields-group a:focus {
  outline: 0;
}

.wpforms-add-fields-group a i {
  color: #86919e;
  float: right;
  font-size: 20px;
  margin: -2px 0 0 0;
  transition-property: transform;
  transition-duration: 0.4s;
  transition-timing-function: ease-out;
}

.wpforms-add-fields-group a i.wpforms-angle-right {
  transform: rotate(-90deg);
}

.wpforms-add-fields-group .wpforms-add-fields-buttons {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-bottom: 20px;
  overflow: hidden;
}

.wpforms-add-fields-group button {
  background-color: #036aab;
  border: none;
  border-radius: 4px;
  color: #ffffff;
  display: block;
  margin: 16px 0 0 0;
  width: calc( 50% - 8px);
  overflow: hidden;
  padding-block: 12px;
  padding-inline-start: 14px;
  padding-inline-end: 5px;
  text-align: start;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.wpforms-add-fields-group button:hover {
  background-color: #215d8f;
  cursor: pointer;
}

.wpforms-add-fields-group button:disabled:hover, .wpforms-add-fields-group button.ui-draggable-disabled:hover {
  background-color: #036aab;
  cursor: no-drop;
}

.wpforms-add-fields-group button:nth-child(1), .wpforms-add-fields-group button:nth-child(2) {
  margin-top: 0;
}

.wpforms-add-fields-group button i {
  color: rgba(255, 255, 255, 0.6);
  display: inline-block;
  margin-inline-end: 7px;
}

.rtl .wpforms-add-fields-group a i {
  float: left;
}

.rtl .wpforms-add-fields-group a i.wpforms-angle-right {
  transform: rotate(90deg);
}

.wpforms-panel .wpforms-search-fields-wrapper {
  position: relative;
}

.wpforms-panel .wpforms-search-fields-wrapper .wpforms-search-fields-no-results {
  margin-top: 20px;
  display: none;
}

.wpforms-panel .wpforms-search-fields-wrapper .wpforms-search-fields-list {
  display: none;
  margin-top: 20px;
}

.wpforms-panel .wpforms-search-fields-wrapper .wpforms-search-fields-list .wpforms-add-fields-group:first-of-type {
  margin: 0;
}

.wpforms-panel .wpforms-search-fields-wrapper #wpforms-search-fields-input {
  font-size: 14px;
  font-weight: 400;
  height: 40px;
  line-height: 18px;
  padding-left: 35px;
  padding-right: 35px;
  width: 100%;
  color: #444444;
  border: 1px solid #b0b6bd;
  border-radius: 4px;
  box-shadow: none;
}

.wpforms-panel .wpforms-search-fields-wrapper #wpforms-search-fields-input::placeholder {
  color: #86919e;
  font-weight: 400;
}

.wpforms-panel .wpforms-search-fields-wrapper #wpforms-search-fields-input:focus {
  border-color: #036aab;
  box-shadow: 0 0 0 1px #036aab;
}

.wpforms-panel .wpforms-search-fields-wrapper #wpforms-search-fields-input::-webkit-search-cancel-button {
  display: none;
}

.wpforms-panel .wpforms-search-fields-wrapper #wpforms-search-fields-input:hover::-webkit-search-cancel-button {
  -webkit-text-fill-color: #86919e;
}

.wpforms-panel .wpforms-search-fields-wrapper .wpforms-search-fields-input-wrapper {
  position: relative;
}

.wpforms-panel .wpforms-search-fields-wrapper .wpforms-search-fields-input-wrapper .wpforms-search-fields-input-close {
  display: none;
  color: #b0b6bd;
  padding: 12px;
  font-size: 16px;
  inset-inline-end: 0;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  transition: 0.05s ease;
}

.wpforms-panel .wpforms-search-fields-wrapper .wpforms-search-fields-input-wrapper .wpforms-search-fields-input-close.active {
  display: block;
}

.wpforms-panel .wpforms-search-fields-wrapper .wpforms-search-fields-input-wrapper .wpforms-search-fields-input-close:hover {
  color: #86919e;
}

.wpforms-panel .wpforms-search-fields-wrapper .wpforms-search-fields-input-wrapper:before {
  font: normal normal normal 14px/1 FontAwesome;
  content: "\f002";
  color: #b0b6bd;
  padding: 12px;
  font-size: 16px;
  inset-inline-start: 0;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}

.wpforms-smart-tags-widget-container {
  position: relative;
  align-items: center;
  display: flex;
  margin-bottom: 10px;
  border-radius: 4px;
  border: 1px solid #b0b6bd;
}

.wpforms-smart-tags-widget-container:has(.wpforms-smart-tags-widget:focus), .wpforms-smart-tags-widget-container:has(.wpforms-show-smart-tags.active) {
  border: 1px solid #036aab;
  box-shadow: 0 0 0 1px #036aab;
  outline: none;
}

.wpforms-smart-tags-widget-container .insert-smart-tag-dropdown {
  height: 266px;
  width: calc(100% - 10px);
  max-width: 370px;
  right: -5px;
  margin-top: -5px;
  border-radius: 6px;
  transition: opacity 150ms ease;
}

.wpforms-smart-tags-widget-container .insert-smart-tag-dropdown.open-down {
  margin-top: 13px;
}

.wpforms-smart-tags-widget-container .insert-smart-tag-dropdown ul {
  max-height: none;
  margin-bottom: -1px;
}

.wpforms-smart-tags-widget-container .insert-smart-tag-dropdown li:has(.heading) {
  cursor: default;
  color: #999999;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  background-color: #f8f8f8;
}

.wpforms-smart-tags-widget-container:has(.wpforms-smart-tags-widget.wpforms-readonly) .wpforms-show-smart-tags {
  pointer-events: none;
}

.wpforms-smart-tags-widget-container .wpforms-readonly {
  opacity: 0.7;
  pointer-events: none;
}

.wpforms-smart-tags-widget-original {
  display: none;
}

.wpforms-show-smart-tags {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 32px;
  height: 30px;
  cursor: pointer;
  background-color: #f8f8f8;
  border: 1px solid #b0b6bd;
  border-left: none;
  border-radius: 0 4px 4px 0;
  text-align: center;
  color: #bbbbbb;
}

.wpforms-show-smart-tags i {
  pointer-events: none;
  font-size: 15px;
}

.wpforms-show-smart-tags:hover, .wpforms-show-smart-tags.active {
  color: #777777;
}

.wpforms-smart-tags-widget {
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  outline: none;
  box-shadow: none;
  color: #444444;
  font-size: 14px;
  line-height: 22px;
  padding: 6px 10px;
  margin: 0;
  cursor: text;
}

.wpforms-smart-tags-widget .tag {
  position: relative;
  display: inline-block;
  height: 22px;
  background-color: #036aab;
  color: #ffffff;
  border-radius: 2px;
  margin-bottom: 2px;
  font-size: 12px;
  line-height: 14px;
  padding: 4px 23px 4px 7px;
  margin-right: 1px;
}

.wpforms-smart-tags-widget .tag:focus-visible {
  outline: none;
  color: #036aab;
  background-color: #f2f8fb;
  box-shadow: inset 0 0 0 1px #036aab;
}

.wpforms-smart-tags-widget .tag + .tag-edit-ok {
  margin-block-start: 5px;
  margin-inline-end: 9px;
  margin-block-end: 0;
  margin-inline-start: -20px;
  opacity: 0.5;
  color: #036aab;
  font-size: 14px;
  height: 12px;
  width: 12px;
  cursor: pointer;
  vertical-align: top;
}

.wpforms-smart-tags-widget .tag + .tag-edit-ok:hover {
  opacity: 0.65;
}

.wpforms-smart-tags-widget .tag:last-child {
  margin-right: 0;
}

.wpforms-smart-tags-widget .tag i {
  display: inline-block;
  position: absolute;
  top: 4px;
  inset-inline-end: 7px;
  opacity: 0.5;
  height: 12px;
  width: 12px;
  color: white;
  cursor: pointer;
  font-size: 14px;
}

.wpforms-smart-tags-widget .tag i:hover {
  opacity: 0.65;
}

.wpforms-smart-tags-widget-textarea {
  height: 120px;
  padding-inline-end: 40px;
  overflow: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
  resize: vertical;
}

.wpforms-smart-tags-widget-textarea + .wpforms-show-smart-tags {
  position: absolute;
  top: 0;
  inset-inline-end: 0;
  border-radius: 0 4px 0 4px;
  border: 1px solid #e4e6e8;
  border-top: none;
  border-inline-end: none;
}

.wpforms-smart-tags-widget-textarea::-webkit-scrollbar {
  background: transparent;
  width: 15px;
  height: 15px;
}

.wpforms-smart-tags-widget-textarea::-webkit-scrollbar-track {
  background: transparent;
}

.wpforms-smart-tags-widget-textarea::-webkit-scrollbar-thumb {
  box-shadow: inset 0 0 5px 5px rgba(0, 0, 0, 0);
  background: transparent;
  border-radius: 15px;
  border: solid 4px transparent;
}

.wpforms-smart-tags-widget-textarea::-webkit-resizer, .wpforms-smart-tags-widget-textarea::-webkit-scrollbar-button, .wpforms-smart-tags-widget-textarea::-webkit-scrollbar-corner {
  display: none;
}

@-moz-document url-prefix() {
  .wpforms-smart-tags-widget-textarea {
    scrollbar-color: rgba(0, 0, 0, 0) transparent;
    scrollbar-gutter: initial !important;
    scrollbar-width: thin;
  }
}

.wpforms-smart-tags-widget-textarea:hover::-webkit-scrollbar, .wpforms-smart-tags-widget-textarea:focus::-webkit-scrollbar {
  background: transparent;
  width: 15px;
  height: 15px;
}

.wpforms-smart-tags-widget-textarea:hover::-webkit-scrollbar-track, .wpforms-smart-tags-widget-textarea:focus::-webkit-scrollbar-track {
  background: transparent;
}

.wpforms-smart-tags-widget-textarea:hover::-webkit-scrollbar-thumb, .wpforms-smart-tags-widget-textarea:focus::-webkit-scrollbar-thumb {
  box-shadow: inset 0 0 5px 5px rgba(0, 0, 0, 0.5);
  background: transparent;
  border-radius: 15px;
  border: solid 4px transparent;
}

.wpforms-smart-tags-widget-textarea:hover::-webkit-resizer, .wpforms-smart-tags-widget-textarea:hover::-webkit-scrollbar-button, .wpforms-smart-tags-widget-textarea:hover::-webkit-scrollbar-corner, .wpforms-smart-tags-widget-textarea:focus::-webkit-resizer, .wpforms-smart-tags-widget-textarea:focus::-webkit-scrollbar-button, .wpforms-smart-tags-widget-textarea:focus::-webkit-scrollbar-corner {
  display: none;
}

@-moz-document url-prefix() {
  .wpforms-smart-tags-widget-textarea:hover, .wpforms-smart-tags-widget-textarea:focus {
    scrollbar-color: rgba(0, 0, 0, 0.5) transparent;
    scrollbar-gutter: initial !important;
    scrollbar-width: thin;
  }
}

.wpforms-smart-tags-widget-input {
  position: relative;
  display: inline-block;
  height: 32px;
  padding: 4.5px 10px;
  white-space: nowrap;
  overflow-y: hidden;
  overflow-x: auto;
  border-radius: 4px 0 0 4px;
  border-right: 1px solid #e4e6e8;
  background-color: #ffffff;
  scroll-padding-left: 10px;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.wpforms-smart-tags-widget-input + .wpforms-show-smart-tags {
  border: none;
  flex-shrink: 0;
}

.wpforms-smart-tags-widget-input .tag {
  line-height: 22px;
  margin-bottom: 0;
  padding: 0 25px 0 7px;
}

.wpforms-smart-tags-widget-input ::-webkit-scrollbar {
  display: none;
}

.wpforms-panel-sidebar .wpforms-smart-tags-widget-input + .wpforms-show-smart-tags {
  background: #f8f8f8;
  border-color: #e3e5e8;
}

.wpforms-panel-sidebar .wpforms-smart-tags-widget-input + .wpforms-show-smart-tags i {
  color: #b0b6bd;
}

.wpforms-panel-sidebar .wpforms-smart-tags-widget-input + .wpforms-show-smart-tags:hover i, .wpforms-panel-sidebar .wpforms-smart-tags-widget-input + .wpforms-show-smart-tags.active i {
  color: #86919e;
}

.wp-editor-wrap .mce-toolbar .mce-wpforms-smart-tags-mce-button.active {
  background: #f0f0f1;
  border-color: #50575e;
}

.wp-editor-wrap .mce-toolbar .mce-wpforms-smart-tags-mce-button i {
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.wp-editor-wrap .insert-smart-tag-dropdown {
  height: 266px;
  width: 100%;
  max-width: 370px;
  left: 85px;
  margin-top: -10px;
  border-radius: 6px;
  transition: opacity 150ms ease;
}

.wp-editor-wrap .insert-smart-tag-dropdown.open-down {
  margin-top: 13px;
}

.wp-editor-wrap .insert-smart-tag-dropdown ul {
  max-height: none;
  margin-bottom: -1px;
}

.wp-editor-wrap .insert-smart-tag-dropdown li:has(.heading) {
  color: #999999;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  background-color: #f8f8f8;
}

.wpforms-field-option-row .insert-smart-tag-dropdown {
  width: auto;
  left: -5px;
  right: -5px;
}

.rtl .wpforms-smart-tags-widget-input {
  direction: rtl;
  text-align: right;
  border-radius: 0 4px 4px 0;
  border-left: 1px solid #e4e6e8;
  scroll-padding-right: 10px;
}

.rtl .wpforms-smart-tags-widget-input + .wpforms-show-smart-tags {
  border-radius: 4px 0 0 4px;
}

.rtl .wpforms-smart-tags-widget-input .tag {
  padding: 0 7px 0 25px;
}

.rtl .wpforms-smart-tags-widget-textarea .tag {
  padding: 4px 7px 4px 23px;
}

.rtl .wpforms-smart-tags-widget-textarea + .wpforms-show-smart-tags {
  border-radius: 4px 0;
}

.wpforms-field-option-row {
  margin-bottom: 20px;
}

.wpforms-field-option-row-description p.note {
  margin-top: -15px;
}

.wpforms-field-option-row label {
  display: flex;
  font-size: 14px;
  font-weight: 400;
  line-height: 17px;
  margin: 0 0 8px 1px;
  vertical-align: text-top;
}

.wpforms-field-option-row label.inline {
  display: inline-block;
  font-weight: 400;
  margin: 0 0 0 10px;
}

.wpforms-field-option-row label.sub-label {
  display: block;
}

.wpforms-field-option-row .after-label-description {
  margin-left: auto;
  color: #86919e;
  font-size: 12px;
  text-decoration: none;
}

.wpforms-field-option-row .after-label-description:hover {
  color: #777777;
  text-decoration: underline;
}

.wpforms-field-option-row input:not(:focus),
.wpforms-field-option-row textarea:not(:focus),
.wpforms-field-option-row select:not(:focus) {
  border-color: #b0b6bd;
}

.wpforms-field-option-row input[type=checkbox],
.wpforms-field-option-row input[type=radio] {
  margin-inline-end: 0;
}

.wpforms-field-option-row input[type=text],
.wpforms-field-option-row input[type=number],
.wpforms-field-option-row textarea,
.wpforms-field-option-row select {
  width: calc( 100% - 2px);
}

.wpforms-field-option-row input[type=text] + .wpforms-alert,
.wpforms-field-option-row input[type=number] + .wpforms-alert,
.wpforms-field-option-row textarea + .wpforms-alert,
.wpforms-field-option-row select + .wpforms-alert {
  margin-top: 10px;
}

.wpforms-field-option-row input[type=text].has-before {
  float: right;
  width: 95%;
}

.wpforms-field-option-row .before-input {
  float: left;
  padding: 7px 0 0 0;
  width: 4%;
}

.wpforms-field-option-row p {
  font-size: 14px;
  margin: 0;
}

.wpforms-field-option-row .meta {
  font-weight: 400;
}

.wpforms-field-option-row span.id {
  color: #86919e;
  font-size: 12px;
}

.wpforms-field-option-row.wpforms-field-option-row-code textarea {
  font-family: monospace;
  font-size: 12px;
  min-height: 120px;
  direction: ltr;
}

.wpforms-field-option-row.wpforms-field-option-row-limit_controls {
  align-items: center;
  display: flex;
}

.wpforms-field-option-row.wpforms-field-option-row-limit_controls.wpforms-hide {
  display: none;
}

.wpforms-field-option-row.wpforms-field-option-row-limit_controls input {
  margin-inline-end: 10px;
}

.wpforms-field-option-row.wpforms-field-option-row-date_disable_todays_date.wpforms-hide {
  display: none;
}

.wpforms-field-option-row.color-picker-row {
  overflow: visible;
}

.wpforms-field-option-row.color-picker-row .minicolors {
  width: 100%;
}

.wpforms-field-option-row.color-picker-row .minicolors .minicolors-swatch {
  height: 22px;
  width: 22px;
  border-radius: 2px;
  border: none;
  overflow: hidden;
  box-shadow: inset 0 0 1px rgba(0, 0, 0, 0.1);
}

.wpforms-field-option-row.color-picker-row .minicolors .minicolors-swatch .minicolors-swatch-color {
  box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.15);
}

.wpforms-field-option-row.color-picker-row .wpforms-color-picker {
  height: auto;
  padding-inline-start: 35px !important;
}

.wpforms-field-option-row .wpforms-confirm-disabled .wpforms-field-option-row-confirmation_placeholder,
.wpforms-field-option-row .wpforms-confirm-disabled .wpforms-field-option-row-sublabel_hide {
  display: none;
}

.wpforms-field-option-row .wpforms-field-options-quantity-columns {
  display: flex;
  gap: 10px;
}

.wpforms-field-option-row .wpforms-field-options-quantity-columns .wpforms-field-options-quantity-column {
  display: flex;
  flex-direction: column;
}

#wpforms-field-options .no-fields.wpforms-alert {
  position: fixed;
  z-index: 100;
  margin-top: -20px;
  margin-inline-start: 20px;
  width: 360px;
}

.wpforms-field-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 7px;
}

.wpforms-field-header label {
  margin: 0;
}

.wpforms-field-header .wpforms-toggle-control {
  gap: 2px;
}

#wpforms-panel-fields .layout-selector-display .layouts {
  background-color: #ffffff;
  border: 1px solid #b0b6bd;
  border-radius: 4px;
  display: flex;
  flex-wrap: wrap;
  margin: 1px !important;
  padding: 10px 5px 2px 5px;
  width: 100% !important;
}

#wpforms-panel-fields .layout-selector-display .layout-selector-display-layout {
  cursor: pointer;
  min-width: 25%;
  padding: 0 5px 5px 5px;
}

#wpforms-panel-fields .layout-selector-display span {
  background-color: #b0b6bd;
  border: 1px solid #ffffff;
  display: inline-block;
  height: 30px;
  transition-property: background-color;
  transition-duration: 0.25s;
  transition-timing-function: ease-in;
}

#wpforms-panel-fields .layout-selector-display span.one-half {
  width: 50%;
}

#wpforms-panel-fields .layout-selector-display span.one-third {
  width: 33.33333%;
}

#wpforms-panel-fields .layout-selector-display span.two-third {
  width: 66.66667%;
}

#wpforms-panel-fields .layout-selector-display span.one-fourth {
  width: 25%;
}

#wpforms-panel-fields .layout-selector-display span.two-fourth {
  width: 50%;
}

#wpforms-panel-fields .layout-selector-display .layout-selector-display-layout:hover span,
#wpforms-panel-fields .layout-selector-display .layout-selector-display-columns span:hover {
  background-color: #036aab;
}

#wpforms-panel-fields .layout-selector-display .layout-selector-display-columns {
  min-width: 100%;
  padding: 0 5px 5px 5px;
}

#wpforms-panel-fields .layout-selector-display .layout-selector-display-columns span {
  border: 2px solid #ffffff;
  cursor: pointer;
  height: 70px;
}

.wpforms-icon-picker-jconfirm-box {
  border-top: none !important;
}

.wpforms-icon-picker-jconfirm-content-pane {
  margin-bottom: 15px !important;
  background-color: #f8f8f8;
  border-radius: 6px;
}

.wpforms-icon-picker-title .jconfirm-title {
  margin-top: 0 !important;
}

.wpforms-icon-picker-title .wpforms-icon-picker-description {
  display: block;
  margin: 15px 0 25px 0;
  font-size: 16px;
  line-height: 22px;
  font-weight: normal;
}

.wpforms-icon-picker-title input {
  width: 100%;
  margin: 10px auto;
  border: none;
  box-shadow: 0 0 0 1px #cccccc;
  text-align: center;
  height: 40px;
  border-radius: 20px;
  transition: box-shadow 0.25s ease-in-out;
}

.wpforms-icon-picker-title input:focus {
  box-shadow: 0 0 0 2px #036aab;
}

.wpforms-icon-picker-title input::placeholder {
  color: #bbbbbb;
}

.wpforms-icon-picker-container .wpforms-icon-picker-icons {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 22px;
  grid-auto-rows: min-content;
  padding: 21px;
  min-height: 368px;
}

.wpforms-icon-picker-container .wpforms-icon-picker-icons li {
  background-color: #ffffff;
  box-shadow: 0 0 0 1px #cccccc;
  border-radius: 6px;
  display: flex;
  gap: 10px;
  flex-direction: column;
  cursor: pointer;
  padding: 10px;
  margin: 0;
  transition: box-shadow 0.05s ease-in-out;
}

.wpforms-icon-picker-container .wpforms-icon-picker-icons li:hover {
  box-shadow: 0 0 0 2px #777777, 0 4px 4px rgba(0, 0, 0, 0.1);
}

.wpforms-icon-picker-container .wpforms-icon-picker-icons li.selected {
  box-shadow: 0 0 0 2px #e27730, 0 4px 4px rgba(0, 0, 0, 0.1);
}

.wpforms-icon-picker-container .wpforms-icon-picker-icons li.selected .ic-fa-solid,
.wpforms-icon-picker-container .wpforms-icon-picker-icons li.selected .ic-fa-brands {
  color: #e27730;
}

.wpforms-icon-picker-container .wpforms-icon-picker-icons li.selected span {
  color: #777777;
}

.wpforms-icon-picker-container .wpforms-icon-picker-icons li .ic-fa-brands,
.wpforms-icon-picker-container .wpforms-icon-picker-icons li .ic-fa-regular,
.wpforms-icon-picker-container .wpforms-icon-picker-icons li .ic-fa-solid {
  font-size: var(--wpforms-icon-choices-size-medium);
  line-height: var(--wpforms-icon-choices-size-medium);
  color: #777777;
}

.wpforms-icon-picker-container .wpforms-icon-picker-icons li span {
  font-size: 13px;
  line-height: 16px;
  color: #bbbbbb;
  font-weight: 400;
  width: 102px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.wpforms-icon-picker-container .wpforms-icon-picker-pagination {
  overflow: hidden;
  height: 0;
}

.wpforms-icon-picker-container .wpforms-icon-picker-not-found {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #777777;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.wpforms-icon-picker-container .wpforms-icon-picker-not-found.wpforms-hidden {
  display: none;
}

.wpforms-icon-picker-container .wpforms-icon-picker-not-found strong {
  margin-left: 5px;
}

.smart-tags-list,
.smart-tags-list-display {
  background-color: #ffffff;
  border: 1px solid #b0b6bd;
  border-radius: 4px;
  max-height: 175px;
  overflow-x: hidden;
  overflow-y: auto;
  padding: 0;
}

.smart-tags-list li.heading,
.smart-tags-list-display li.heading {
  padding: 10px !important;
}

.wpforms-panel-content .smart-tags-list,
.wpforms-panel-content .smart-tags-list-display {
  border-color: #cccccc;
}

.wpforms-toggle-control {
  align-items: flex-start;
  display: flex;
  gap: 10px;
}

.wpforms-toggle-control.wpforms-toggle-control-disabled {
  pointer-events: none;
  opacity: 50%;
}

.wpforms-toggle-control input[type=checkbox] {
  display: none;
  height: 0;
  width: 0;
}

.wpforms-toggle-control input[type=checkbox]:checked + label.wpforms-toggle-control-icon {
  background-color: #036aab;
}

.wpforms-toggle-control input[type=checkbox]:checked + label.wpforms-toggle-control-icon:after {
  inset-inline-start: calc( 100% - 13px - 2px);
}

.wpforms-toggle-control span,
.wpforms-toggle-control label {
  align-items: flex-start;
  display: flex;
  gap: 10px;
  margin: 0;
  vertical-align: unset;
}

.wpforms-toggle-control .wpforms-toggle-control-label:hover {
  cursor: pointer;
}

.wpforms-toggle-control .wpforms-toggle-control-status {
  color: #86919e;
  font-size: 12px;
  line-height: 14px;
  margin: 2px 5px;
}

.wpforms-toggle-control .wpforms-toggle-control-icon {
  background-color: #bbbbbb;
  border-radius: 8.5px;
  cursor: pointer;
  display: inline-block;
  height: 17px;
  margin: 0 1px;
  position: relative;
  text-indent: -9999px;
  width: 27px;
  flex: 0 0 auto;
}

.wpforms-toggle-control .wpforms-toggle-control-icon:after {
  background: #ffffff;
  border-radius: 50%;
  content: "";
  height: 13px;
  inset-inline-start: 2px;
  position: absolute;
  top: 2px;
  width: 13px;
  transition-property: all;
  transition-duration: 0.25s;
  transition-timing-function: ease-out;
}

.wpforms-toggle-control .wpforms-help-tooltip {
  margin: 0 !important;
  /* Override default margin set with ID in general.scss */
}

.wpforms-toggle-control:hover input:checked + label.wpforms-toggle-control-icon {
  background-color: #215d8f;
}

.wpforms-toggle-control:hover .wpforms-toggle-control-icon {
  background-color: #777777;
}

.wpforms-panel-sidebar .wpforms-toggle-control .wpforms-toggle-control-icon {
  background-color: #b0b6bd;
}

.wpforms-panel-sidebar .wpforms-toggle-control:hover .wpforms-toggle-control-icon {
  background-color: #86919e;
}

.wpforms-panel-sidebar .wpforms-toggle-control.wpforms-field-option-in-label-right .wpforms-toggle-control-label {
  color: #86919e;
  font-size: 12px;
  line-height: 14px;
  margin: 2px 5px;
  max-width: initial;
}

.wpforms-field-option-group-toggle {
  color: #444444;
  display: block;
  font-size: 15px;
  inset-inline-start: 190px;
  margin: 0;
  outline: 0;
  overflow: hidden;
  padding: 12px 12px 9px 12px;
  position: fixed;
  text-decoration: none;
  text-overflow: ellipsis;
  text-transform: capitalize;
  top: calc( 124px + var( --wpforms-admin-bar-height ));
  white-space: nowrap;
  z-index: 11;
  transition-property: top, width, inset-inline-start;
  transition-duration: 0.25s, 0.15s;
  transition-timing-function: ease-out;
}

.wpforms-field-option-group-toggle:hover {
  color: #86919e;
}

.wpforms-field-option-group-toggle.education-modal {
  opacity: .6;
}

.wpforms-field-option-field-title {
  color: #444444;
  display: block;
  font-size: 15px;
  font-weight: 600;
  line-height: 18px;
  margin: 0 0 0 1px;
  outline: 0;
  padding: 20px 20px 0 20px;
  text-decoration: none;
}

.wpforms-field-option-field-title span {
  color: #86919e;
  font-weight: 400;
}

.wpforms-field-option-field-title-notice {
  padding-block-start: 20px;
  padding-block-end: 0;
  padding-inline-start: 20px;
  padding-inline-end: 20px;
}

.wpforms-field-option-field-title-notice .wpforms-educational-alert.wpforms-alert {
  margin: 0;
  border-inline-start: none;
  border-radius: 6px;
  box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.05);
  padding: 15px;
}

.wpforms-field-option-field-title-notice .wpforms-educational-alert.wpforms-alert h4 {
  font-size: 15px;
  line-height: 18px;
}

.wpforms-field-option-field-title-notice .wpforms-educational-alert.wpforms-alert p {
  color: #777777;
}

.wpforms-field-option-field-title-notice .wpforms-educational-alert.wpforms-alert button {
  margin-top: 10px;
}

.wpforms-field-option-group-basic .wpforms-field-option-group-toggle {
  inset-inline-start: 115px;
  max-width: 83px;
}

.wpforms-field-option-group-advanced .wpforms-field-option-group-toggle {
  inset-inline-start: 192px;
  max-width: 99px;
}

.wpforms-field-option-group-conditionals .wpforms-field-option-group-toggle {
  inset-inline-start: 281px;
  max-width: 116px;
}

.wpforms-panel-sidebar-closed .wpforms-field-option-group-basic .wpforms-field-option-group-toggle {
  inset-inline-start: calc( 115px - 400px);
  max-width: 83px;
}

.wpforms-panel-sidebar-closed .wpforms-field-option-group-advanced .wpforms-field-option-group-toggle {
  inset-inline-start: calc( 192px - 400px);
  max-width: 99px;
}

.wpforms-panel-sidebar-closed .wpforms-field-option-group-conditionals .wpforms-field-option-group-toggle {
  inset-inline-start: calc( 281px - 400px);
  max-width: 116px;
}

.wpforms-field-option-group .wpforms-field-option-group-inner {
  display: none;
  padding-block-start: 20px;
  padding-block-end: 20px;
  padding-inline-start: 20px;
  padding-inline-end: 20px;
}

.wpforms-field-option-group.active .wpforms-field-option-group-toggle {
  border-bottom: 3px solid #e27730;
  font-weight: 600;
}

.wpforms-field-option-group.active .wpforms-field-option-group-toggle:hover {
  color: #444444;
}

.wpforms-field-option-group.active .wpforms-field-option-group-inner {
  display: block;
}

.wpforms-field-is-pro .wpforms-field-option-group-inner {
  cursor: default;
  pointer-events: none;
}

.wpforms-field-is-pro .wpforms-field-option-group-inner .wpforms-field-option-row input,
.wpforms-field-is-pro .wpforms-field-option-group-inner .wpforms-field-option-row select,
.wpforms-field-is-pro .wpforms-field-option-group-inner .wpforms-field-option-row textarea,
.wpforms-field-is-pro .wpforms-field-option-group-inner .wpforms-field-option-row .wpforms-toggle-control-icon,
.wpforms-field-is-pro .wpforms-field-option-group-inner .wpforms-field-option-row .choices,
.wpforms-field-is-pro .wpforms-field-option-group-inner .wpforms-field-option-row .choices-list .add,
.wpforms-field-is-pro .wpforms-field-option-group-inner .wpforms-field-option-row .choices-list .remove,
.wpforms-field-is-pro .wpforms-field-option-group-inner .wpforms-field-option-row .minicolors-sprite {
  opacity: .5;
}

.wpforms-field-is-pro .wpforms-field-option-group-inner .wpforms-expandable-editor {
  opacity: .5;
}
