#wpadminbar .wpforms-menu-notification-counter {
  display: inline-block !important;
  min-width: 18px !important;
  height: 18px !important;
  border-radius: 9px !important;
  margin: 7px 0 0 2px !important;
  vertical-align: top !important;
  font-size: 11px !important;
  line-height: 1.6 !important;
  text-align: center !important;
}

#wpadminbar .wpforms-menu-notification-indicator {
  float: right !important;
  margin: 10px 0 0 !important;
  width: 8px !important;
  height: 8px !important;
  border-radius: 4px !important;
}

#wpadminbar .wpforms-menu-notification-indicator:after {
  display: block !important;
  content: "";
  position: absolute !important;
  width: inherit !important;
  height: inherit !important;
  border-radius: inherit !important;
  background-color: inherit !important;
  animation: wpforms-menu-notification-indicator-pulse 1.5s infinite !important;
}

@keyframes wpforms-menu-notification-indicator-pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(3);
    opacity: 0;
  }
}

#wpadminbar .wpforms-menu-form-notifications {
  border-top: 1px solid #3c4146 !important;
  margin-top: 6px !important;
  padding-top: 6px !important;
}

#wpadminbar .wpforms-menu-form-notifications > .ab-item .wp-admin-bar-arrow::before {
  top: 6px !important;
}

#wpadminbar #wp-admin-bar-wpforms-upgrade a {
  background-color: #00a32a;
  color: #ffffff;
}

#wpadminbar #wp-admin-bar-wpforms-upgrade a:hover {
  background-color: #008a20;
}

#wpadminbar .wpforms-menu-form-last {
  border-bottom: 1px solid #3c4146 !important;
  margin-bottom: 6px !important;
  padding-bottom: 6px !important;
}
