.wpforms-admin-page .wpforms-btn {
  border-width: 1px;
  border-style: solid;
  border-radius: 4px;
  cursor: pointer;
  display: inline-block;
  margin: 0;
  text-decoration: none;
  text-align: center;
  vertical-align: middle;
  white-space: nowrap;
  box-shadow: none;
  transition-property: all;
  transition-duration: 0.05s;
  transition-timing-function: ease-in;
}

.wpforms-admin-page .wpforms-btn.inactive {
  background-color: #d7d7d7;
  border-color: #cccccc;
  color: #444444;
  cursor: no-drop;
}

.wpforms-admin-page .wpforms-btn .wpforms-button-icon,
.wpforms-admin-page .wpforms-btn i {
  margin-inline-end: 10px !important;
}

.wpforms-admin-page .wpforms-btn-block {
  display: block;
  width: 100%;
}

.wpforms-admin-page .wpforms-btn-sm {
  font-size: 14px;
  font-weight: 500;
  padding: 0 10px;
  height: 32px;
  line-height: 30px;
}

.wpforms-admin-page .wpforms-btn-md {
  font-size: 14px;
  font-weight: 600;
  line-height: 17px;
  padding: 10px 15px;
}

.wpforms-admin-page .wpforms-btn-lg {
  font-size: 16px;
  font-weight: 600;
  padding: 16px 28px;
}

.wpforms-admin-page .wpforms-btn-orange {
  background-color: #e27730;
  border-color: #e27730;
  color: #ffffff;
}

.wpforms-admin-page .wpforms-btn-orange:hover {
  background-color: #cd6622;
  border-color: #cd6622;
  color: #ffffff;
}

.wpforms-admin-page .wpforms-btn-blue {
  background-color: #036aab;
  border-color: #036aab;
  color: #ffffff;
}

.wpforms-admin-page .wpforms-btn-blue:hover {
  background-color: #215d8f;
  border-color: #215d8f;
  color: #ffffff;
}

.wpforms-admin-page .wpforms-btn-blue.wpforms-settings-keyword-filter-save-changes {
  font-weight: 400;
  margin-right: 8px;
}

.wpforms-admin-page .wpforms-btn-blue.wpforms-settings-keyword-filter-save-changes .wpforms-loading-spinner {
  margin-left: 0;
  margin-right: 5px;
}

.wpforms-admin-page .wpforms-btn-grey {
  background-color: #eeeeee;
  border-color: #cccccc;
  color: #777777;
}

.wpforms-admin-page .wpforms-btn-grey:hover {
  background-color: #d7d7d7;
  border-color: #cccccc;
  color: #444444;
}

.wpforms-admin-page .wpforms-btn-light-grey {
  background-color: #f8f8f8;
  border-color: #cccccc;
  color: #777777;
}

.wpforms-admin-page .wpforms-btn-light-grey:hover {
  background-color: #eeeeee;
  border-color: #cccccc;
  color: #444444;
}

.wpforms-admin-page .wpforms-btn-blue-borders,
.wpforms-admin-page .wpforms-btn-light-grey-blue-borders {
  background-color: #f8f8f8;
  border-color: currentColor;
  color: #036aab;
}

.wpforms-admin-page .wpforms-btn-blue-borders:hover,
.wpforms-admin-page .wpforms-btn-light-grey-blue-borders:hover {
  background-color: #eeeeee;
  color: #215d8f;
}

.wpforms-admin-page .wpforms-btn-blue-borders:focus,
.wpforms-admin-page .wpforms-btn-light-grey-blue-borders:focus {
  box-shadow: 0 0 0 1px #215d8f;
  outline: 0;
}

.wpforms-admin-page .wpforms-btn-red-borders {
  background-color: #fcf0f1;
  border-color: currentColor;
  color: #d63638;
}

.wpforms-admin-page .wpforms-btn-red-borders:focus {
  box-shadow: 0 0 0 1px #b32d2e;
  outline: 0;
}

.wpforms-admin-page .wpforms-btn-red-borders:hover {
  color: #b32d2e;
  background-color: #f9e1e1;
}

.wpforms-admin-page .wpforms-btn-purple {
  background-color: #faf5fe;
  border-color: #7a30e2;
  color: #7a30e2;
}

.wpforms-admin-page .wpforms-btn-purple:hover, .wpforms-admin-page .wpforms-btn-purple:focus {
  background-color: #f5e9ff;
  border-color: #5c24a9;
  color: #5c24a9;
}

.wpforms-admin-page .wpforms-btn-purple.wpforms-help-tooltip {
  color: #7a30e2;
}

.wpforms-admin-page .wpforms-btn-purple-dark {
  background-color: #7a30e2;
  border-color: #7a30e2;
  color: #ffffff;
}

.wpforms-admin-page .wpforms-btn-purple-dark:hover, .wpforms-admin-page .wpforms-btn-purple-dark:focus {
  background-color: #5c24a9;
  border-color: #5c24a9;
  color: #ffffff;
}

.wpforms-admin-page .wpforms-dismiss-button {
  background: none;
  border: none;
  color: #bbbbbb;
  cursor: pointer;
  padding: 3px;
}

.wpforms-admin-page .wpforms-dismiss-button:hover {
  color: #777777;
}

.wpforms-admin-page .wpforms-dismiss-button:before {
  background: 0 0;
  content: "\f153";
  display: block;
  font: normal 16px/16px dashicons, sans-serif;
  height: 16px;
  speak: none;
  text-align: center;
  width: 16px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.wpforms-admin-page .wpforms-disabled {
  cursor: default;
  opacity: .5;
  pointer-events: none;
}

.wpforms-admin-page .wpforms-inactive {
  cursor: default;
  opacity: .5;
}

.wpforms-admin-page .wpforms-inactive:hover {
  pointer-events: none;
}

.wpforms-admin-empty-state-container {
  color: #50575e;
  font-style: normal;
  padding: 30px;
  text-align: center;
}

.wpforms-admin-empty-state-container .waving-hand-emoji {
  background-image: url(../images/empty-states/waving-hand-emoji.png);
  background-position: 0 0;
  background-repeat: no-repeat;
  background-size: 24px 24px;
  display: inline-block;
  padding-left: 34px;
}

.wpforms-admin-empty-state-container h2 {
  color: #1d2327;
  font-family: inherit;
  font-size: 24px;
  font-weight: 600;
  line-height: 29px;
  margin: 0 0 10px;
}

.wpforms-admin-empty-state-container h4 {
  color: #32373c;
  font-family: inherit;
  font-size: 16px;
  font-weight: 600;
  line-height: 1.5;
  margin-block: 0;
}

.wpforms-admin-empty-state-container img {
  max-width: 428px;
  width: 100%;
  margin: 30px auto;
}

.wpforms-admin-empty-state-container p {
  font-family: inherit;
  font-size: 16px;
  line-height: 24px;
  margin-block: 0;
  text-align: center;
}

.wpforms-admin-empty-state-container.wpforms-admin-no-forms p:first-of-type {
  font-weight: 600;
}

.wpforms-admin-empty-state-container.wpforms-admin-no-forms .wpforms-btn, .wpforms-admin-empty-state-container.wpforms-admin-no-forms .wpforms-btn:active {
  border: none;
  border-radius: 3px;
  font-family: inherit;
  font-weight: 600;
  font-size: 16px;
  line-height: 1.5;
  text-align: center;
  color: #ffffff;
  padding: 15px 30px;
  margin: 0;
  text-decoration: none;
}

.wpforms-admin-empty-state-container.wpforms-admin-no-forms .wpforms-btn:hover, .wpforms-admin-empty-state-container.wpforms-admin-no-forms .wpforms-btn:active:hover {
  text-decoration: none;
}

.wpforms-admin-empty-state-container.wpforms-admin-no-forms .wpforms-btn:focus, .wpforms-admin-empty-state-container.wpforms-admin-no-forms .wpforms-btn:active:focus {
  outline: none;
}

.wpforms-admin-empty-state-container.wpforms-admin-no-forms .wpforms-admin-no-forms-footer {
  margin-top: 30px;
  font-size: 14px;
  line-height: 16px;
}

.wpforms-admin-empty-state-container.wpforms-elementor-no-forms {
  font-family: 'Helvetica Neue', sans-serif;
  background: rgba(255, 255, 255, 0.5);
  border: 1px solid #71d7d7;
  box-sizing: border-box;
  padding: 20px;
}

.wpforms-admin-empty-state-container.wpforms-elementor-no-forms img {
  max-width: 240px;
  width: 100%;
  margin: 0 auto;
}

.wpforms-admin-empty-state-container.wpforms-elementor-no-forms p {
  font-family: inherit;
  font-weight: normal;
  font-size: 14px;
  line-height: 18px;
  text-align: center;
  color: #495157;
  max-width: 450px;
  margin: 20px auto 0;
}

.wpforms-admin-empty-state-container.wpforms-elementor-no-forms a {
  color: inherit;
  text-decoration: underline;
  position: relative;
}

.wpforms-admin-empty-state-container.wpforms-elementor-no-forms a:hover {
  text-decoration: none;
}

.wpforms-admin-empty-state-container.wpforms-elementor-no-forms .wpforms-btn {
  background: #525962;
  border: none;
  border-radius: 3px;
  font-family: inherit;
  font-weight: 600;
  font-size: 12px;
  line-height: 1.5;
  text-align: center;
  text-transform: uppercase;
  color: #ffffff;
  padding: 7px 17px;
  margin: 20px 0 0;
  text-decoration: none;
}

.wpforms-admin-empty-state-container.wpforms-elementor-no-forms .wpforms-btn:hover {
  text-decoration: none;
  background: #2b2c31;
}

.wpforms-admin-empty-state-container.wpforms-elementor-no-forms .wpforms-btn:focus {
  outline: none;
}

.wpforms-admin-empty-state-container.wpforms-elementor-no-forms .wpforms-admin-no-forms-footer {
  font-size: 12px;
  line-height: 1.5;
  color: #6d7882;
}

.wpforms-admin-empty-state-container.wpforms-admin-no-entries img {
  max-width: 413px;
  display: block;
}

.wpforms-admin-empty-state-container.wpforms-admin-no-payments {
  max-width: 640px;
  margin: 0 auto;
}

.wpforms-admin-empty-state-container.wpforms-admin-no-payments img {
  display: block;
}

.wpforms-admin-empty-state-container.wpforms-admin-no-payments .wpforms-btn-lg {
  border-radius: 4px;
  padding: 14px 20px;
}

.wpforms-admin-empty-state-container.wpforms-admin-no-payments a:not([class]) {
  color: #056aab;
  text-decoration: underline;
}

.wpforms-admin-empty-state-container.wpforms-admin-no-payments a:not([class]):hover {
  text-decoration: none;
}

.wpforms-admin-empty-state-container.wpforms-admin-no-payments .wpforms-admin-no-forms-footer {
  margin-top: 30px;
  font-size: 14px;
}

.wpforms-admin-empty-state-container.wpforms-admin-no-user-templates h2, .wpforms-admin-empty-state-container.wpforms-admin-no-user-templates h4 {
  font-weight: 500;
}

.wpforms-admin-empty-state-container.wpforms-admin-no-user-templates p {
  font-size: 14px;
  color: #777777;
}

.wpforms-admin-empty-state-container.wpforms-admin-no-user-templates p.wpforms-admin-no-forms-footer {
  color: #50575e;
}

.wpforms-admin-empty-state-container.wpforms-admin-no-user-templates img {
  max-width: 560px;
  border-radius: 4px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

#wpforms-panel-setup .wpforms-panel-content {
  align-items: stretch;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  padding: 0;
}

#wpforms-setup-form-name {
  align-items: center;
  background-color: #f8f8f8;
  border-bottom: 1px solid #dddddd;
  display: flex;
  justify-content: space-between;
  overflow: hidden;
  padding: 30px;
}

#wpforms-setup-form-name label {
  display: inline-block;
  font-size: 20px;
  font-weight: 600;
  min-width: 200px;
  padding: 0;
}

#wpforms-setup-name {
  display: inline-block;
  font-size: 20px;
  height: 50px;
  margin-inline-start: 30px;
  padding: 13px 10px;
  width: calc( 100% - 290px);
}

.wpforms-setup-title {
  font-size: 20px;
  font-weight: 600;
  margin: 30px 30px 0 30px;
  color: #444444;
}

.wpforms-setup-title .count {
  color: #bbbbbb;
}

.wpforms-setup-desc {
  margin: 10px 30px 30px 30px;
  color: #777777;
}

.wpforms-setup-desc a {
  color: #777777;
}

.wpforms-setup-desc a:hover {
  color: #444444;
}

.wpforms-setup-templates {
  align-items: stretch;
  border-top: 1px solid #dddddd;
  display: flex;
  flex-grow: 2;
  justify-content: space-between;
  margin: 0 30px;
  padding: 0 0 30px 0;
}

.wpforms-setup-templates-sidebar {
  border-inline-end: 1px solid #dddddd;
  padding-inline-end: 30px;
  padding-top: 30px;
  width: 224px;
  box-sizing: content-box;
}

#wpforms-setup-template-search {
  font-size: 16px;
  font-weight: 400;
  height: 40px;
  line-height: 20px;
  padding-inline-start: 35px;
  width: 100%;
  color: #444444;
  border: 1px solid #cccccc;
  border-radius: 4px;
  box-shadow: none;
}

#wpforms-setup-template-search::placeholder {
  color: #bbbbbb;
  font-weight: 400;
}

#wpforms-setup-template-search:focus {
  border-color: #036aab;
  box-shadow: 0 0 0 1px #036aab;
}

.wpforms-setup-templates-search-wrap {
  position: relative;
}

.wpforms-setup-templates-search-wrap i.fa {
  color: #bbbbbb;
  display: block;
  font-size: 16px;
  inset-inline-start: 10px;
  position: absolute;
  top: 12px;
  width: 16px;
}

.wpforms-setup-templates-categories {
  margin-top: 30px;
  width: 224px;
  box-sizing: content-box;
}

.wpforms-setup-templates-categories > li {
  color: #444444;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  line-height: 16px;
  margin: 0;
}

.wpforms-setup-templates-categories > li div {
  border-radius: 4px;
  padding: 10px;
}

.wpforms-setup-templates-categories > li div:hover {
  color: #444444;
  text-decoration: underline;
}

.wpforms-setup-templates-categories > li div > span {
  float: inline-end;
  color: #777777;
  opacity: 0.5;
  font-weight: 400;
}

.wpforms-setup-templates-categories > li.divider {
  border-bottom: 1px solid #dddddd;
  margin: 30px 0;
  padding: 0;
}

.wpforms-setup-templates-categories > li.active div {
  background: #ebf3fc;
  color: #0399ed;
  position: relative;
}

.wpforms-setup-templates-categories > li.active div span {
  color: #0399ed;
  opacity: 1;
  font-weight: 600;
}

.wpforms-setup-templates-categories > li.active div:hover {
  text-decoration: none;
}

.wpforms-setup-templates-categories > li.active.opened .wpforms-setup-templates-subcategories {
  display: block;
}

.wpforms-setup-templates-categories > li.active .chevron {
  color: #0399ed;
}

.wpforms-setup-templates-categories > li i.chevron {
  display: none;
}

.wpforms-setup-templates-categories > li:has(ul) i.chevron {
  display: inline-block;
  position: relative;
  top: -1px;
  padding: 0 5px;
  font-size: 12px;
  color: #777777;
}

.wpforms-setup-templates-categories > li.opened i.chevron {
  transform: rotate(180deg);
}

.wpforms-setup-templates-categories > li.opened ul.wpforms-setup-templates-subcategories {
  display: block;
}

.wpforms-setup-templates-subcategories {
  display: none;
}

.wpforms-setup-templates-subcategories li {
  display: flex;
  justify-content: space-between;
  color: #777777;
  cursor: pointer;
  font-size: 14px;
  font-weight: 400;
  line-height: 18px;
  margin: 0;
  padding-block: 5px;
  padding-inline-end: 10px;
  padding-inline-start: 20px;
  position: relative;
}

.wpforms-setup-templates-subcategories li:hover span:first-child {
  text-decoration: underline;
}

.wpforms-setup-templates-subcategories li.active {
  color: #0399ed;
  font-weight: 400;
}

.wpforms-setup-templates-subcategories li.active span:last-child {
  color: #0399ed;
  font-weight: 400;
  opacity: 1;
}

.wpforms-setup-templates-subcategories li i {
  color: #999999;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  inset-inline-start: 10px;
}

.wpforms-setup-templates-subcategories li span:first-child {
  flex-grow: 1;
  padding-inline-end: 5px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.wpforms-setup-templates-subcategories li span:last-child {
  min-width: 32px;
  color: #777777;
  opacity: 0.5;
  font-weight: 400;
  text-align: end;
}

#wpforms-setup-templates-list {
  padding-top: 30px;
  width: calc( 100% - 250px);
}

#wpforms-setup-templates-list .wpforms-admin-empty-state-container .waving-hand-emoji {
  background-image: url(../../images/empty-states/waving-hand-emoji.png);
}

#wpforms-setup-templates-list .list {
  display: grid;
  grid-gap: 30px;
  grid-template-columns: repeat(auto-fill, minmax(Max(200px, 260px), 1fr));
  padding-inline-start: 30px;
}

#wpforms-setup-templates-list .wpforms-template {
  border-radius: 6px;
  height: 100%;
  overflow: hidden;
  padding: 0 0 15px;
  position: relative;
  box-shadow: 0 0 0 1px #cccccc;
  transition: box-shadow 0.15s ease-in-out;
}

#wpforms-setup-templates-list .wpforms-template:hover, #wpforms-setup-templates-list .wpforms-template.active {
  box-shadow: 0 0 0 2px #777777, 0 3px 4px rgba(0, 0, 0, 0.15);
  outline: none;
}

#wpforms-setup-templates-list .wpforms-template:hover .wpforms-template-buttons,
#wpforms-setup-templates-list .wpforms-template:hover .wpforms-template-favorite,
#wpforms-setup-templates-list .wpforms-template:hover .wpforms-template-remove, #wpforms-setup-templates-list .wpforms-template.active .wpforms-template-buttons,
#wpforms-setup-templates-list .wpforms-template.active .wpforms-template-favorite,
#wpforms-setup-templates-list .wpforms-template.active .wpforms-template-remove {
  opacity: 1;
}

#wpforms-setup-templates-list .wpforms-template:hover .wpforms-badge, #wpforms-setup-templates-list .wpforms-template.active .wpforms-badge {
  opacity: 0;
}

#wpforms-setup-templates-list .wpforms-template.badge h3 {
  padding-inline-end: 45px;
}

#wpforms-setup-templates-list .wpforms-template.selected {
  box-shadow: 0 0 0 2px #e27730, 0 3px 4px rgba(0, 0, 0, 0.15);
}

#wpforms-setup-templates-list .wpforms-template .wpforms-template-thumbnail {
  background-color: #F5F9FD;
  border-bottom: 1px solid #EBEEF1;
  overflow: hidden;
  padding: 20px 54px 0;
}

#wpforms-setup-templates-list .wpforms-template .wpforms-template-thumbnail > img {
  border-radius: 2px 2px 0 0;
  box-shadow: 0px 1px 4px rgba(0, 0, 0, 0.1);
  display: block;
  margin: 0 auto;
  max-width: 100%;
}

#wpforms-setup-templates-list .wpforms-template .wpforms-template-thumbnail .wpforms-template-thumbnail-placeholder {
  align-items: center;
  aspect-ratio: 1;
  background: #fff;
  border-radius: 2px 2px 0 0;
  box-shadow: 0px 1px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: center;
  margin: 0 auto;
  max-width: 350px;
  min-height: 100%;
  width: 100%;
}

#wpforms-setup-templates-list .wpforms-template h3 {
  font-size: 16px;
  font-weight: 600;
  line-height: 18px;
  padding: 20px 20px 2px;
  margin: 0;
  overflow: hidden;
  position: relative;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #444444;
}

#wpforms-setup-templates-list .wpforms-template .wpforms-template-favorite,
#wpforms-setup-templates-list .wpforms-template .wpforms-template-remove {
  display: block;
  position: absolute;
  inset-inline-end: 10px;
  top: 10px;
  font-size: 18px;
  line-height: 18px;
  opacity: 0;
  color: #aaaaaa;
  transition-property: all;
  transition-duration: 0.15s;
  transition-timing-function: ease-out;
}

#wpforms-setup-templates-list .wpforms-template .wpforms-template-favorite:hover,
#wpforms-setup-templates-list .wpforms-template .wpforms-template-remove:hover {
  color: #d63637;
  cursor: pointer;
  transition-property: all;
  transition-duration: 0.05s;
  transition-timing-function: ease-out;
}

#wpforms-setup-templates-list .wpforms-template .wpforms-template-favorite .fa-heart,
#wpforms-setup-templates-list .wpforms-template .wpforms-template-remove .fa-heart {
  color: #d63638;
}

#wpforms-setup-templates-list .wpforms-template.selected h3 {
  padding-inline-end: 60px;
}

#wpforms-setup-templates-list .wpforms-template.pro h3 {
  padding-inline-end: 40px;
}

#wpforms-setup-templates-list .wpforms-template p {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
  color: #777777;
  font-size: 14px;
  line-height: 18px;
  margin: 10px 0 0;
  max-height: 55px;
  min-height: 45px;
  padding: 0 20px;
}

#wpforms-setup-templates-list .wpforms-template-upgrade-banner {
  background: rgba(226, 119, 48, 0.08);
  border-radius: 6px;
  display: flex;
  flex-direction: row;
  grid-column: 1 / -1;
  justify-content: space-between;
  align-items: center;
  padding-inline-end: 20px;
}

#wpforms-setup-templates-list .wpforms-template-upgrade-banner .wpforms-template-content {
  padding: 20px;
  width: 80%;
  color: #444444;
}

#wpforms-setup-templates-list .wpforms-template-upgrade-banner .wpforms-template-content h3 {
  font-weight: 600;
  font-size: 16px;
  line-height: 18px;
  margin-top: 0;
  margin-bottom: 5px;
}

#wpforms-setup-templates-list .wpforms-template-upgrade-banner .wpforms-template-content p {
  font-weight: 400;
  font-size: 14px;
  line-height: 18px;
  margin: 0;
}

#wpforms-setup-templates-list .wpforms-template-upgrade-banner .wpforms-template-upgrade-button {
  width: 20%;
  text-align: end;
}

#wpforms-setup-templates-list .wpforms-template-upgrade-banner .wpforms-template-upgrade-button a {
  min-height: 13px;
}

@media screen and (max-width: 1279px) {
  #wpforms-setup-templates-list .wpforms-template-upgrade-banner .wpforms-template-upgrade-button a {
    box-sizing: border-box;
    width: 100%;
  }
}

#wpforms-setup-templates-list .wpforms-templates-no-results {
  display: none;
  margin-inline-start: 30px;
}

#wpforms-setup-templates-list .wpforms-templates-no-results p {
  font-size: 16px;
  line-height: 40px;
  margin: 0;
  color: #444444;
}

.rtl #wpforms-setup-templates-list .wpforms-badge {
  border-radius: 0 0 3px 0;
  left: 0;
  right: auto;
}

.wpforms-template-buttons {
  background-color: #ffffff;
  border-radius: 6px;
  display: flex;
  opacity: 0;
  padding: 15px 15px 0;
  width: 100%;
  align-items: flex-start;
  gap: 10px;
  position: absolute;
  bottom: 15px;
  transition-property: all;
  transition-duration: 0.15s;
  transition-timing-function: ease-out;
}

.wpforms-template-buttons .wpforms-btn {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-weight: 600;
  font-size: 13px;
  line-height: 16px;
  padding: 11px 10px;
}

.wpforms-template-buttons .visible {
  opacity: 1;
}

@media (max-width: 1439px) {
  #wpforms-setup-name {
    width: calc( 100% - 250px);
  }
}

@media (max-width: 1369px) {
  #wpforms-setup-templates-list .list {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 1279px) {
  #wpforms-setup-templates-list .list {
    grid-template-columns: 1fr;
  }
  #wpforms-setup-templates-list .list .wpforms-template .wpforms-template-buttons {
    position: relative;
    bottom: 0;
    margin: 10px 0 0 0;
    opacity: 1;
    padding: 0 20px;
  }
  #wpforms-setup-templates-list .list .wpforms-template p {
    min-height: auto;
  }
  #wpforms-setup-templates-list .list .wpforms-template-upgrade-banner {
    flex-direction: column;
    padding: 20px;
  }
  #wpforms-setup-templates-list .list .wpforms-template-upgrade-banner .wpforms-template-content,
  #wpforms-setup-templates-list .list .wpforms-template-upgrade-banner .wpforms-template-upgrade-button {
    width: 100%;
  }
  #wpforms-setup-templates-list .list .wpforms-template-upgrade-banner .wpforms-template-content {
    padding: 0;
  }
  #wpforms-setup-templates-list .list .wpforms-template-upgrade-banner .wpforms-template-upgrade-button {
    text-align: center;
    margin-top: 20px;
  }
}

#wpforms-form-templates {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
}

#wpforms-form-templates h1.page-title {
  font-size: 22px;
  font-weight: 400;
  line-height: 26px;
  color: #3c434a;
}

#wpforms-form-templates .wpforms-setup-title {
  line-height: 28px;
}

#wpforms-form-templates .wpforms-setup-desc {
  font-weight: 400;
  font-size: 15px;
  line-height: 22px;
  margin-top: 3px;
}

#wpforms-form-templates .wpforms-form-setup-content {
  align-items: stretch;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  padding: 0;
  margin: 20px;
  background-color: #ffffff;
  min-height: 100%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  border-radius: 7px;
}

#wpforms-form-templates .wpforms-form-setup-content #wpforms-setup-templates-list .wpforms-template {
  box-sizing: border-box;
}

#wpforms-form-templates .wpforms-form-setup-content #wpforms-setup-templates-list .wpforms-template .wpforms-template-buttons {
  box-sizing: border-box;
}

#wpforms-form-templates .wpforms-form-setup-content #wpforms-setup-templates-list .wpforms-template .wpforms-template-buttons .wpforms-btn {
  min-height: 16px;
}

@media (max-width: 1279px) {
  #wpforms-form-templates .wpforms-form-setup-content .wpforms-setup-desc br {
    display: none;
  }
}

@media screen and (max-width: 750px) {
  #wpforms-form-templates .wpforms-form-setup-content .wpforms-setup-desc br {
    display: none;
  }
  #wpforms-form-templates .wpforms-form-setup-content .wpforms-setup-templates {
    flex-direction: column;
  }
  #wpforms-form-templates .wpforms-form-setup-content .wpforms-setup-templates .wpforms-setup-templates-sidebar {
    width: 100%;
  }
  #wpforms-form-templates .wpforms-form-setup-content .wpforms-setup-templates .wpforms-setup-templates-categories {
    width: 100%;
    margin-bottom: 30px;
  }
  #wpforms-form-templates .wpforms-form-setup-content .wpforms-setup-templates #wpforms-setup-templates-list {
    width: 100%;
    padding-top: 0;
  }
  #wpforms-form-templates .wpforms-form-setup-content .wpforms-setup-templates #wpforms-setup-templates-list .list {
    padding: 0;
  }
  #wpforms-form-templates .wpforms-form-setup-content .wpforms-setup-templates #wpforms-setup-templates-list .wpforms-template-upgrade-banner {
    margin-left: 0;
  }
}

.wpforms-admin-empty-state-container .waving-hand-emoji {
  background-image: url("../../images/empty-states/waving-hand-emoji.png");
}
