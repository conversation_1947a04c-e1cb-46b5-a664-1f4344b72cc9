body,
.body {
  height: 100% !important;
  margin: 0;
  Margin: 0;
  padding: 0;
  width: 100% !important;
  min-width: 100%;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important;
}

img {
  outline: none;
  text-decoration: none;
  -ms-interpolation-mode: bicubic;
  width: auto;
  max-width: 100%;
  clear: both;
  display: block;
}

a img {
  border: none;
}

p {
  margin: 0 0 10px 0;
  Margin: 0 0 10px 0;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

td {
  word-wrap: break-word;
  -webkit-hyphens: auto;
  -moz-hyphens: auto;
  hyphens: auto;
  border-collapse: collapse !important;
}

table,
tr,
td {
  padding: 0;
  vertical-align: top;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
  Margin: 0;
  padding: 0;
}

.ReadMsgBody,
.ExternalClass {
  width: 100%;
}

.ExternalClass {
  width: 100%;
}

.ExternalClass,
.ExternalClass p,
.ExternalClass span,
.ExternalClass font,
.ExternalClass td,
.ExternalClass div {
  line-height: 100%;
}

table,
td {
  mso-table-lspace: 0pt;
  mso-table-rspace: 0pt;
}

#outlook a {
  padding: 0;
}

img {
  -ms-interpolation-mode: bicubic;
}

body, table, td, p, a, li, blockquote {
  -ms-text-size-adjust: 100%;
  -webkit-text-size-adjust: 100%;
}

body,
table.body,
h1,
h2,
h3,
h4,
h5,
h6,
p,
td,
th,
a {
  color: #444444;
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-weight: normal;
  padding: 0;
  margin: 0;
  Margin: 0;
  mso-line-height-rule: exactly;
  line-height: 1.4;
  line-height: 140%;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  color: #444444;
  word-wrap: normal;
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-weight: bold;
  margin: 0 0 15px 0;
  Margin: 0 0 15px 0;
  mso-line-height-rule: exactly;
  line-height: 1.3;
  line-height: 130%;
}

h1.normal,
h2.normal,
h3.normal,
h4.normal,
h5.normal,
h6.normal {
  font-weight: normal;
}

h1 {
  font-size: 32px;
}

h2 {
  font-size: 30px;
}

h3 {
  font-size: 28px;
}

h4 {
  font-size: 24px;
}

h5 {
  font-size: 20px;
}

h6 {
  font-size: 18px;
}

body,
table.body,
p,
td,
th {
  font-size: 14px;
  mso-line-height-rule: exactly;
  line-height: 1.4;
  line-height: 140%;
}

p {
  margin: 0 0 15px 0;
  Margin: 0 0 15px 0;
  overflow-wrap: break-word;
  word-wrap: break-word;
  -ms-word-break: break-all;
  word-break: break-all;
  -ms-hyphens: auto;
  -moz-hyphens: auto;
  -webkit-hyphens: auto;
  hyphens: auto;
}

p.large, p.text-large {
  font-size: 16px;
}

p.bold, p.text-bold {
  font-weight: 700;
}

p a {
  Margin: inherit;
  margin: inherit;
}

small {
  font-size: 80%;
}

center {
  width: 100%;
}

a {
  color: #1c75a6;
}

a:visited {
  color: #1c75a6;
}

a:hover, a:active {
  color: #0d374f;
}

h1 a,
h1 a:visited,
h2 a,
h2 a:visited,
h3 a,
h3 a:visited,
h4 a,
h4 a:visited,
h5 a,
h5 a:visited,
h6 a,
h6 a:visited {
  color: #1c75a6;
}

table.text-center,
th.text-center,
td.text-center,
h1.text-center,
h2.text-center,
h3.text-center,
h4.text-center,
h5.text-center,
h6.text-center,
p.text-center,
span.text-center {
  text-align: center;
}

table.text-left,
th.text-left,
td.text-left,
h1.text-left,
h2.text-left,
h3.text-left,
h4.text-left,
h5.text-left,
h6.text-left,
p.text-left,
span.text-left {
  text-align: left;
}

table.text-right,
th.text-right,
td.text-right,
h1.text-right,
h2.text-right,
h3.text-right,
h4.text-right,
h5.text-right,
h6.text-right,
p.text-right,
span.text-right {
  text-align: right;
}

table.primary,
th.primary,
td.primary,
h1.primary,
h2.primary,
h3.primary,
h4.primary,
h5.primary,
h6.primary,
p.primary,
span.primary {
  color: #e27730;
}

table.orange,
th.orange,
td.orange,
h1.orange,
h2.orange,
h3.orange,
h4.orange,
h5.orange,
h6.orange,
p.orange,
span.orange {
  color: #e27730;
}

table.blue,
th.blue,
td.blue,
h1.blue,
h2.blue,
h3.blue,
h4.blue,
h5.blue,
h6.blue,
p.blue,
span.blue {
  color: #509fe2;
}

span.text-center {
  display: block;
  width: 100%;
  text-align: center;
}

ol,
ul {
  margin: 0 0 15px 20px;
  Margin: 0 0 15px 20px;
  padding: 0;
}

ol li,
ul li {
  list-style-type: decimal;
  padding-top: 5px;
}

ol ol,
ol ul,
ul ol,
ul ul {
  margin-bottom: 0 !important;
}

/* Helper class for breaking long URLs. */
.break-all {
  word-break: break-all !important;
}

.break-all > a {
  word-break: break-all !important;
}

table.button {
  width: auto;
}

table.button td {
  mso-line-height-rule: exactly;
  line-height: 1;
  line-height: 100%;
}

table.button td.button-inner {
  padding: 20px 0 20px 0;
}

table.button table td {
  text-align: center;
  color: #ffffff;
  background: #e27730;
  border: 1px solid #c45e1b;
  border-bottom: 3px solid #c45e1b;
  mso-line-height-rule: exactly;
  line-height: 1;
  line-height: 100%;
}

table.button table td a {
  font-family: Helvetica, Arial, sans-serif;
  font-size: 16px;
  font-weight: bold;
  color: #ffffff;
  text-decoration: none;
  text-align: center;
  display: inline-block;
  padding: 10px 16px 8px 16px;
  border: 0 solid #c45e1b;
  mso-line-height-rule: exactly;
  line-height: 1;
  line-height: 100%;
}

table.button:hover table tr td a,
table.button:active table tr td a,
table.button table tr td a:visited,
table.button.tiny:hover table tr td a,
table.button.tiny:active table tr td a,
table.button.tiny table tr td a:visited,
table.button.small:hover table tr td a,
table.button.small:active table tr td a,
table.button.small table tr td a:visited,
table.button.large:hover table tr td a,
table.button.large:active table tr td a,
table.button.large table tr td a:visited {
  color: #ffffff;
  text-decoration: none !important;
}

table.button.small table td,
table.button.small table a {
  padding: 5px 10px 5px 10px;
  font-size: 12px;
}

table.button.large table a {
  padding: 14px 20px 12px 20px;
  font-size: 20px;
}

table.button.expand, table.button.full, table.button.expanded {
  width: 100% !important;
}

table.button.expand table, table.button.full table, table.button.expanded table {
  width: 100% !important;
}

table.button.expand table a, table.button.full table a, table.button.expanded table a {
  text-align: center;
  width: 100%;
  padding-left: 0;
  padding-right: 0;
}

table.button:hover table td,
table.button:active table td {
  background: #c45e1b;
  color: #fefefe;
}

table.button:hover table a,
table.button:active table a {
  border: 0 solid #c45e1b;
}

table.button.blue table td {
  color: #ffffff;
  background: #509fe2;
  border: 1px solid #2487db;
  border-bottom: 3px solid #2487db;
}

table.button.blue table a {
  color: #ffffff;
  border: 0 solid #2487db;
}

table.button.blue:hover table td, table.button.blue:active table td {
  color: #ffffff;
  background-color: #2487db;
}

table.button.green table td {
  color: #ffffff;
  background: #74ae5e;
  border: 1px solid #5c9049;
  border-bottom: 3px solid #5c9049;
}

table.button.green table a {
  color: #ffffff;
  border: 0 solid #5c9049;
}

table.button.green:hover table td, table.button.green:active table td {
  color: #ffffff;
  background-color: #5c9049;
}

body,
.body {
  background-color: #f1f1f1;
  text-align: center;
}

.body-inner {
  text-align: center;
}

.container {
  width: 600px;
  margin: 0 auto 0 auto;
  Margin: 0 auto 0 auto;
  text-align: inherit;
}

.header {
  text-align: center;
  padding: 30px 30px 22px 30px;
}

.header img {
  display: inline-block !important;
}

.content {
  background-color: #ffffff;
  padding: 60px 75px 45px 75px;
  border-top: 3px solid #e27730;
  border-right: 1px solid #dddddd;
  border-bottom: 1px solid #dddddd;
  border-left: 1px solid #dddddd;
}

.aside {
  background-color: #f8f8f8;
  padding: 50px 75px 35px 75px;
  border-top: 1px solid #dddddd;
  border-right: 1px solid #dddddd;
  border-bottom: 1px solid #dddddd;
  border-left: 1px solid #dddddd;
}

.footer {
  padding: 30px;
  color: #72777c;
  font-size: 12px;
  text-align: center;
}

.footer a {
  color: #72777c;
  text-decoration: underline;
}

.footer a:hover {
  color: #444444;
}

@media only screen and (max-width: 599px) {
  .wpforms-layout-table-display-rows .wpforms-layout-table-row .field-name {
    display: block !important;
  }
  .wpforms-layout-table .wpforms-layout-table-row {
    display: block;
    width: 100%;
  }
  .wpforms-layout-table .wpforms-layout-table-row table, .wpforms-layout-table .wpforms-layout-table-row thead, .wpforms-layout-table .wpforms-layout-table-row tbody, .wpforms-layout-table .wpforms-layout-table-row tr, .wpforms-layout-table .wpforms-layout-table-row td, .wpforms-layout-table .wpforms-layout-table-row th {
    display: block;
    width: 100% !important;
    padding-bottom: 0 !important;
  }
  .wpforms-layout-table .wpforms-layout-table-row tr {
    padding-top: 0 !important;
  }
  .wpforms-layout-table .wpforms-layout-table-row td.field-value {
    padding-bottom: 25px !important;
  }
  .wpforms-layout-table .wpforms-layout-table-row td.field-name {
    padding-bottom: 10px !important;
  }
  .wpforms-layout-table > td {
    padding-bottom: 0 !important;
  }
}

a {
  text-decoration: none;
}

@media only screen and (max-width: 599px) {
  table.body .container {
    width: 95% !important;
  }
  .header {
    padding: 15px 15px 12px 15px !important;
  }
  .header img {
    width: 200px !important;
    height: auto !important;
  }
  .content,
  .aside {
    padding: 30px 40px 20px 40px !important;
  }
  .upsell-pro table.features td {
    width: 100% !important;
    display: block !important;
  }
  table.receipt-details td.receipt-details-inner {
    padding: 30px 0px 20px 0px !important;
  }
}

.wpforms-layout-table > td {
  padding-bottom: 25px;
}

.wpforms-layout-table .wpforms-layout-table-row {
  width: 100%;
}

.wpforms-layout-table .wpforms-layout-table-row .field-value {
  padding-bottom: 25px;
}

.wpforms-layout-table .wpforms-layout-table-row > tr > td {
  padding-right: 20px;
}

.wpforms-layout-table .wpforms-layout-table-row > tr > td:last-child {
  padding-right: 0;
}

.wpforms-layout-table-display-rows .wpforms-layout-table-row:not(.wpforms-first-row) td.field-name {
  display: none;
}

.wpforms-layout-table-display-rows .wpforms-layout-table-row .field-value {
  padding-bottom: 15px;
}

.wpforms-layout-table-display-rows .wpforms-layout-table-row:last-child .field-value {
  padding-bottom: 0;
}

.wpforms-layout-table-display-blocks .wpforms-layout-table-row:last-child .field-value, .wpforms-layout-table-display-columns .wpforms-layout-table-row:last-child .field-value {
  padding-bottom: 0;
}

.wpforms-layout-table .wpforms-layout-table-cell td {
  border: 0 !important;
}

.wpforms-layout-table .wpforms-layout-table-cell.wpforms-width-25 .field-payment-total .wpforms-payment-total, .wpforms-layout-table .wpforms-layout-table-cell.wpforms-width-33 .field-payment-total .wpforms-payment-total {
  display: block !important;
}

.wpforms-layout-table .wpforms-layout-table-cell.wpforms-width-25 .field-payment-total .wpforms-order-summary-preview, .wpforms-layout-table .wpforms-layout-table-cell.wpforms-width-33 .field-payment-total .wpforms-order-summary-preview {
  display: none !important;
}

.field-payment-total .wpforms-payment-total {
  display: none !important;
}
