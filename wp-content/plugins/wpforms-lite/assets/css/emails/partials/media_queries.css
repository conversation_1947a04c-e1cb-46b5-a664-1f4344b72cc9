@media only screen and (max-width: 599px) {
  .wpforms-layout-table-display-rows .wpforms-layout-table-row .field-name {
    display: block !important;
  }
  .wpforms-layout-table .wpforms-layout-table-row {
    display: block;
    width: 100%;
  }
  .wpforms-layout-table .wpforms-layout-table-row table, .wpforms-layout-table .wpforms-layout-table-row thead, .wpforms-layout-table .wpforms-layout-table-row tbody, .wpforms-layout-table .wpforms-layout-table-row tr, .wpforms-layout-table .wpforms-layout-table-row td, .wpforms-layout-table .wpforms-layout-table-row th {
    display: block;
    width: 100% !important;
    padding-bottom: 0 !important;
  }
  .wpforms-layout-table .wpforms-layout-table-row tr {
    padding-top: 0 !important;
  }
  .wpforms-layout-table .wpforms-layout-table-row td.field-value {
    padding-bottom: 25px !important;
  }
  .wpforms-layout-table .wpforms-layout-table-row td.field-name {
    padding-bottom: 10px !important;
  }
  .wpforms-layout-table > td {
    padding-bottom: 0 !important;
  }
}

a {
  text-decoration: none;
}

@media only screen and (max-width: 599px) {
  table.body .container {
    width: 95% !important;
  }
  .header {
    padding: 15px 15px 12px 15px !important;
  }
  .header img {
    width: 200px !important;
    height: auto !important;
  }
  .content,
  .aside {
    padding: 30px 40px 20px 40px !important;
  }
  .upsell-pro table.features td {
    width: 100% !important;
    display: block !important;
  }
  table.receipt-details td.receipt-details-inner {
    padding: 30px 0px 20px 0px !important;
  }
}
