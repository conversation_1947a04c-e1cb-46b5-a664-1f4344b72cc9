@media only screen and (max-width: 599px) {
  .wpforms-layout-table-display-rows .wpforms-layout-table-row .field-name {
    display: block !important;
  }
  .wpforms-layout-table .wpforms-layout-table-row {
    display: block;
    width: 100%;
  }
  .wpforms-layout-table .wpforms-layout-table-row table, .wpforms-layout-table .wpforms-layout-table-row thead, .wpforms-layout-table .wpforms-layout-table-row tbody, .wpforms-layout-table .wpforms-layout-table-row tr, .wpforms-layout-table .wpforms-layout-table-row td, .wpforms-layout-table .wpforms-layout-table-row th {
    display: block;
    width: 100% !important;
    padding-bottom: 0 !important;
  }
  .wpforms-layout-table .wpforms-layout-table-row tr {
    padding-top: 0 !important;
  }
  .wpforms-layout-table .wpforms-layout-table-row td.field-value {
    padding-bottom: 25px !important;
  }
  .wpforms-layout-table .wpforms-layout-table-row td.field-name {
    padding-bottom: 10px !important;
  }
  .wpforms-layout-table > td {
    padding-bottom: 0 !important;
  }
}

@media only screen and (max-width: 700px) {
  .header .header-image {
    max-width: 221px;
  }
  .body-inner {
    padding-bottom: 0 !important;
  }
  .summary-content {
    border-radius: 0 !important;
    padding: 30px !important;
  }
  .summary-header,
  .email-summaries-overview-wrapper {
    margin-bottom: 30px !important;
    Margin-bottom: 30px !important;
  }
  .email-summaries-overview h5 {
    font-size: 18px !important;
    line-height: 26px !important;
  }
  .email-summaries-overview p {
    font-size: 14px !important;
    line-height: 20px !important;
  }
  .email-summaries .entry-count,
  .email-summaries .form-name {
    font-size: 14px !important;
  }
  .summary-notice h4 {
    font-size: 20px !important;
    line-height: 30px !important;
  }
  .summary-notification-block,
  .summary-info-block {
    border-radius: 0 !important;
    padding: 30px 30px 6px 30px !important;
  }
}

@media only screen and (max-width: 320px) {
  .email-summaries th {
    padding: 15px !important;
  }
  .email-summaries .form-name,
  .email-summaries .entry-count,
  .email-summaries .summary-trend {
    padding: 12px !important;
  }
}

@media (prefers-color-scheme: light) {
  tr.dark-mode {
    display: none !important;
  }
}

@media (prefers-color-scheme: dark) {
  tr.dark-mode {
    display: table-row !important;
  }
  tr.light-mode {
    display: none !important;
  }
  body,
  table.body,
  .email-summaries th {
    background-color: #2d2f31 !important;
  }
  .email-summaries td,
  .summary-header,
  .summary-content {
    background-color: #1f1f1f !important;
  }
  body, table.body, h4, h6, p, td, th {
    color: #dddddd !important;
  }
  .email-summaries th, .email-summaries td {
    border: 1px solid #424446 !important;
  }
  .email-summaries-overview {
    border: 1px solid #424446 !important;
    background: #2d2f31 !important;
  }
  .email-summaries-overview h5 {
    color: #dddddd !important;
  }
  .email-summaries-overview p {
    color: #999999 !important;
  }
  .footer,
  .entry-count {
    color: #999999 !important;
  }
  .footer a,
  .entry-count a {
    color: #999999 !important;
  }
  .footer a:hover,
  .entry-count a:hover {
    color: #999999 !important;
  }
  .summary-notice {
    color: #ffffff !important;
  }
  .summary-notice h4, .summary-notice p {
    color: #ffffff !important;
  }
  .summary-info-block {
    background-color: #383230 !important;
  }
  .summary-notification-block {
    background-color: #2e353b !important;
  }
  .summary-notification-block .summary-notice-content a {
    color: #3389bd !important;
  }
  .button-blue a,
  .button-orange a {
    color: #ffffff !important;
  }
  .button-blue-outline {
    border: 1px solid #3389bd !important;
  }
  .button-blue-outline a {
    color: #3389bd !important;
  }
}

[data-ogsc] tr.dark-mode {
  display: table-row !important;
}

[data-ogsc] tr.light-mode {
  display: none !important;
}

[data-ogsc] body,
[data-ogsc] table.body,
[data-ogsc] .email-summaries th {
  background-color: #2d2f31 !important;
}

[data-ogsc] .email-summaries td,
[data-ogsc] .summary-header,
[data-ogsc] .summary-content {
  background-color: #1f1f1f !important;
}

[data-ogsc] body, [data-ogsc] table.body, [data-ogsc] h4, [data-ogsc] h6, [data-ogsc] p, [data-ogsc] td, [data-ogsc] th {
  color: #dddddd !important;
}

[data-ogsc] .email-summaries th, [data-ogsc] .email-summaries td {
  border: 1px solid #424446 !important;
}

[data-ogsc] .email-summaries-overview {
  border: 1px solid #424446 !important;
  background: #2d2f31 !important;
}

[data-ogsc] .email-summaries-overview h5 {
  color: #dddddd !important;
}

[data-ogsc] .email-summaries-overview p {
  color: #999999 !important;
}

[data-ogsc] .footer,
[data-ogsc] .entry-count {
  color: #999999 !important;
}

[data-ogsc] .footer a,
[data-ogsc] .entry-count a {
  color: #999999 !important;
}

[data-ogsc] .footer a:hover,
[data-ogsc] .entry-count a:hover {
  color: #999999 !important;
}

[data-ogsc] .summary-notice {
  color: #ffffff !important;
}

[data-ogsc] .summary-notice h4, [data-ogsc] .summary-notice p {
  color: #ffffff !important;
}

[data-ogsc] .summary-info-block {
  background-color: #383230 !important;
}

[data-ogsc] .summary-notification-block {
  background-color: #2e353b !important;
}

[data-ogsc] .summary-notification-block .summary-notice-content a {
  color: #3389bd !important;
}

[data-ogsc] .button-blue a,
[data-ogsc] .button-orange a {
  color: #ffffff !important;
}

[data-ogsc] .button-blue-outline {
  border: 1px solid #3389bd !important;
}

[data-ogsc] .button-blue-outline a {
  color: #3389bd !important;
}
