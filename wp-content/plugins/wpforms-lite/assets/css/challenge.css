#wpforms-welcome .challenge {
  border-radius: 2px;
  box-shadow: 0 0 50px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
  background: #444 url("../images/challenge/getting-started.png") center right no-repeat;
  background-size: contain;
}

#wpforms-welcome .challenge .block {
  max-width: 480px;
}

#wpforms-welcome .challenge h1 {
  color: #ffffff;
  text-align: left;
  margin: 20px 0 22px;
}

#wpforms-welcome .challenge h6 {
  font-size: 17px;
  font-weight: 400;
  text-align: left;
  color: #cccccc;
  margin: 0 0 34px;
}

.wpforms-challenge {
  display: none;
  position: fixed;
  inset-inline-end: 20px;
  bottom: 0;
  width: 230px;
  z-index: 100110;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
  box-shadow: 0 0 30px rgba(0, 0, 0, 0.15);
}

.wpforms-challenge p {
  font-size: 14px;
  line-height: 20px;
  margin: 0 0 15px 0;
  color: #222222;
}

.wpforms-challenge b {
  font-weight: 600;
}

.wpforms-challenge.frozen {
  pointer-events: none;
}

.wpforms-challenge.wpforms-challenge-start {
  display: initial;
}

.wpforms-challenge .wpforms-challenge-list-block {
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
  padding: 26px 20px 20px 20px;
  background-color: white;
  overflow: hidden;
  position: relative;
  transition: all 0.3s;
  transition-timing-function: ease;
}

.wpforms-challenge .wpforms-challenge-list-block .list-block-button {
  position: absolute;
  color: #b6b6b6;
  top: 10px;
  cursor: pointer;
  width: 16px;
  height: 16px;
  background-size: 16px 16px;
  background-position: center center;
  background-repeat: no-repeat;
  opacity: 0.25;
  transition: 0.3s;
  z-index: 999;
}

.wpforms-challenge .wpforms-challenge-list-block .list-block-button.toggle-list {
  display: none;
  inset-inline-end: 30px;
  background-image: url(../images/challenge/chevron-circle-down-regular.svg);
}

.wpforms-challenge .wpforms-challenge-list-block .list-block-button.challenge-skip, .wpforms-challenge .wpforms-challenge-list-block .list-block-button.challenge-cancel {
  inset-inline-end: 10px;
  background-image: url(../images/challenge/times-circle-regular.svg);
  color: #b6b6b6;
}

.wpforms-challenge .wpforms-challenge-list-block .list-block-button:hover {
  opacity: 0.5;
}

.wpforms-challenge .wpforms-challenge-list-block.closed {
  padding: 10px;
}

.wpforms-challenge .wpforms-challenge-list-block.closed .list-block-button.toggle-list {
  transform: rotate(180deg);
}

.wpforms-challenge .wpforms-challenge-list-block.closed .wpforms-challenge-list {
  display: block;
}

.wpforms-challenge .wpforms-challenge-list-block.closed .wpforms-challenge-list li {
  opacity: 0;
  height: 0;
  margin: 0;
}

.wpforms-challenge .wpforms-challenge-list-block.closed .wpforms-challenge-list .wpforms-challenge-item-completed {
  opacity: 0 !important;
  height: 0 !important;
}

.wpforms-challenge .wpforms-challenge-list-block.closed .wpforms-challenge-list .wpforms-challenge-item-current {
  opacity: 1;
  height: 16px;
}

.wpforms-challenge .wpforms-challenge-list-block.closed .wpforms-challenge-list .wpforms-challenge-item-current span:before {
  opacity: 0;
  margin-left: -20px;
  transition: opacity 0s;
}

.wpforms-challenge .wpforms-challenge-list-block.transition-back .wpforms-challenge-list li {
  transition: opacity 0.1s 0.2s, height 0.3s, margin 0.3s;
}

.wpforms-challenge .wpforms-challenge-list-block.transition-back .wpforms-challenge-list li.wpforms-challenge-completed {
  transition: opacity 0.1s, height 0.3s, margin 0.3s;
}

.wpforms-challenge .wpforms-challenge-list-block.transition-back .wpforms-challenge-list .wpforms-challenge-item-current span:before {
  transition: opacity 0.1s 0.2s;
}

.wpforms-challenge .wpforms-challenge-list {
  list-style: none;
  margin: 0;
  font-size: 12px;
  line-height: 16px;
  transition: all 0.3s;
  display: flex;
  flex-direction: column;
  gap: 10px;
  justify-content: flex-start;
}

.wpforms-challenge .wpforms-challenge-list li {
  transition: opacity 0.1s, height 0.3s, margin 0.3s;
  display: flex;
}

.wpforms-challenge .wpforms-challenge-list li span:before {
  display: inline-block;
  font-family: FontAwesome;
  content: "\f1db";
  font-size: 18px;
  color: #b6b6b6;
  margin-inline-end: 8px;
  line-height: 16px;
  vertical-align: bottom;
  border-radius: 50%;
}

.wpforms-challenge .wpforms-challenge-list li.wpforms-challenge-item-current {
  font-weight: bold;
  cursor: pointer;
}

.wpforms-challenge .wpforms-challenge-list li.wpforms-challenge-item-current span:before {
  color: #df7739;
  content: '\f111';
  font-size: 17.5px;
  line-height: 1;
  text-indent: .5px;
}

.wpforms-challenge .wpforms-challenge-list li.wpforms-challenge-item-completed {
  font-weight: initial;
  text-decoration: line-through;
  color: #777777;
}

.wpforms-challenge .wpforms-challenge-list li.wpforms-challenge-item-completed span:before {
  color: #74ae5e;
  font-size: 18px;
  background-color: white;
  content: "\f058";
}

.wpforms-challenge .wpforms-challenge-list li.wpforms-challenge-completed {
  opacity: 0;
  height: 0;
  margin-bottom: 0;
  transition: opacity 0.1s 0.3s, height 0.3s, margin 0.3s;
}

.wpforms-challenge .wpforms-challenge-list li.wpforms-challenge-completed span:before {
  opacity: 0;
  margin-left: -20px;
}

.wpforms-challenge .wpforms-challenge-list li .dashicons-yes {
  display: none;
  vertical-align: middle;
}

.wpforms-challenge .wpforms-challenge-bar {
  background-color: #dddddd;
}

.wpforms-challenge .wpforms-challenge-bar div {
  width: 0;
  height: 8px;
  border-radius: 0;
  background-image: url(../images/challenge/bar-bg.png);
  background-size: auto 8px;
  background-repeat: repeat-x;
  animation: wpforms-challenge-bar-shift 1.5s linear infinite;
}

@keyframes wpforms-challenge-bar-shift {
  0% {
    background-position-x: 27px;
  }
  100% {
    background-position-x: 0;
  }
}

.wpforms-challenge.paused .wpforms-challenge-bar div, .wpforms-challenge.wpforms-challenge-completed .wpforms-challenge-bar div {
  animation-play-state: paused;
}

.wpforms-challenge.wpforms-challenge-completed .wpforms-challenge-block-timer p {
  color: #6ab255;
  opacity: 1;
}

.wpforms-challenge.wpforms-challenge-completed .wpforms-challenge-block-under-timer .wpforms-challenge-pause,
.wpforms-challenge.wpforms-challenge-completed .wpforms-challenge-block-under-timer .wpforms-challenge-resume {
  display: none !important;
}

.wpforms-challenge.wpforms-challenge-completed .wpforms-challenge-block-under-timer .wpforms-challenge-end {
  display: inline-block !important;
}

.wpforms-challenge .wpforms-challenge-cancel,
.wpforms-challenge .wpforms-challenge-skip {
  float: right;
  color: #909090;
  font-size: 12px;
  font-weight: 100;
}

.wpforms-challenge .wpforms-btn {
  cursor: pointer;
  color: #ffffff;
  border-radius: 2px;
}

.wpforms-challenge .wpforms-btn-md {
  min-height: initial;
  font-size: 11px;
  font-weight: 600;
  line-height: 15px;
  padding: 5px 10px;
  border: none;
}

.wpforms-challenge .wpforms-btn-orange {
  background-color: #df7739;
}

.wpforms-challenge .wpforms-btn-orange:hover {
  background-color: #d06e2d;
}

.wpforms-challenge .wpforms-btn-grey {
  background-color: #555555;
}

.wpforms-challenge .wpforms-btn-grey:hover {
  background-color: #777777;
}

.wpforms-challenge .wpforms-challenge-block-timer {
  padding: 10px 0;
  background-color: #222222;
  box-sizing: border-box;
  display: flex;
  justify-content: center;
  gap: 10px;
  align-items: center;
  line-height: 20px;
}

.wpforms-challenge .wpforms-challenge-block-timer img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}

.wpforms-challenge .wpforms-challenge-block-timer h3 {
  font-size: 15px;
  font-weight: 600;
  color: #ffffff;
  margin: 0;
}

.wpforms-challenge .wpforms-challenge-block-timer p {
  font-size: 14px;
  font-weight: 300;
  color: #ffffff;
  opacity: 0.6;
  margin: 0;
}

.wpforms-challenge .wpforms-challenge-block-under-timer {
  background-color: #2d2d2d;
  text-align: center;
  padding: 10px;
  min-height: 24.4px;
}

@keyframes wpforms-challenge-dot-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(223, 119, 57, 0.6);
  }
  100% {
    box-shadow: 0 0 0 10px rgba(223, 119, 57, 0);
  }
}

span.wpforms-challenge-dot {
  display: inline-block;
  width: 12px;
  height: 12px !important;
  padding: 0 !important;
  border: 0 !important;
  border-radius: 50%;
  background-color: #df7739;
  animation: wpforms-challenge-dot-pulse 1.5s infinite !important;
}

span.wpforms-challenge-dot.wpforms-challenge-dot-step1 {
  margin: 0;
  margin-inline-start: 20px;
  margin-inline-end: 80px;
  position: absolute;
  inset-inline-end: 1px;
}

span.wpforms-challenge-dot.wpforms-challenge-dot-step2 {
  margin-left: 10px;
  vertical-align: -2px;
}

span.wpforms-challenge-dot.wpforms-challenge-dot-step3 {
  float: right;
  margin-top: 5px;
}

span.wpforms-challenge-dot.wpforms-challenge-dot-step3.wpforms-challenge-dot-completed {
  margin-top: 2px;
  background: transparent;
}

span.wpforms-challenge-dot.wpforms-challenge-dot-step4 {
  margin-left: 24px;
  vertical-align: middle;
}

span.wpforms-challenge-dot.wpforms-challenge-dot-step5 {
  margin: 3px 10px;
}

span.wpforms-challenge-dot.wpforms-challenge-dot-next {
  background-color: #3178a0;
  box-shadow: 0 0 0 6px #eeeeee;
  animation: none !important;
}

span.wpforms-challenge-dot.wpforms-challenge-dot-completed {
  background-color: #ffffff;
  width: 16px !important;
  height: 16px !important;
  box-shadow: none;
  position: relative;
  animation: none !important;
}

span.wpforms-challenge-dot.wpforms-challenge-dot-completed.wpforms-challenge-dot-step1 {
  position: absolute;
}

span.wpforms-challenge-dot.wpforms-challenge-dot-completed:before {
  color: #74ae5e;
  font-size: 18px;
  line-height: 16px;
  width: 14px;
  height: 14px;
  background-color: #ffffff;
  content: "\f058";
  font-family: FontAwesome;
  position: absolute;
  border-radius: 50%;
  left: 0;
}

.wp-editor-tools span.wpforms-challenge-dot.wpforms-challenge-dot-step5 {
  margin-top: 9px;
}

.wpforms-challenge-tooltips {
  display: none;
}

.wpforms-challenge-popup-container {
  display: none;
  background-color: rgba(112, 128, 144, 0.6);
  height: 100vh;
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100100;
  overflow: hidden;
  overflow-y: scroll;
  transition: all .25s ease-out;
}

.wpforms-challenge-popup {
  display: none;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  max-width: 600px;
  border-radius: 8px;
  overflow: hidden;
  background-color: white;
  box-shadow: 0 0 40px 0 rgba(0, 0, 0, 0.2);
  z-index: 9999;
}

.wpforms-challenge-popup-congrats {
  background: #ffffff url("../images/challenge/confetti.svg") repeat center;
}

.wpforms-challenge-popup-header {
  width: 100%;
  height: 212px;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

.wpforms-challenge-popup-header-contact {
  background: #eeeeee url("../images/challenge/popup-contact.png") no-repeat center;
  background-size: auto 57%;
}

.wpforms-challenge-popup-footer {
  padding: 40px;
  background: #f5f5f5;
}

.wpforms-challenge-popup-footer h3 {
  font-size: 20px;
  line-height: 24px;
  color: #2c3338;
  margin: 0 0 20px;
}

.wpforms-challenge-popup-footer h3 img {
  vertical-align: -4px;
  margin-left: 2px;
}

.wpforms-challenge-popup-footer p {
  font-weight: normal;
  font-size: 16px;
  line-height: 24px;
}

.wpforms-challenge-popup-footer a.wpforms-challenge-popup-btn {
  margin-left: 0 !important;
  background: #2271b1;
}

.wpforms-challenge-popup-footer a.wpforms-challenge-popup-btn:hover {
  background: #215d8f;
}

.wpforms-challenge-popup-content {
  padding: 40px;
  -webkit-font-smoothing: antialiased;
}

.wpforms-challenge-popup-content h3 {
  color: #df7739;
  margin: 0 0 10px 0;
  font-size: 24px;
  font-weight: 600;
  line-height: 30px;
}

.wpforms-challenge-popup-content h3 img {
  vertical-align: -6px;
  margin-left: 6px;
  width: 32px;
  height: 32px;
}

.wpforms-challenge-popup-content p {
  font-size: 16px;
  margin: 0 0 20px;
  color: #444444;
  line-height: 28px;
}

.wpforms-challenge-popup-content b {
  font-weight: 600;
}

.wpforms-challenge-popup-content .wpforms-challenge-contact-message {
  box-shadow: none;
  resize: none;
  margin-bottom: 21px;
  width: 100%;
  min-height: 175px;
  padding: 10px;
}

.wpforms-challenge-popup-content label {
  font-size: 13.8px;
  display: block;
  margin-bottom: 23px;
}

.wpforms-challenge-popup-content input[type=checkbox] {
  margin-right: 8px;
}

.wpforms-challenge-popup-content .rating-stars {
  color: #fdb72c;
  font-size: 18px;
  font-weight: bold;
}

.wpforms-challenge-popup-content select, .wpforms-challenge-popup-content input[type=text] {
  border-radius: 4px;
  border: 1px solid #d6d6d6;
  padding: 8px 12px;
  width: 320px;
  font-size: 14px;
  line-height: 21px;
  color: #777777;
  vertical-align: middle;
}

.wpforms-challenge-popup-content select:focus, .wpforms-challenge-popup-content input[type=text]:focus {
  color: #777777;
}

.wpforms-challenge-popup .wpforms-challenge-popup-close {
  font-size: 27px;
  color: #a7aaad;
  position: absolute;
  right: 0;
  margin: 11px 10px 10px 10px;
  background-color: #ffffff;
  border-radius: 50%;
  cursor: pointer;
}

.wpforms-challenge-popup .wpforms-challenge-popup-close:hover {
  color: #777777;
}

.wpforms-challenge-popup .wpforms-challenge-popup-btn {
  display: inline-block;
  border-radius: 4px;
  cursor: pointer;
  text-decoration: none;
  text-align: center;
  vertical-align: middle;
  white-space: nowrap;
  box-shadow: none;
  font-size: 16px;
  font-weight: 600;
  line-height: 19px;
  padding: 10px 20px;
  border: none;
  background-color: #df7739;
  color: #ffffff;
}

.wpforms-challenge-popup .wpforms-challenge-popup-btn:hover {
  background-color: #b85a1b;
}

.wpforms-challenge-popup .wpforms-challenge-popup-btn .dashicons-external {
  margin-left: 6px;
}

.wpforms-challenge-popup .wpforms-challenge-popup-btn:last-child {
  margin-left: 20px;
}

.wpforms-challenge-popup .wpforms-challenge-popup-btn.wpforms-challenge-popup-contact-btn {
  margin-left: 0;
}

.wpforms-challenge-popup .wpforms-challenge-popup-btn.wpforms-challenge-popup-rate-btn {
  margin-left: 0;
}

.wpforms-challenge-popup .wpforms-challenge-popup-btn:disabled {
  cursor: default;
  opacity: 0.5;
}

.wpforms-challenge-popup .wpforms-challenge-popup-btn:disabled:hover {
  background-color: #df7739;
}

.wpforms-challenge-popup .wpforms-challenge-popup-flex {
  display: flex;
  flex-wrap: nowrap;
  justify-content: space-between;
  align-items: stretch;
}

.wpforms-challenge-popup .wpforms-challenge-popup-flex * {
  margin: 0 !important;
}

.wpforms-challenge-popup.wpforms-challenge-popup-plain {
  border-radius: 6px;
  max-width: 550px;
  text-align: center;
}

.wpforms-challenge-popup.wpforms-challenge-popup-plain .wpforms-challenge-popup-content {
  padding: 60px 50px;
}

.wpforms-challenge-popup.wpforms-challenge-popup-plain h3 {
  text-align: center;
  font-weight: bold;
  font-size: 24px;
  letter-spacing: 0;
  color: #444444;
}

.wpforms-challenge-popup.wpforms-challenge-popup-plain p {
  text-align: center;
  font-size: 18px;
  letter-spacing: 0;
  color: #777777;
}

.wpforms-challenge-popup .wpforms-admin-popup-content {
  padding: 60px 50px;
}

@media all and (max-height: 900px) {
  #wpforms-challenge-contact-popup {
    margin: 50px 0 20px;
  }
}

.wpforms-challenge-tooltip.tooltipster-sidetip {
  z-index: 100099 !important;
}

.wpforms-challenge-tooltip.tooltipster-sidetip.tooltipster-top .tooltipster-box {
  margin-bottom: 18px;
}

.wpforms-challenge-tooltip.tooltipster-sidetip.tooltipster-top .tooltipster-arrow-background {
  top: 0;
}

.wpforms-challenge-tooltip.tooltipster-sidetip.tooltipster-right .tooltipster-box {
  margin-left: 18px;
}

.wpforms-challenge-tooltip.tooltipster-sidetip.tooltipster-right .tooltipster-arrow {
  left: 8px;
}

.wpforms-challenge-tooltip.tooltipster-sidetip.tooltipster-base .tooltipster-box {
  max-width: 260px;
  background: white;
  border: none;
  border-radius: 4px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.25);
}

.wpforms-challenge-tooltip.tooltipster-sidetip.tooltipster-base .tooltipster-box .tooltipster-content {
  color: #444444;
  padding: 16px 20px 18px;
  text-align: center;
}

.wpforms-challenge-tooltip.tooltipster-sidetip.tooltipster-base .tooltipster-box .tooltipster-content div *:first-child {
  margin-top: 0 !important;
}

.wpforms-challenge-tooltip.tooltipster-sidetip.tooltipster-base .tooltipster-box .tooltipster-content h3 {
  font-size: 16px;
  letter-spacing: 0;
  line-height: 18px;
  margin: 0;
  color: #23282c;
}

.wpforms-challenge-tooltip.tooltipster-sidetip.tooltipster-base .tooltipster-box .tooltipster-content p {
  font-size: 14px;
  letter-spacing: 0;
  line-height: 18px;
  margin: 10px 0 0;
  color: #444444;
}

.wpforms-challenge-tooltip.tooltipster-sidetip.tooltipster-base .tooltipster-box .tooltipster-content a {
  color: #1d7bac;
}

.wpforms-challenge-tooltip.tooltipster-sidetip.tooltipster-base .tooltipster-box .wpforms-challenge-done-btn {
  border-radius: 3px;
  cursor: pointer;
  text-decoration: none;
  text-align: center;
  vertical-align: middle;
  white-space: nowrap;
  box-shadow: none;
  font-size: 14px;
  font-weight: 600;
  letter-spacing: 0;
  padding: 7px 18px;
  border: none;
  background-color: #df7739;
  color: #ffffff;
  display: block;
  margin: 15px auto 0;
}

.wpforms-challenge-tooltip.tooltipster-sidetip.tooltipster-base .tooltipster-box .wpforms-challenge-done-btn:hover {
  background-color: #b85a1b;
}

.wpforms-challenge-tooltip.tooltipster-sidetip.tooltipster-base .tooltipster-box .wpforms-challenge-done-btn:disabled {
  cursor: default;
  opacity: 0.5;
}

.wpforms-challenge-tooltip.tooltipster-sidetip.tooltipster-base .tooltipster-box .wpforms-challenge-done-btn:disabled:hover {
  background-color: #df7739;
}

.wpforms-challenge-tooltip.tooltipster-sidetip .tooltipster-arrow-border {
  border: none;
}

.wpforms-challenge-tooltip.tooltipster-sidetip.tooltipster-base.tooltipster-top .tooltipster-arrow-background {
  border-top-color: white;
}

.wpforms-challenge-tooltip.tooltipster-sidetip.tooltipster-base.tooltipster-right .tooltipster-arrow-background {
  border-right-color: white;
}

.wpforms-challenge-tooltip.tooltipster-sidetip.tooltipster-base.tooltipster-bottom .tooltipster-arrow-background {
  border-bottom-color: white;
}

.wpforms-challenge-tooltip.tooltipster-sidetip.tooltipster-base.tooltipster-left .tooltipster-arrow-background {
  border-left-color: white;
}

.wpforms-challenge-tooltip.tooltipster-sidetip.wpforms-hidden {
  display: none;
}

.wpforms-builder-help-open .wpforms-challenge-frozen-tooltip.tooltipster-fade.tooltipster-show,
.wpforms-builder-help-open .wpforms-challenge-tooltip.tooltipster-fade.tooltipster-show {
  opacity: 0;
}

.wpforms-ai-form-generator-active .wpforms-challenge-tooltip.tooltipster-fade.tooltipster-show {
  opacity: 0;
}

.wpforms-challenge-frozen-tooltip.tooltipster-fade.tooltipster-show {
  opacity: 1;
  transition: opacity .5s ease-in-out;
  pointer-events: none;
}

@media all and (max-width: 1023px) {
  .wpforms-challenge-tooltip.tooltipster-sidetip, .wpforms-challenge {
    display: none !important;
  }
}

.block-editor-page .edit-post-layout .components-notice-list > div {
  padding-left: 50px;
}

.block-editor-page .wpforms-challenge-dot-step5 {
  position: absolute;
  top: 75px;
  left: 20px;
  z-index: 9999;
}

.block-editor-page .wpforms-challenge-tooltip.wpforms-challenge-tooltip-step5 {
  width: 260px !important;
  z-index: 1000002 !important;
  margin-top: 5px;
}

.block-editor-page .wpforms-challenge-tooltip.wpforms-challenge-tooltip-step5.wpforms-challenge-tooltip-step5-hide {
  z-index: -1 !important;
}

.block-editor-page .wpforms-challenge-tooltip.wpforms-challenge-tooltip-step5 .tooltipster-box {
  margin-top: 10px;
}

.block-editor-page .wpforms-challenge-tooltip.wpforms-challenge-tooltip-step5 .wpforms-challenge-tooltips-red-arrow {
  position: absolute;
  display: block;
  width: 15px;
  height: 42px;
  top: -65px;
  left: 145px;
  background-image: url(../images/challenge/red-arrow.svg);
  background-size: 15px 42px;
  background-repeat: no-repeat;
}

.block-editor-page.is-fullscreen-mode .edit-post-layout .components-notice-list > div {
  padding-left: 125px;
}

.block-editor-page.is-fullscreen-mode .wpforms-challenge-dot-step5 {
  left: 75px;
}

.block-editor-page.is-fullscreen-mode .wpforms-challenge-tooltip .wpforms-challenge-tooltips-red-arrow {
  left: 105px;
}

#wpforms-builder .wpforms-setup-title.core {
  display: inline-block;
  padding-right: 15px;
  margin-right: 0;
}

.wpforms_page_wpforms-builder .wpforms-challenge {
  z-index: 100099;
}

.wpforms_page_wpforms-builder .wpforms-challenge-tooltip.tooltipster-sidetip.tooltipster-right .tooltipster-arrow {
  left: 0;
}

.wpforms-invisible {
  opacity: 0 !important;
}

@media (max-width: 1024px) {
  .wpforms-challenge,
  .wpforms-challenge *,
  .wpforms-challenge-dot,
  .wpforms-challenge-dot *,
  .wpforms-challenge-tooltips,
  .wpforms-challenge-tooltips *,
  .wpforms-challenge-tooltip,
  .wpforms-challenge-tooltip *,
  .wpforms-challenge-popup-container,
  .wpforms-challenge-popup-container *,
  #wpforms-welcome .challenge {
    display: none !important;
    visibility: hidden !important;
  }
}
