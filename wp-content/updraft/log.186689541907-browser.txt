0000.010 (R) [notice] Looking for db archive: file name: backup_2025-06-03-1044_Loan_City_1ecc1a7fe2fd-db.gz
0000.010 (R) [notice] Archive is expected to be size: 429.1 KB: OK
0000.012 (R) [notice] Looking for plugins archive: file name: backup_2025-06-03-1044_Loan_City_1ecc1a7fe2fd-plugins2.zip
0000.012 (R) [notice] Archive is expected to be size: 10805.2 KB: OK
0000.012 (R) [notice] Looking for plugins archive: file name: backup_2025-06-03-1044_Loan_City_1ecc1a7fe2fd-plugins.zip
0000.012 (R) [notice] Archive is expected to be size: 25497.1 KB: OK
0000.014 (R) [notice] Looking for themes archive: file name: backup_2025-06-03-1044_Loan_City_1ecc1a7fe2fd-themes.zip
0000.014 (R) [notice] Archive is expected to be size: 1503 KB: OK
0000.014 (R) [notice] Looking for uploads archive: file name: backup_2025-06-03-1044_Loan_City_1ecc1a7fe2fd-uploads.zip
0000.014 (R) [notice] Archive is expected to be size: 9356.7 KB: OK
0000.015 (R) [notice] Looking for others archive: file name: backup_2025-06-03-1044_Loan_City_1ecc1a7fe2fd-others.zip
0000.015 (R) [notice] Archive is expected to be size: 0.5 KB: OK
0000.016 (R) [notice] Will not delete any archives after unpacking them, because there was no cloud storage for this backup
0000.019 (R) [notice] Unpacking backup... (backup_2025-06-03-1044_Loan_City_1ecc1a7fe2fd-db.gz, 0.4 Mb)
0000.020 (R) [notice] Restoring the database (on a large site this can take a long time - if it times out (which can happen if your web hosting company has configured your hosting to limit resources) then you should use a different method, such as phpMyAdmin)...
0000.076 (R) [notice] Enabling Maintenance mode&#8230;
0000.098 (R) [notice] Backup of: https://loancity.sg
0000.101 (R) [notice] Content URL: https://loancity.sg/wp-content
0000.101 (R) [notice] Uploads URL: https://loancity.sg/wp-content/uploads
0000.102 (R) [notice] Old table prefix: wp_
0000.102 (R) [notice] Old ABSPATH: /var/www/lc-landing-page/
0000.102 (R) [notice] UpdraftPlus plugin slug: updraftplus/updraftplus.php
0000.102 (R) [notice] Site information: multisite = 0
0000.102 (R) [notice] Site information: sql_mode = NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION
0000.102 (R) [notice] New table prefix: yz_
0000.110 (R) [notice] Processing table (InnoDB):  wp_options - will restore as: yz_options
0000.201 (R) [notice] Skipping table wp_users: user has chosen not to restore this table
0000.201 (R) [notice] Skipping table wp_usermeta: user has chosen not to restore this table
0000.209 (R) [notice] Atomic restore: dropping original table (wp_options)
0000.220 (R) [notice] Atomic restore: renaming new table (yz_options) to final table name (wp_options)
0000.610 (R) [notice] Search and replacing table: wp_options: rows: 413
0000.629 (R) [notice] Processing table (InnoDB):  wp_actionscheduler_actions - will restore as: yz_actionscheduler_actions
0000.655 (R) [notice] Atomic restore: dropping original table (wp_actionscheduler_actions)
0000.661 (R) [notice] Atomic restore: renaming new table (yz_actionscheduler_actions) to final table name (wp_actionscheduler_actions)
0000.667 (R) [notice] Search and replacing table: wp_actionscheduler_actions: rows: 97
0000.668 (R) [notice] Processing table (InnoDB):  wp_actionscheduler_claims - will restore as: yz_actionscheduler_claims
0000.680 (R) [notice] Atomic restore: dropping original table (wp_actionscheduler_claims)
0000.688 (R) [notice] Atomic restore: renaming new table (yz_actionscheduler_claims) to final table name (wp_actionscheduler_claims)
0000.694 (R) [notice] Search and replacing table: wp_actionscheduler_claims: rows: 0
0000.694 (R) [notice] Processing table (InnoDB):  wp_actionscheduler_groups - will restore as: yz_actionscheduler_groups
0000.707 (R) [notice] Atomic restore: dropping original table (wp_actionscheduler_groups)
0000.712 (R) [notice] Atomic restore: renaming new table (yz_actionscheduler_groups) to final table name (wp_actionscheduler_groups)
0000.718 (R) [notice] Search and replacing table: wp_actionscheduler_groups: rows: 3
0000.718 (R) [notice] Processing table (InnoDB):  wp_actionscheduler_logs - will restore as: yz_actionscheduler_logs
0000.734 (R) [notice] Atomic restore: dropping original table (wp_actionscheduler_logs)
0000.740 (R) [notice] Atomic restore: renaming new table (yz_actionscheduler_logs) to final table name (wp_actionscheduler_logs)
0000.746 (R) [notice] Search and replacing table: wp_actionscheduler_logs: rows: 277
0000.747 (R) [notice] Processing table (InnoDB):  wp_commentmeta - will restore as: yz_commentmeta
0000.762 (R) [notice] Atomic restore: dropping original table (wp_commentmeta)
0000.768 (R) [notice] Atomic restore: renaming new table (yz_commentmeta) to final table name (wp_commentmeta)
0000.774 (R) [notice] Search and replacing table: wp_commentmeta: rows: 0
0000.774 (R) [notice] Processing table (InnoDB):  wp_comments - will restore as: yz_comments
0000.789 (R) [notice] Atomic restore: dropping original table (wp_comments)
0000.799 (R) [notice] Atomic restore: renaming new table (yz_comments) to final table name (wp_comments)
0000.811 (R) [notice] Search and replacing table: wp_comments: rows: 0
0000.812 (R) [notice] Processing table (InnoDB):  wp_links - will restore as: yz_links
0000.831 (R) [notice] Atomic restore: dropping original table (wp_links)
0000.837 (R) [notice] Atomic restore: renaming new table (yz_links) to final table name (wp_links)
0000.843 (R) [notice] Search and replacing table: wp_links: rows: 0
0000.844 (R) [notice] Processing table (InnoDB):  wp_postmeta - will restore as: yz_postmeta
0000.865 (R) [notice] Atomic restore: dropping original table (wp_postmeta)
0000.871 (R) [notice] Atomic restore: renaming new table (yz_postmeta) to final table name (wp_postmeta)
0000.876 (R) [notice] Search and replacing table: wp_postmeta: rows: 5
0000.878 (R) [notice] Processing table (InnoDB):  wp_posts - will restore as: yz_posts
0000.949 (R) [notice] Atomic restore: dropping original table (wp_posts)
0000.955 (R) [notice] Atomic restore: renaming new table (yz_posts) to final table name (wp_posts)
0000.961 (R) [notice] Search and replacing table: wp_posts: rows: 157
0001.029 (R) [notice] Processing table (InnoDB):  wp_term_relationships - will restore as: yz_term_relationships
0001.044 (R) [notice] Atomic restore: dropping original table (wp_term_relationships)
0001.048 (R) [notice] Atomic restore: renaming new table (yz_term_relationships) to final table name (wp_term_relationships)
0001.055 (R) [notice] Skipping this table: data in this table (wp_term_relationships) should not be search/replaced
0001.055 (R) [notice] Processing table (InnoDB):  wp_term_taxonomy - will restore as: yz_term_taxonomy
0001.073 (R) [notice] Atomic restore: dropping original table (wp_term_taxonomy)
0001.080 (R) [notice] Atomic restore: renaming new table (yz_term_taxonomy) to final table name (wp_term_taxonomy)
0001.086 (R) [notice] Search and replacing table: wp_term_taxonomy: rows: 4
0001.086 (R) [notice] Processing table (InnoDB):  wp_termmeta - will restore as: yz_termmeta
0001.100 (R) [notice] Database queries processed: 50 in 1.08 seconds
0001.100 (R) [notice] Atomic restore: dropping original table (wp_termmeta)
0001.106 (R) [notice] Atomic restore: renaming new table (yz_termmeta) to final table name (wp_termmeta)
0001.111 (R) [notice] Search and replacing table: wp_termmeta: rows: 0
0001.111 (R) [notice] Processing table (InnoDB):  wp_terms - will restore as: yz_terms
0001.128 (R) [notice] Atomic restore: dropping original table (wp_terms)
0001.133 (R) [notice] Atomic restore: renaming new table (yz_terms) to final table name (wp_terms)
0001.139 (R) [notice] Search and replacing table: wp_terms: rows: 4
0001.139 (R) [notice] Processing table (InnoDB):  wp_db7_forms - will restore as: yz_db7_forms
0001.156 (R) [notice] Atomic restore: dropping original table (wp_db7_forms)
0001.161 (R) [notice] Atomic restore: renaming new table (yz_db7_forms) to final table name (wp_db7_forms)
0001.167 (R) [notice] Search and replacing table: wp_db7_forms: rows: 25
0001.168 (R) [notice] Processing table (InnoDB):  wp_trustindex_google_reviews - will restore as: yz_trustindex_google_reviews
0001.184 (R) [notice] Atomic restore: dropping original table (wp_trustindex_google_reviews)
0001.189 (R) [notice] Atomic restore: renaming new table (yz_trustindex_google_reviews) to final table name (wp_trustindex_google_reviews)
0001.194 (R) [notice] Search and replacing table: wp_trustindex_google_reviews: rows: 10
0001.195 (R) [notice] Processing table (InnoDB):  wp_vxcf_googlesheets - will restore as: yz_vxcf_googlesheets
0001.208 (R) [notice] Atomic restore: dropping original table (wp_vxcf_googlesheets)
0001.216 (R) [notice] Atomic restore: renaming new table (yz_vxcf_googlesheets) to final table name (wp_vxcf_googlesheets)
0001.221 (R) [notice] Search and replacing table: wp_vxcf_googlesheets: rows: 2
0001.222 (R) [notice] Processing table (InnoDB):  wp_vxcf_googlesheets_accounts - will restore as: yz_vxcf_googlesheets_accounts
0001.236 (R) [notice] Atomic restore: dropping original table (wp_vxcf_googlesheets_accounts)
0001.241 (R) [notice] Atomic restore: renaming new table (yz_vxcf_googlesheets_accounts) to final table name (wp_vxcf_googlesheets_accounts)
0001.249 (R) [notice] Search and replacing table: wp_vxcf_googlesheets_accounts: rows: 3
0001.253 (R) [notice] Processing table (InnoDB):  wp_vxcf_googlesheets_log - will restore as: yz_vxcf_googlesheets_log
0001.274 (R) [notice] Atomic restore: dropping original table (wp_vxcf_googlesheets_log)
0001.283 (R) [notice] Atomic restore: renaming new table (yz_vxcf_googlesheets_log) to final table name (wp_vxcf_googlesheets_log)
0001.292 (R) [notice] Search and replacing table: wp_vxcf_googlesheets_log: rows: 0
0001.292 (R) [notice] Processing table (InnoDB):  wp_wpforms_logs - will restore as: yz_wpforms_logs
0001.308 (R) [notice] Atomic restore: dropping original table (wp_wpforms_logs)
0001.317 (R) [notice] Atomic restore: renaming new table (yz_wpforms_logs) to final table name (wp_wpforms_logs)
0001.325 (R) [notice] Search and replacing table: wp_wpforms_logs: rows: 0
0001.325 (R) [notice] Processing table (InnoDB):  wp_wpforms_payment_meta - will restore as: yz_wpforms_payment_meta
0001.347 (R) [notice] Atomic restore: dropping original table (wp_wpforms_payment_meta)
0001.354 (R) [notice] Atomic restore: renaming new table (yz_wpforms_payment_meta) to final table name (wp_wpforms_payment_meta)
0001.364 (R) [notice] Search and replacing table: wp_wpforms_payment_meta: rows: 0
0001.364 (R) [notice] Processing table (InnoDB):  wp_wpforms_payments - will restore as: yz_wpforms_payments
0001.387 (R) [notice] Atomic restore: dropping original table (wp_wpforms_payments)
0001.397 (R) [notice] Atomic restore: renaming new table (yz_wpforms_payments) to final table name (wp_wpforms_payments)
0001.406 (R) [notice] Search and replacing table: wp_wpforms_payments: rows: 0
0001.407 (R) [notice] Processing table (InnoDB):  wp_wpforms_tasks_meta - will restore as: yz_wpforms_tasks_meta
0001.429 (R) [notice] Atomic restore: dropping original table (wp_wpforms_tasks_meta)
0001.437 (R) [notice] Atomic restore: renaming new table (yz_wpforms_tasks_meta) to final table name (wp_wpforms_tasks_meta)
0001.446 (R) [notice] Search and replacing table: wp_wpforms_tasks_meta: rows: 5
0001.446 (R) [notice] Processing table (InnoDB):  wp_wpmailsmtp_debug_events - will restore as: yz_wpmailsmtp_debug_events
0001.466 (R) [notice] Atomic restore: dropping original table (wp_wpmailsmtp_debug_events)
0001.472 (R) [notice] Atomic restore: renaming new table (yz_wpmailsmtp_debug_events) to final table name (wp_wpmailsmtp_debug_events)
0001.481 (R) [notice] Search and replacing table: wp_wpmailsmtp_debug_events: rows: 5
0001.486 (R) [notice] Processing table (InnoDB):  wp_wpmailsmtp_tasks_meta - will restore as: yz_wpmailsmtp_tasks_meta
0001.502 (R) [notice] Atomic restore: dropping original table (wp_wpmailsmtp_tasks_meta)
0001.508 (R) [notice] Atomic restore: renaming new table (yz_wpmailsmtp_tasks_meta) to final table name (wp_wpmailsmtp_tasks_meta)
0001.514 (R) [notice] Search and replacing table: wp_wpmailsmtp_tasks_meta: rows: 1
0001.514 (R) [notice] Processing table (InnoDB):  wp_yoast_indexable - will restore as: yz_yoast_indexable
0001.539 (R) [notice] Atomic restore: dropping original table (wp_yoast_indexable)
0001.545 (R) [notice] Atomic restore: renaming new table (yz_yoast_indexable) to final table name (wp_yoast_indexable)
0001.552 (R) [notice] Search and replacing table: wp_yoast_indexable: rows: 29
0001.578 (R) [notice] Processing table (InnoDB):  wp_yoast_indexable_hierarchy - will restore as: yz_yoast_indexable_hierarchy
0001.599 (R) [notice] Atomic restore: dropping original table (wp_yoast_indexable_hierarchy)
0001.606 (R) [notice] Atomic restore: renaming new table (yz_yoast_indexable_hierarchy) to final table name (wp_yoast_indexable_hierarchy)
0001.614 (R) [notice] Search and replacing table: wp_yoast_indexable_hierarchy: rows: 26
0001.615 (R) [notice] Processing table (InnoDB):  wp_yoast_migrations - will restore as: yz_yoast_migrations
0001.634 (R) [notice] Atomic restore: dropping original table (wp_yoast_migrations)
0001.641 (R) [notice] Atomic restore: renaming new table (yz_yoast_migrations) to final table name (wp_yoast_migrations)
0001.648 (R) [notice] Search and replacing table: wp_yoast_migrations: rows: 24
0001.648 (R) [notice] Processing table (InnoDB):  wp_yoast_primary_term - will restore as: yz_yoast_primary_term
0001.663 (R) [notice] Atomic restore: dropping original table (wp_yoast_primary_term)
0001.670 (R) [notice] Atomic restore: renaming new table (yz_yoast_primary_term) to final table name (wp_yoast_primary_term)
0001.678 (R) [notice] Search and replacing table: wp_yoast_primary_term: rows: 0
0001.678 (R) [notice] Processing table (InnoDB):  wp_yoast_seo_links - will restore as: yz_yoast_seo_links
0001.691 (R) [notice] Disabling Maintenance mode&#8230;
0001.691 (R) [notice] Atomic restore: dropping original table (wp_yoast_seo_links)
0001.696 (R) [notice] Database queries processed: 100 in 1.67 seconds
0001.696 (R) [notice] Atomic restore: renaming new table (yz_yoast_seo_links) to final table name (wp_yoast_seo_links)
0001.703 (R) [notice] Search and replacing table: wp_yoast_seo_links: rows: 43
0001.729 (R) [notice] Finished: lines processed: 100 in 1.71 seconds
0001.729 (R) [notice] Cleaning up rubbish...
0001.730 (R) [notice] Database search and replace: replace https://loancity.sg in backup dump with http://loan-city.com
0001.730 (R) [notice] Database search and replace: replace http://www.loancity.sg in backup dump with http://loan-city.com
0001.731 (R) [notice] Database search and replace: replace http://loancity.sg in backup dump with http://loan-city.com
0001.731 (R) [notice] Database search and replace: replace https://www.loancity.sg in backup dump with http://loan-city.com
0001.731 (R) [notice] Database search and replace: replace /var/www/lc-landing-page in backup dump with /var/www/html
0001.731 (R) [notice] Search and replacing table: wp_actionscheduler_actions: already done
0001.731 (R) [notice] Search and replacing table: wp_actionscheduler_claims: already done
0001.731 (R) [notice] Search and replacing table: wp_actionscheduler_groups: already done
0001.731 (R) [notice] Search and replacing table: wp_actionscheduler_logs: already done
0001.731 (R) [notice] Search and replacing table: wp_commentmeta: already done
0001.731 (R) [notice] Search and replacing table: wp_comments: already done
0001.731 (R) [notice] Search and replacing table: wp_db7_forms: already done
0001.731 (R) [notice] Search and replacing table: wp_links: already done
0001.731 (R) [notice] Search and replacing table: wp_options: already done
0001.731 (R) [notice] Search and replacing table: wp_postmeta: already done
0001.731 (R) [notice] Search and replacing table: wp_posts: already done
0001.731 (R) [notice] Search and replacing table: wp_term_relationships: already done
0001.732 (R) [notice] Search and replacing table: wp_term_taxonomy: already done
0001.732 (R) [notice] Search and replacing table: wp_termmeta: already done
0001.732 (R) [notice] Search and replacing table: wp_terms: already done
0001.732 (R) [notice] Search and replacing table: wp_trustindex_google_reviews: already done
0001.732 (R) [notice] Search and replacing table: wp_vxcf_googlesheets: already done
0001.732 (R) [notice] Search and replacing table: wp_vxcf_googlesheets_accounts: already done
0001.732 (R) [notice] Search and replacing table: wp_vxcf_googlesheets_log: already done
0001.732 (R) [notice] Search and replacing table: wp_wpforms_logs: already done
0001.732 (R) [notice] Search and replacing table: wp_wpforms_payment_meta: already done
0001.732 (R) [notice] Search and replacing table: wp_wpforms_payments: already done
0001.732 (R) [notice] Search and replacing table: wp_wpforms_tasks_meta: already done
0001.732 (R) [notice] Search and replacing table: wp_wpmailsmtp_debug_events: already done
0001.732 (R) [notice] Search and replacing table: wp_wpmailsmtp_tasks_meta: already done
0001.732 (R) [notice] Search and replacing table: wp_yoast_indexable: already done
0001.732 (R) [notice] Search and replacing table: wp_yoast_indexable_hierarchy: already done
0001.733 (R) [notice] Search and replacing table: wp_yoast_migrations: already done
0001.733 (R) [notice] Search and replacing table: wp_yoast_primary_term: already done
0001.733 (R) [notice] Search and replacing table: wp_yoast_seo_links: already done
0001.733 (R) [notice] Search and replacing table: wp_usermeta: rows: 28
0001.734 (R) [notice] Search and replacing table: wp_users: rows: 1
0001.736 (R) [notice] Search and replacing table: wp_wpr_rucss_resources: rows: 0
0001.737 (R) [notice] Search and replacing table: wp_wpr_rucss_used_css: rows: 0
0001.737 (R) [notice] Tables examined: 33
0001.737 (R) [notice] Rows examined: 1162
0001.737 (R) [notice] Changes made: 136
0001.737 (R) [notice] SQL update commands run: 122
0001.737 (R) [notice] Errors: 0
0001.737 (R) [notice] Time taken (seconds): 0.176
0001.742 (R) [notice] Unpacking backup... (backup_2025-06-03-1044_Loan_City_1ecc1a7fe2fd-plugins.zip, 24.9 MB)
0001.982 (R) [notice] Unzip progress: 1444 out of 9337 files (29.1 KB, plugins/contact-form-7/assets/icon.png)
0002.242 (R) [notice] Unzip progress: 2445 out of 9337 files (21 MB, plugins/wpforms-lite/templates/admin/form-embed-wizard/popup.php)
0002.477 (R) [notice] Unzip progress: 3446 out of 9337 files (26.7 MB, plugins/wpforms-lite/vendor_prefixed/square/square/src/Models/CreateRefundRequest.php)
0002.667 (R) [notice] Unzip progress: 4447 out of 9337 files (29.1 MB, plugins/wpforms-lite/vendor_prefixed/square/square/src/Models/Builders/AddGroupToCustomerResponseBuilder.php)
0002.920 (R) [notice] Unzip progress: 5448 out of 9337 files (32.8 MB, plugins/wpforms-lite/vendor_prefixed/stripe/stripe-php/lib/V2/EventDestination.php)
0003.137 (R) [notice] Unzip progress: 6449 out of 9337 files (41.3 MB, plugins/wp-mail-smtp/assets/css/vendor/jquery-confirm.min.css)
0003.352 (R) [notice] Unzip progress: 7450 out of 9337 files (47.4 MB, plugins/gtranslate/flags/32/uz.png)
0003.569 (R) [notice] Unzip progress: 8451 out of 9337 files (56.5 MB, plugins/wordpress-seo/src/integrations/front-end/category-term-description.php)
0003.760 (R) [notice] Unzip progress: 9337 out of 9337 files (64 MB, plugins/updraftplus/includes/Google/IO/cacerts.pem)
0003.762 (R) [notice] Moving old data out of the way...
0003.944 (R) [notice] Moving unpacked backup into place...
0004.207 (R) [notice] Cleaning up rubbish...
0004.211 (R) [notice] Unpacking backup... (backup_2025-06-03-1044_Loan_City_1ecc1a7fe2fd-plugins2.zip, 10.6 MB)
0004.613 (R) [notice] Unzip progress: 1002 out of 2728 files (20.4 MB, plugins/updraftplus/vendor/guzzle/guzzle/src/Guzzle/Batch/BatchCommandTransfer.php)
0004.862 (R) [notice] Unzip progress: 2003 out of 2728 files (33.8 MB, plugins/google-site-kit/third-party/google/apiclient-services/src/TagManager/Container.php)
0005.003 (R) [notice] Unzip progress: 2728 out of 2728 files (37 MB, plugins/google-site-kit/readme.txt)
0005.003 (R) [notice] Moving unpacked backup into place...
0005.589 (R) [notice] Cleaning up rubbish...
0005.629 (R) [notice] Unpacking backup... (backup_2025-06-03-1044_Loan_City_1ecc1a7fe2fd-themes.zip, 1.5 MB)
0005.679 (R) [notice] Unzip progress: 190 out of 190 files (2.1 MB, themes/loan-city/loan-application-html-rendered.html)
0005.680 (R) [notice] Moving old data out of the way...
0005.699 (R) [notice] Moving unpacked backup into place...
0005.704 (R) [notice] Cleaning up rubbish...
0005.710 (R) [notice] Unpacking backup... (backup_2025-06-03-1044_Loan_City_1ecc1a7fe2fd-uploads.zip, 9.1 MB)
0005.783 (R) [notice] Unzip progress: 90 out of 90 files (10.8 MB, uploads/2025/04/Offers-600x400.png)
0005.784 (R) [notice] Moving old data out of the way...
0005.792 (R) [notice] Moving unpacked backup into place...
0005.796 (R) [notice] Cleaning up rubbish...
0005.803 (R) [notice] Unpacking backup... (backup_2025-06-03-1044_Loan_City_1ecc1a7fe2fd-others.zip, 0 MB)
0005.806 (R) [notice] Unzip progress: 4 out of 4 files (28 B, index.php)
0005.807 (R) [notice] Cleaning up rubbish...
0005.808 (R) [notice] Error: Failed to delete working directory after restoring. (/var/www/html/wp-content/upgrade/89d57fef)
0005.808 (R) [notice] Files found:
0005.808 (R) [notice] * upgrade
0005.828 (R) [notice] Restore successful!
