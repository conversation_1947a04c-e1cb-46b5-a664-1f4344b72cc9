0000.009 () Opened log file at time: <PERSON><PERSON>, 03 Jun 2025 02:48:23 +0000 on http://loan-city.com
0000.010 () UpdraftPlus WordPress backup plugin (https://updraftplus.com): 1.25.5 WP: 6.8.1 PHP: 8.2.28 (apa<PERSON><PERSON><PERSON><PERSON>, Linux 5e576445307b 6.14.8-orbstack-00288-g80b66077b748-dirty #127 SMP Tue May 27 09:02:55 UTC 2025 aarch64) MySQL: 10.6.21-MariaDB-ubu2004 (max packet size=16777216) WPLANG: en_US Server: Apache/2.4.62 (Debian) safe_mode: 0 max_execution_time: 900 memory_limit: 1024M (used: 5.7M | 8M) multisite: N openssl: OpenSSL 3.0.15 3 Sep 2024 mcrypt: N LANG: C WP Proxy: disabled ZipArchive::addFile: Y
0000.010 () Free space on disk containing <PERSON>draft's temporary directory: 52039.1 MB
0000.055 () Restore setup, now closing connection and starting restore over AJAX.
0000.002 () Opened log file at time: <PERSON><PERSON>, 03 Jun 2025 02:48:23 +0000 on http://loan-city.com
0000.002 () UpdraftPlus WordPress backup plugin (https://updraftplus.com): 1.25.5 WP: 6.8.1 PHP: 8.2.28 (apache2handler, Linux 5e576445307b 6.14.8-orbstack-00288-g80b66077b748-dirty #127 SMP Tue May 27 09:02:55 UTC 2025 aarch64) MySQL: 10.6.21-MariaDB-ubu2004 (max packet size=16777216) WPLANG: en_US Server: Apache/2.4.62 (Debian) safe_mode: 0 max_execution_time: 900 memory_limit: 1024M (used: 4.5M | 6M) multisite: N openssl: OpenSSL 3.0.15 3 Sep 2024 mcrypt: N LANG: C WP Proxy: disabled ZipArchive::addFile: Y
0000.002 () Free space on disk containing Updraft's temporary directory: 52038 MB
0000.003 () Ensuring WP_Filesystem is setup for a restore
0000.005 () WP_Filesystem is setup and ready for a restore
0000.009 () Restore job started. Entities to restore: plugins, themes, uploads, others, db. Restore options: {"updraft_restorer_replacesiteurl":"1","include_unspecified_tables":false,"tables_to_restore":["wp_options","wp_actionscheduler_actions","wp_actionscheduler_claims","wp_actionscheduler_groups","wp_actionscheduler_logs","wp_commentmeta","wp_comments","wp_links","wp_postmeta","wp_posts","wp_term_relationships","wp_term_taxonomy","wp_termmeta","wp_terms","wp_db7_forms","wp_trustindex_google_reviews","wp_vxcf_googlesheets","wp_vxcf_googlesheets_accounts","wp_vxcf_googlesheets_log","wp_wpforms_logs","wp_wpforms_payment_meta","wp_wpforms_payments","wp_wpforms_tasks_meta","wp_wpmailsmtp_debug_events","wp_wpmailsmtp_tasks_meta","wp_yoast_indexable","wp_yoast_indexable_hierarchy","wp_yoast_migrations","wp_yoast_primary_term","wp_yoast_seo_links"],"tables_to_skip":["wp_users","wp_usermeta"],"updraft_encryptionphrase":"","updraft_restorer_wpcore_includewpconfig":false,"updraft_incremental_restore_point":-1}
0000.011 () PHP event: code E_WARNING: mkdir(): File exists (line 558, wp-admin/includes/class-wp-filesystem-direct.php)
0000.016 () Will not delete any archives after unpacking them, because there was no cloud storage for this backup
0000.018 () Entity: db
0000.019 () restore_backup(backup_file=backup_2025-06-03-1044_Loan_City_1ecc1a7fe2fd-db.gz, type=db, info=a:0:{}, last_one=)
0000.019 () Unpacking backup... (backup_2025-06-03-1044_Loan_City_1ecc1a7fe2fd-db.gz, 0.4 Mb)
0000.019 () PHP event: code E_WARNING: mkdir(): File exists (line 558, wp-admin/includes/class-wp-filesystem-direct.php)
0000.020 () Database successfully unpacked
0000.020 () Restoring the database (on a large site this can take a long time - if it times out (which can happen if your web hosting company has configured your hosting to limit resources) then you should use a different method, such as phpMyAdmin)...
0000.021 () Using direct MySQL access; value of use_mysqli is: 1
0000.021 () SQL compatibility mode is: NO_AUTO_VALUE_ON_ZERO,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION
0000.076 () Tried to raise max_allowed_packet from 16 MB to 32 MB, but failed (Access denied; you need (at least one of) the SUPER privilege(s) for this operation, b:0;)
0000.076 () Max packet size: 16 MB
0000.076 () Entering maintenance mode
0000.076 () Enabling Maintenance mode&#8230;
0000.098 () Backup of: https://loancity.sg
0000.101 () Content URL: https://loancity.sg/wp-content
0000.101 () Uploads URL: https://loancity.sg/wp-content/uploads
0000.102 () Old table prefix: wp_
0000.102 () Old ABSPATH: /var/www/lc-landing-page/
0000.102 () UpdraftPlus plugin slug: updraftplus/updraftplus.php
0000.102 () Site information: multisite=0
0000.102 () Site information: sql_mode=NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION
0000.102 () Skipped execution of SQL statement (unwanted or internally handled type=18): /*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
0000.102 () New table prefix: yz_
0000.110 () Processing table (InnoDB): wp_options - will restore as: yz_options
0000.209 () Atomic restore: dropping original table (wp_options)
0000.220 () Atomic restore: renaming new table (yz_options) to final table name (wp_options)
0000.231 () Restoring prior UD configuration (table: wp_options; keys: 102)
0000.334 () PHP event: code E_DEPRECATED: substr(): Passing null to parameter #1 ($string) of type string is deprecated (line 169, wp-content/plugins/updraftplus/includes/class-manipulation-functions.php)
0000.334 () PHP event: code E_DEPRECATED: substr(): Passing null to parameter #1 ($string) of type string is deprecated (line 169, wp-content/plugins/updraftplus/includes/class-manipulation-functions.php)
0000.334 () PHP event: code E_DEPRECATED: preg_match(): Passing null to parameter #2 ($subject) of type string is deprecated (line 169, wp-content/plugins/updraftplus/includes/class-manipulation-functions.php)
0000.335 () PHP event: code E_DEPRECATED: substr(): Passing null to parameter #1 ($string) of type string is deprecated (line 169, wp-content/plugins/updraftplus/includes/class-manipulation-functions.php)
0000.335 () PHP event: code E_DEPRECATED: substr(): Passing null to parameter #1 ($string) of type string is deprecated (line 169, wp-content/plugins/updraftplus/includes/class-manipulation-functions.php)
0000.335 () PHP event: code E_DEPRECATED: preg_match(): Passing null to parameter #2 ($subject) of type string is deprecated (line 169, wp-content/plugins/updraftplus/includes/class-manipulation-functions.php)
0000.376 () PHP event: code E_DEPRECATED: preg_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated (line 48, wp-content/plugins/updraftplus/includes/class-manipulation-functions.php)
0000.404 () PHP event: code E_DEPRECATED: preg_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated (line 48, wp-content/plugins/updraftplus/includes/class-manipulation-functions.php)
0000.410 () PHP event: code E_DEPRECATED: preg_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated (line 48, wp-content/plugins/updraftplus/includes/class-manipulation-functions.php)
0000.529 () PHP event: code E_DEPRECATED: preg_match(): Passing null to parameter #2 ($subject) of type string is deprecated (line 267, wp-content/plugins/updraftplus/options.php)
0000.533 () PHP event: code E_DEPRECATED: preg_match(): Passing null to parameter #2 ($subject) of type string is deprecated (line 267, wp-content/plugins/updraftplus/options.php)
0000.610 () Search and replacing table: wp_options: rows: 413
0000.617 () Incomplete object detected in database: stdClass; Search and replace will be skipped for these entries
0000.617 () Incomplete object detected in database: FS_Plugin; Search and replace will be skipped for these entries
0000.624 () Incomplete object detected in database: WpOrg\Requests\Utility\CaseInsensitiveDictionary; Search and replace will be skipped for these entries
0000.629 () Processing table (InnoDB): wp_actionscheduler_actions - will restore as: yz_actionscheduler_actions
0000.655 () Atomic restore: dropping original table (wp_actionscheduler_actions)
0000.661 () Atomic restore: renaming new table (yz_actionscheduler_actions) to final table name (wp_actionscheduler_actions)
0000.667 () Search and replacing table: wp_actionscheduler_actions: rows: 97
0000.667 () Incomplete object detected in database: ActionScheduler_SimpleSchedule; Search and replace will be skipped for these entries
0000.667 () Incomplete object detected in database: ActionScheduler_IntervalSchedule; Search and replace will be skipped for these entries
0000.667 () Incomplete object detected in database: ActionScheduler_NullSchedule; Search and replace will be skipped for these entries
0000.668 () Processing table (InnoDB): wp_actionscheduler_claims - will restore as: yz_actionscheduler_claims
0000.680 () Atomic restore: dropping original table (wp_actionscheduler_claims)
0000.688 () Atomic restore: renaming new table (yz_actionscheduler_claims) to final table name (wp_actionscheduler_claims)
0000.694 () Search and replacing table: wp_actionscheduler_claims: rows: 0
0000.694 () Processing table (InnoDB): wp_actionscheduler_groups - will restore as: yz_actionscheduler_groups
0000.707 () Atomic restore: dropping original table (wp_actionscheduler_groups)
0000.712 () Atomic restore: renaming new table (yz_actionscheduler_groups) to final table name (wp_actionscheduler_groups)
0000.718 () Search and replacing table: wp_actionscheduler_groups: rows: 3
0000.718 () Processing table (InnoDB): wp_actionscheduler_logs - will restore as: yz_actionscheduler_logs
0000.734 () Atomic restore: dropping original table (wp_actionscheduler_logs)
0000.740 () Atomic restore: renaming new table (yz_actionscheduler_logs) to final table name (wp_actionscheduler_logs)
0000.746 () Search and replacing table: wp_actionscheduler_logs: rows: 277
0000.747 () Processing table (InnoDB): wp_commentmeta - will restore as: yz_commentmeta
0000.762 () Atomic restore: dropping original table (wp_commentmeta)
0000.768 () Atomic restore: renaming new table (yz_commentmeta) to final table name (wp_commentmeta)
0000.774 () Search and replacing table: wp_commentmeta: rows: 0
0000.774 () Processing table (InnoDB): wp_comments - will restore as: yz_comments
0000.789 () Atomic restore: dropping original table (wp_comments)
0000.799 () Atomic restore: renaming new table (yz_comments) to final table name (wp_comments)
0000.812 () Search and replacing table: wp_comments: rows: 0
0000.812 () Processing table (InnoDB): wp_links - will restore as: yz_links
0000.831 () Atomic restore: dropping original table (wp_links)
0000.837 () Atomic restore: renaming new table (yz_links) to final table name (wp_links)
0000.844 () Search and replacing table: wp_links: rows: 0
0000.844 () Processing table (InnoDB): wp_postmeta - will restore as: yz_postmeta
0000.864 () Atomic restore: dropping original table (wp_postmeta)
0000.871 () Atomic restore: renaming new table (yz_postmeta) to final table name (wp_postmeta)
0000.876 () Search and replacing table: wp_postmeta: rows: 5
0000.878 () Processing table (InnoDB): wp_posts - will restore as: yz_posts
0000.949 () Atomic restore: dropping original table (wp_posts)
0000.955 () Atomic restore: renaming new table (yz_posts) to final table name (wp_posts)
0000.961 () Skipping search/replace on GUID column in posts table
0000.961 () Search and replacing table: wp_posts: rows: 157
0001.029 () Processing table (InnoDB): wp_term_relationships - will restore as: yz_term_relationships
0001.043 () Atomic restore: dropping original table (wp_term_relationships)
0001.048 () Atomic restore: renaming new table (yz_term_relationships) to final table name (wp_term_relationships)
0001.055 () Skipping this table: data in this table (wp_term_relationships) should not be search/replaced
0001.055 () Processing table (InnoDB): wp_term_taxonomy - will restore as: yz_term_taxonomy
0001.073 () Atomic restore: dropping original table (wp_term_taxonomy)
0001.079 () Atomic restore: renaming new table (yz_term_taxonomy) to final table name (wp_term_taxonomy)
0001.086 () Search and replacing table: wp_term_taxonomy: rows: 4
0001.086 () Processing table (InnoDB): wp_termmeta - will restore as: yz_termmeta
0001.100 () Database queries processed: 50 in 1.08 seconds
0001.100 () Atomic restore: dropping original table (wp_termmeta)
0001.106 () Atomic restore: renaming new table (yz_termmeta) to final table name (wp_termmeta)
0001.111 () Search and replacing table: wp_termmeta: rows: 0
0001.111 () Processing table (InnoDB): wp_terms - will restore as: yz_terms
0001.128 () Atomic restore: dropping original table (wp_terms)
0001.133 () Atomic restore: renaming new table (yz_terms) to final table name (wp_terms)
0001.139 () Search and replacing table: wp_terms: rows: 4
0001.139 () Processing table (InnoDB): wp_db7_forms - will restore as: yz_db7_forms
0001.156 () Atomic restore: dropping original table (wp_db7_forms)
0001.161 () Atomic restore: renaming new table (yz_db7_forms) to final table name (wp_db7_forms)
0001.167 () Search and replacing table: wp_db7_forms: rows: 25
0001.168 () Processing table (InnoDB): wp_trustindex_google_reviews - will restore as: yz_trustindex_google_reviews
0001.184 () Atomic restore: dropping original table (wp_trustindex_google_reviews)
0001.189 () Atomic restore: renaming new table (yz_trustindex_google_reviews) to final table name (wp_trustindex_google_reviews)
0001.194 () Search and replacing table: wp_trustindex_google_reviews: rows: 10
0001.195 () Processing table (InnoDB): wp_vxcf_googlesheets - will restore as: yz_vxcf_googlesheets
0001.208 () Atomic restore: dropping original table (wp_vxcf_googlesheets)
0001.216 () Atomic restore: renaming new table (yz_vxcf_googlesheets) to final table name (wp_vxcf_googlesheets)
0001.221 () Search and replacing table: wp_vxcf_googlesheets: rows: 2
0001.222 () Processing table (InnoDB): wp_vxcf_googlesheets_accounts - will restore as: yz_vxcf_googlesheets_accounts
0001.236 () Atomic restore: dropping original table (wp_vxcf_googlesheets_accounts)
0001.241 () Atomic restore: renaming new table (yz_vxcf_googlesheets_accounts) to final table name (wp_vxcf_googlesheets_accounts)
0001.249 () Search and replacing table: wp_vxcf_googlesheets_accounts: rows: 3
0001.253 () Processing table (InnoDB): wp_vxcf_googlesheets_log - will restore as: yz_vxcf_googlesheets_log
0001.274 () Atomic restore: dropping original table (wp_vxcf_googlesheets_log)
0001.283 () Atomic restore: renaming new table (yz_vxcf_googlesheets_log) to final table name (wp_vxcf_googlesheets_log)
0001.292 () Search and replacing table: wp_vxcf_googlesheets_log: rows: 0
0001.292 () Processing table (InnoDB): wp_wpforms_logs - will restore as: yz_wpforms_logs
0001.308 () Atomic restore: dropping original table (wp_wpforms_logs)
0001.317 () Atomic restore: renaming new table (yz_wpforms_logs) to final table name (wp_wpforms_logs)
0001.325 () Search and replacing table: wp_wpforms_logs: rows: 0
0001.325 () Processing table (InnoDB): wp_wpforms_payment_meta - will restore as: yz_wpforms_payment_meta
0001.347 () Atomic restore: dropping original table (wp_wpforms_payment_meta)
0001.354 () Atomic restore: renaming new table (yz_wpforms_payment_meta) to final table name (wp_wpforms_payment_meta)
0001.364 () Search and replacing table: wp_wpforms_payment_meta: rows: 0
0001.364 () Processing table (InnoDB): wp_wpforms_payments - will restore as: yz_wpforms_payments
0001.387 () Atomic restore: dropping original table (wp_wpforms_payments)
0001.397 () Atomic restore: renaming new table (yz_wpforms_payments) to final table name (wp_wpforms_payments)
0001.407 () Search and replacing table: wp_wpforms_payments: rows: 0
0001.407 () Processing table (InnoDB): wp_wpforms_tasks_meta - will restore as: yz_wpforms_tasks_meta
0001.429 () Atomic restore: dropping original table (wp_wpforms_tasks_meta)
0001.437 () Atomic restore: renaming new table (yz_wpforms_tasks_meta) to final table name (wp_wpforms_tasks_meta)
0001.446 () Search and replacing table: wp_wpforms_tasks_meta: rows: 5
0001.446 () Processing table (InnoDB): wp_wpmailsmtp_debug_events - will restore as: yz_wpmailsmtp_debug_events
0001.466 () Atomic restore: dropping original table (wp_wpmailsmtp_debug_events)
0001.472 () Atomic restore: renaming new table (yz_wpmailsmtp_debug_events) to final table name (wp_wpmailsmtp_debug_events)
0001.481 () Search and replacing table: wp_wpmailsmtp_debug_events: rows: 5
0001.486 () Processing table (InnoDB): wp_wpmailsmtp_tasks_meta - will restore as: yz_wpmailsmtp_tasks_meta
0001.502 () Atomic restore: dropping original table (wp_wpmailsmtp_tasks_meta)
0001.508 () Atomic restore: renaming new table (yz_wpmailsmtp_tasks_meta) to final table name (wp_wpmailsmtp_tasks_meta)
0001.514 () Search and replacing table: wp_wpmailsmtp_tasks_meta: rows: 1
0001.514 () Processing table (InnoDB): wp_yoast_indexable - will restore as: yz_yoast_indexable
0001.539 () Atomic restore: dropping original table (wp_yoast_indexable)
0001.545 () Atomic restore: renaming new table (yz_yoast_indexable) to final table name (wp_yoast_indexable)
0001.552 () Search and replacing table: wp_yoast_indexable: rows: 29
0001.578 () Processing table (InnoDB): wp_yoast_indexable_hierarchy - will restore as: yz_yoast_indexable_hierarchy
0001.599 () Atomic restore: dropping original table (wp_yoast_indexable_hierarchy)
0001.606 () Atomic restore: renaming new table (yz_yoast_indexable_hierarchy) to final table name (wp_yoast_indexable_hierarchy)
0001.614 () Search and replacing table: wp_yoast_indexable_hierarchy: rows: 26
0001.615 () Processing table (InnoDB): wp_yoast_migrations - will restore as: yz_yoast_migrations
0001.634 () Atomic restore: dropping original table (wp_yoast_migrations)
0001.641 () Atomic restore: renaming new table (yz_yoast_migrations) to final table name (wp_yoast_migrations)
0001.648 () Search and replacing table: wp_yoast_migrations: rows: 24
0001.648 () Processing table (InnoDB): wp_yoast_primary_term - will restore as: yz_yoast_primary_term
0001.663 () Atomic restore: dropping original table (wp_yoast_primary_term)
0001.670 () Atomic restore: renaming new table (yz_yoast_primary_term) to final table name (wp_yoast_primary_term)
0001.678 () Search and replacing table: wp_yoast_primary_term: rows: 0
0001.678 () Processing table (InnoDB): wp_yoast_seo_links - will restore as: yz_yoast_seo_links
0001.689 () Skipped execution of SQL statement (unwanted or internally handled type=18): /*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
0001.691 () Unlocking database and leaving maintenance mode
0001.691 () Disabling Maintenance mode&#8230;
0001.691 () Atomic restore: dropping original table (wp_yoast_seo_links)
0001.696 () Database queries processed: 100 in 1.67 seconds
0001.696 () Atomic restore: renaming new table (yz_yoast_seo_links) to final table name (wp_yoast_seo_links)
0001.703 () Search and replacing table: wp_yoast_seo_links: rows: 43
0001.728 () Finished: lines processed: 100 in 1.71 seconds
0001.729 () Cleaning up rubbish...
0001.730 () Begin search and replace (updraftplus_restored_db)
0001.730 () [Database-replace-site-url] Database: search and replace site URL
0001.730 () Database search and replace: replace https://loancity.sg in backup dump with http://loan-city.com
0001.730 () Database search and replace: replace http://www.loancity.sg in backup dump with http://loan-city.com
0001.731 () Database search and replace: replace http://loancity.sg in backup dump with http://loan-city.com
0001.731 () Database search and replace: replace https://www.loancity.sg in backup dump with http://loan-city.com
0001.731 () Database search and replace: replace /var/www/lc-landing-page in backup dump with /var/www/html
0001.731 () Search and replacing table: wp_actionscheduler_actions: already done
0001.731 () Search and replacing table: wp_actionscheduler_claims: already done
0001.731 () Search and replacing table: wp_actionscheduler_groups: already done
0001.731 () Search and replacing table: wp_actionscheduler_logs: already done
0001.731 () Search and replacing table: wp_commentmeta: already done
0001.731 () Search and replacing table: wp_comments: already done
0001.731 () Search and replacing table: wp_db7_forms: already done
0001.731 () Search and replacing table: wp_links: already done
0001.731 () Search and replacing table: wp_options: already done
0001.731 () Search and replacing table: wp_postmeta: already done
0001.731 () Search and replacing table: wp_posts: already done
0001.731 () Search and replacing table: wp_term_relationships: already done
0001.732 () Search and replacing table: wp_term_taxonomy: already done
0001.732 () Search and replacing table: wp_termmeta: already done
0001.732 () Search and replacing table: wp_terms: already done
0001.732 () Search and replacing table: wp_trustindex_google_reviews: already done
0001.732 () Search and replacing table: wp_vxcf_googlesheets: already done
0001.732 () Search and replacing table: wp_vxcf_googlesheets_accounts: already done
0001.732 () Search and replacing table: wp_vxcf_googlesheets_log: already done
0001.732 () Search and replacing table: wp_wpforms_logs: already done
0001.732 () Search and replacing table: wp_wpforms_payment_meta: already done
0001.732 () Search and replacing table: wp_wpforms_payments: already done
0001.732 () Search and replacing table: wp_wpforms_tasks_meta: already done
0001.732 () Search and replacing table: wp_wpmailsmtp_debug_events: already done
0001.732 () Search and replacing table: wp_wpmailsmtp_tasks_meta: already done
0001.732 () Search and replacing table: wp_yoast_indexable: already done
0001.733 () Search and replacing table: wp_yoast_indexable_hierarchy: already done
0001.733 () Search and replacing table: wp_yoast_migrations: already done
0001.733 () Search and replacing table: wp_yoast_primary_term: already done
0001.733 () Search and replacing table: wp_yoast_seo_links: already done
0001.733 () Search and replacing table: wp_usermeta: rows: 28
0001.734 () Search and replacing table: wp_users: rows: 1
0001.736 () Search and replacing table: wp_wpr_rucss_resources: rows: 0
0001.737 () Search and replacing table: wp_wpr_rucss_used_css: rows: 0
0001.742 () Entity: plugins
0001.742 () restore_backup(backup_file=backup_2025-06-03-1044_Loan_City_1ecc1a7fe2fd-plugins.zip, type=plugins, info=a:3:{s:4:"path";s:32:"/var/www/html/wp-content/plugins";s:11:"description";s:7:"Plugins";s:20:"singular_description";s:6:"Plugin";}, last_one=)
0001.742 () Unpacking backup... (backup_2025-06-03-1044_Loan_City_1ecc1a7fe2fd-plugins.zip, 24.9 MB)
0001.744 () Unzipping backup_2025-06-03-1044_Loan_City_1ecc1a7fe2fd-plugins.zip to /var/www/html/wp-content/upgrade/1b362bdd/ using UpdraftPlus_ZipArchive, starting index 0
0001.982 () Unzip progress: 1444 out of 9337 files (29.1 KB, plugins/contact-form-7/assets/icon.png)
0002.242 () Unzip progress: 2445 out of 9337 files (21 MB, plugins/wpforms-lite/templates/admin/form-embed-wizard/popup.php)
0002.477 () Unzip progress: 3446 out of 9337 files (26.7 MB, plugins/wpforms-lite/vendor_prefixed/square/square/src/Models/CreateRefundRequest.php)
0002.668 () Unzip progress: 4447 out of 9337 files (29.1 MB, plugins/wpforms-lite/vendor_prefixed/square/square/src/Models/Builders/AddGroupToCustomerResponseBuilder.php)
0002.920 () Unzip progress: 5448 out of 9337 files (32.8 MB, plugins/wpforms-lite/vendor_prefixed/stripe/stripe-php/lib/V2/EventDestination.php)
0003.138 () Unzip progress: 6449 out of 9337 files (41.3 MB, plugins/wp-mail-smtp/assets/css/vendor/jquery-confirm.min.css)
0003.353 () Unzip progress: 7450 out of 9337 files (47.4 MB, plugins/gtranslate/flags/32/uz.png)
0003.570 () Unzip progress: 8451 out of 9337 files (56.5 MB, plugins/wordpress-seo/src/integrations/front-end/category-term-description.php)
0003.760 () Unzip progress: 9337 out of 9337 files (64 MB, plugins/updraftplus/includes/Google/IO/cacerts.pem)
0003.762 () Moving old data: filesystem method / updraft_dir is potentially possible
0003.762 () Moving old data: can potentially use wp_filesystem method / -old
0003.762 () Moving old data out of the way...
0003.941 () Top-level entities being moved: index.php, wp-rocket, wp-reviews-plugin-for-google, contact-form-cfdb7, contact-form-7, wp-mail-smtp, easy-utm-tracking-with-contact-form-7, updraftplus, utm-to-google-sheets, gtranslate, wordpress-seo, integration-for-contact-form-7-and-google-sheets
0003.944 () Moving unpacked backup into place...
0004.204 () Top-level entities being moved: index.php, wp-reviews-plugin-for-google, contact-form-cfdb7, contact-form-7, wpforms-lite, wp-mail-smtp, easy-utm-tracking-with-contact-form-7, updraftplus, google-site-kit, gtranslate, wordpress-seo, integration-for-contact-form-7-and-google-sheets
0004.207 () Cleaning up rubbish...
0004.210 () restore_backup(backup_file=backup_2025-06-03-1044_Loan_City_1ecc1a7fe2fd-plugins2.zip, type=plugins, info=a:3:{s:4:"path";s:32:"/var/www/html/wp-content/plugins";s:11:"description";s:7:"Plugins";s:20:"singular_description";s:6:"Plugin";}, last_one=)
0004.211 () Unpacking backup... (backup_2025-06-03-1044_Loan_City_1ecc1a7fe2fd-plugins2.zip, 10.6 MB)
0004.211 () Unzipping backup_2025-06-03-1044_Loan_City_1ecc1a7fe2fd-plugins2.zip to /var/www/html/wp-content/upgrade/8ddac3ea/ using UpdraftPlus_ZipArchive, starting index 0
0004.613 () Unzip progress: 1002 out of 2728 files (20.4 MB, plugins/updraftplus/vendor/guzzle/guzzle/src/Guzzle/Batch/BatchCommandTransfer.php)
0004.862 () Unzip progress: 2003 out of 2728 files (33.8 MB, plugins/google-site-kit/third-party/google/apiclient-services/src/TagManager/Container.php)
0005.003 () Unzip progress: 2728 out of 2728 files (37 MB, plugins/google-site-kit/readme.txt)
0005.003 () Moving unpacked backup into place...
0005.088 () Top-level entities being moved: updraftplus, google-site-kit
0005.589 () Cleaning up rubbish...
0005.629 () Entity: themes
0005.629 () restore_backup(backup_file=backup_2025-06-03-1044_Loan_City_1ecc1a7fe2fd-themes.zip, type=themes, info=a:3:{s:4:"path";s:31:"/var/www/html/wp-content/themes";s:11:"description";s:6:"Themes";s:20:"singular_description";s:5:"Theme";}, last_one=)
0005.629 () Unpacking backup... (backup_2025-06-03-1044_Loan_City_1ecc1a7fe2fd-themes.zip, 1.5 MB)
0005.629 () Unzipping backup_2025-06-03-1044_Loan_City_1ecc1a7fe2fd-themes.zip to /var/www/html/wp-content/upgrade/205546a2/ using UpdraftPlus_ZipArchive, starting index 0
0005.680 () Unzip progress: 190 out of 190 files (2.1 MB, themes/loan-city/loan-application-html-rendered.html)
0005.680 () Moving old data: filesystem method / updraft_dir is potentially possible
0005.680 () Moving old data: can potentially use wp_filesystem method / -old
0005.680 () Moving old data out of the way...
0005.698 () Top-level entities being moved: index.php, loan-city, twentytwentyfive, twentytwentyfour, twentytwentythree
0005.699 () Moving unpacked backup into place...
0005.704 () Top-level entities being moved: loan-city
0005.704 () Cleaning up rubbish...
0005.710 () Entity: uploads
0005.710 () restore_backup(backup_file=backup_2025-06-03-1044_Loan_City_1ecc1a7fe2fd-uploads.zip, type=uploads, info=a:2:{s:4:"path";s:32:"/var/www/html/wp-content/uploads";s:11:"description";s:7:"Uploads";}, last_one=)
0005.710 () Unpacking backup... (backup_2025-06-03-1044_Loan_City_1ecc1a7fe2fd-uploads.zip, 9.1 MB)
0005.710 () Unzipping backup_2025-06-03-1044_Loan_City_1ecc1a7fe2fd-uploads.zip to /var/www/html/wp-content/upgrade/f8ecc748/ using UpdraftPlus_ZipArchive, starting index 0
0005.783 () Unzip progress: 90 out of 90 files (10.8 MB, uploads/2025/04/Offers-600x400.png)
0005.784 () Moving old data: filesystem method / updraft_dir is potentially possible
0005.784 () Moving old data: can potentially use wp_filesystem method / -old
0005.784 () Moving old data out of the way...
0005.792 () Moving unpacked backup into place...
0005.796 () Cleaning up rubbish...
0005.802 () Entity: others
0005.802 () restore_backup(backup_file=backup_2025-06-03-1044_Loan_City_1ecc1a7fe2fd-others.zip, type=others, info=a:2:{s:4:"path";s:24:"/var/www/html/wp-content";s:11:"description";s:6:"Others";}, last_one=1)
0005.803 () Unpacking backup... (backup_2025-06-03-1044_Loan_City_1ecc1a7fe2fd-others.zip, 0 MB)
0005.803 () Unzipping backup_2025-06-03-1044_Loan_City_1ecc1a7fe2fd-others.zip to /var/www/html/wp-content/upgrade/89d57fef/ using UpdraftPlus_ZipArchive, starting index 0
0005.807 () Unzip progress: 4 out of 4 files (28 B, index.php)
0005.807 () Cleaning up rubbish...
0005.808 () PHP event: code E_WARNING: rmdir(/var/www/html/wp-content/upgrade/89d57fef): Directory not empty (line 386, wp-admin/includes/class-wp-filesystem-direct.php)
0005.808 () Error: Failed to delete working directory after restoring. (/var/www/html/wp-content/upgrade/89d57fef)
0005.823 () Purging cache directory: /var/www/html/wp-content/cache
0005.828 () Restore successful!
0005.828 () Restore successful
