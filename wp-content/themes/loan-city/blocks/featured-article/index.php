<?php
/**
 * Featured Article Block
 *
 * @package Loan_City
 */

/**
 * Registers the block using the metadata loaded from the `block.json` file.
 * Behind the scenes, it registers also all assets so they can be enqueued
 * through the block editor in the corresponding context.
 */
function loan_city_featured_article_block_init() {
    // Register the block
    register_block_type( __DIR__, array(
        'render_callback' => 'loan_city_render_featured_article_block',
        'attributes' => array(
            'articleId' => array(
                'type' => 'number',
                'default' => 0,
            ),
            'title' => array(
                'type' => 'string',
                'default' => 'Featured',
            ),
        ),
    ) );

    // Register editor script
    wp_register_script(
        'loan-city-featured-article-editor',
        get_template_directory_uri() . '/blocks/featured-article/editor.js',
        array( 'wp-blocks', 'wp-element', 'wp-editor', 'wp-components', 'wp-data', 'wp-i18n', 'wp-block-editor' ),
        filemtime( get_template_directory() . '/blocks/featured-article/editor.js' )
    );
}
add_action( 'init', 'loan_city_featured_article_block_init' );

/**
 * Render callback function.
 *
 * @param array    $attributes Block attributes.
 * @param string   $content    Block content.
 * @return string Rendered block HTML.
 */
function loan_city_render_featured_article_block( $attributes, $content ) {
    // Get block attributes
    $article_id = 0;

    // Debug information
    $debug_info = '';
    $debug_enabled = false; // Disable debugging after fix

    // Try different ways to get the article ID
    if (isset($attributes['articleId'])) {
        $article_id = intval($attributes['articleId']);
        if ($debug_enabled) $debug_info .= "Found articleId in attributes: {$article_id}<br>";
    } elseif (isset($attributes['articleid'])) {
        // Try lowercase version
        $article_id = intval($attributes['articleid']);
        if ($debug_enabled) $debug_info .= "Found articleid in attributes: {$article_id}<br>";
    } elseif (isset($attributes['article_id'])) {
        // Try with underscore
        $article_id = intval($attributes['article_id']);
        if ($debug_enabled) $debug_info .= "Found article_id in attributes: {$article_id}<br>";
    }

    // If article ID is still 0, try to extract it from the block content
    if ($article_id === 0 && !empty($content)) {
        // Try to extract the article ID from the block content
        if (preg_match('/<!-- wp:loan-city\/featured-article\s+(\{.*?\})\s+\/-->/', $content, $matches)) {
            if (!empty($matches[1])) {
                $block_data = json_decode($matches[1], true);
                if (isset($block_data['articleId']) && intval($block_data['articleId']) > 0) {
                    $article_id = intval($block_data['articleId']);
                    if ($debug_enabled) $debug_info .= "Found articleId in block content: {$article_id}<br>";
                }
            }
        }
    }

    // If article ID is still 0, try to get it from the global post
    if ($article_id === 0) {
        global $post;
        if ($post && $post->post_content) {
            if (preg_match('/<!-- wp:loan-city\/featured-article\s+(\{.*?\})\s+\/-->/', $post->post_content, $matches)) {
                if (!empty($matches[1])) {
                    $block_data = json_decode($matches[1], true);
                    if (isset($block_data['articleId']) && intval($block_data['articleId']) > 0) {
                        $article_id = intval($block_data['articleId']);
                        if ($debug_enabled) $debug_info .= "Found articleId in global post: {$article_id}<br>";
                    }
                }
            }
        }
    }

    // Check if the article exists and is published
    if ($article_id > 0) {
        $article = get_post($article_id);
        if (!$article || $article->post_status !== 'publish') {
            if ($debug_enabled) $debug_info .= "Article ID {$article_id} does not exist or is not published<br>";
            $article_id = 0; // Reset to 0 so we can try to find another article
        }
    }

    $title = isset($attributes['title']) ? $attributes['title'] : 'Featured';

    // If no article is selected, try to get the latest article
    if (!$article_id) {
        if ($debug_enabled) $debug_info .= "No valid article ID found, trying to get latest article<br>";

        // Get the latest article
        $latest_articles = get_posts(array(
            'post_type' => 'article',
            'post_status' => 'publish',
            'numberposts' => 1,
            'orderby' => 'date',
            'order' => 'DESC',
        ));

        if (!empty($latest_articles)) {
            $article_id = $latest_articles[0]->ID;
            if ($debug_enabled) $debug_info .= "Using latest article ID: {$article_id}<br>";
        } else {
            // If still no article, return a message with debug info
            if ($debug_enabled) {
                return '<div class="featured-article-block"><p>' . esc_html__('Please select an article to feature in the block settings.', 'loan-city') . '</p><div style="background: #f8f8f8; padding: 10px; border: 1px solid #ddd; margin-top: 10px; font-family: monospace; font-size: 12px;">Debug info:<br>' . $debug_info . '</div></div>';
            } else {
                return '<div class="featured-article-block"><p>' . esc_html__('Please select an article to feature in the block settings. Click on the block and look for the Featured Article Settings in the right sidebar.', 'loan-city') . '</p></div>';
            }
        }
    }

    // Get the article
    $article = get_post($article_id);

    if ($debug_enabled) $debug_info .= "Final article ID being used: {$article_id}<br>";
    if ($debug_enabled && $article) $debug_info .= "Article title: " . esc_html($article->post_title) . "<br>";

    // Add a hidden comment for debugging purposes (visible in page source)
    $debug_comment = "<!-- Featured Article Debug: ID={$article_id}, Title=" . esc_attr($article->post_title) . " -->";

    // Check if the article exists
    if (!$article) {
        if ($debug_enabled) {
            return '<div class="featured-article-block"><p>' . esc_html__('The selected article could not be found.', 'loan-city') . '</p><div style="background: #f8f8f8; padding: 10px; border: 1px solid #ddd; margin-top: 10px; font-family: monospace; font-size: 12px;">Debug info:<br>' . $debug_info . '</div></div>';
        } else {
            return '<div class="featured-article-block"><p>' . esc_html__('The selected article (ID: ' . $article_id . ') could not be found. Please select a different article.', 'loan-city') . '</p></div>';
        }
    }

    // Check if the article is published
    if ($article->post_status !== 'publish') {
        if ($debug_enabled) {
            return '<div class="featured-article-block"><p>' . esc_html__('The selected article is not published.', 'loan-city') . '</p><div style="background: #f8f8f8; padding: 10px; border: 1px solid #ddd; margin-top: 10px; font-family: monospace; font-size: 12px;">Debug info:<br>' . $debug_info . '</div></div>';
        } else {
            return '<div class="featured-article-block"><p>' . esc_html__('The selected article is not published. Please select a published article.', 'loan-city') . '</p></div>';
        }
    }

    // Accept both 'article' and 'post' post types
    if ($article->post_type !== 'article' && $article->post_type !== 'post') {
        return '<div class="featured-article-block"><p>' . esc_html__('The selected content (ID: ' . $article_id . ') is not an article. It is a ' . $article->post_type . '. Please select an article from the Articles section.', 'loan-city') . '</p></div>';
    }

    // Get article details
    $article_title = get_the_title($article_id);
    $article_excerpt = get_the_excerpt($article_id);
    $article_date = get_the_date('', $article_id);
    $article_link = get_permalink($article_id);

    // Add post type indicator to the title if it's a regular post
    $post_type_label = '';
    if ($article->post_type === 'post') {
        $post_type_label = ' <span style="font-size: 16px; color: #828282;">(WordPress Post)</span>';
    }

    // Get featured image
    $featured_image = '';
    if (has_post_thumbnail($article_id)) {
        $featured_image = get_the_post_thumbnail_url($article_id, 'loan-city-featured');
    } else {
        $featured_image = get_template_directory_uri() . '/assets/images/placeholder-image.jpg';
    }

    // Start output buffering
    ob_start();
    ?>
    <?php echo $debug_comment; ?>
    <div class="featured-article-block">
        <?php if ($debug_enabled): ?>
        <div style="background: #f8f8f8; padding: 10px; border: 1px solid #ddd; margin-bottom: 10px; font-family: monospace; font-size: 12px;">
            <strong>Debug info:</strong><br>
            <?php echo $debug_info; ?>
        </div>
        <?php endif; ?>

        <div class="featured-article-content wp-block-columns" style="gap: 135px;">
            <div class="featured-article-image wp-block-column" style="flex-basis:50%">
                <figure class="featured-article-img" style="margin: 0;">
                    <img src="<?php echo esc_url($featured_image); ?>" alt="<?php echo esc_attr($article_title); ?>" style="width: 100%; height: 600px; min-height: 600px; object-fit: cover;" />
                </figure>
            </div>

            <div class="featured-article-details wp-block-column" style="flex-basis:50%; display: flex; flex-direction: column; justify-content: center;">
                <h2 style="color:#F4333D;font-size:64px;font-weight:600;margin-top:0;margin-bottom:24px;"><?php echo esc_html($title); ?></h2>

                <h3 style="color:#000000;font-size:24px;font-weight:500;margin-top:0;margin-bottom:16px;"><?php echo esc_html($article_title) . $post_type_label; ?></h3>

                <p style="color:#828282;font-size:20px;font-weight:400;margin-top:0;margin-bottom:16px;"><?php echo esc_html($article_excerpt); ?></p>

                <p style="color:#828282;font-size:16px;font-weight:400;margin-top:0;margin-bottom:24px;"><?php echo esc_html($article_date); ?></p>

                <div class="wp-block-buttons">
                    <div class="wp-block-button" style="width: 100%;">
                        <a class="wp-block-button__link" href="<?php echo esc_url($article_link); ?>" style="background-color:#F4333D;color:#FFFFFF;border-radius:4px;width:100%;text-align:center;padding:12px 24px;display:block;">Read more</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php
    // Get the buffered content
    return ob_get_clean();
}
