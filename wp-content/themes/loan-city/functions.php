<?php
/**
 * Loan City Theme functions and definitions
 *
 * @link https://developer.wordpress.org/themes/basics/theme-functions/
 *
 * @package Loan_City
 */

if ( ! defined( 'LOAN_CITY_VERSION' ) ) {
	define( 'LOAN_CITY_VERSION', '1.0.0' );
}

/**
 * Sets up theme defaults and registers support for various WordPress features.
 */
function loan_city_setup() {
	// Load theme text domain
	load_theme_textdomain( 'loan-city', get_template_directory() . '/languages' );

	// Add default posts and comments RSS feed links to head.
	add_theme_support( 'automatic-feed-links' );

	// Let WordPress manage the document title.
	add_theme_support( 'title-tag' );

	// Enable support for Post Thumbnails on posts and pages.
	add_theme_support( 'post-thumbnails' );

	// Add support for Block Styles.
	add_theme_support( 'wp-block-styles' );

	// Add support for editor styles.
	add_theme_support( 'editor-styles' );

	// Enqueue editor styles.
	add_editor_style( 'assets/css/editor-style.css' );

	// Add support for responsive embedded content.
	add_theme_support( 'responsive-embeds' );

	// Register nav menus.
	register_nav_menus(
		array(
			'primary' => __( 'Primary Menu', 'loan-city' ),
			'footer'  => __( 'Footer Menu', 'loan-city' ),
		)
	);

	// Add theme support for selective refresh for widgets.
	add_theme_support( 'customize-selective-refresh-widgets' );

	// Add support for custom logo with SVG support.
	add_theme_support(
		'custom-logo',
		array(
			'height'      => 250,
			'width'       => 250,
			'flex-width'  => true,
			'flex-height' => true,
			'unlink-homepage-logo' => false, // Keep the logo linked on the homepage
		)
	);
}
add_action( 'after_setup_theme', 'loan_city_setup' );

/**
 * Enqueue scripts and styles.
 */
function loan_city_scripts() {
	wp_enqueue_style( 'loan-city-style', get_stylesheet_uri(), array(), LOAN_CITY_VERSION );

	// Add custom fonts
	wp_enqueue_style( 'loan-city-fonts', 'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Righteous&display=swap', array(), null );

	// Add component styles
	wp_enqueue_style( 'loan-city-promo-block', get_template_directory_uri() . '/assets/css/promo-block.css', array(), LOAN_CITY_VERSION );
	wp_enqueue_style( 'loan-city-partnership-logos-slider', get_template_directory_uri() . '/assets/css/partnership-logos-slider.css', array(), LOAN_CITY_VERSION );
	wp_enqueue_style( 'loan-city-testimonials-slider', get_template_directory_uri() . '/assets/css/testimonials-slider.css', array(), LOAN_CITY_VERSION );
	wp_enqueue_style( 'loan-city-reviews-slider', get_template_directory_uri() . '/assets/css/reviews-slider.css', array(), LOAN_CITY_VERSION );
	wp_enqueue_style( 'loan-city-how-it-works', get_template_directory_uri() . '/assets/css/how-it-works.css', array(), LOAN_CITY_VERSION );
	wp_enqueue_style( 'loan-city-loan-offer-comparison', get_template_directory_uri() . '/assets/css/loan-offer-comparison.css', array(), LOAN_CITY_VERSION );
	wp_enqueue_style( 'loan-city-articles-block', get_template_directory_uri() . '/assets/css/articles-block.css', array(), LOAN_CITY_VERSION );
	wp_enqueue_style( 'loan-city-featured-article-block', get_template_directory_uri() . '/assets/css/featured-article-block.css', array(), LOAN_CITY_VERSION );
	wp_enqueue_style( 'loan-city-latest-articles', get_template_directory_uri() . '/assets/css/latest-articles.css', array(), LOAN_CITY_VERSION );
	wp_enqueue_style( 'loan-city-faq-styles', get_template_directory_uri() . '/assets/css/faq-styles.css', array(), LOAN_CITY_VERSION );

	// FAQ Toggle script - enables collapsible FAQ functionality
	wp_enqueue_script( 'loan-city-faq-toggle', get_template_directory_uri() . '/assets/js/faq-toggle.js', array('jquery'), LOAN_CITY_VERSION, true );

	// Slick Slider CSS and JS from CDN
	wp_enqueue_style( 'slick-slider-css', 'https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.css', array(), '1.8.1' );
	wp_enqueue_style( 'slick-slider-theme-css', 'https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick-theme.css', array(), '1.8.1' );
	wp_enqueue_script( 'slick-slider-js', 'https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.min.js', array('jquery'), '1.8.1', true );

	// Main theme script
	wp_enqueue_script( 'loan-city-script', get_template_directory_uri() . '/assets/js/main.js', array(), LOAN_CITY_VERSION, true );

	// Traditional menu script
	wp_enqueue_script( 'loan-city-traditional-menu', get_template_directory_uri() . '/assets/js/traditional-menu.js', array('jquery'), LOAN_CITY_VERSION, true );

	// Slick Slider implementations
	wp_enqueue_script( 'loan-city-testimonials-slider', get_template_directory_uri() . '/assets/js/testimonials-slider.js', array('jquery', 'slick-slider-js'), LOAN_CITY_VERSION, true );

	// Use the safe version of the partnership logos slider
	wp_enqueue_script( 'loan-city-partnership-logos-safe', get_template_directory_uri() . '/assets/js/partnership-logos-safe.js', array('jquery', 'slick-slider-js'), LOAN_CITY_VERSION, true );

	// Form controls for Contact Form 7 - load on all pages with forms
	wp_enqueue_style( 'loan-city-contact-form-styles', get_template_directory_uri() . '/assets/css/contact-form-styles.css', array(), LOAN_CITY_VERSION );
	wp_enqueue_style( 'loan-city-contact-map', get_template_directory_uri() . '/assets/css/contact-map.css', array(), LOAN_CITY_VERSION );

	// UTM tracking script for Contact Form 7 hidden fields
	wp_enqueue_script( 'loan-city-utm-hidden-fields', get_template_directory_uri() . '/assets/js/utm-hidden-fields.js', array('jquery'), LOAN_CITY_VERSION, true );

	// Consolidated form styles for loan application
	wp_enqueue_style( 'loan-city-consolidated-form', get_template_directory_uri() . '/assets/css/consolidated-loan-form.css', array(), LOAN_CITY_VERSION );

	// Contact Form 7 button text fix - changes "Send" to "Submit"
	wp_enqueue_script( 'loan-city-cf7-button-fix', get_template_directory_uri() . '/assets/js/cf7-button-fix.js', array(), LOAN_CITY_VERSION, true );

	// Menu override styles - load with high priority to override other styles
	wp_enqueue_style( 'loan-city-menu-override', get_template_directory_uri() . '/assets/css/menu-override.css', array(), LOAN_CITY_VERSION );

	// Menu fix script - ensures menu styling is applied correctly
	wp_enqueue_script( 'loan-city-menu-fix', get_template_directory_uri() . '/assets/js/menu-fix.js', array('jquery'), LOAN_CITY_VERSION, true );

	// Responsive header styles and script
	wp_enqueue_style( 'loan-city-responsive-header', get_template_directory_uri() . '/assets/css/responsive-header.css', array(), LOAN_CITY_VERSION );
	wp_enqueue_script( 'loan-city-responsive-header', get_template_directory_uri() . '/assets/js/responsive-header.js', array('jquery'), LOAN_CITY_VERSION, true );

	// Mobile footer script
	wp_enqueue_script( 'loan-city-mobile-footer', get_template_directory_uri() . '/assets/js/mobile-footer.js', array('jquery'), LOAN_CITY_VERSION, true );

	// Footer menu styles
	wp_enqueue_style( 'loan-city-footer-menu', get_template_directory_uri() . '/assets/css/footer-menu.css', array(), LOAN_CITY_VERSION );

	// Loan Application page script
	if (is_page_template('templates/loan-application.html')) {
		// Enqueue jQuery UI datepicker
		wp_enqueue_style( 'jquery-ui-style', get_template_directory_uri() . '/assets/css/date-picker.css', array(), LOAN_CITY_VERSION );

		// Enqueue custom validation script first
		wp_enqueue_script( 'loan-city-loan-application-validation', get_template_directory_uri() . '/assets/js/loan-application-validation.js', array('jquery'), LOAN_CITY_VERSION, true );

		// Enqueue multi-step form scripts (depends on validation script)
		wp_enqueue_script( 'loan-city-multi-step-form', get_template_directory_uri() . '/assets/js/multi-step-form.js', array('jquery', 'loan-city-loan-application-validation'), LOAN_CITY_VERSION, true );

		wp_enqueue_script( 'jquery-ui-datepicker' ); // WordPress built-in
		wp_enqueue_script( 'loan-city-date-picker', get_template_directory_uri() . '/assets/js/date-picker.js', array('jquery', 'jquery-ui-datepicker'), LOAN_CITY_VERSION, true );
		wp_enqueue_script( 'loan-city-form-focus-handler', get_template_directory_uri() . '/assets/js/form-focus-handler.js', array('jquery'), LOAN_CITY_VERSION, true );
		wp_enqueue_script( 'loan-city-loan-application', get_template_directory_uri() . '/assets/js/loan-application.js', array('jquery'), LOAN_CITY_VERSION, true );

		// Enqueue select option styling script
		wp_enqueue_script( 'loan-city-select-option-styling', get_template_directory_uri() . '/assets/js/select-option-styling.js', array('jquery'), LOAN_CITY_VERSION, true );


		// Enqueue form redirect scripts - using multiple approaches for reliability
		wp_enqueue_script( 'loan-city-form-redirect', get_template_directory_uri() . '/assets/js/form-redirect.js', array('jquery'), LOAN_CITY_VERSION, true );
		wp_enqueue_script( 'loan-city-loan-application-redirect', get_template_directory_uri() . '/assets/js/loan-application-redirect.js', array('jquery'), LOAN_CITY_VERSION, true );

		// Add inline script as a final fallback for form redirect
		wp_add_inline_script( 'loan-city-loan-application-redirect', '
			document.addEventListener("wpcf7mailsent", function() {
				setTimeout(function() { window.location.href = "/thank-you/"; }, 1000);
			});
		');
	}
}
add_action( 'wp_enqueue_scripts', 'loan_city_scripts' );

/**
 * This function has been removed as we now position labels at the top left corner by default
 * using the form-label-fix.css file. No JavaScript manipulation is needed.
 */
function loan_city_fix_select_boxes() {
	// Function kept as a placeholder but not used
	// All label positioning is now handled via CSS
}
// Action hook removed as this function is no longer needed

/**
 * Enqueue editor assets.
 */
function loan_city_editor_assets() {
	wp_enqueue_script(
		'loan-city-editor-script',
		get_template_directory_uri() . '/assets/js/editor.js',
		array( 'wp-blocks', 'wp-dom-ready', 'wp-edit-post' ),
		LOAN_CITY_VERSION,
		true
	);
}
add_action( 'enqueue_block_editor_assets', 'loan_city_editor_assets' );

/**
 * Register block patterns.
 */
function loan_city_register_block_patterns() {
	if ( function_exists( 'register_block_pattern_category' ) ) {
		register_block_pattern_category(
			'loan-city',
			array( 'label' => __( 'Loan City', 'loan-city' ) )
		);
	}
}
add_action( 'init', 'loan_city_register_block_patterns' );

/**
 * Add admin page for theme documentation.
 */
function loan_city_add_admin_page() {
	add_theme_page(
		__( 'Loan City Theme Guide', 'loan-city' ),
		__( 'Loan City Guide', 'loan-city' ),
		'edit_theme_options',
		'loan-city-guide',
		'loan_city_admin_page_content'
	);
}
add_action( 'admin_menu', 'loan_city_add_admin_page' );

/**
 * Display admin page content.
 */
function loan_city_admin_page_content() {
	require_once get_template_directory() . '/inc/admin-page.php';
}

/**
 * Include additional files.
 */
require get_template_directory() . '/inc/template-functions.php';
require get_template_directory() . '/inc/block-patterns.php';
require get_template_directory() . '/inc/custom-post-types.php';
require get_template_directory() . '/inc/cf7-customizations.php';
require get_template_directory() . '/inc/create-thank-you-page.php';
require get_template_directory() . '/inc/translation.php';
require get_template_directory() . '/inc/contact-info.php';
require get_template_directory() . '/inc/social-media-admin.php';

// Translation script is now loaded via wp_enqueue_script in the loan_city_scripts function

// Include sample generators (remove these in production)
// Sample reviews disabled since we're now using Google reviews plugin
// require get_template_directory() . '/sample-reviews.php';
require get_template_directory() . '/sample-faqs.php';
require get_template_directory() . '/sample-social-media.php';

/**
 * Create the Blog page if it doesn't exist and ensure it has the correct template and content.
 */
function loan_city_create_blog_page() {
    // Check if the page already exists
    $blog_page = get_page_by_path('blog');

    // Check if page exists

    // If the page doesn't exist, create it
    if (!$blog_page) {
        // Get a valid article ID for the featured article
        $articles = get_posts(array(
            'post_type' => 'article',
            'post_status' => 'publish',
            'numberposts' => 1,
            'orderby' => 'date',
            'order' => 'DESC',
        ));

        $article_id = 0;
        if (!empty($articles)) {
            $article_id = $articles[0]->ID;
        } else {
            // Try regular posts as fallback
            $posts = get_posts(array(
                'post_type' => 'post',
                'post_status' => 'publish',
                'numberposts' => 1,
                'orderby' => 'date',
                'order' => 'DESC',
            ));
            if (!empty($posts)) {
                $article_id = $posts[0]->ID;
            }
        }

        // Create the page content with the complete structure
        $page_content = '<!-- wp:loan-city/featured-article {"articleId":' . $article_id . ',"title":"Featured"} /-->

<!-- wp:spacer {"height":"56px"} -->
<div style="height:56px" aria-hidden="true" class="wp-block-spacer"></div>
<!-- /wp:spacer -->

<!-- wp:shortcode -->
[latest_articles_exclude_featured count="6"]
<!-- /wp:shortcode -->';

        // Create the page
        $page_id = wp_insert_post(
            array(
                'post_title'    => 'Blog',
                'post_name'     => 'blog',
                'post_status'   => 'publish',
                'post_type'     => 'page',
                'post_content'  => $page_content,
                'page_template' => 'articles-page.html',
            )
        );

        // Set the page template
        if ($page_id) {
            update_post_meta($page_id, '_wp_page_template', 'articles-page.html');
        }
    } else {
        // Page exists, but check if it has the correct content structure
        $content = $blog_page->post_content;

        // Check if page has correct content structure

        // Check if the page contains a featured article block without article ID
        if (strpos($content, 'wp:loan-city/featured-article') !== false &&
            !preg_match('/"articleId":\d+/', $content)) {

            // Get a valid article ID
            $articles = get_posts(array(
                'post_type' => 'article',
                'post_status' => 'publish',
                'numberposts' => 1,
                'orderby' => 'date',
                'order' => 'DESC',
            ));

            $article_id = 0;
            if (!empty($articles)) {
                $article_id = $articles[0]->ID;
            } else {
                // Try regular posts as fallback
                $posts = get_posts(array(
                    'post_type' => 'post',
                    'post_status' => 'publish',
                    'numberposts' => 1,
                    'orderby' => 'date',
                    'order' => 'DESC',
                ));
                if (!empty($posts)) {
                    $article_id = $posts[0]->ID;
                }
            }

            if ($article_id > 0) {
                // Replace the block with one that has the article ID
                $new_content = preg_replace(
                    '/<!-- wp:loan-city\/featured-article\s*\/-->/',
                    '<!-- wp:loan-city/featured-article {"articleId":' . $article_id . ',"title":"Featured"} /-->',
                    $content
                );

                // Update the page content if it changed
                if ($new_content !== $content) {
                    wp_update_post(array(
                        'ID' => $blog_page->ID,
                        'post_content' => $new_content,
                    ));
                }
            }
        }

        // Ensure the page has the correct template
        $current_template = get_page_template_slug($blog_page->ID);
        if ($current_template !== 'articles-page.html') {
            update_post_meta($blog_page->ID, '_wp_page_template', 'articles-page.html');
        }
    }
}

// Run the function when the theme is activated
add_action('after_switch_theme', 'loan_city_create_blog_page');

// Also run the function on init to ensure the page exists
add_action('init', 'loan_city_check_blog_page');



/**
 * Shortcode to render blog page content in archive template
 */
function loan_city_blog_page_content_shortcode() {
    // Get the blog page
    $blog_page = get_page_by_path('blog');

    if (!$blog_page) {
        // Fallback to default content if page doesn't exist
        return '<!-- wp:loan-city/featured-article /-->

        <!-- wp:spacer {"height":"56px"} -->
        <div style="height:56px" aria-hidden="true" class="wp-block-spacer"></div>
        <!-- /wp:spacer -->

        <!-- wp:shortcode -->
        [latest_articles_exclude_featured count="6"]
        <!-- /wp:shortcode -->';
    }

    // Get the page content
    $content = $blog_page->post_content;

    // Check if content has the complete structure and fix if needed
    if (strpos($content, 'latest_articles_exclude_featured') === false) {
        // Add the missing latest articles section
        $content .= '

        <!-- wp:spacer {"height":"56px"} -->
        <div style="height:56px" aria-hidden="true" class="wp-block-spacer"></div>
        <!-- /wp:spacer -->

        <!-- wp:shortcode -->
        [latest_articles_exclude_featured count="6"]
        <!-- /wp:shortcode -->';

        // Update the page content to include the missing section
        wp_update_post(array(
            'ID' => $blog_page->ID,
            'post_content' => $content,
        ));
    }

    // Process WordPress blocks
    $content = do_blocks($content);

    // Apply content filters
    $content = apply_filters('the_content', $content);

    return $content;
}
add_shortcode('blog_page_content', 'loan_city_blog_page_content_shortcode');

/**
 * Check if the Blog page exists and create it if it doesn't
 */
function loan_city_check_blog_page() {
    // Only run this check once per hour to avoid performance issues
    if (get_transient('loan_city_blog_page_checked')) {
        return;
    }

    // Create the page if it doesn't exist or fix it if needed
    loan_city_create_blog_page();

    // Set transient to prevent running this check too frequently
    set_transient('loan_city_blog_page_checked', true, HOUR_IN_SECONDS);
}

/**
 * Check if there are any articles in the database and create a sample article if none exist.
 * This helps ensure that the featured article block has content to display.
 */
function loan_city_check_articles() {
    // Only run this check once
    if (get_option('loan_city_articles_checked')) {
        return;
    }

    // Check if there are any articles
    $articles = get_posts(array(
        'post_type' => 'article',
        'post_status' => 'publish',
        'numberposts' => 1,
    ));

    // If no articles exist, create a sample article
    if (empty($articles)) {
        // Create a sample article
        $article_id = wp_insert_post(array(
            'post_title' => 'Sample Article',
            'post_content' => 'This is a sample article created automatically to ensure the featured article block has content to display.',
            'post_status' => 'publish',
            'post_type' => 'article',
            'post_excerpt' => 'This is a sample article created automatically.',
        ));
    }

    // Mark as checked
    update_option('loan_city_articles_checked', true);
}
add_action('init', 'loan_city_check_articles');

/**
 * Register custom blocks.
 */
function loan_city_register_blocks() {
	register_block_type( get_template_directory() . '/blocks/loan-calculator' );

	// The featured-article block is registered in its own index.php file
}
add_action( 'init', 'loan_city_register_blocks' );

/**
 * Include block files.
 */
require get_template_directory() . '/blocks/featured-article/index.php';

/**
 * Register a shortcode for the partnership logos slider.
 * This allows us to use [partnership_logos_slider] in any content area.
 *
 * @return string HTML output of the partnership logos slider.
 */
function loan_city_partnership_logos_shortcode() {
    ob_start();
    include get_template_directory() . '/patterns/partnership-logos-simple.php';
    return ob_get_clean();
}
add_shortcode('partnership_logos_slider', 'loan_city_partnership_logos_shortcode');

/**
 * Helper function to ensure featured article blocks have valid article IDs.
 * This function runs during initialization to fix any issues with featured article blocks.
 */
function loan_city_ensure_featured_article_blocks() {
    // Force run by deleting the transient
    delete_transient('loan_city_featured_article_checked');

    // Set a transient to prevent running this check too frequently after this run
    set_transient('loan_city_featured_article_checked', true, HOUR_IN_SECONDS);

    // Get a valid article ID
    $articles = get_posts(array(
        'post_type' => 'article',
        'post_status' => 'publish',
        'numberposts' => 1,
        'orderby' => 'date',
        'order' => 'DESC',
    ));

    if (empty($articles)) {
        // Try regular posts as fallback
        $articles = get_posts(array(
            'post_type' => 'post',
            'post_status' => 'publish',
            'numberposts' => 1,
            'orderby' => 'date',
            'order' => 'DESC',
        ));
    }

    // If we have a valid article, use it to fix any broken featured article blocks
    if (!empty($articles)) {
        $article_id = $articles[0]->ID;

        // Get all pages that might contain the featured article block
        $pages = get_posts(array(
            'post_type' => 'page',
            'post_status' => 'publish',
            'posts_per_page' => -1,
        ));

        foreach ($pages as $page) {
            $content = $page->post_content;

            // Check if the page contains a featured article block with no article ID or article ID is 0
            if (strpos($content, 'wp:loan-city/featured-article') !== false &&
                (strpos($content, '"articleId":0') !== false ||
                 (!preg_match('/"articleId":\d+/', $content) &&
                  !preg_match('/<!-- wp:loan-city\/featured-article \/>/', $content)))) {

                // Replace the block with a new one that has the article ID
                $new_content = preg_replace(
                    '/<!-- wp:loan-city\/featured-article\s+(\{.*?\})\s+\/-->/',
                    '<!-- wp:loan-city/featured-article {"articleId":' . $article_id . ',"title":"Featured"} /-->',
                    $content
                );

                // Update the page content
                if ($new_content !== $content) {
                    wp_update_post(array(
                        'ID' => $page->ID,
                        'post_content' => $new_content,
                    ));
                }
            }
        }

        // Also check template files
        $template_directory = get_template_directory();
        $template_files = glob($template_directory . '/templates/*.html');

        foreach ($template_files as $template_file) {
            $template_content = file_get_contents($template_file);

            // We'll skip modifying template files to allow for dynamic article selection
            // This ensures that the featured article block in templates can use the latest article
            // or the article selected in the block editor
            continue;
        }
    }
}
add_action('init', 'loan_city_ensure_featured_article_blocks');

/**
 * Enable SVG uploads.
 *
 * @param array $mimes Allowed mime types.
 * @return array Modified mime types.
 */
function loan_city_enable_svg_upload( $mimes ) {
	$mimes['svg'] = 'image/svg+xml';
	$mimes['svgz'] = 'image/svg+xml';
	return $mimes;
}
add_filter( 'upload_mimes', 'loan_city_enable_svg_upload' );

/**
 * Add SVG to allowed file types for the WordPress Media Library.
 *
 * @param array  $data File data.
 * @param array  $file File object.
 * @param string $filename File name.
 * @param array  $mimes Allowed mime types.
 * @return array Modified file data.
 */
function loan_city_check_svg_upload( $data, $file, $filename, $mimes ) {
	$filetype = wp_check_filetype( $filename, $mimes );

	return [
		'ext'             => $filetype['ext'],
		'type'            => $filetype['type'],
		'proper_filename' => $data['proper_filename'],
	];
}
add_filter( 'wp_check_filetype_and_ext', 'loan_city_check_svg_upload', 10, 4 );

/**
 * Sanitize SVG uploads to prevent security issues.
 *
 * @param array $file The file data.
 * @return array The file data.
 */
function loan_city_sanitize_svg( $file ) {
	if ( $file['type'] === 'image/svg+xml' ) {
		// Read the file
		$file_content = file_get_contents( $file['tmp_name'] );

		// Basic sanitization - remove scripts, iframes, etc.
		$file_content = preg_replace( '/<script\b[^>]*>(.*?)<\/script>/is', '', $file_content );
		$file_content = preg_replace( '/<iframe\b[^>]*>(.*?)<\/iframe>/is', '', $file_content );
		$file_content = preg_replace( '/<embed\b[^>]*>(.*?)<\/embed>/is', '', $file_content );
		$file_content = preg_replace( '/<object\b[^>]*>(.*?)<\/object>/is', '', $file_content );

		// Remove any JavaScript event handlers (onclick, onload, etc.)
		$file_content = preg_replace( '/on\w+="[^"]*"/i', '', $file_content );
		$file_content = preg_replace( '/on\w+=\'[^\']*\'/i', '', $file_content );

		// Write the sanitized content back to the file
		file_put_contents( $file['tmp_name'], $file_content );
	}

	return $file;
}
add_filter( 'wp_handle_upload_prefilter', 'loan_city_sanitize_svg' );

/**
 * Fix SVG display in Media Library.
 */
function loan_city_fix_svg_media_display() {
	echo '<style>
		.attachment-266x266, .thumbnail img {
			width: 100% !important;
			height: auto !important;
		}
	</style>';
}
add_action( 'admin_head', 'loan_city_fix_svg_media_display' );

/**
 * Render the traditional WordPress menu for use in block templates.
 * This function outputs the primary menu as HTML that can be included in block templates.
 *
 * @return string HTML output of the primary menu.
 */
function loan_city_render_primary_menu() {
    ob_start();

    if (has_nav_menu('primary')) {
        wp_nav_menu(array(
            'theme_location' => 'primary',
            'menu_class'     => 'primary-menu',
            'container'      => 'nav',
            'container_class' => 'wp-block-navigation',
            'items_wrap'     => '<ul class="wp-block-navigation__container">%3$s</ul>',
            'fallback_cb'    => false,
            'depth'          => 2,
        ));
    } else {
        echo '<nav class="wp-block-navigation"><ul class="wp-block-navigation__container">';

        // Fallback to list of pages if no menu is assigned
        $pages = get_pages(array('sort_column' => 'menu_order'));
        foreach ($pages as $page) {
            $current = (is_page($page->ID)) ? 'current-menu-item' : '';
            echo '<li class="wp-block-navigation-item ' . $current . '">';
            echo '<a class="wp-block-navigation-item__content" href="' . get_permalink($page->ID) . '">' . $page->post_title . '</a>';
            echo '</li>';
        }

        echo '</ul></nav>';
    }

    return ob_get_clean();
}

/**
 * Register a shortcode to render the primary menu.
 * This allows us to use [primary_menu] in block templates.
 *
 * @return string HTML output of the primary menu.
 */
function loan_city_primary_menu_shortcode() {
    // Direct implementation to ensure it works in all contexts
    ob_start();

    // Try to get the menu by location first
    if (has_nav_menu('primary')) {
        wp_nav_menu(array(
            'theme_location' => 'primary',
            'menu_class'     => 'primary-menu',
            'container'      => 'nav',
            'container_class' => 'wp-block-navigation',
            'items_wrap'     => '<ul class="wp-block-navigation__container">%3$s</ul>',
            'fallback_cb'    => false,
            'depth'          => 2,
        ));
    }
    // If no menu is assigned to primary location, try to get the menu by name
    else {
        $menu_exists = wp_get_nav_menu_object('Main menu'); // Try to get the menu by name

        if ($menu_exists) {
            wp_nav_menu(array(
                'menu'           => 'Main menu', // Use the menu name directly
                'menu_class'     => 'primary-menu',
                'container'      => 'nav',
                'container_class' => 'wp-block-navigation',
                'items_wrap'     => '<ul class="wp-block-navigation__container">%3$s</ul>',
                'depth'          => 2,
            ));
        }
        // Final fallback - display all pages
        else {
            echo '<nav class="wp-block-navigation"><ul class="wp-block-navigation__container">';

            // Hardcoded menu items as a last resort
            echo '<li class="wp-block-navigation-item"><a class="wp-block-navigation-item__content" href="' . home_url('/') . '">Homepage</a></li>';
            echo '<li class="wp-block-navigation-item"><a class="wp-block-navigation-item__content" href="' . home_url('/loan-application/') . '">Loan Application</a></li>';
            echo '<li class="wp-block-navigation-item"><a class="wp-block-navigation-item__content" href="' . home_url('/blog/') . '">Blog</a></li>';
            echo '<li class="wp-block-navigation-item"><a class="wp-block-navigation-item__content" href="' . home_url('/sample-page/') . '">Sample Page</a></li>';

            echo '</ul></nav>';
        }
    }

    return ob_get_clean();
}
add_shortcode('primary_menu', 'loan_city_primary_menu_shortcode');

/**
 * Debug shortcode to check menu assignment
 * Use [menu_debug] to display information about menu assignments
 */
function loan_city_menu_debug_shortcode() {
    $output = '<div style="background: #f8f8f8; padding: 10px; border: 1px solid #ddd; margin: 10px 0; font-family: monospace;">';
    $output .= '<h4>Menu Debug Information</h4>';

    // Check if primary menu location is registered
    $locations = get_registered_nav_menus();
    $output .= '<p>Registered menu locations: ' . implode(', ', array_keys($locations)) . '</p>';

    // Check if a menu is assigned to primary location
    $menu_locations = get_nav_menu_locations();
    $output .= '<p>Menu locations with assigned menus: ' . implode(', ', array_keys($menu_locations)) . '</p>';

    if (isset($menu_locations['primary'])) {
        $menu_id = $menu_locations['primary'];
        $menu_obj = wp_get_nav_menu_object($menu_id);
        $output .= '<p>Primary menu is assigned to: "' . $menu_obj->name . '" (ID: ' . $menu_id . ')</p>';

        // Get menu items
        $menu_items = wp_get_nav_menu_items($menu_id);
        if ($menu_items) {
            $output .= '<p>Menu items (' . count($menu_items) . '):</p><ul>';
            foreach ($menu_items as $item) {
                $output .= '<li>' . $item->title . ' (ID: ' . $item->ID . ')</li>';
            }
            $output .= '</ul>';
        } else {
            $output .= '<p>No menu items found in this menu.</p>';
        }
    } else {
        $output .= '<p>No menu is assigned to the primary location.</p>';
    }

    $output .= '</div>';
    return $output;
}
add_shortcode('menu_debug', 'loan_city_menu_debug_shortcode');

/**
 * Register a shortcode to render the footer menu.
 * This allows us to use [footer_menu] in block templates.
 *
 * @return string HTML output of the footer menu.
 */
function loan_city_footer_menu_shortcode() {
    // Direct implementation to ensure it works in all contexts
    ob_start();

    // Try to get the menu by location first
    if (has_nav_menu('footer')) {
        wp_nav_menu(array(
            'theme_location' => 'footer',
            'menu_class'     => 'footer-menu-items',
            'container'      => 'nav',
            'container_class' => 'footer-navigation',
            'items_wrap'     => '<ul class="footer-navigation__container">%3$s</ul>',
            'fallback_cb'    => false,
            'depth'          => 1,
        ));
    }
    // If no menu is assigned to footer location, try to get the menu by name
    else {
        $menu_exists = wp_get_nav_menu_object('Footer menu'); // Try to get the menu by name

        if ($menu_exists) {
            wp_nav_menu(array(
                'menu'           => 'Footer menu', // Use the menu name directly
                'menu_class'     => 'footer-menu-items',
                'container'      => 'nav',
                'container_class' => 'footer-navigation',
                'items_wrap'     => '<ul class="footer-navigation__container">%3$s</ul>',
                'depth'          => 1,
            ));
        }
        // Final fallback - display hardcoded menu items
        else {
            echo '<nav class="footer-navigation"><ul class="footer-navigation__container">';

            // Hardcoded menu items as a last resort - using the same links as in the current footer
            echo '<li class="menu-item"><a href="' . home_url('/blog/') . '" style="color: #000">Blog</a></li>';
            echo '<li class="menu-item"><a href="' . home_url('/about/') . '" style="color: #000">About</a></li>';
            echo '<li class="menu-item"><a href="' . home_url('/terms-and-conditions/') . '" style="color: #000">Terms and conditions</a></li>';
            echo '<li class="menu-item"><a href="' . home_url('/privacy-policy/') . '" style="color: #000">Privacy Policy</a></li>';

            echo '</ul></nav>';
        }
    }

    return ob_get_clean();
}
add_shortcode('footer_menu', 'loan_city_footer_menu_shortcode');

/**
 * Flush rewrite rules to update article URL structure from 'article/slug' to 'blog/slug'.
 * This function runs once and sets an option to prevent running on every page load.
 */
function loan_city_flush_article_rewrite_rules() {
    // Force flush rewrite rules by deleting the option
    delete_option('loan_city_article_rewrite_flushed');

    // Check if we've already flushed the rules for this change
    if (get_option('loan_city_article_rewrite_flushed')) {
        return;
    }

    // Flush rewrite rules
    flush_rewrite_rules();

    // Set option to prevent running this again
    update_option('loan_city_article_rewrite_flushed', true);
}
add_action('init', 'loan_city_flush_article_rewrite_rules', 20); // Run after post types are registered


