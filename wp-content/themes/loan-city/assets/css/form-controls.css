/**
 * Form Controls - Combined styles for floating labels and select boxes
 * Merges styles from:
 * - floating-labels.css
 * - select-boxes.css
 * - select-boxes-label-fix.css
 * - select-box-fix.css
 * - select-boxes-fix.css
 */

/* ======================================
   FLOATING LABELS - GENERAL STYLES
   ====================================== */

/* Form field container with floating label */
.wpcf7-form .form-field-float {
    position: relative;
    margin-bottom: 20px;
}

/* Ensure the form-field-float class works correctly with the has-focus and has-content classes */
.wpcf7-form .form-field-float.has-focus input,
.wpcf7-form .form-field-float.has-focus select,
.wpcf7-form .form-field-float.has-focus textarea,
.form-field-float.has-focus input,
.form-field-float.has-focus select,
.form-field-float.has-focus textarea {
    border-color: #4CAF50 !important; /* Green color for focus */
    box-shadow: 0 0 0 1px #4CAF50 !important;
    outline: none !important;
}

/* Direct fix for the specific HTML structure in the loan-application-html-rendered.html file */
.form-field-float p span.wpcf7-form-control-wrap input:focus + label,
.form-field-float p span.wpcf7-form-control-wrap select:focus + label,
.form-field-float p span.wpcf7-form-control-wrap textarea:focus + label {
    top: -10px !important;
    left: 10px !important;
    font-size: 12px !important;
    color: #4CAF50 !important; /* Green color for focus */
    background-color: white !important;
    padding: 0 5px !important;
    transition: all 0.3s ease !important;
    display: inline !important;
    width: fit-content !important;
    z-index: 10 !important;
    transform: translateY(0) scale(0.75) !important;
}

/* Direct fix for the specific HTML structure - ensure p > label is visible when input is focused */
.form-field-float:focus-within p > label {
    display: inline !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: absolute !important;
    top: -10px !important;
    left: 10px !important;
    font-size: 12px !important;
    color: #4CAF50 !important; /* Green color for focus */
    background-color: white !important;
    padding: 0 5px !important;
    z-index: 10 !important;
    width: fit-content !important;
    height: auto !important;
    transform: translateY(0) scale(0.75) !important;
}

.wpcf7-form .form-field-float.has-content input,
.wpcf7-form .form-field-float.has-content select,
.wpcf7-form .form-field-float.has-content textarea,
.wpcf7-form .form-field-float.has-value input,
.wpcf7-form .form-field-float.has-value select,
.wpcf7-form .form-field-float.has-value textarea {
    border-color: #ddd !important;
}

/* Style for the floating label */
.wpcf7-form .form-field-float label {
    position: absolute;
    top: 10px;
    left: 10px;
    color: #666;
    font-size: 16px;
    transition: all 0.3s ease;
    pointer-events: none;
    z-index: 5; /* Increased z-index to ensure visibility */
    margin: 0;
    padding: 0 5px;
    background-color: transparent;
    display: block;
    width: auto;
}

/* Ensure label is positioned correctly relative to the form field */
.wpcf7-form .form-field-float {
    position: relative;
}

/* When input is focused or has content, move the label up */
.wpcf7-form .form-field-float .wpcf7-form-control-wrap input:focus ~ label,
.wpcf7-form .form-field-float .wpcf7-form-control-wrap select:focus ~ label,
.wpcf7-form .form-field-float .wpcf7-form-control-wrap textarea:focus ~ label,
.wpcf7-form .form-field-float.has-focus .wpcf7-form-control-wrap label,
.wpcf7-form .form-field-float.has-focus label {
    top: -10px !important;
    left: 10px !important;
    font-size: 12px !important;
    color: #4CAF50 !important; /* Green color for focus */
    background-color: white !important;
    padding: 0 5px !important;
    transition: all 0.3s ease !important;
    display: inline !important;
    width: fit-content !important;
    z-index: 10 !important;
    transform: translateY(0) scale(0.75) !important;
}

/* Direct focus state for inputs */
.wpcf7-form .form-field-float input:focus ~ label,
.wpcf7-form .form-field-float select:focus ~ label,
.wpcf7-form .form-field-float textarea:focus ~ label,
.form-field-float input:focus ~ label,
.form-field-float select:focus ~ label,
.form-field-float textarea:focus ~ label,
/* Target the specific structure in the rendered HTML */
.form-field-float p span.wpcf7-form-control-wrap input:focus ~ label,
.form-field-float p span.wpcf7-form-control-wrap select:focus ~ label,
.form-field-float p span.wpcf7-form-control-wrap textarea:focus ~ label,
/* Target using adjacent sibling selector */
.form-field-float p span.wpcf7-form-control-wrap:focus-within + label,
.form-field-float p span.wpcf7-form-control-wrap:has(input:focus) + label,
.form-field-float p span.wpcf7-form-control-wrap:has(select:focus) + label,
.form-field-float p span.wpcf7-form-control-wrap:has(textarea:focus) + label {
    top: -10px !important;
    left: 10px !important;
    font-size: 12px !important;
    color: #4CAF50 !important; /* Green color for focus */
    background-color: white !important;
    padding: 0 5px !important;
    transition: all 0.3s ease !important;
    display: inline !important;
    width: fit-content !important;
    z-index: 10 !important;
    transform: translateY(0) scale(0.75) !important;
}

/* When input has content but is not focused */
.wpcf7-form .form-field-float .wpcf7-form-control-wrap input:not(:placeholder-shown):not(:focus) ~ label,
.wpcf7-form .form-field-float .wpcf7-form-control-wrap select:not([value=""]):not([value="0"]):not([value="Select below"]):not(:focus) ~ label,
.wpcf7-form .form-field-float .wpcf7-form-control-wrap textarea:not(:placeholder-shown):not(:focus) ~ label,
.wpcf7-form .form-field-float.has-value:not(.has-focus) label,
.wpcf7-form .form-field-float.has-content:not(.has-focus) label {
    top: -10px !important;
    left: 10px !important;
    font-size: 12px !important;
    color: #666 !important; /* Gray color for filled but not focused */
    background-color: white !important;
    padding: 0 5px !important;
    transition: all 0.3s ease !important;
    display: inline !important;
    width: fit-content !important;
    z-index: 10 !important;
    transform: translateY(0) scale(0.75) !important;
}

/* Style for inputs with floating labels */
.wpcf7-form .form-field-float .wpcf7-form-control-wrap input,
.wpcf7-form .form-field-float .wpcf7-form-control-wrap select,
.wpcf7-form .form-field-float .wpcf7-form-control-wrap textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
    background-color: transparent;
    height: 42px; /* Ensure consistent height for all inputs */
    position: relative; /* Ensure proper stacking context */
    z-index: 1; /* Lower than the label's z-index */
}

/* Adjust textarea height */
.wpcf7-form .form-field-float .wpcf7-form-control-wrap textarea {
    height: auto;
    min-height: 100px;
}

/* Style for focused inputs */
.wpcf7-form .form-field-float .wpcf7-form-control-wrap input:focus,
.wpcf7-form .form-field-float .wpcf7-form-control-wrap select:focus,
.wpcf7-form .form-field-float .wpcf7-form-control-wrap textarea:focus,
.wpcf7-form .form-field-float.has-focus .wpcf7-form-control-wrap input,
.wpcf7-form .form-field-float.has-focus .wpcf7-form-control-wrap select,
.wpcf7-form .form-field-float.has-focus .wpcf7-form-control-wrap textarea,
/* Target direct inputs */
.form-field-float input:focus,
.form-field-float select:focus,
.form-field-float textarea:focus,
/* Target the specific structure in the rendered HTML */
.form-field-float p span.wpcf7-form-control-wrap input:focus,
.form-field-float p span.wpcf7-form-control-wrap select:focus,
.form-field-float p span.wpcf7-form-control-wrap textarea:focus {
    outline: none !important;
    border-color: #4CAF50 !important; /* Green color for focus */
    box-shadow: 0 0 0 1px #4CAF50 !important;
}

/* Hide the placeholder when input is focused */
.wpcf7-form .form-field-float .wpcf7-form-control-wrap input::placeholder,
.wpcf7-form .form-field-float .wpcf7-form-control-wrap textarea::placeholder {
    color: transparent !important;
    opacity: 0 !important;
}

/* Make sure the placeholder is transparent to allow the label to show */
.wpcf7-form .form-field-float .wpcf7-form-control-wrap input:placeholder-shown,
.wpcf7-form .form-field-float .wpcf7-form-control-wrap textarea:placeholder-shown {
    color: transparent !important;
}

/* Ensure placeholders don't interfere with labels */
.wpcf7-form .form-field-float input::placeholder,
.wpcf7-form .form-field-float textarea::placeholder {
    color: transparent !important;
    opacity: 0 !important;
}

/* Fix for Contact Form 7's structure */
.wpcf7-form .form-field-float .wpcf7-form-control-wrap {
    display: block;
    width: 100%;
    position: relative;
}

/* Fix for labels that appear below inputs */
.wpcf7-form .form-field-float label {
    order: -1;
}

/* Hide the <br> tag in the form field */
.wpcf7-form .form-field-float br {
    display: none;
}

/* Position the label inside the input initially */
.wpcf7-form .form-field-float .wpcf7-form-control-wrap label,
.wpcf7-form.using-floating-labels .form-field-float .wpcf7-form-control-wrap label {
    position: absolute !important;
    top: 10px !important;
    left: 10px !important;
    color: #666 !important;
    font-size: 16px !important;
    transition: all 0.3s ease !important;
    pointer-events: none !important;
    z-index: 5 !important;
    margin: 0 !important;
    padding: 0 5px !important;
    background-color: transparent !important;
    width: calc(100% - 20px) !important; /* Ensure label doesn't overflow */
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* Ensure the label is visible when field is focused */
.wpcf7-form .form-field-float.has-focus .wpcf7-form-control-wrap input,
.wpcf7-form .form-field-float.has-focus .wpcf7-form-control-wrap select,
.wpcf7-form .form-field-float.has-focus .wpcf7-form-control-wrap textarea,
.wpcf7-form .form-field-float input:focus,
.wpcf7-form .form-field-float select:focus,
.wpcf7-form .form-field-float textarea:focus {
    border-color: #4CAF50 !important; /* Green color for focus */
    box-shadow: 0 0 0 1px #4CAF50 !important;
    outline: none !important;
}

/* ======================================
   SELECT BOX SPECIFIC STYLES
   ====================================== */

/* Select input field styling */
.wpcf7-form .wpcf7-select,
.form-field-float .wpcf7-form-control-wrap select {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
    height: 42px;
    box-sizing: border-box;
    background-color: #fff;
    color: #333;
    appearance: none;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="12" height="6"><path d="M0 0l6 6 6-6z" fill="%23666"/></svg>');
    background-repeat: no-repeat;
    background-position: right 10px center;
    padding-right: 30px;
}

/* Style for empty select boxes to show placeholder-like behavior */
.wpcf7-form .wpcf7-select option:first-child,
.form-field-float .wpcf7-form-control-wrap select option:first-child {
    color: #666;
}

/* Style for placeholder options */
.wpcf7-form .wpcf7-select option[value="Select below"],
.wpcf7-form .wpcf7-select option:first-child,
.form-field-float .wpcf7-form-control-wrap select option[value="Select below"] {
    color: #666;
    font-style: italic;
}

/* Style for select boxes with real values */
.wpcf7-form .wpcf7-select,
.form-field-float .wpcf7-form-control-wrap select {
    color: #333;
}

/* Style for select boxes with placeholder options selected */
.wpcf7-form .wpcf7-select option[value="Select below"]:checked,
.wpcf7-form .wpcf7-select option:first-child:checked,
.form-field-float .wpcf7-form-control-wrap select option[value="Select below"]:checked {
    color: #666 !important;
    font-style: italic;
}

/* Style for select boxes with "Select below" as the selected value */
.wpcf7-form .form-field-float:not(.has-content) .wpcf7-select,
.form-field-float:not(.has-content):not(.has-value) .wpcf7-form-control-wrap select {
    color: #666;
}

/* Ensure the select input works with floating labels - focused state */
.form-field-float .wpcf7-select:focus ~ label,
.form-field-float.has-focus .wpcf7-select ~ label,
.form-field-float select:focus ~ label,
.form-field-float.has-focus select ~ label {
    transform: translateY(-24px) scale(0.75);
    color: #4CAF50; /* Green color for focus */
    display: inline !important;
    width: fit-content !important;
    background-color: white;
    padding: 0 5px;
    z-index: 10;
    top: 0;
    left: 10px;
}

/* Ensure the select input works with floating labels - filled state */
.form-field-float.has-content:not(.has-focus) .wpcf7-select ~ label,
.form-field-float.has-content:not(.has-focus) select ~ label,
.form-field-float.has-value:not(.has-focus) select ~ label {
    transform: translateY(-24px) scale(0.75);
    color: #666; /* Gray color for filled but not focused */
    display: inline !important;
    width: fit-content !important;
    background-color: white;
    padding: 0 5px;
    z-index: 10;
    top: 0;
    left: 10px;
}

/* Ensure select boxes have proper border color when focused */
.wpcf7-form .form-field-float.has-focus .wpcf7-select,
.wpcf7-form .form-field-float .wpcf7-select:focus,
.wpcf7-form .form-field-float.has-focus select,
.wpcf7-form .form-field-float select:focus {
    border-color: #4CAF50 !important; /* Green color for focus */
    box-shadow: 0 0 0 1px #4CAF50 !important;
}

/* ======================================
   SELECT BOX LABEL FIXES
   ====================================== */

/* Hide the inner label when a value is selected */
.form-field-float.has-content .wpcf7-form-control-wrap label,
.form-field-float.has-value .wpcf7-form-control-wrap label,
.wpcf7-form.using-floating-labels .form-field-float.has-content .wpcf7-form-control-wrap label,
.wpcf7-form.using-floating-labels .form-field-float.has-value .wpcf7-form-control-wrap label {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
}

/* Show the top label when a value is selected */
.form-field-float.has-content > p > label,
.form-field-float.has-value > p > label,
.wpcf7-form.using-floating-labels .form-field-float.has-content > p > label,
.wpcf7-form.using-floating-labels .form-field-float.has-value > p > label {
    position: absolute !important;
    top: -10px !important;
    left: 10px !important;
    font-size: 12px !important;
    color: #666 !important;
    background-color: white !important;
    padding: 0 5px !important;
    display: inline !important;
    width: fit-content !important;
    z-index: 5 !important;
    opacity: 1 !important;
    visibility: visible !important;
    height: auto !important;
    overflow: visible !important;
}

/* Hide the top label when in default state (Select below) - but not when focused */
.form-field-float:not(.has-content):not(.has-value):not(.has-focus) > p > label,
.wpcf7-form.using-floating-labels .form-field-float:not(.has-content):not(.has-value):not(.has-focus) > p > label {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
}

/* Ensure the label is visible when the input is focused, regardless of content */
.form-field-float.has-focus > p > label,
.wpcf7-form.using-floating-labels .form-field-float.has-focus > p > label,
.form-field-float:focus-within > p > label,
.wpcf7-form.using-floating-labels .form-field-float:focus-within > p > label {
    display: inline !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: absolute !important;
    top: -10px !important;
    left: 10px !important;
    font-size: 12px !important;
    color: #4CAF50 !important; /* Green color for focus */
    background-color: white !important;
    padding: 0 5px !important;
    z-index: 10 !important;
    width: fit-content !important;
    height: auto !important;
    transform: translateY(0) scale(0.75) !important;
}

/* Show the inner label when in default state or when value is "Select below" */
.form-field-float:not(.has-content):not(.has-value) .wpcf7-form-control-wrap label,
.form-field-float .wpcf7-form-control-wrap label,
.wpcf7-form.using-floating-labels .form-field-float:not(.has-content):not(.has-value) .wpcf7-form-control-wrap label,
.wpcf7-form.using-floating-labels .form-field-float .wpcf7-form-control-wrap label {
    position: absolute !important;
    top: 10px;
    left: 10px !important;
    color: #666;
    font-size: 16px;
    background-color: white !important;
    display: block !important;
    width: auto;
    height: auto !important;
    transform: none !important;
    opacity: 1 !important;
    visibility: visible !important;
    z-index: 5 !important;
    overflow: visible !important;
    padding: 0 5px !important;
}

.wpcf7-form .form-field-float .wpcf7-form-control-wrap input:focus ~ label {
    top: -10px !important;
    font-size: 12px !important;
    color: #4CAF50 !important; /* Green color for focus */
    display: inline !important;
    width: fit-content !important;
}

/* Direct fix for select box labels */
.form-field-float.has-value.has-content label,
div.form-field-float label[style*="opacity: 1"],
.form-field-float.has-value.has-content p label,
div.form-field-float p label,
.wpcf7-form p label,
.wpcf7-form-control-wrap label,
html body .wpcf7-form-control-wrap label,
body .wpcf7-form-control-wrap label {
    position: absolute !important;
    top: 10px !important;
    left: 10px !important;
    color: #666 !important;
    font-size: 16px !important;
    background-color: white !important; /* Add white background */
    display: block !important;
    width: auto !important;
    height: auto !important; /* Override the height: 1px */
    transform: none !important;
    opacity: 1 !important;
    visibility: visible !important;
    z-index: 5 !important;
    overflow: visible !important; /* Override the overflow: hidden */
}

/* When the select box is in default state (Select below) */
.form-field-float:not(.has-content) select ~ label,
html body .form-field-float:not(.has-content) select ~ label,
body .form-field-float:not(.has-content) select ~ label {
    position: absolute !important;
    top: 10px !important;
    left: 10px !important;
    color: #666 !important;
    font-size: 16px !important;
    background-color: white !important; /* Change to white */
    display: block !important;
    width: auto !important;
    height: auto !important; /* Override the height: 1px */
    transform: none !important;
    opacity: 1 !important;
    visibility: visible !important;
    z-index: 5 !important;
    overflow: visible !important; /* Override the overflow: hidden */
    padding: 0 5px !important; /* Add padding */
}

/* ======================================
   DATE PICKER SPECIFIC STYLES
   ====================================== */

/* Ensure date picker labels have proper styling when filled but not focused */
.wpcf7-form .form-field-float.has-content:not(.has-focus) .wpcf7-date ~ label {
    display: inline !important;
    width: fit-content !important;
    top: -10px !important;
    left: 10px !important;
    font-size: 12px !important;
    color: #666 !important; /* Gray color for filled but not focused */
    background-color: white !important;
    padding: 0 5px !important;
}

/* ======================================
   RESPONSIVE ADJUSTMENTS
   ====================================== */

@media (max-width: 768px) {
    .wpcf7-form .form-field-float > label {
        font-size: 14px;
    }

    /* Focused labels in responsive mode */
    .wpcf7-form .form-field-float.has-focus > label,
    .wpcf7-form .form-field-float .wpcf7-form-control-wrap input:focus ~ label,
    .wpcf7-form .form-field-float .wpcf7-form-control-wrap select:focus ~ label,
    .wpcf7-form .form-field-float .wpcf7-form-control-wrap textarea:focus ~ label {
        font-size: 11px;
        display: inline !important;
        width: fit-content !important;
        color: #4CAF50 !important; /* Green color for focus */
    }

    /* Filled but not focused labels in responsive mode */
    .wpcf7-form .form-field-float.has-value:not(.has-focus) > label,
    .wpcf7-form .form-field-float .wpcf7-form-control-wrap input:not(:placeholder-shown):not(:focus) ~ label,
    .wpcf7-form .form-field-float .wpcf7-form-control-wrap select:not([value=""]):not([value="0"]):not(:focus) ~ label,
    .wpcf7-form .form-field-float .wpcf7-form-control-wrap textarea:not(:placeholder-shown):not(:focus) ~ label {
        font-size: 11px;
        display: inline !important;
        width: fit-content !important;
        color: #666 !important; /* Gray color for filled but not focused */
    }
}

p > label[for="existing_debt"] {
    position: static !important;
}

p > label[for="existing_debt"] ~ br {
    display: none !important;
}
