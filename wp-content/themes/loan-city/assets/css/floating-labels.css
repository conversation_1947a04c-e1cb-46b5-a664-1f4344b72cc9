/**
 * Floating Labels for Contact Form 7
 */

/* Form field container with floating label */
.wpcf7-form .form-field-float {
    position: relative;
    margin-bottom: 20px;
}

/* Style for the floating label */
.wpcf7-form .form-field-float label {
    position: absolute;
    top: 10px;
    left: 10px;
    color: #666;
    font-size: 16px;
    transition: all 0.3s ease;
    pointer-events: none;
    z-index: 5; /* Increased z-index to ensure visibility */
    margin: 0;
    padding: 0 5px;
    background-color: transparent;
    display: block;
    width: auto;
}

/* Ensure label is positioned correctly relative to the form field */
.wpcf7-form .form-field-float {
    position: relative;
}

/* When input is focused or has content, move the label up */
.wpcf7-form .form-field-float .wpcf7-form-control-wrap input:focus ~ label,
.wpcf7-form .form-field-float .wpcf7-form-control-wrap select:focus ~ label,
.wpcf7-form .form-field-float .wpcf7-form-control-wrap textarea:focus ~ label,
.wpcf7-form .form-field-float.has-focus .wpcf7-form-control-wrap label {
    top: -10px;
    left: 10px;
    font-size: 12px;
    color: #4CAF50; /* Green color for focus */
    background-color: white;
    padding: 0 5px;
    transition: all 0.3s ease;
    display: inline;
    width: fit-content;
}

/* When input has content but is not focused */
.wpcf7-form .form-field-float .wpcf7-form-control-wrap input:not(:placeholder-shown):not(:focus) ~ label,
.wpcf7-form .form-field-float .wpcf7-form-control-wrap select:not([value=""]):not([value="0"]):not(:focus) ~ label,
.wpcf7-form .form-field-float .wpcf7-form-control-wrap textarea:not(:placeholder-shown):not(:focus) ~ label,
.wpcf7-form .form-field-float.has-value:not(.has-focus) label {
    top: -10px;
    left: 10px;
    font-size: 12px;
    color: #666; /* Gray color for filled but not focused */
    background-color: white;
    padding: 0 5px;
    transition: all 0.3s ease;
    display: inline;
    width: fit-content;
}

/* Style for inputs with floating labels */
.wpcf7-form .form-field-float .wpcf7-form-control-wrap input,
.wpcf7-form .form-field-float .wpcf7-form-control-wrap select,
.wpcf7-form .form-field-float .wpcf7-form-control-wrap textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
    background-color: transparent;
    height: 42px; /* Ensure consistent height for all inputs */
    position: relative; /* Ensure proper stacking context */
    z-index: 1; /* Lower than the label's z-index */
}

/* Adjust textarea height */
.wpcf7-form .form-field-float .wpcf7-form-control-wrap textarea {
    height: auto;
    min-height: 100px;
}

/* Style for focused inputs */
.wpcf7-form .form-field-float .wpcf7-form-control-wrap input:focus,
.wpcf7-form .form-field-float .wpcf7-form-control-wrap select:focus,
.wpcf7-form .form-field-float .wpcf7-form-control-wrap textarea:focus {
    outline: none;
    border-color: #4CAF50; /* Green color for focus */
    box-shadow: 0 0 0 1px #4CAF50;
}

/* Hide the placeholder when input is focused */
.wpcf7-form .form-field-float .wpcf7-form-control-wrap input::placeholder,
.wpcf7-form .form-field-float .wpcf7-form-control-wrap textarea::placeholder {
    color: transparent;
}

/* Make sure the placeholder is transparent to allow the label to show */
.wpcf7-form .form-field-float .wpcf7-form-control-wrap input:placeholder-shown,
.wpcf7-form .form-field-float .wpcf7-form-control-wrap textarea:placeholder-shown {
    color: transparent;
}

/* For select elements, ensure they have proper padding */
.wpcf7-form .form-field-float .wpcf7-form-control-wrap select {
    padding-right: 30px;
}

/* Adjust for form columns */
.wpcf7-form .form-columns .form-field-float {
    margin-bottom: 15px;
}

/* Fix for Contact Form 7's structure */
.wpcf7-form .form-field-float .wpcf7-form-control-wrap {
    display: block;
    width: 100%;
    position: relative;
}

/* Fix for labels that appear below inputs */
.wpcf7-form .form-field-float label {
    order: -1;
}

/* Hide the <br> tag in the form field */
.wpcf7-form .form-field-float br {
    display: none;
}

/* Position the label inside the input initially */
.wpcf7-form .form-field-float .wpcf7-form-control-wrap label {
    position: absolute !important;
    top: 10px !important;
    left: 10px !important;
    color: #666 !important;
    font-size: 16px !important;
    transition: all 0.3s ease !important;
    pointer-events: none !important;
    z-index: 5 !important;
    margin: 0 !important;
    padding: 0 5px !important;
    background-color: transparent !important;
    width: calc(100% - 20px) !important; /* Ensure label doesn't overflow */
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    display: block !important;
}

/* Fix for legacy date inputs */
.wpcf7-form .date-inputs {
    position: relative;
    margin-bottom: 20px;
}

.wpcf7-form .date-inputs .date-label {
    display: block;
    margin-bottom: 5px;
    font-size: 14px;
    color: #666;
    position: relative;
    z-index: 2;
    text-align: center;
    font-weight: 500;
}

/* Fix for new date picker input */
.wpcf7-form .form-field-float .wpcf7-date {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
    height: 42px;
    box-sizing: border-box;
    background-color: #fff;
}

/* Ensure the date input works with floating labels - focused state */
.wpcf7-form .form-field-float.has-focus .wpcf7-date,
.wpcf7-form .form-field-float .wpcf7-date:focus {
    border-color: #4CAF50 !important; /* Green color for focus */
    box-shadow: 0 0 0 1px #4CAF50 !important;
}

/* Ensure the date input works with floating labels - filled state */
.wpcf7-form .form-field-float.has-content:not(.has-focus) .wpcf7-date {
    border-color: #ddd;
    box-shadow: none;
}

/* Ensure date picker labels have proper styling when focused */
.wpcf7-form .form-field-float .wpcf7-date:focus ~ label,
.wpcf7-form .form-field-float.has-focus .wpcf7-date ~ label {
    display: inline !important;
    width: fit-content !important;
    top: -10px !important;
    left: 10px !important;
    font-size: 12px !important;
    color: #4CAF50 !important; /* Green color for focus */
    background-color: white !important;
    padding: 0 5px !important;
}

/* Ensure date picker labels have proper styling when filled but not focused */
.wpcf7-form .form-field-float.has-content:not(.has-focus) .wpcf7-date ~ label {
    display: inline !important;
    width: fit-content !important;
    top: -10px !important;
    left: 10px !important;
    font-size: 12px !important;
    color: #666 !important; /* Gray color for filled but not focused */
    background-color: white !important;
    padding: 0 5px !important;
}

/* Hide empty paragraphs */
.wpcf7-form .date-inputs p:empty,
.wpcf7-form .form-column p:empty,
.wpcf7-form .form-field-float p:empty {
    display: none;
}

/* Style for date input fields */
.wpcf7-form .date-inputs input[type="number"] {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
    height: 42px;
    box-sizing: border-box;
}

/* Style for date input columns */
.wpcf7-form .form-columns {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -10px;
}

.wpcf7-form .form-column {
    flex: 1;
    padding: 0 10px;
    min-width: 0; /* Fix for flexbox min-width issue */
}

/* Date of birth title styling */
.wpcf7-form p:has(+ .form-columns .date-inputs),
.wpcf7-form p:has(+ div + .form-columns .date-inputs) {
    font-size: 16px;
    color: #333;
    margin-bottom: 10px;
    font-weight: 500;
}

/* Error message styling */
.wpcf7-form .wpcf7-not-valid-tip {
    color: #dc3232;
    font-size: 12px;
    display: block;
    margin-top: 5px;
    text-align: left;
}

/* Style for invalid inputs */
.wpcf7-form .wpcf7-not-valid {
    border-color: #dc3232 !important;
}

/* Fix for date inputs with error messages */
.wpcf7-form .date-inputs .wpcf7-form-control-wrap {
    display: block;
    width: 100%;
}

/* Ensure radio buttons display properly */
.wpcf7-form .wpcf7-radio {
    display: flex;
    gap: 15px;
}

.wpcf7-form .wpcf7-radio .wpcf7-list-item {
    margin: 0;
}

/* Additional fixes for Contact Form 7 - Focused state */
.wpcf7-form .form-field-float.has-focus label,
.wpcf7-form .form-field-float.has-focus .wpcf7-form-control-wrap label {
    top: -10px !important;
    left: 10px !important;
    font-size: 12px !important;
    color: #4CAF50 !important; /* Green color for focus */
    background-color: white !important;
    padding: 0 5px !important;
    transition: all 0.3s ease !important;
    z-index: 10 !important;
    display: inline !important;
    width: fit-content !important;
}

/* Additional fixes for Contact Form 7 - Filled state */
.wpcf7-form .form-field-float.has-value:not(.has-focus) label,
.wpcf7-form .form-field-float.has-value:not(.has-focus) .wpcf7-form-control-wrap label {
    top: -10px !important;
    left: 10px !important;
    font-size: 12px !important;
    color: #666 !important; /* Gray color for filled but not focused */
    background-color: white !important;
    padding: 0 5px !important;
    transition: all 0.3s ease !important;
    z-index: 10 !important;
    display: inline !important;
    width: fit-content !important;
}

/* Ensure the label is visible when field is focused */
.wpcf7-form .form-field-float.has-focus .wpcf7-form-control-wrap input,
.wpcf7-form .form-field-float.has-focus .wpcf7-form-control-wrap select,
.wpcf7-form .form-field-float.has-focus .wpcf7-form-control-wrap textarea {
    border-color: #4CAF50 !important; /* Green color for focus */
    box-shadow: 0 0 0 1px #4CAF50 !important;
}

/* Fix for select dropdowns */
.wpcf7-form .form-field-float select {
    width: 100%;
    height: 42px; /* Ensure consistent height */
    padding-left: 10px;
    color: #333;
}

/* Style for empty select boxes to show placeholder-like behavior */
.wpcf7-form .form-field-float select option:first-child {
    color: #666;
}

/* Ensure select boxes have proper label styling when focused */
.wpcf7-form .form-field-float select:focus ~ label,
.wpcf7-form .form-field-float.has-focus select ~ label {
    transform: translateY(-24px) scale(0.75);
    display: inline !important;
    width: fit-content !important;
    color: #4CAF50 !important; /* Green color for focus */
    background-color: white;
    padding: 0 5px;
}

/* Ensure select boxes have proper label styling when filled but not focused */
.wpcf7-form .form-field-float.has-content:not(.has-focus) select ~ label {
    transform: translateY(-24px) scale(0.75);
    display: inline !important;
    width: fit-content !important;
    color: #666 !important; /* Gray color for filled but not focused */
    background-color: white;
    padding: 0 5px;
}

/* Fix for date inputs */
.wpcf7-form .date-inputs input[type="number"] {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
}

/* Fix for radio buttons */
.wpcf7-form .wpcf7-radio {
    margin-bottom: 20px;
    display: flex;
    gap: 20px;
}

.wpcf7-form .wpcf7-radio .wpcf7-list-item {
    margin: 0;
    display: flex;
    align-items: center;
}

.wpcf7-form .wpcf7-radio .wpcf7-list-item input {
    margin-right: 5px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .wpcf7-form .form-field-float > label {
        font-size: 14px;
    }

    /* Focused labels in responsive mode */
    .wpcf7-form .form-field-float.has-focus > label,
    .wpcf7-form .form-field-float .wpcf7-form-control-wrap input:focus ~ label,
    .wpcf7-form .form-field-float .wpcf7-form-control-wrap select:focus ~ label,
    .wpcf7-form .form-field-float .wpcf7-form-control-wrap textarea:focus ~ label {
        font-size: 11px;
        display: inline !important;
        width: fit-content !important;
        color: #4CAF50 !important; /* Green color for focus */
    }

    /* Filled but not focused labels in responsive mode */
    .wpcf7-form .form-field-float.has-value:not(.has-focus) > label,
    .wpcf7-form .form-field-float .wpcf7-form-control-wrap input:not(:placeholder-shown):not(:focus) ~ label,
    .wpcf7-form .form-field-float .wpcf7-form-control-wrap select:not([value=""]):not([value="0"]):not(:focus) ~ label,
    .wpcf7-form .form-field-float .wpcf7-form-control-wrap textarea:not(:placeholder-shown):not(:focus) ~ label {
        font-size: 11px;
        display: inline !important;
        width: fit-content !important;
        color: #666 !important; /* Gray color for filled but not focused */
    }
}
