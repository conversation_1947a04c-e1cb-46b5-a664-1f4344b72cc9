/**
 * Select Box Styles for Loan City Theme
 */

/* Select input field styling */
.wpcf7-form .wpcf7-select {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
    height: 42px;
    box-sizing: border-box;
    background-color: #fff;
    color: #333;
    appearance: none;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="12" height="6"><path d="M0 0l6 6 6-6z" fill="%23666"/></svg>');
    background-repeat: no-repeat;
    background-position: right 10px center;
    padding-right: 30px;
}

/* Style for empty select boxes to show placeholder-like behavior */
.wpcf7-form .wpcf7-select option:first-child {
    color: #666;
}

/* Style for placeholder options */
.wpcf7-form .wpcf7-select option[value="Select below"],
.wpcf7-form .wpcf7-select option:first-child {
    color: #666;
}

/* Style for select boxes with real values */
.wpcf7-form .wpcf7-select {
    color: #333;
}

/* Style for select boxes with placeholder options selected */
.wpcf7-form .wpcf7-select option[value="Select below"]:checked,
.wpcf7-form .wpcf7-select option:first-child:checked {
    color: #666 !important;
    font-style: italic;
}

/* Style for select boxes with "Select below" as the selected value */
.wpcf7-form .form-field-float:not(.has-content) .wpcf7-select {
    color: #666;
}

/* Hide the label when select has "Select below" as value and is not focused */
.wpcf7-form .form-field-float:not(.has-content):not(.has-focus) .wpcf7-select ~ label {
    display: block !important;
    width: auto !important;
    top: 10px !important;
    left: 10px !important;
    font-size: 16px !important;
    color: #666 !important;
    background-color: transparent !important;
    padding: 0 !important;
    transform: none !important;
    z-index: 5 !important;
    position: absolute !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* Style for select boxes with the select-placeholder class */
.wpcf7-form .wpcf7-select.select-placeholder {
    color: #666 !important;
    font-style: italic;
}

/* Ensure the label is properly positioned when the select box has the default value */
.wpcf7-form .form-field-float:not(.has-content) .wpcf7-select ~ label {
    position: absolute;
    top: 10px !important;
    left: 10px !important;
    color: #666 !important;
    font-size: 16px !important;
    transition: all 0.3s ease;
    pointer-events: none;
    z-index: 5;
    margin: 0;
    padding: 0;
    background-color: transparent !important;
    display: block !important;
    width: auto !important;
    transform: none !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* Style for the default CF7 placeholder text */
.wpcf7-form .wpcf7-select option:first-child,
.wpcf7-form .wpcf7-select option[value="---Please choose an option---"],
.wpcf7-form .wpcf7-select option[value*="choose"],
.wpcf7-form .wpcf7-select option[value*="select"],
.wpcf7-form .wpcf7-select option[value*="Choose"],
.wpcf7-form .wpcf7-select option[value*="Select"] {
    color: #666;
    font-style: italic;
}

/* Ensure the select input works with floating labels - focused state */
.form-field-float .wpcf7-select:focus ~ label,
.form-field-float.has-focus .wpcf7-select ~ label {
    transform: translateY(-24px) scale(0.75);
    color: #4CAF50; /* Green color for focus */
    display: inline !important;
    width: fit-content !important;
    background-color: white;
    padding: 0 5px;
    z-index: 10;
    top: 0;
    left: 10px;
}

/* Ensure the select input works with floating labels - filled state */
.form-field-float.has-content:not(.has-focus) .wpcf7-select ~ label {
    transform: translateY(-24px) scale(0.75);
    color: #666; /* Gray color for filled but not focused */
    display: inline !important;
    width: fit-content !important;
    background-color: white;
    padding: 0 5px;
    z-index: 10;
    top: 0;
    left: 10px;
}

/* Ensure select boxes have proper border color when focused */
.wpcf7-form .form-field-float.has-focus .wpcf7-select,
.wpcf7-form .form-field-float .wpcf7-select:focus {
    border-color: #4CAF50 !important; /* Green color for focus */
    box-shadow: 0 0 0 1px #4CAF50 !important;
}

/* Ensure select boxes have proper border color when filled but not focused */
.wpcf7-form .form-field-float.has-content:not(.has-focus) .wpcf7-select {
    border-color: #ddd;
    box-shadow: none;
}
