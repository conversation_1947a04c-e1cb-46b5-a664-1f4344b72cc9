/**
 * Additional fixes for select box labels
 */

/* Hide the inner label when a value is selected */
.form-field-float.has-content .wpcf7-form-control-wrap label,
.form-field-float.has-value .wpcf7-form-control-wrap label {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
}

/* Show the top label when a value is selected */
.form-field-float.has-content > p > label,
.form-field-float.has-value > p > label {
    position: absolute !important;
    top: -10px !important;
    left: 10px !important;
    font-size: 12px !important;
    color: #666 !important;
    background-color: white !important;
    padding: 0 5px !important;
    display: inline !important;
    width: fit-content !important;
    z-index: 5 !important;
    opacity: 1 !important;
    visibility: visible !important;
    height: auto !important;
    overflow: visible !important;
}

/* Hide the top label when in default state (Select below) */
.form-field-float:not(.has-content):not(.has-value) > p > label {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
}

/* Only show the top label when has-content and has-value classes are present */
.form-field-float.has-content.has-value > p > label {
    display: inline !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Show the inner label when in default state or when value is "Select below" */
.form-field-float:not(.has-content):not(.has-value) .wpcf7-form-control-wrap label,
.form-field-float .wpcf7-form-control-wrap label {
    position: absolute !important;
    top: 10px !important;
    left: 10px !important;
    color: #666 !important;
    font-size: 16px !important;
    background-color: white !important;
    display: block !important;
    width: auto !important;
    height: auto !important;
    transform: none !important;
    opacity: 1 !important;
    visibility: visible !important;
    z-index: 5 !important;
    overflow: visible !important;
    padding: 0 5px !important;
}

/* Hide the inner label only when has-content and has-value classes are present and value is not "Select below" */
.form-field-float.has-content.has-value .wpcf7-form-control-wrap label {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
}

/* Ensure the select box has proper styling */
.form-field-float .wpcf7-form-control-wrap select {
    width: 100% !important;
    padding: 10px !important;
    border: 1px solid #ddd !important;
    border-radius: 4px !important;
    font-size: 16px !important;
    height: 42px !important;
    box-sizing: border-box !important;
    background-color: #fff !important;
    color: #333 !important;
    appearance: none !important;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="12" height="6"><path d="M0 0l6 6 6-6z" fill="%23666"/></svg>') !important;
    background-repeat: no-repeat !important;
    background-position: right 10px center !important;
    padding-right: 30px !important;
}

/* Style for the default option */
.form-field-float .wpcf7-form-control-wrap select option[value="Select below"] {
    color: #666 !important;
    font-style: italic !important;
}

/* Style for other options */
.form-field-float .wpcf7-form-control-wrap select option:not([value="Select below"]) {
    color: #333 !important;
    font-style: normal !important;
}
