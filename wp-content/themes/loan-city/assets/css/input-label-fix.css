/**
 * Input Label Fix - Simplified approach for normal input fields
 * This CSS supports the new approach for handling the transition of labels
 * for normal input fields (not select boxes) when they are focused or have content.
 */

/* Hide the p label for normal inputs (we'll only use the inner label) */
.wpcf7-form .form-field-float.input-field > p > label {
    position: absolute !important;
    opacity: 0 !important;
    visibility: hidden !important;
    pointer-events: none !important;
    height: 1px !important;
    width: 1px !important;
    overflow: hidden !important;
    display: none !important;
}

/* Ensure the inner label is visible and properly positioned for normal inputs */
.wpcf7-form .form-field-float.input-field .wpcf7-form-control-wrap label {
    position: absolute !important;
    top: 10px !important;
    left: 10px !important;
    color: #666 !important;
    font-size: 16px !important;
    transition: all 0.3s ease !important;
    pointer-events: none !important;
    z-index: 5 !important;
    margin: 0 !important;
    padding: 0 5px !important;
    background-color: transparent !important;
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
    width: auto !important;
    transform: none !important;
}

/* When input is focused, move the inner label up and style it */
.wpcf7-form .form-field-float.input-field.has-focus .wpcf7-form-control-wrap label {
    position: absolute !important;
    top: -10px !important;
    left: 10px !important;
    font-size: 12px !important;
    color: #4CAF50 !important; /* Green color for focus */
    background-color: white !important;
    padding: 0 5px !important;
    transition: all 0.3s ease !important;
    display: inline !important;
    width: fit-content !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* When input has content but is not focused, keep the label up but change color */
.wpcf7-form .form-field-float.input-field.has-content:not(.has-focus) .wpcf7-form-control-wrap label {
    position: absolute !important;
    top: -10px !important;
    left: 10px !important;
    font-size: 12px !important;
    color: #666 !important; /* Gray color for filled but not focused */
    background-color: white !important;
    padding: 0 5px !important;
    transition: all 0.3s ease !important;
    display: inline !important;
    width: fit-content !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* Style for focused inputs */
.wpcf7-form .form-field-float.input-field.has-focus .wpcf7-form-control-wrap input,
.wpcf7-form .form-field-float.input-field.has-focus .wpcf7-form-control-wrap textarea {
    outline: none !important;
    border-color: #4CAF50 !important; /* Green color for focus */
    box-shadow: 0 0 0 1px #4CAF50 !important;
}

/* Ensure the placeholder is transparent to allow the label to show */
.wpcf7-form .form-field-float.input-field .wpcf7-form-control-wrap input::placeholder,
.wpcf7-form .form-field-float.input-field .wpcf7-form-control-wrap textarea::placeholder {
    color: transparent !important;
}

/* Make sure the placeholder is transparent to allow the label to show */
.wpcf7-form .form-field-float.input-field .wpcf7-form-control-wrap input:placeholder-shown,
.wpcf7-form .form-field-float.input-field .wpcf7-form-control-wrap textarea:placeholder-shown {
    color: transparent !important;
}

/* Override any inline styles that might be interfering */
.wpcf7-form .form-field-float.input-field > p > label[style] {
    opacity: 0 !important;
    visibility: hidden !important;
    display: none !important;
}

/* Ensure the inner label is always visible */
.wpcf7-form .form-field-float.input-field .wpcf7-form-control-wrap label[style] {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* Ensure the inner label moves up when the input is focused */
.wpcf7-form .form-field-float.input-field.has-focus .wpcf7-form-control-wrap label[style] {
    top: -10px !important;
    font-size: 12px !important;
    color: #4CAF50 !important;
    background-color: white !important;
    display: inline !important;
    width: fit-content !important;
}

/* Ensure the inner label stays up when the input has content */
.wpcf7-form .form-field-float.input-field.has-content:not(.has-focus) .wpcf7-form-control-wrap label[style] {
    top: -10px !important;
    font-size: 12px !important;
    color: #666 !important;
    background-color: white !important;
    display: inline !important;
    width: fit-content !important;
}
