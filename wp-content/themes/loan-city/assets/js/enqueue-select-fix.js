/**
 * This file is a placeholder to prevent 404 errors.
 * The functionality has been moved to form-controls.js
 * The CSS has been merged into form-controls.css
 */

// Log a message to the console
console.log('Enqueue select fix script initialized');

// Create a link element for the CSS
var link = document.createElement('link');
link.rel = 'stylesheet';
link.type = 'text/css';
link.href = '/wp-content/themes/loan-city/assets/css/form-controls.css';
link.media = 'all';
link.onload = function() {
    console.log('Form controls CSS loaded successfully');
};

// Add it to the head
document.head.appendChild(link);

// Log a message to the console
console.log('Select box functionality and CSS have been moved to form-controls.js and form-controls.css');
