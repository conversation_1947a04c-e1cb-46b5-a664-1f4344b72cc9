/**
 * Form Scripts Loader
 * This script checks if a Contact Form 7 form is present on the page
 * and loads the necessary scripts for form label fixes.
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Form scripts loader initialized');

    // Function to load scripts and styles
    function loadFormScripts() {
        console.log('Loading form scripts and styles');

        // Load the CSS files first
        var selectBoxCss = document.createElement('link');
        selectBoxCss.rel = 'stylesheet';
        selectBoxCss.href = '/wp-content/themes/loan-city/assets/css/select-boxes.css';
        selectBoxCss.onload = function() {
            console.log('Select box CSS loaded successfully');
        };
        document.head.appendChild(selectBoxCss);

        var inputLabelCss = document.createElement('link');
        inputLabelCss.rel = 'stylesheet';
        inputLabelCss.href = '/wp-content/themes/loan-city/assets/css/input-label-fix.css';
        inputLabelCss.onload = function() {
            console.log('Input label fix CSS loaded successfully');

            // After CSS is loaded, load the JavaScript files

            // Load the select box fix script
            var selectFixScript = document.createElement('script');
            selectFixScript.src = '/wp-content/themes/loan-city/assets/js/enqueue-select-fix.js';
            selectFixScript.onload = function() {
                console.log('Select box fix script loaded successfully');
            };
            document.body.appendChild(selectFixScript);

            // Load the input label fix script directly (skip the enqueue script)
            var inputLabelFixScript = document.createElement('script');
            inputLabelFixScript.src = '/wp-content/themes/loan-city/assets/js/input-label-fix.js';
            inputLabelFixScript.onload = function() {
                console.log('Input label fix script loaded successfully');
            };
            document.body.appendChild(inputLabelFixScript);
        };
        document.head.appendChild(inputLabelCss);
    }

    // Check if a Contact Form 7 form is present on the page
    var cf7Form = document.querySelector('.wpcf7-form');

    if (cf7Form) {
        console.log('Contact Form 7 form found on the page');
        loadFormScripts();
    } else {
        console.log('No Contact Form 7 form found on the page initially, waiting for it to appear');

        // Set up a MutationObserver to watch for the form being added to the page
        var observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.addedNodes && mutation.addedNodes.length > 0) {
                    for (var i = 0; i < mutation.addedNodes.length; i++) {
                        var node = mutation.addedNodes[i];
                        if (node.nodeType === 1 && (node.classList && node.classList.contains('wpcf7-form') || node.querySelector('.wpcf7-form'))) {
                            console.log('Contact Form 7 form found after page load');
                            loadFormScripts();
                            observer.disconnect();
                            return;
                        }
                    }
                }
            });
        });

        // Start observing the document body for changes
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });

        // Also try loading the scripts after a delay, in case the form is added in a way the observer doesn't catch
        setTimeout(function() {
            if (document.querySelector('.wpcf7-form')) {
                console.log('Contact Form 7 form found after delay');
                loadFormScripts();
                observer.disconnect();
            }
        }, 1000);
    }

    // Also listen for Contact Form 7 events
    document.addEventListener('wpcf7invalid', function() {
        console.log('Contact Form 7 invalid event triggered');
        setTimeout(function() {
            if (document.querySelector('.wpcf7-form')) {
                loadFormScripts();
            }
        }, 500);
    });

    document.addEventListener('wpcf7mailsent', function() {
        console.log('Contact Form 7 mailsent event triggered');
        setTimeout(function() {
            if (document.querySelector('.wpcf7-form')) {
                loadFormScripts();
            }
        }, 500);
    });
});
