/**
 * Direct fix for the focus state of inputs in the loan application form
 */

document.addEventListener('DOMContentLoaded', function() {
    // Function to fix the focus state of inputs
    function fixFocusState() {
        // Find all form fields
        const formFields = document.querySelectorAll('.form-field-float');
        
        formFields.forEach(function(field) {
            // Find all inputs, selects, and textareas in the field
            const inputs = field.querySelectorAll('input, select, textarea');
            
            inputs.forEach(function(input) {
                // Add focus event listener
                input.addEventListener('focus', function() {
                    // Add has-focus class to the field
                    field.classList.add('has-focus');
                    
                    // Find the label in the p tag
                    const pLabel = field.querySelector('p > label');
                    if (pLabel) {
                        // Make sure the label is visible
                        pLabel.style.display = 'inline';
                        pLabel.style.visibility = 'visible';
                        pLabel.style.opacity = '1';
                        pLabel.style.position = 'absolute';
                        pLabel.style.top = '-10px';
                        pLabel.style.left = '10px';
                        pLabel.style.fontSize = '12px';
                        pLabel.style.color = '#4CAF50';
                        pLabel.style.backgroundColor = 'white';
                        pLabel.style.padding = '0 5px';
                        pLabel.style.width = 'fit-content';
                        pLabel.style.height = 'auto';
                        pLabel.style.zIndex = '10';
                        pLabel.style.transform = 'translateY(0) scale(0.75)';
                        
                        // Add !important to all styles
                        pLabel.setAttribute('style', pLabel.getAttribute('style') + ' !important');
                    }
                });
                
                // Add blur event listener
                input.addEventListener('blur', function() {
                    // Remove has-focus class from the field
                    field.classList.remove('has-focus');
                    
                    // Find the label in the p tag
                    const pLabel = field.querySelector('p > label');
                    
                    // If the input has no value, reset the label
                    if (!this.value || this.value === '' || (this.tagName === 'SELECT' && this.value === 'Select below')) {
                        if (pLabel) {
                            // Hide the label
                            pLabel.style.display = 'none';
                            pLabel.style.visibility = 'hidden';
                            pLabel.style.opacity = '0';
                            
                            // Add !important to all styles
                            pLabel.setAttribute('style', pLabel.getAttribute('style') + ' !important');
                        }
                    } else {
                        if (pLabel) {
                            // Keep the label visible but change the color
                            pLabel.style.color = '#666';
                            
                            // Add !important to all styles
                            pLabel.setAttribute('style', pLabel.getAttribute('style') + ' !important');
                        }
                    }
                });
            });
        });
    }
    
    // Run the fix immediately and after a delay
    fixFocusState();
    setTimeout(fixFocusState, 500);
    setTimeout(fixFocusState, 1000);
    
    // Also run when the window is fully loaded
    window.addEventListener('load', function() {
        fixFocusState();
        setTimeout(fixFocusState, 500);
    });
});
