/**
 * Form Controls - Combined script for floating labels and select box fixes
 * Merges functionality from floating-labels.js and select-box-fix.js
 */

document.addEventListener('DOMContentLoaded', function() {
    // Add direct style to ensure floating labels and select boxes work correctly
    const style = document.createElement('style');
    style.textContent = `
        /* Form field container */
        .wpcf7-form .form-field-float {
            position: relative;
            margin-bottom: 20px;
        }
        .wpcf7-form .form-field-float .wpcf7-form-control-wrap {
            position: relative;
            display: block;
            width: 100%;
        }
        /* Hide original label */
        .wpcf7-form .form-field-float label:first-child {
            position: absolute;
            opacity: 0;
            visibility: hidden;
        }
        /* Style for the floating label inside the input */
        .wpcf7-form .form-field-float .wpcf7-form-control-wrap label {
            position: absolute;
            top: 10px;
            left: 10px;
            color: #666;
            font-size: 16px;
            transition: all 0.3s ease;
            pointer-events: none;
            z-index: 5;
            margin: 0;
            padding: 0 5px;
            background-color: transparent;
            width: calc(100% - 20px);
            text-overflow: ellipsis;
            white-space: nowrap;
            overflow: hidden;
        }
        /* When input is focused or has content, move the label up */
        .wpcf7-form .form-field-float.has-focus .wpcf7-form-control-wrap label,
        .wpcf7-form .form-field-float.has-value .wpcf7-form-control-wrap label,
        .wpcf7-form .form-field-float.has-content .wpcf7-form-control-wrap label,
        .wpcf7-form .form-field-float .wpcf7-form-control-wrap input:focus ~ label,
        .wpcf7-form .form-field-float .wpcf7-form-control-wrap input:not(:placeholder-shown) ~ label,
        .wpcf7-form .form-field-float .wpcf7-form-control-wrap select:focus ~ label,
        .wpcf7-form .form-field-float .wpcf7-form-control-wrap select:not([value=""]):not([value="0"]):not([value="Select below"]) ~ label,
        .wpcf7-form .form-field-float .wpcf7-form-control-wrap textarea:focus ~ label,
        .wpcf7-form .form-field-float .wpcf7-form-control-wrap textarea:not(:placeholder-shown) ~ label {
            top: -10px !important;
            left: 10px !important;
            font-size: 12px !important;
            color: #4CAF50 !important; /* Green color for focus */
            background-color: white !important;
            padding: 0 5px !important;
            z-index: 10 !important;
            transform: translateY(0) scale(0.75) !important;
            display: inline !important;
            width: fit-content !important;
        }

        /* Different color for focused state */
        .wpcf7-form .form-field-float.has-focus .wpcf7-form-control-wrap label {
            color: #4CAF50 !important; /* Green color for focus */
        }

        /* Different color for filled but not focused state */
        .wpcf7-form .form-field-float.has-content:not(.has-focus) .wpcf7-form-control-wrap label {
            color: #666 !important; /* Gray color for filled but not focused */
        }
        /* Make placeholders transparent to allow the label to show */
        .wpcf7-form .form-field-float .wpcf7-form-control-wrap input::placeholder,
        .wpcf7-form .form-field-float .wpcf7-form-control-wrap textarea::placeholder {
            color: transparent !important;
        }
        /* Ensure labels are visible by default */
        .wpcf7-form.using-floating-labels .form-field-float .wpcf7-form-control-wrap label {
            display: block !important;
            opacity: 1 !important;
            visibility: visible !important;
            position: absolute !important;
            top: 10px !important;
            left: 10px !important;
            color: #666 !important;
            font-size: 16px !important;
            transition: all 0.3s ease !important;
            pointer-events: none !important;
            z-index: 5 !important;
            margin: 0 !important;
            padding: 0 5px !important;
            background-color: transparent !important;
        }

        /* Hide the original label and <br> tag */
        .wpcf7-form.using-floating-labels .form-field-float > p > br {
            display: none !important;
        }
        /* Style for inputs with floating labels */
        .wpcf7-form .form-field-float .wpcf7-form-control-wrap input,
        .wpcf7-form .form-field-float .wpcf7-form-control-wrap select,
        .wpcf7-form .form-field-float .wpcf7-form-control-wrap textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            background-color: transparent;
            height: 42px;
            position: relative;
            z-index: 1;
        }

        /* Special handling for select boxes with "Select below" value */
        .wpcf7-form .form-field-float:not(.has-content):not(.has-value) .wpcf7-form-control-wrap select ~ label {
            display: block !important;
            opacity: 1 !important;
            visibility: visible !important;
            position: absolute !important;
            top: 10px !important;
            left: 10px !important;
            color: #666 !important;
            font-size: 16px !important;
            background-color: transparent !important;
            transform: none !important;
            z-index: 5 !important;
        }

        /* Hide the top label when select has default value */
        .wpcf7-form.using-floating-labels .form-field-float:not(.has-content):not(.has-value) > p > label {
            display: none !important;
        }

        /* Show the top label when select has a value */
        .wpcf7-form.using-floating-labels .form-field-float.has-content > p > label,
        .wpcf7-form.using-floating-labels .form-field-float.has-value > p > label {
            position: absolute !important;
            top: -10px !important;
            left: 10px !important;
            font-size: 12px !important;
            color: #666 !important;
            background-color: white !important;
            padding: 0 5px !important;
            display: inline !important;
            width: fit-content !important;
            height: auto !important;
            opacity: 1 !important;
            visibility: visible !important;
            z-index: 5 !important;
            overflow: visible !important;
        }

        /* Hide the inner label when select has a value */
        .wpcf7-form.using-floating-labels .form-field-float.has-content .wpcf7-form-control-wrap label,
        .wpcf7-form.using-floating-labels .form-field-float.has-value .wpcf7-form-control-wrap label {
            display: none !important;
        }
    `;
    document.head.appendChild(style);

    // Initialize form controls
    setTimeout(initFormControls, 100); // Small delay to ensure CF7 has fully initialized
    setTimeout(fixAllSelectBoxes, 200); // Additional delay to fix select boxes specifically

    // Also initialize immediately to ensure it runs as soon as possible
    initFormControls();
    fixAllSelectBoxes();

    // Re-initialize on CF7 form load/reload
    document.addEventListener('wpcf7invalid', function() {
        setTimeout(initFormControls, 100);
        setTimeout(fixAllSelectBoxes, 200);
    });
    document.addEventListener('wpcf7spam', function() {
        setTimeout(initFormControls, 100);
        setTimeout(fixAllSelectBoxes, 200);
    });
    document.addEventListener('wpcf7mailsent', function() {
        setTimeout(initFormControls, 100);
        setTimeout(fixAllSelectBoxes, 200);
    });
    document.addEventListener('wpcf7mailfailed', function() {
        setTimeout(initFormControls, 100);
        setTimeout(fixAllSelectBoxes, 200);
    });
    document.addEventListener('wpcf7submit', function() {
        setTimeout(initFormControls, 100);
        setTimeout(fixAllSelectBoxes, 200);
    });

    // Function to initialize all form controls (floating labels and select boxes)
    function initFormControls() {
        const forms = document.querySelectorAll('.wpcf7-form');

        if (forms.length > 0) {
            // Add a class to the form to indicate that we're using floating labels
            forms.forEach(form => {
                form.classList.add('using-floating-labels');
            });

            // Handle date input fields
            initDateInputs();

            // Find all form fields with floating labels
            const floatingFields = document.querySelectorAll('.wpcf7-form .form-field-float');

            floatingFields.forEach((field) => {
                // In CF7, inputs are wrapped in .wpcf7-form-control-wrap
                const controlWrap = field.querySelector('.wpcf7-form-control-wrap');
                if (!controlWrap) {
                    return;
                }

                // Get the input element
                const input = controlWrap.querySelector('input, select, textarea');
                if (!input) {
                    return;
                }

                // Get the label element (could be inside a p tag)
                let label = field.querySelector('label');

                if (!label) {
                    return;
                }

                // Move the label inside the control wrap for proper positioning
                if (!controlWrap.querySelector('label')) {
                    // Clone the label to avoid removing it from its original position
                    const labelClone = label.cloneNode(true);
                    controlWrap.appendChild(labelClone);

                    // Hide the original label visually but keep it for screen readers
                    label.style.opacity = '0';
                    label.style.position = 'absolute';
                    label.style.pointerEvents = 'none';
                    label.style.height = '1px';
                    label.style.width = '1px';
                    label.style.overflow = 'hidden';

                    // Position the cloned label inside the input
                    labelClone.style.position = 'absolute';
                    labelClone.style.top = '10px';
                    labelClone.style.left = '10px';
                    labelClone.style.color = '#666';
                    labelClone.style.fontSize = '16px';
                    labelClone.style.transition = 'all 0.3s ease';
                    labelClone.style.pointerEvents = 'none';
                    labelClone.style.zIndex = '5';
                    labelClone.style.margin = '0';
                    labelClone.style.padding = '0 5px';
                    labelClone.style.backgroundColor = 'transparent';
                    labelClone.style.display = 'block';
                    labelClone.style.opacity = '1';
                    labelClone.style.visibility = 'visible';

                    // Also hide the <br> tag if it exists
                    const br = label.nextElementSibling;
                    if (br && br.tagName === 'BR') {
                        br.style.display = 'none';
                    }
                }

                // For select elements - special handling
                if (input.tagName === 'SELECT') {
                    input.addEventListener('change', function() {
                        // Check if the value is not "Select below"
                        if (this.value && this.value !== '' && this.value !== 'Select below') {
                            field.classList.add('has-value');
                            field.classList.add('has-content');

                            // Hide the inner label with !important to ensure it's hidden
                            const innerLabel = controlWrap.querySelector('label');
                            if (innerLabel) {
                                innerLabel.setAttribute('style', 'display: none !important; visibility: hidden !important; opacity: 0 !important;');
                            }

                            // Show the top label with !important to ensure it's visible
                            const topLabel = field.querySelector('p > label');
                            if (topLabel) {
                                topLabel.setAttribute('style', 'position: absolute !important; top: -10px !important; left: 10px !important; font-size: 12px !important; color: #666 !important; background-color: white !important; padding: 0 5px !important; display: inline !important; width: fit-content !important; height: auto !important; opacity: 1 !important; visibility: visible !important; z-index: 5 !important; overflow: visible !important;');
                            }
                        } else {
                            field.classList.remove('has-value');
                            field.classList.remove('has-content');

                            // Show the inner label with !important to ensure it's visible
                            const innerLabel = controlWrap.querySelector('label');
                            if (innerLabel) {
                                innerLabel.setAttribute('style', 'position: absolute !important; top: 10px !important; left: 10px !important; color: #666 !important; font-size: 16px !important; background-color: white !important; display: block !important; width: auto !important; height: auto !important; transform: none !important; opacity: 1 !important; visibility: visible !important; z-index: 5 !important; overflow: visible !important; padding: 0 5px !important;');
                            }

                            // Hide the top label with !important to ensure it's hidden
                            const topLabel = field.querySelector('p > label');
                            if (topLabel) {
                                topLabel.setAttribute('style', 'display: none !important; visibility: hidden !important; opacity: 0 !important;');
                            }
                        }

                        // Force a repaint to ensure styles are applied
                        field.style.display = 'none';
                        field.offsetHeight; // This line forces a repaint
                        field.style.display = '';
                    });

                    // Initialize select state - don't add classes if value is "Select below"
                    if (input.value && input.value !== '' && input.value !== 'Select below') {
                        field.classList.add('has-value');
                        field.classList.add('has-content');

                        // Hide the inner label with !important to ensure it's hidden
                        const innerLabel = controlWrap.querySelector('label');
                        if (innerLabel) {
                            innerLabel.setAttribute('style', 'display: none !important; visibility: hidden !important; opacity: 0 !important;');
                        }

                        // Show the top label with !important to ensure it's visible
                        const topLabel = field.querySelector('p > label');
                        if (topLabel) {
                            topLabel.setAttribute('style', 'position: absolute !important; top: -10px !important; left: 10px !important; font-size: 12px !important; color: #666 !important; background-color: white !important; padding: 0 5px !important; display: inline !important; width: fit-content !important; height: auto !important; opacity: 1 !important; visibility: visible !important; z-index: 5 !important; overflow: visible !important;');
                        }
                    } else {
                        // Ensure classes are removed for "Select below"
                        field.classList.remove('has-value');
                        field.classList.remove('has-content');

                        // Show the inner label with !important to ensure it's visible
                        const innerLabel = controlWrap.querySelector('label');
                        if (innerLabel) {
                            innerLabel.setAttribute('style', 'position: absolute !important; top: 10px !important; left: 10px !important; color: #666 !important; font-size: 16px !important; background-color: white !important; display: block !important; width: auto !important; height: auto !important; transform: none !important; opacity: 1 !important; visibility: visible !important; z-index: 5 !important; overflow: visible !important; padding: 0 5px !important;');
                        }

                        // Hide the top label with !important to ensure it's hidden
                        const topLabel = field.querySelector('p > label');
                        if (topLabel) {
                            topLabel.setAttribute('style', 'display: none !important; visibility: hidden !important; opacity: 0 !important;');
                        }
                    }

                    // Force a repaint to ensure styles are applied
                    field.style.display = 'none';
                    field.offsetHeight; // This line forces a repaint
                    field.style.display = '';
                }

                // For regular inputs with existing values
                if (input.tagName !== 'SELECT' && input.value && input.value !== '') {
                    field.classList.add('has-value');
                    field.classList.add('has-content');
                }

                // Add focus event listener
                input.addEventListener('focus', function() {
                    field.classList.add('has-focus');

                    // Get the top label (p > label)
                    const topLabel = field.querySelector('p > label');
                    if (topLabel) {
                        // Make sure the top label is visible
                        topLabel.style.display = 'inline';
                        topLabel.style.visibility = 'visible';
                        topLabel.style.opacity = '1';
                        topLabel.style.position = 'absolute';
                        topLabel.style.top = '-10px';
                        topLabel.style.left = '10px';
                        topLabel.style.fontSize = '12px';
                        topLabel.style.color = '#4CAF50';
                        topLabel.style.backgroundColor = 'white';
                        topLabel.style.padding = '0 5px';
                        topLabel.style.width = 'fit-content';
                        topLabel.style.height = 'auto';
                        topLabel.style.zIndex = '10';
                        topLabel.style.transform = 'translateY(0) scale(0.75)';

                        // Add !important to all styles
                        topLabel.setAttribute('style', topLabel.getAttribute('style') + ' !important');
                    }

                    // Get any other label element
                    const otherLabels = field.querySelectorAll('label:not(p > label)');
                    otherLabels.forEach(label => {
                        // Apply focus styling directly to the label
                        label.style.top = '-10px';
                        label.style.left = '10px';
                        label.style.fontSize = '12px';
                        label.style.color = '#4CAF50';
                        label.style.backgroundColor = 'white';
                        label.style.padding = '0 5px';
                        label.style.display = 'inline';
                        label.style.width = 'fit-content';
                        label.style.zIndex = '10';
                        label.style.transform = 'translateY(0) scale(0.75)';

                        // Add !important to all styles
                        label.setAttribute('style', label.getAttribute('style') + ' !important');
                    });

                    // Also apply to the label inside the control wrap
                    const controlWrap = field.querySelector('.wpcf7-form-control-wrap');
                    if (controlWrap) {
                        const innerLabel = controlWrap.querySelector('label');
                        if (innerLabel) {
                            innerLabel.style.top = '-10px';
                            innerLabel.style.left = '10px';
                            innerLabel.style.fontSize = '12px';
                            innerLabel.style.color = '#4CAF50';
                            innerLabel.style.backgroundColor = 'white';
                            innerLabel.style.padding = '0 5px';
                            innerLabel.style.display = 'inline';
                            innerLabel.style.width = 'fit-content';
                            innerLabel.style.zIndex = '10';
                            innerLabel.style.transform = 'translateY(0) scale(0.75)';

                            // Add !important to all styles
                            innerLabel.setAttribute('style', innerLabel.getAttribute('style') + ' !important');
                        }
                    }
                });

                // Add blur event listener
                input.addEventListener('blur', function() {
                    field.classList.remove('has-focus');

                    // Reset label styling if there's no value
                    if (!this.value || this.value === '' || (this.tagName === 'SELECT' && this.value === 'Select below')) {
                        // Get the label element
                        const label = field.querySelector('label');
                        if (label) {
                            // Reset to default styling
                            label.style.top = '10px';
                            label.style.left = '10px';
                            label.style.fontSize = '16px';
                            label.style.color = '#666';
                            label.style.backgroundColor = 'transparent';
                            label.style.padding = '0 5px';
                            label.style.display = 'block';
                            label.style.width = 'auto';
                            label.style.zIndex = '5';
                            label.style.transform = 'none';

                            // Add !important to all styles
                            label.setAttribute('style', label.getAttribute('style') + ' !important');
                        }

                        // Also reset the label inside the control wrap
                        const controlWrap = field.querySelector('.wpcf7-form-control-wrap');
                        if (controlWrap) {
                            const innerLabel = controlWrap.querySelector('label');
                            if (innerLabel) {
                                innerLabel.style.top = '10px';
                                innerLabel.style.left = '10px';
                                innerLabel.style.fontSize = '16px';
                                innerLabel.style.color = '#666';
                                innerLabel.style.backgroundColor = 'transparent';
                                innerLabel.style.padding = '0 5px';
                                innerLabel.style.display = 'block';
                                innerLabel.style.width = 'auto';
                                innerLabel.style.zIndex = '5';
                                innerLabel.style.transform = 'none';

                                // Add !important to all styles
                                innerLabel.setAttribute('style', innerLabel.getAttribute('style') + ' !important');
                            }
                        }
                    }

                    // Check if the field has a value
                    if (this.value && this.value !== '') {
                        // For select elements, don't add classes if value is "Select below"
                        if (this.tagName === 'SELECT' && this.value === 'Select below') {
                            field.classList.remove('has-value');
                            field.classList.remove('has-content');

                            // Show the inner label with !important to ensure it's visible
                            const innerLabel = controlWrap.querySelector('label');
                            if (innerLabel) {
                                innerLabel.setAttribute('style', 'position: absolute !important; top: 10px !important; left: 10px !important; color: #666 !important; font-size: 16px !important; background-color: white !important; display: block !important; width: auto !important; height: auto !important; transform: none !important; opacity: 1 !important; visibility: visible !important; z-index: 5 !important; overflow: visible !important; padding: 0 5px !important;');
                            }

                            // Hide the top label with !important to ensure it's hidden
                            const topLabel = field.querySelector('p > label');
                            if (topLabel) {
                                topLabel.setAttribute('style', 'display: none !important; visibility: hidden !important; opacity: 0 !important;');
                            }
                        } else {
                            field.classList.add('has-value');
                            field.classList.add('has-content');

                            if (this.tagName === 'SELECT') {
                                // Hide the inner label with !important to ensure it's hidden
                                const innerLabel = controlWrap.querySelector('label');
                                if (innerLabel) {
                                    innerLabel.setAttribute('style', 'display: none !important; visibility: hidden !important; opacity: 0 !important;');
                                }

                                // Show the top label with !important to ensure it's visible
                                const topLabel = field.querySelector('p > label');
                                if (topLabel) {
                                    topLabel.setAttribute('style', 'position: absolute !important; top: -10px !important; left: 10px !important; font-size: 12px !important; color: #666 !important; background-color: white !important; padding: 0 5px !important; display: inline !important; width: fit-content !important; height: auto !important; opacity: 1 !important; visibility: visible !important; z-index: 5 !important; overflow: visible !important;');
                                }
                            }
                        }

                        // Force a repaint to ensure styles are applied
                        if (this.tagName === 'SELECT') {
                            field.style.display = 'none';
                            field.offsetHeight; // This line forces a repaint
                            field.style.display = '';
                        }
                    } else {
                        field.classList.remove('has-value');
                        field.classList.remove('has-content');
                    }
                });

                // Make sure placeholder is empty space to allow label to show
                if (input.hasAttribute('placeholder')) {
                    // Save the original placeholder for accessibility
                    if (!input.hasAttribute('data-original-placeholder')) {
                        input.setAttribute('data-original-placeholder', input.getAttribute('placeholder'));
                    }
                    // Set placeholder to space to allow the label to be visible
                    input.setAttribute('placeholder', ' ');
                } else if (input.tagName !== 'SELECT') {
                    // Add placeholder if it doesn't exist (except for select elements)
                    input.setAttribute('placeholder', ' ');
                }

                // Ensure the label inside the control wrap is visible
                const innerLabel = controlWrap.querySelector('label');
                if (innerLabel) {
                    innerLabel.style.display = 'block';
                    innerLabel.style.position = 'absolute';
                    innerLabel.style.top = '10px';
                    innerLabel.style.left = '10px';
                    innerLabel.style.zIndex = '5';
                    innerLabel.style.opacity = '1';
                    innerLabel.style.visibility = 'visible';
                    innerLabel.style.pointerEvents = 'none';
                    innerLabel.style.color = '#666';
                    innerLabel.style.fontSize = '16px';
                    innerLabel.style.transition = 'all 0.3s ease';
                }

                // Initial check for values
                if (input.value && input.value !== '') {
                    // For select elements, don't add classes if value is "Select below"
                    if (input.tagName === 'SELECT' && input.value === 'Select below') {
                        field.classList.remove('has-value');
                        field.classList.remove('has-content');

                        // Show the inner label with !important to ensure it's visible
                        if (innerLabel) {
                            innerLabel.setAttribute('style', 'position: absolute !important; top: 10px !important; left: 10px !important; color: #666 !important; font-size: 16px !important; background-color: white !important; display: block !important; width: auto !important; height: auto !important; transform: none !important; opacity: 1 !important; visibility: visible !important; z-index: 5 !important; overflow: visible !important; padding: 0 5px !important;');
                        }

                        // Hide the top label with !important to ensure it's hidden
                        const topLabel = field.querySelector('p > label');
                        if (topLabel) {
                            topLabel.setAttribute('style', 'display: none !important; visibility: hidden !important; opacity: 0 !important;');
                        }
                    } else {
                        field.classList.add('has-value');
                        field.classList.add('has-content');

                        if (input.tagName === 'SELECT') {
                            // Hide the inner label with !important to ensure it's hidden
                            if (innerLabel) {
                                innerLabel.setAttribute('style', 'display: none !important; visibility: hidden !important; opacity: 0 !important;');
                            }

                            // Show the top label with !important to ensure it's visible
                            const topLabel = field.querySelector('p > label');
                            if (topLabel) {
                                topLabel.setAttribute('style', 'position: absolute !important; top: -10px !important; left: 10px !important; font-size: 12px !important; color: #666 !important; background-color: white !important; padding: 0 5px !important; display: inline !important; width: fit-content !important; height: auto !important; opacity: 1 !important; visibility: visible !important; z-index: 5 !important; overflow: visible !important;');
                            }
                        }
                    }

                    // Force a repaint to ensure styles are applied for select elements
                    if (input.tagName === 'SELECT') {
                        field.style.display = 'none';
                        field.offsetHeight; // This line forces a repaint
                        field.style.display = '';
                    }
                }
            });
        }
    }

    // Function to initialize date input fields
    function initDateInputs() {
        // Handle legacy date inputs (separate day/month/year fields)
        const legacyDateInputs = document.querySelectorAll('.wpcf7-form .date-inputs');

        legacyDateInputs.forEach((dateInput) => {
            // Get the date label
            const dateLabel = dateInput.querySelector('.date-label');
            if (!dateLabel) {
                return;
            }

            // Get the input field
            const input = dateInput.querySelector('input[type="number"]');
            if (!input) {
                return;
            }

            // Style the input field
            input.style.width = '100%';
            input.style.padding = '10px';
            input.style.border = '1px solid #ddd';
            input.style.borderRadius = '4px';
            input.style.fontSize = '16px';
            input.style.height = '42px';
            input.style.boxSizing = 'border-box';

            // Style the date label
            dateLabel.style.textAlign = 'center';
            dateLabel.style.fontWeight = '500';
            dateLabel.style.fontSize = '14px';
            dateLabel.style.color = '#666';
            dateLabel.style.marginBottom = '5px';

            // Hide empty paragraphs
            const emptyParagraphs = dateInput.querySelectorAll('p:empty');
            emptyParagraphs.forEach(p => {
                p.style.display = 'none';
            });
        });

        // Handle new date picker inputs
        const datePickerInputs = document.querySelectorAll('.wpcf7-form .wpcf7-date');
        datePickerInputs.forEach((input) => {
            // Ensure the input has proper styling
            input.style.width = '100%';
            input.style.padding = '10px';
            input.style.border = '1px solid #ddd';
            input.style.borderRadius = '4px';
            input.style.fontSize = '16px';
            input.style.height = '42px';
            input.style.boxSizing = 'border-box';

            // Handle floating label behavior
            input.addEventListener('focus', function() {
                const parent = this.closest('.form-field-float');
                if (parent) {
                    parent.classList.add('has-focus');
                }
            });

            input.addEventListener('blur', function() {
                const parent = this.closest('.form-field-float');
                if (parent) {
                    parent.classList.remove('has-focus');
                    if (this.value) {
                        parent.classList.add('has-content');
                    } else {
                        parent.classList.remove('has-content');
                    }
                }
            });

            // Check initial state
            if (input.value) {
                const parent = input.closest('.form-field-float');
                if (parent) {
                    parent.classList.add('has-content');
                }
            }
        });
    }

    // Function to specifically fix select boxes
    function fixAllSelectBoxes() {
        const selectBoxes = document.querySelectorAll('.form-field-float select');

        selectBoxes.forEach(function(select) {
            const field = select.closest('.form-field-float');
            if (!field) return;

            const controlWrap = field.querySelector('.wpcf7-form-control-wrap');
            if (!controlWrap) return;

            // Check if the select has a value other than "Select below"
            if (select.value && select.value !== '' && select.value !== 'Select below') {
                field.classList.add('has-value');
                field.classList.add('has-content');

                // Hide the inner label with !important
                const innerLabel = controlWrap.querySelector('label');
                if (innerLabel) {
                    innerLabel.setAttribute('style', 'display: none !important; visibility: hidden !important; opacity: 0 !important;');
                }

                // Show the top label with !important
                const topLabel = field.querySelector('p > label');
                if (topLabel) {
                    topLabel.setAttribute('style', 'position: absolute !important; top: -10px !important; left: 10px !important; font-size: 12px !important; color: #666 !important; background-color: white !important; padding: 0 5px !important; display: inline !important; width: fit-content !important; height: auto !important; opacity: 1 !important; visibility: visible !important; z-index: 5 !important; overflow: visible !important;');
                }
            } else {
                field.classList.remove('has-value');
                field.classList.remove('has-content');

                // Show the inner label with !important
                const innerLabel = controlWrap.querySelector('label');
                if (innerLabel) {
                    innerLabel.setAttribute('style', 'position: absolute !important; top: 10px !important; left: 10px !important; color: #666 !important; font-size: 16px !important; background-color: white !important; display: block !important; width: auto !important; height: auto !important; transform: none !important; opacity: 1 !important; visibility: visible !important; z-index: 5 !important; overflow: visible !important; padding: 0 5px !important;');
                }

                // Hide the top label with !important
                const topLabel = field.querySelector('p > label');
                if (topLabel) {
                    topLabel.setAttribute('style', 'display: none !important; visibility: hidden !important; opacity: 0 !important;');
                }
            }

            // Force a repaint to ensure styles are applied
            field.style.display = 'none';
            field.offsetHeight; // This line forces a repaint
            field.style.display = '';
        });
    }

    // Also initialize when the window loads (backup)
    window.addEventListener('load', function() {
        // Run immediately
        initFormControls();
        fixAllSelectBoxes();

        // And with a delay to ensure everything is fully loaded
        setTimeout(initFormControls, 500);
        setTimeout(fixAllSelectBoxes, 600);

        // And one more time after a longer delay for good measure
        setTimeout(initFormControls, 1000);
        setTimeout(fixAllSelectBoxes, 1100);
    });

    // Add a mutation observer to detect when the form is loaded or modified
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                // Check if any of the added nodes are form elements
                for (let i = 0; i < mutation.addedNodes.length; i++) {
                    const node = mutation.addedNodes[i];
                    if (node.nodeType === 1 && (
                        node.classList.contains('wpcf7-form') ||
                        node.querySelector('.wpcf7-form')
                    )) {
                        setTimeout(initFormControls, 100);
                        setTimeout(fixAllSelectBoxes, 200);
                        break;
                    }
                }
            }
        });
    });

    // Start observing the document body for changes
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
});
