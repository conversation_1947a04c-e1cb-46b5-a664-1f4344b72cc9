/**
 * Simple translation functionality for Loan City theme
 * Uses direct Google Translate link instead of embedding the API
 */

console.log('Translate.js file loaded - Simple version');

// Function to initialize translation functionality
function initTranslation() {
    console.log('Initializing simple translation functionality');

    // Check if we need to highlight the Chinese indicator based on URL parameter
    const url = new URL(window.location.href);
    const langParam = url.searchParams.get('lang');

    // If the language parameter is set to 'zh', update the visual indicator
    if (langParam === 'zh') {
        console.log('Language parameter is set to Chinese');

        // Update the visual indicator
        const chineseText = document.querySelector('.language-selector span');
        if (chineseText) {
            chineseText.style.color = '#4285F4';
            chineseText.style.fontWeight = 'bold';
        }

        // If we're on the Chinese version, redirect to Google Translate
        redirectToGoogleTranslate();
    }

    // Add click event listener to the language selector
    const languageSelector = document.querySelector('#translate-toggle');
    if (languageSelector) {
        console.log('Language selector found, adding click event');
        languageSelector.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Language selector clicked');
            toggleLanguage();
        });
    } else {
        console.error('Language selector with ID #translate-toggle not found');
        // Try to find it by class as a fallback
        const fallbackSelector = document.querySelector('.language-selector a');
        if (fallbackSelector) {
            console.log('Found language selector by class, adding click event');
            fallbackSelector.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('Language selector clicked (fallback)');
                toggleLanguage();
            });
        } else {
            console.error('No language selector found at all');
        }
    }
}

/**
 * Toggle between English and Chinese
 */
function toggleLanguage() {
    console.log('Toggling language');

    // Get the current URL and check if it has a language parameter
    const url = new URL(window.location.href);
    const currentLang = url.searchParams.get('lang');

    // If we're already in Chinese, switch back to English
    if (currentLang === 'zh') {
        console.log('Currently in Chinese, switching back to English');
        url.searchParams.delete('lang');
        window.location.href = url.toString();
        return;
    }

    // Otherwise, switch to Chinese
    console.log('Switching to Chinese');
    url.searchParams.set('lang', 'zh');
    window.location.href = url.toString();
}

/**
 * Redirect to Google Translate
 */
function redirectToGoogleTranslate() {
    console.log('Redirecting to Google Translate');

    // Get the current URL without the lang parameter
    const url = new URL(window.location.href);
    url.searchParams.delete('lang'); // Remove the lang parameter for the translation URL
    const cleanUrl = url.toString();

    // Create the Google Translate URL
    const translateUrl = 'https://translate.google.com/translate?sl=auto&tl=zh-CN&u=' + encodeURIComponent(cleanUrl);

    // Check if we've already redirected to avoid infinite loops
    if (!sessionStorage.getItem('translationRedirected')) {
        // Set a flag in session storage to prevent multiple redirects
        sessionStorage.setItem('translationRedirected', 'true');

        // Redirect to Google Translate
        window.location.href = translateUrl;
    } else {
        console.log('Already redirected once, not redirecting again');
        // Clear the flag after a short delay to allow future translations
        setTimeout(function() {
            sessionStorage.removeItem('translationRedirected');
        }, 5000);
    }
}

// Wait for the DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM fully loaded, initializing translation');
    initTranslation();
});
