/**
 * Input Label Fix - Simplified approach for normal input fields
 * This script handles the transition of labels for normal input fields (not select boxes)
 * when they are focused or have content, using only one label instead of two.
 */

document.addEventListener('DOMContentLoaded', function() {
    // Function to fix the label transition for normal input fields
    function fixInputLabels() {
        console.log('Input label fix script loaded');

        // Find all form fields with floating labels
        const formFields = document.querySelectorAll('.form-field-float');

        console.log('Found ' + formFields.length + ' form fields');

        formFields.forEach(function(field) {
            // Skip fields that contain select elements (they use a different approach)
            if (field.querySelector('select')) {
                // Add a class to select fields to differentiate them
                field.classList.add('select-field');
                return;
            }

            // Find the input, textarea, or date input in the field
            const input = field.querySelector('input, textarea');
            if (!input) return;

            // Special handling for date inputs
            if (input.type === 'date') {
                input.setAttribute('data-original-placeholder', ' ');
            }

            // Add a class to identify this as a normal input field (not a select)
            field.classList.add('input-field');

            // Find the label inside the wpcf7-form-control-wrap
            const innerLabel = field.querySelector('.wpcf7-form-control-wrap label');

            // Find the label in the p tag (the "hidden" label)
            const pLabel = field.querySelector('p > label');

            // If we don't have at least one label, skip this field
            if (!innerLabel && !pLabel) {
                console.log('No labels found for field', field);
                return;
            }

            // If we have both labels, hide the p label completely
            if (pLabel) {
                pLabel.style.cssText = 'opacity: 0 !important; position: absolute !important; pointer-events: none !important; height: 1px !important; width: 1px !important; overflow: hidden !important; visibility: hidden !important; display: none !important;';
            }

            // If we have an inner label, use it for the transition
            if (innerLabel) {
                // Make sure the inner label is visible and properly positioned
                innerLabel.style.cssText = 'position: absolute !important; top: 10px !important; left: 10px !important; color: #666 !important; font-size: 16px !important; transition: 0.3s !important; pointer-events: none !important; z-index: 5 !important; margin: 0 !important; padding: 0 5px !important; background-color: transparent !important; display: block !important; opacity: 1 !important; visibility: visible !important; width: auto !important; transform: none !important;';

                // Add focus event listener
                input.addEventListener('focus', function() {
                    // Add has-focus class to the field
                    field.classList.add('has-focus');

                    // Move the inner label up and style it for focus
                    innerLabel.style.cssText = 'position: absolute !important; top: -10px !important; left: 10px !important; font-size: 12px !important; color: #4CAF50 !important; background-color: white !important; padding: 0 5px !important; transition: 0.3s !important; pointer-events: none !important; z-index: 5 !important; margin: 0 !important; display: inline !important; width: fit-content !important; opacity: 1 !important; visibility: visible !important;';
                });

                // Add blur event listener
                input.addEventListener('blur', function() {
                    // Remove has-focus class from the field
                    field.classList.remove('has-focus');

                    // If the input has content, keep the label up but change color
                    if (this.value) {
                        field.classList.add('has-content');
                        innerLabel.style.cssText = 'position: absolute !important; top: -10px !important; left: 10px !important; font-size: 12px !important; color: #666 !important; background-color: white !important; padding: 0 5px !important; transition: 0.3s !important; pointer-events: none !important; z-index: 5 !important; margin: 0 !important; display: inline !important; width: fit-content !important; opacity: 1 !important; visibility: visible !important;';
                    } else {
                        // If the input is empty, move the label back to the default position
                        field.classList.remove('has-content');
                        innerLabel.style.cssText = 'position: absolute !important; top: 10px !important; left: 10px !important; color: #666 !important; font-size: 16px !important; transition: 0.3s !important; pointer-events: none !important; z-index: 5 !important; margin: 0 !important; padding: 0 5px !important; background-color: transparent !important; display: block !important; opacity: 1 !important; visibility: visible !important; width: auto !important; transform: none !important;';
                    }
                });

                // Check initial state
                if (input.value) {
                    field.classList.add('has-content');
                    innerLabel.style.cssText = 'position: absolute !important; top: -10px !important; left: 10px !important; font-size: 12px !important; color: #666 !important; background-color: white !important; padding: 0 5px !important; transition: 0.3s !important; pointer-events: none !important; z-index: 5 !important; margin: 0 !important; display: inline !important; width: fit-content !important; opacity: 1 !important; visibility: visible !important;';
                }
            }
        });
    }

    // Run the fix immediately and after delays to ensure it works
    fixInputLabels();
    setTimeout(fixInputLabels, 500);
    setTimeout(fixInputLabels, 1000);

    // Also run when the window is fully loaded
    window.addEventListener('load', function() {
        fixInputLabels();
        setTimeout(fixInputLabels, 500);
    });

    // Also run when Contact Form 7 initializes
    document.addEventListener('wpcf7invalid', fixInputLabels);
    document.addEventListener('wpcf7spam', fixInputLabels);
    document.addEventListener('wpcf7mailsent', fixInputLabels);
    document.addEventListener('wpcf7mailfailed', fixInputLabels);
    document.addEventListener('wpcf7submit', fixInputLabels);
});
