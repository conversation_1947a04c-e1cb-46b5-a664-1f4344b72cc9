/**
 * Enqueue Input Label Fix
 * This script loads the CSS and JavaScript files for the input label fix.
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Enqueue input label fix script initialized');

    // Load the CSS
    var link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = '/wp-content/themes/loan-city/assets/css/input-label-fix.css';
    link.onload = function() {
        console.log('Input label fix CSS loaded successfully');
    };
    document.head.appendChild(link);

    // Load the JavaScript
    var script = document.createElement('script');
    script.src = '/wp-content/themes/loan-city/assets/js/input-label-fix.js';
    script.onload = function() {
        console.log('Input label fix JS loaded successfully');
    };
    document.body.appendChild(script);
});
